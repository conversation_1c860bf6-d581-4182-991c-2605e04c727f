# Dialer Frontend API Summary

## Overview
The Dialer Frontend API provides comprehensive functionality for managing call center operations, including campaigns, leads, agents, sales, invoices, and reporting.

## Total Endpoints: 140+

### Endpoint Categories

#### Core Business Objects
- **Campaigns** (20+ endpoints): Campaign management, file uploads, statistics
- **Leads** (5+ endpoints): Lead management, card tokenization
- **Agents** (4+ endpoints): Agent management and configuration
- **Users** (4+ endpoints): User account management
- **Clients** (5+ endpoints): Client management and campaigns

#### Call Center Operations
- **Call Results** (4+ endpoints): Call outcome tracking
- **Call Records** (4+ endpoints): Call recording management
- **Callbacks** (5+ endpoints): Callback scheduling
- **Sales** (5+ endpoints): Sales transaction management
- **Dispositions** (4+ endpoints): Call disposition management

#### Financial Management
- **Invoices** (9+ endpoints): Invoice creation, payments, notes
- **Payment Logs** (6+ endpoints): Payment tracking and history
- **Recurring Payments** (2+ endpoints): Subscription management

#### Campaign Management
- **Campaign Stages** (7+ endpoints): Stage workflows and rules
- **Campaign Products** (2+ endpoints): Product management
- **Campaign Training Docs** (2+ endpoints): Training material management
- **Campaign Notes** (2+ endpoints): Campaign annotations

#### System Configuration
- **Skills/Sub-skills** (6+ endpoints): Skill taxonomy management
- **Devices** (3+ endpoints): Phone device management
- **Campaign Types** (2+ endpoints): Campaign type definitions
- **Date/Time Rules** (3+ endpoints): Scheduling rule management
- **Agent States** (2+ endpoints): Agent status management

#### Reporting & Analytics
- **Reports** (5+ endpoints): Report generation and scheduling
- **Report Modules** (1+ endpoint): Available report components
- **Report History** (1+ endpoint): Report execution history
- **Report Schedules** (2+ endpoints): Automated report scheduling

#### System Administration
- **System Config** (6+ endpoints): System-wide configuration
- **Authentication** (1+ endpoint): User login and session management
- **Audit Trails** (3+ endpoints): Activity tracking
- **Email History** (1+ endpoint): Email communication tracking

## Key Features

### File Upload Capabilities
- **Lead Import**: CSV-only validation with multi-layer protection
- **Product Upload**: CSV file processing
- **Training Documents**: Multiple file type support
- **Batch Operations**: CSV-based bulk operations

### Security Features
- JWT-based authentication
- Role-based access control (Admin, Supervisor, Agent, Client Admin)
- Client-level data isolation
- File type validation
- SQL injection protection

### Data Validation
- Server-side validation for all endpoints
- Required field enforcement
- Data type validation
- Foreign key constraint checking
- Custom business rule validation

### Integration Points
- TSYS payment processing integration
- Redis for session management and caching
- Queue system for background processing
- Email system integration
- Reporting engine integration

## Common Patterns

### Standard CRUD Operations
Most resources follow RESTful patterns:
- GET `/resource` - List all
- GET `/resource/:id` - Get by ID
- POST `/resource` - Create new
- PUT `/resource/:id` - Update existing
- DELETE `/resource/:id` - Delete

### Nested Resources
Many endpoints provide access to related data:
- `/campaigns/:id/agents` - Agents for specific campaign
- `/invoices/:id/payments` - Payments for specific invoice
- `/campaigns/:id/uploads` - Upload history for campaign

### File Upload Endpoints
File uploads typically return task IDs for background processing:
```json
{
  "success": true,
  "taskId": "unique-task-id"
}
```

### Pagination Support
List endpoints often support pagination:
- `limit` - Number of records
- `offset` - Starting position
- `orderBy` - Sort field
- `orderDir` - Sort direction

## Error Handling

### Standard HTTP Status Codes
- 200: Success
- 201: Created
- 400: Bad Request
- 401: Unauthorized
- 403: Forbidden
- 404: Not Found
- 500: Internal Server Error

### Error Response Format
```json
{
  "error": "Descriptive error message"
}
```

## Authentication Requirements

### Headers Required
- `X-Access-Token`: JWT token
- `X-Key`: Username

### Public Endpoints
Only the login endpoint (`POST /api/v1/login`) does not require authentication.

## File Upload Restrictions

### CSV Files (Lead Import)
- **Validation**: Multi-layer (browser, client-side, server-side)
- **Required**: Only CSV files accepted
- **Error Handling**: Clear error messages for invalid file types

### Other Uploads
- Training documents: Multiple file types
- Product uploads: CSV preferred
- Batch operations: CSV required

## Performance Considerations

### Background Processing
Long-running operations use background tasks:
- File imports
- Report generation
- Batch operations

### Caching
- Redis for session data
- System configuration caching
- Campaign statistics caching

### Rate Limiting
- API call rate limiting
- File upload size restrictions
- Bulk operation limits

## Development and Testing

### Test Endpoints
- Payment testing endpoint for development
- System monitoring endpoints
- Task progress tracking

### Debugging Support
- Detailed error messages
- Audit trail endpoints
- System configuration access

## API Versioning
Current version: v1
Base URL: `/api/v1`

## Documentation
Complete endpoint documentation available in `API_DOCUMENTATION.md` with:
- Detailed parameter descriptions
- Required/optional field specifications
- Response format examples
- Error handling information
- Data validation rules
