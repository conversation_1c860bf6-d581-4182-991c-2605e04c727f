var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
    db = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(db, Sequelize)

const TEST_STAGE_ID = 942
const TEST_AGENT_ID = 17

// Random number between 90000 and 99999
const FAKE_WRONG_AGENT = Math.floor(Math.random() * 10000) + 90000

if (FAKE_WRONG_AGENT)
    console.log(`Using fake wrong agent: ${FAKE_WRONG_AGENT}`)

getNextLeadForAgent()

function getNextLeadForAgent () {
    var now = new Date()

    var campaignStage = {
        id: TEST_STAGE_ID
    }
   
    var session = {
        agentId: TEST_AGENT_ID
    }
    
    var callbackLeadIds = []

    for (var i = 1; i < 200; i++) {
        callbackLeadIds.push(i)
    }
    
    var callAttemptWhereQuery = {
        dontContactUntil: {
            $or: {
                $eq: null,
                $lt: now
            }
        },
        $or: [{
            phone_home: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            }
        }, {
            phone_mobile: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            },
        }, {
            phone_work: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            },
        }, {
            phone_workmobile: {
                $and: {
                    $not: null,
                    $ne: ''
                }
            }
        }]
    }

    if (callbackLeadIds && callbackLeadIds.length) {
        callAttemptWhereQuery['id'] = {
            $notIn: callbackLeadIds
        }
    }

    callAttemptWhereQuery.agentPortfolioTag = {
        $or: {
            $in: [''],
            $eq: null
        }
    }

    var result

    return Promise.resolve(
        db.transaction(function (t) {
            return Promise.resolve(
                Models.CallAttempt.findOne({
                    include: [{
                        model: Models.Lead,
                        where: callAttemptWhereQuery,
                        include: [{
                            model: Models.Skill,
                            as: 'tfSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tfSubSkill'
                        }, {
                            model: Models.Skill,
                            as: 'tmSkill'
                        }, {
                            model: Models.SubSkill,
                            as: 'tmSubSkill'
                        }, {
                            model: Models.Invoice
                        }, {
                            model: Models.CampaignLead,
                            where: {
                                currentCampaignStageId: campaignStage.id
                            }
                        }, {
                            model: Models.PaymentLog,
                            include: [Models.RecurringPayment, {
                                model: Models.Campaign,
                                attributes: ['id', 'name']
                            }]
                        }]
                    }],
                    where: Sequelize.and({
                        campaignstageId: campaignStage.id
                    }, {
                        startTime: {
                            $lt: '23:59:59'
                        }
                    }, {
                        endTime: {
                            $gt: '00:00:00'
                        }
                    }, {
                        startDate: {
                            $or: {
                                $lt: now,
                                $eq: null
                            }
                        }
                    }, {
                        endDate: {
                            $or: {
                                $gt: now,
                                $eq: null
                            }
                        }
                    }),
                    order: [
                        ['lastDispositionDate'],
                        ['randomSelector']
                    ]
                }, {
                    transaction: t,
                    lock: t.LOCK.UPDATE
                }).then(function (callAttempt) {
                    if (callAttempt) {
                        console.log(`Selected call attempt ${callAttempt.id}`)
                        return callAttempt.lead.updateAttributes({
                            // dontContactUntil: nowPlusHours(24),
                            lastAgent: FAKE_WRONG_AGENT || session.agentId
                        }, {
                            transaction: t
                        }).then(() => {
                            return callAttempt
                        }).catch(Promise.reject)
                    } else
                        return Promise.resolve()
                }).catch(Promise.reject)
            )
        }).then(function (_result) {
            result = _result

            console.log('Result: ', JSON.stringify(result))

            if (result && result.lead) {
                console.log(`Reloading lead ${result.lead.id}`)
                return result.lead.reload().then(l => console.log('Lead reload completed at ' + result.lead.updatedAt))
            }
        }).then(_ => {
            if (result && result.lead && result.lead.lastAgent !== session.agentId) {
                return console.log('Selected lead for wrong agent: ', result.lead.lastAgent)
            } else if (result) {
                return console.log('Destroy call attempt', result)
            }
            else {
                console.log(`Unknown response`)
            }
        }).catch(function (e) {
            return Promise.reject(e)
        })
    )
}