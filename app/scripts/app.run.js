'use strict';

angular.module('dialerFrontendApp')
  .run(function($rootScope, $state, AuthenticationFactory, $location, $window) {
    $rootScope.$state = $state;

    //when the page refreshes, check if the user is already logged in
    AuthenticationFactory.check();

    $rootScope.$on('$stateChangeStart', function (event, toState) {
      if ((toState.data && toState.data.requiredLogin) && !AuthenticationFactory.isLogged) {
        $location.path('/login');
        event.preventDefault();
      }
      else {
        // check if user object exists else fetch it. This is incase of a page refresh
        if (!AuthenticationFactory.user) {
          AuthenticationFactory.user = $window.sessionStorage.user;
        }

        if (!AuthenticationFactory.userRoles || !AuthenticationFactory.isAdmin) {
          AuthenticationFactory.userRoles = { }; 
          AuthenticationFactory.userRoles.isAdmin = $window.sessionStorage.isAdmin;
          AuthenticationFactory.userRoles.isSupervisor = $window.sessionStorage.isSupervisor;
          AuthenticationFactory.userRoles.isAgent = $window.sessionStorage.isAgent;
          AuthenticationFactory.userRoles.isSuperManager = $window.sessionStorage.isSuperManager;
          AuthenticationFactory.userRoles.isSuperAdmin = $window.sessionStorage.user === 'dualtone_admin';
        }

        if (toState.data && toState.data.requiredRole) {
          switch(toState.data.requiredRole) {
            case 'admin':
              if(!AuthenticationFactory.userRoles.isAdmin && !AuthenticationFactory.userRoles.isSuperManager) {
                event.preventDefault();
              }
              break;
            case 'supervisor':
              if(!AuthenticationFactory.userRoles.isSupervisor) {
                event.preventDefault();
              }
              break;
            case 'agent':
              if(!AuthenticationFactory.userRoles.isAgent) {
                event.preventDefault();
              }
              break;
            case 'system':
              if(!AuthenticationFactory.userRoles.isSuperAdmin) {
                event.preventDefault();
              }
              break;
            case 'collections':
              if(!AuthenticationFactory.userRoles.isAdmin || !AuthenticationFactory.userRoles.isSupervisor) {
                event.preventDefault();
              }
              break;
            }
          }
        }
      }
    );

    $rootScope.$on('$stateChangeSuccess', function () {
      $rootScope.showMenu = AuthenticationFactory.isLogged;
      $rootScope.roles = AuthenticationFactory.userRoles;

      // if the user is already logged in, take him to the home page
      if (AuthenticationFactory.isLogged === true && $location.path() === '/login') {
        $location.path('/');
      }
    });
  });