'use strict';

angular.module('dialerFrontendApp')
    .controller('TestPaymentCtrl', function ($window, $scope, $http, moment, APP_SETTINGS) {
        $scope.creditCard = {
            name: '',
            number: '',
            expiry: '',
            pin: ''
        };

        $scope.errors = {

        };

        $scope.cardtoken = {

        };


        $scope.testMe = function () {
            // $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + 1317133 + '/cardtoken').catch(angular.noop)
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + 1317133 + '/cardtoken', {
                tsepToken: 1234
            })
        };

        extScope = $scope;
        $scope.tsysEvent = function (eventType, event) {
            console.log(eventType, event)
            switch (eventType) {
                case 'FieldValidationErrorEvent':
                    $scope.errors[event.fieldName] = event.message;
                    break;
                case 'TokenEvent':
                    if (event.message === 'Success') {
                        $scope.cardtoken = event;
                    } else {
                        $scope.errors.master = event.message;
                    }
                    break;
            }
        };

        $scope.stringify = function (obj) {
            return JSON.stringify(obj, null, 2);
        };

        function buildTsys() {
            $http.get(APP_SETTINGS.BASE_API_URL + 'manifest').then(function (response) {
                var manifest = response.data;
                var s = document.createElement('script');
                s.src = 'https://stagegw.transnox.com/transit-tsep-web/jsView/88700000302202?' + manifest;
                document.head.appendChild(s);
            });
        };

        function resetTsys() {
            $scope.errors = {};
            $scope.cardtoken = {};
            $('#tsep-cardNum').remove();
            $('#tsep-datepicker').remove();
            $('#tsep-cvv2').remove();
            buildTsys();
        };

        buildTsys();

        $scope.message = '';
        $scope.error = '';
        $scope.authError = '';
        $scope.chargeError = '';

        $scope.amount = 10;
        $scope.authResponse = '';
        $scope.chargeResponse = '';

        $scope.send = function () {
            resetTsys();
            return

            var body = {
                amount: $scope.amount,
                creditCardNumber: $scope.creditCard.number,
                creditCardDate: $scope.creditCard.expiry,
                creditCardPin: $scope.creditCard.pin,
                address1: $scope.creditCard.address1,
                zip: $scope.creditCard.zip
            }

            $scope.authResponse = '';
            $scope.chargeResponse = '';
            $scope.authError = '';
            $scope.chargeError = '';

            var request = {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'testpayment',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: body
            }
            $http(request).then(function (response) {
                if (response.data.Success === false) {
                    $scope.error = response.data.message
                } else {
                    console.log(response.data)
                    if (response.data.authResponse.status === 'PASS') {
                        $scope.authResponse = response.data.authResponse.responseMessage;
                    } else {
                        $scope.authError = response.data.authResponse.responseMessage;
                    }
                    if (response.data.chargeResponse.status === 'PASS') {
                        $scope.chargeResponse = response.data.chargeResponse.responseMessage;
                    } else {
                        $scope.chargeError = response.data.chargeResponse.responseMessage;
                    }

                }
            }, function (response) {
                $scope.error = response.data.message
            })
        };
    })