'use strict';

angular.module('dialerFrontendApp')
    .controller('TestPledgeV2Ctrl', function ($window, $scope, $rootScope, $http, moment, APP_SETTINGS) {
        $scope.creditCard = {
            name: '',
            number: '',
            expiry: '',
            pin: ''
        };

        $scope.formData = {
            paymentType: 'Credit Card',
            initialPaymentDate: new Date(),
            giftAmount: 150
        }

        $scope.lead = {
            id: 1,
            clientRef: 100,
            first_name: '<PERSON>',
            last_name: '<PERSON>'
        }

        $scope.payments = {
            count: 1,
            every: 1,
            unit: 'months',
            values: [{
                amount: 0,
                date: new Date()
            }]
        };


        $scope.format = 'dd-MMMM-yyyy';
        $scope.initialOptions = {
            minDate: new Date(),
            maxDate: moment().add(90, 'days').toDate()
        };
        $scope.dates = {
            initial: false,
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.updatePaymentCount = function () {
            try {
                $scope.payments.values = []
                var range = Math.ceil($scope.formData.giftAmount / $scope.payments.count);
                $scope.payments.total = $scope.formData.giftAmount;
                $scope.payments.difference = 'correct amount';
                var total = 0, date = moment($scope.formData.initialPaymentDate);
                for (var i = 0; i < $scope.payments.count; i++) {
                    var amount;
                    if ((total + range) < $scope.formData.giftAmount)
                        amount = range;
                    else
                        amount = $scope.formData.giftAmount - total;

                    if (amount < 0) amount = 0;

                    $scope.payments.values.push({
                        amount, date: moment(date).toDate()
                    });

                    total += range

                    date = date.add($scope.payments.every, $scope.payments.unit);
                }
            } catch (err) {
                console.log(err)
            }
        };

        $scope.updatePayment = function () {
            var total = 0
            for (let i = 0; i < $scope.payments.values.length; i++) {
                total += $scope.payments.values[i].amount;
            }
            $scope.payments.total = total
            try {
                var difference = total - $scope.formData.giftAmount;
                if (difference === 0)
                    $scope.payments.difference = 'correct amount'
                else if (difference > 0)
                    $scope.payments.difference = '$' + difference + ' too much'
                else
                    $scope.payments.difference = '$' + difference + ' too little'
            }
            catch (err) {
                console.log(err)
            }

            $scope.paymentsBroken = !!difference;
        };

        $scope.fixPayments = function (index) {
            var difference = $scope.formData.giftAmount - $scope.payments.total;
            $scope.payments.values[index].amount += difference;
            $scope.updatePayment();
        }

        $scope.message = '';
        $scope.error = '';

        $scope.amount = 10;
        $scope.response = '';

        $scope.send = function () {
            var body = {
                amount: $scope.amount,
                creditCardNumber: $scope.creditCard.number,
                creditCardDate: $scope.creditCard.expiry,
                creditCardPin: $scope.creditCard.pin
            }

            $scope.response = '';
            $scope.error = '';

            var request = {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'testpayment',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: body
            }
            $http(request).then(function (response) {
                if (response.data.Success === false) {
                    $scope.error = response.data.message
                } else {
                    if (response.data.SaleResponse.status === 'PASS') {
                        $scope.response = response.data.SaleResponse.responseMessage
                    } else {
                        $scope.error = response.data.SaleResponse.responseMessage
                    }
                }
            }, function (response) {
                $scope.error = response.data.message
            })
        };

        $scope.ok = function () {
            if ($scope.formData.splitPayments) {
                var lastDate = $scope.payments.values[$scope.payments.values.length - 1].date
                var err = $rootScope.checkCardDate(lastDate, $scope.creditCard.expiry);
                console.log(err);
            }
        };

        $rootScope.buildTsys(8);
    })