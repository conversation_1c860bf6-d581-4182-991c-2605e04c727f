'use strict';

angular.module('dialerFrontendApp')
    .controller('AdminRecurringPaymentEditCtrl', function ($scope, $rootScope, $uibModalInstance, APP_SETTINGS, $http, recurring, PaymentLog, RecurringPayment, CallResult, _, disposition, agent) {
        $scope.recurring = angular.copy(recurring);
        $scope.payments = recurring.paymentlogs;
        $scope.lead = recurring.lead;
        $scope.lastError = '';
        $scope.notes = $scope.recurring.invoice.notes;

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);

        var firstLoad = true;
        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            // dont do this the first time as the cardtoken gets reset to blank immediately and we lose the stored card
            if (!firstLoad) {
                $scope.cardToken = $rootScope.cardtoken;
                if ($scope.cardToken.tsepToken && !$scope.validtoken && !$scope.saving) {
                    // just save it so the user doesnt have to
                    $scope.saveCard();
                }
            }
            firstLoad = false;
        }, true);

        $scope.nextPayment = recurring.paymentlogs ? recurring.paymentlogs[recurring.paymentlogs.length - 1] : '';
        if ($scope.nextPayment.status !== 'pending' || $scope.nextPayment.deleted || $scope.nextPayment.disabled) {
            $scope.lastError = $scope.nextPayment.error;
            $scope.nextPayment = {};
        }

        $scope.format = 'dd-MMMM-yyyy';
        $scope.dates = {
            initial: false,
        };
        $scope.minDate = new Date();

        $scope.validtoken = false;
        $scope.saving = false;
        $scope.cardToken = $scope.lead.cardtoken || {};
        $scope.existingCard = angular.copy($scope.lead.cardtoken || {});
        $rootScope.cardtoken = {};
        if ($scope.cardToken.maskedCardNumber) {
            $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
        }
        $rootScope.clientId = recurring.clientId;
        $scope.cardToken.cardHolderName = $scope.lead.first_name + ' ' + $scope.lead.last_name;

        var expirationDate = moment($scope.cardToken.expirationDate, 'MMYYYY')
        $scope.cardToken.expirationDate = expirationDate.format('MM/YYYY')
        if (expirationDate > moment()) {
            $scope.validtoken = true;
            $scope.tokenerror = '';
            $scope.existingCard = {};
        } else {
            $scope.tokenerror = 'Saved Card Expired';
        }

        if (!$scope.validtoken) {
            $rootScope.resetTsys();
        }

        $scope.carderrors = {};

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $scope.saving = true;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.saving = false;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;

                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.saving = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.save = function () {
            if ($scope.notes !== $scope.recurring.invoice.notes) {
                $http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.recurring.invoice.id, {
                    notes: $scope.notes
                })
            }

            // update the recurring obj;
            $scope.recurring.isCancelled = false;
            $scope.recurring.isFinished = false;
			
			if (!agent) {
                // create a callresult for reporting
                CallResult.save({
                    wrapup: 'Recurring Maintenance',
                    leadId: $scope.recurring.leadId,
                    agentId: $rootScope.loggedInUser.agentId,
                    campaignstageId: null,
                    campaignId: $scope.recurring.campaignId,
                    clientId: $scope.recurring.clientId,
                    numberOfInstallments: $scope.recurring.id, //this is the important bit
                    notes: $scope.notes,
                    callAttemptJson: JSON.stringify({
                        startTime: '00:00:00',
                        endTime: '23:59:59',
                        monday: 1,
                        tuesday: 1,
                        wednesday: 1,
                        thursday: 1,
                        friday: 1,
                        saturday: 1,
                        sunday: 1,
                        isCallback: true,
                        randomSelector: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaaa',
                        campaignstageId: null,
                        campaignId: $scope.recurring.campaignId,
                        leadId: $scope.recurring.leadId
                    })
                }).$promise.finally(angular.noop);
            }

            var callResultData = {
                numberOfInstallments: $scope.recurring.id
            }

            RecurringPayment.update($scope.recurring, function () {
                if ($scope.nextPayment.id) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + $scope.nextPayment.id, {
                        amount: $scope.recurring.amount,
                        paymentDate: $scope.nextPayment.paymentDate
                    }).then(function () {
                        $uibModalInstance.close({ disposition, wrapUpNotes: $scope.notes });
                    })
                } else {
                    var payment = new PaymentLog();
                    payment.amount = $scope.recurring.amount;
                    payment.paymentDate = $scope.nextPayment.paymentDate;
                    payment.isPaid = false;
                    payment.status = 'pending';
                    payment.deleted = false;
                    payment.disabled = false;
                    payment.source = 'recurring';
                    payment.recurringpaymentId = $scope.recurring.id;
                    payment.leadId = $scope.recurring.leadId;
                    payment.callresultId = $scope.recurring.callresultId;
                    payment.userId = $rootScope.loggedInUser.id;
                    payment.clientId = $scope.recurring.clientId;
                    payment.campaignId = $scope.recurring.campaignId;
                    $http.post(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.recurring.invoiceId + '/audit', {
                        invoiceId: $scope.recurring.invoiceId,
                        userId: $rootScope.loggedInUser.id,
                        changeType: 'Next Payment Date',
                        field: 'paymentDate',
                        fromValue: null,
                        toValue: $scope.nextPayment.paymentDate
                    }).catch(function () { })
                    payment.$save(function () {
                        $uibModalInstance.close({ disposition, wrapUpNotes: $scope.notes, callResultData });
                    })
                }
            })
        };

        $scope.close = function () {
            $uibModalInstance.close(false);
        };
    });