'use strict';

angular.module('dialerFrontendApp').controller('AdminRecurringCtrl', function ($scope, $rootScope, $uibModal, APP_SETTINGS, $http, moment, RecurringPayment, recurring, campaigns, SweetAlert, _) {
    $rootScope.pageTitle = 'Recurring Payments';
    $scope.campaigns = []
    $scope.filters = {
        status: '',
        clientRef: '',
        leadId: '',
        campaign: ''
    };

    function initialLoad() {
        $scope.campaigns = campaigns.filter((c) => recurring.some(r => r.campaignId === c.id));
        recurring.forEach(row => {
            var last = row.paymentlogs[row.paymentlogs.length - 1]
            if (last.status === 'pending' && !last.deleted && !last.disabled) {
                row.nextPayment = last.paymentDate
            } else {
                row.nextPayment = ''
            }
            row.count = row.paymentlogs.filter(l => l.status !== 'pending' && !l.deleted && !l.disabled).length
        })
    }

    initialLoad();

    $scope.total = function (row) {
        let total = 0;
        row.paymentlogs.forEach(function (payment) {
            if (payment.deleted || payment.disabled || payment.status === 'pending') return
            total += payment.amount
        })
        return total
    };

    $scope.filtered = function () {
        return recurring.filter(function (r) {
            if ($scope.filters.status === 'active' && (r.isCancelled || r.isFinished)) return false
            if ($scope.filters.status === 'finished' && !r.isFinished) return false
            if ($scope.filters.status === 'cancelled' && !r.isCancelled) return false
            if ($scope.filters.clientRef && r.lead.clientRef != $scope.filters.clientRef) return false
            if ($scope.filters.leadId && r.lead.id != $scope.filters.leadId) return false
            if ($scope.filters.campaign && r.campaignId != $scope.filters.campaign) return false
            return true
        }).sort((a, b) => new Date(a.nextPayment) - new Date(b.nextPayment))
    };

    $scope.unit = function (row) {
		if (row.unit === 'day') return 'Daily'
        return row.unit.charAt(0).toUpperCase() + row.unit.slice(1) + 'ly'
    };

    $scope.edit = function (row) {
        var modalInstance = $uibModal.open({
            templateUrl: 'views/collections/recurring/edit.html',
            controller: 'AdminRecurringPaymentEditCtrl',
            backdrop: 'static',
            keyboard: false,
            size: 'lg',
            resolve: {
                recurring: function () {
                    return RecurringPayment.get({
                        id: row.id
                    }).$promise;
                },
                disposition: function () {
                    return null;
                },
                agent: function () {
                    return false
                }
            }
        });

        modalInstance.result.then(function (reload) {
            if (reload) {
                reloadPayments()
            }
        });
    };

    $scope.payments = function (row) {
        $uibModal.open({
            templateUrl: 'views/collections/recurring/admin.payments.html',
            controller: 'AdminRecurringPaymentModalCtrl',
            backdrop: 'static',
            keyboard: false,
            resolve: {
                recurring: function () {
                    return row
                }
            }
        });
    };

    $scope.cancel = function (row) {
        SweetAlert.swal({
            title: "Cancel Recurring Payment?",
            text: "Cancelling will cancel the next payment, you will need to move the lead manually!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes",
            cancelButtonText: "No",
            closeOnConfirm: true,
            closeOnCancel: true
        }, function (isConfirm) {
            if (isConfirm) {
                //   delete the next payment and set the isCancelled on the recurring
                function cancel() {
                    row.isCancelled = true;
                    row.cancelledDate = new Date();
                    row.$update(function () {
                        reloadPayments();
                    })
                }
                var nextPayment = row.paymentlogs[row.paymentlogs.length - 1];
                if (nextPayment.status !== 'pending' || nextPayment.deleted || nextPayment.disabled) {
                    cancel()
                } else {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + nextPayment.id, {
                        deleted: true,
                        disabled: true
                    }).then(function () {
                        cancel();
                    })
                }
            }
        });
    };

    function reloadPayments() {
        RecurringPayment.query().$promise.then(function (data) {
            recurring = data;
            initialLoad();
        })
    }
});