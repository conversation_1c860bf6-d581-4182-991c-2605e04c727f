'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('AdminRecurringPaymentModalCtrl', function ($scope, $rootScope, $uibModalInstance, APP_SETTINGS, $http, moment, recurring, SweetAlert, _) {
        $scope.recurring = recurring;
        $scope.payments = recurring.paymentlogs;
        $scope.lead = recurring.lead;

        $scope.close = function () {
            $uibModalInstance.close();
        };

    });