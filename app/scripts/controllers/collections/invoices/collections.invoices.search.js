'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('InvoiceSearchCtrl', function ($scope, $rootScope, $uibModalInstance, filters, clients, campaigns, campaignId) {
		$scope.filters = filters;
		$scope.campaignId = campaignId;
		$scope.campaigns = campaigns;
		$scope.clients = clients;

		$scope.search = function () {
			for (var key in filters) {
				if(filters[key] === '' || filters[key] === null)
					delete filters[key]
			}
			$uibModalInstance.close(filters);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}
	})