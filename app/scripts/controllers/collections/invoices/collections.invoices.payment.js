'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('InvoicePaymentCtrl', function ($scope, $rootScope, $uibModalInstance, $window, invoice) {
		$scope.amountToPay = invoice.amountRemaining;
		$scope.payAmount = 0;

		$scope.save = function () {
			$uibModalInstance.close($scope.payAmount);
		}

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}
	})