'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('InvoicesCtrl', function ($scope, $rootScope, $uibModal, $window, $http, APP_SETTINGS, Invoice, SweetAlert, Campaign, Client, PaymentLog, campaign, _, moment) {
		$rootScope.pageTitle = campaign ? campaign.name + ' | Invoices' : 'Invoices';
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.sortType = 'invoice.id';
		$scope.filters = {};
		$scope.invoices = [];
		$scope.totalInvoices = 0;
		$scope.campaignId = campaign ? campaign.id : null;

		$scope.generate = function (invoice) {
			Invoice.generate({
				id: invoice.id
			}).$promise.then(function (result) {
				if (result.error) {
					SweetAlert.swal("Error", result.error, "error");
				} else if (result.id) {
					$window.location.href = APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + result.id + '/pdf?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
				} else {
					SweetAlert.swal("Success", "Creation Complete", "success");
				}
			})
		};

		$scope.generateTestInvoice = function (invoice) {
			Invoice.generateTest({
				id: invoice.id
			}).$promise.then(function (result) {
				if (result.error) {
					SweetAlert.swal("Error", result.error, "error");
				} else if (result.id) {
					$window.location.href = APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + result.id + '/pdf?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
				} else {
					SweetAlert.swal("Success", "Creation Complete", "success");
				}
			})
		};

		$scope.friendly = function (field) {
			switch (field) {
				case 'id':
					return 'KAOS Id';
				case 'clientRef':
					return 'Client Ref';
				case 'campaignName':
					return 'Campaign';
				case 'clientName':
					return 'Client';
				case 'leadFirstName':
					return 'Lead First Name';
				case 'leadLastName':
					return 'Lead Last Name';
				case 'paymentType':
					return 'Payment Type';
				case 'amountRemaining':
					return 'Total To Pay';
				default:
					return field;
			}
		};

		$scope.removeFilter = function (property) {
			delete $scope.filters[property];
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		$scope.removeAllFilters = function () {
			$scope.filters = {};
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		$scope.pageChanged = function (newPageNumber) {
			getResultsPage(newPageNumber);
		};

		$scope.filterChanged = function (field) {
			$scope.sortType = field;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		$scope.search = function () {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/collections/invoices/collections.invoices.search.html',
				controller: 'InvoiceSearchCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					filters: function () {
						return angular.copy($scope.filters);
					},
					campaignId: function () {
						return campaign ? campaign.id : null;
					},
					campaigns: function () {
						return Campaign.query().$promise;
					},
					clients: function () {
						return Client.query().$promise;
					}
				}
			});

			modalInstance.result.then(function (filters) {
				$scope.filters = filters;
				$scope.pagination.current = 1;
				getResultsPage(1);
			});
		};

		$scope.badPayment = function (invoice) {
			SweetAlert.swal({
				title: 'Flag as bad payment',
				text: 'This will move this lead into the bad credit cards stage and reset the amount paid\n\nDo you wish to continue?',
				type: 'warning',
				showCancelButton: true,
				confirmButtonText: "Yes",
				cancelButtonText: "No",
				closeOnConfirm: true,
				closeOnCancel: true
			}, function (isConfirm) {
				if (isConfirm) {
					//set amount paid to 0 and move lead to collections campaign stage
					invoice.amountRemaining = invoice.grandTotal;
					invoice.writtenOff = false;
					invoice.writtenOffAmount = 0;
					invoice.sendInvoice = false;
					invoice.dueDate = moment().add(14, 'days').toDate();
					Invoice.update({
						id: invoice.id
					}, invoice);

					$http.delete(APP_SETTINGS.BASE_API_URL + 'invoices/' + invoice.id + '/payments');

					Campaign.getCampaignStages({
						id: invoice.campaign.id
					}).$promise.then(function (stages) {
						var collectionsStage = _.findWhere(stages, {
							name: 'Bad Credit Cards'
						});
						if (collectionsStage) {
							Campaign.moveLeadToCampaignStage({
								id: invoice.campaign.id
							}, {
								leadId: invoice.lead.id,
								newCampaignStageId: collectionsStage.id
							});
						}
					})
				}
			})
		};

		$scope.writeoff = function (invoice) {
			SweetAlert.swal({
				title: 'Write off balance',
				text: 'This will clear the full balance of the invoice\n\nDo you wish to continue?',
				type: 'warning',
				showCancelButton: true,
				confirmButtonText: "Yes",
				cancelButtonText: "No",
				closeOnConfirm: true,
				closeOnCancel: true
			}, function (isConfirm) {
				if (isConfirm) {
					Invoice.writeOff({
						id: invoice.id
					}, {
						amount: null
					}).$promise
					.then(updatedInvoice => {
						invoice.writtenOff = updatedInvoice.writtenOff
						invoice.writtenOffAmount = updatedInvoice.writtenOffAmount
						invoice.amountRemaining = updatedInvoice.amountRemaining
						invoice.sendInvoice = updatedInvoice.sendInvoice
					});
				}
			})
		};

		$scope.addPayment = function (invoice) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/collections/invoices/collections.invoices.payment.html',
				controller: 'InvoicePaymentCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					invoice: function () {
						return invoice;
					}
				}
			});

			modalInstance.result.then(function (payAmount) {
				invoice.amountRemaining = invoice.amountRemaining - payAmount;
				$http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + invoice.id, invoice).then(function (response) {
					if (response.data.destination) {
						SweetAlert.info('Payment Received in Full', 'This lead has now been moved to the \'' + response.data.destination + '\' stage')
					} else if (response.data.destination === null) {
						SweetAlert.info('Payment Received in Full', 'This lead has now been moved out of this Campaign')
					}
				})

				var payment = new PaymentLog();
				payment.invoiceId = invoice.id;
				payment.amount = payAmount;
				payment.paymentDate = new Date();
				payment.actualPaymentDate = new Date();
				payment.isPaid = true;
				payment.disabled = false;
				payment.deleted = false;
				payment.userId = $rootScope.loggedInUser.id;
				payment.clientId = invoice.clientId;
				payment.campaignId = invoice.campaignId;
				payment.leadId = invoice.leadId;
				payment.callresultId = invoice.callresultId;
				payment.source = 'manual'
				payment.$save();

				// if (invoice.amountRemaining <= 0) {
				// 	Campaign.moveLeadToCampaignStage({
				// 		id: invoice.campaignId
				// 	}, {
				// 		leadId: invoice.leadId,
				// 		newCampaignStageId: null
				// 	});

				// 	SweetAlert.info('Payment Received in Full', 'This lead has now been removed from the collections stage.\n\nIf you wish to move to another stage, please do so manually via \'Change Stage\'');
				// }
			});
		};

		$scope.fixPayment = function (invoice) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/collections/invoices/collections.invoices.fix.html',
				controller: 'InvoiceFixCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					invoice: function () {
						return invoice;
					}
				}
			});

			modalInstance.result.then(function (payAmount) {
				invoice.amountRemaining = payAmount;
				invoice.writtenOff = false;
                invoice.writtenOffAmount = 0;
				invoice.deleted = false;

				Invoice.update({
					id: invoice.id
				}, invoice).$promise.then(function () {
					getResultsPage($scope.pagination.current)
				});
			})
		};

		function getResultsPage(pageNumber) {
			var filters = angular.copy($scope.filters);
			delete filters.clientRef;
			delete filters.campaignName;
			delete filters.clientName;
			delete filters.leadFirstName;
			delete filters.leadLastName;
			delete filters.paymentType;
			delete filters.id;

			if ($scope.filters.clientName) {
				filters.client = {
					name: $scope.filters.clientName
				};
			}
			if ($scope.filters.campaignName || campaign) {
				filters.campaign = {}
				if ($scope.filters.campaignName) filters.campaign.name = $scope.filters.campaignName;
				if (campaign && campaign.id) filters.campaign.id = campaign.id;
			}
			if ($scope.filters.paymentType) {
				filters.invoiceType = $scope.filters.paymentType;
			}
			if ($scope.filters.clientRef || $scope.filters.leadFirstName || $scope.filters.leadLastName || $scope.filters.id) {
				filters.lead = {};
				if ($scope.filters.id) filters.lead.id = $scope.filters.id;
				if ($scope.filters.clientRef) filters.lead.clientRef = $scope.filters.clientRef;
				if ($scope.filters.leadFirstName) filters.lead.first_name = $scope.filters.leadFirstName;
				if ($scope.filters.leadLastName) filters.lead.last_name = $scope.filters.leadLastName;
			}

			Invoice.query({
				page: pageNumber - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: filters
			}).$promise.then(function (result) {
				$scope.invoices = result.data.invoices;
				$scope.totalInvoices = result.data.count;
			})
		}

		getResultsPage(1);
	})