'use strict';

angular.module('dialerFrontendApp')
    .controller('InvoiceChangeCardCtrl', function ($scope, $rootScope, $uibModalInstance, $window, $http, APP_SETTINGS, leadId, clientId, _, moment) {
        $scope.cardToken = {};
        $scope.existingCard;
        $scope.digits = "";
        $scope.saving = false;
        $scope.validtoken = false;
        $scope.tokenerror = '';
        $scope.carderrors = {};
        $rootScope.clientId = clientId;

        $http.get(APP_SETTINGS.BASE_API_URL + 'leads/' + leadId + '/cardtoken').then(function (response) {
            $scope.existingCard = response.data || {};
        });

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);


        var firstLoad = true;
        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            // dont do this the first time as the cardtoken gets reset to blank immediately and we lose the stored card
            if (!firstLoad) {
                $scope.cardToken = $rootScope.cardtoken;
                if ($scope.cardToken.tsepToken && !$scope.validtoken && !$scope.saving) {
                    // just save it so the user doesnt have to
                    $scope.saveCard();
                }
            }
            firstLoad = false;
        }, true);

        $rootScope.resetTsys();

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $scope.saving = true;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + leadId + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.saving = false;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;

                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.saving = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + leadId + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.close = $uibModalInstance.close;
    });
