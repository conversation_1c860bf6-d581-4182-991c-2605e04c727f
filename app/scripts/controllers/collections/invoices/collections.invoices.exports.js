'use strict';

angular.module('dialerFrontendApp')
    .controller('InvoiceExportsCtrl', function($scope, $rootScope, $uibModal, $window, $http, APP_SETTINGS, invoicesHistory, SweetAlert) {
        $rootScope.pageTitle = 'Invoice Exports';
        $scope.totalResults = 0;
        $scope.resultsPerPage = 30;
        $scope.sortReverse = true;
        $scope.pagination = {
            current: 1
        };
        $scope.sortType = 'createdAt';
        $scope.invoicesHistory = invoicesHistory;

        $scope.downloadPDF = function(item) {
            $window.location.href = APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + item.id + '/pdf?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
        };

        $scope.email = function(history) {
            SweetAlert.swal({
                title: 'Email Invoice',
                text: 'Would you like to email this invoice to ' + history.invoice.lead.email + ' from ' + (history.invoice.client.returnEmail || '<EMAIL>') + '?',
                type: 'info',
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function(isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + history.id + '/email').then(function(result) {
                        SweetAlert.swal('Success', '', 'success');
                    }).catch(function(err) {
                        SweetAlert.swal('Warning', 'email failed', 'warning');
                    })
                };
            })
        };
    })