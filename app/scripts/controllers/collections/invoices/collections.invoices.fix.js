'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('InvoiceFixCtrl', function ($scope, $rootScope, $uibModalInstance, $window, invoice) {
        $scope.amountToPay = invoice.amountRemaining;
        $scope.max = invoice.grandTotal;

        $scope.save = function () {
            if ($scope.amountToPay > $scope.max) return
            $uibModalInstance.close($scope.amountToPay);
        }

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        }
    })