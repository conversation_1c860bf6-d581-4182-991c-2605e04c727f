'use strict';

angular.module('dialerFrontendApp')
    .controller('InvoiceHistoryCtrl', function ($scope, $rootScope, $uibModal, $window, $http, APP_SETTINGS, SweetAlert, Invoice, Campaign, CampaignStage, Skill, SubSkill, PaymentLog, RecurringPayment, DownloadUtils, invoice, _, moment) {
        $rootScope.pageTitle = 'Invoice History | KAOS ID: ' + invoice.leadId;
        $scope.invoice = invoice;
        $scope.stage = {};
        $scope.lead = {};
        $scope.skill = {};
        $scope.subskill = {};
        $scope.stages = [];
        $scope.paymentLogs = [];
        $scope.auditEvents = [];
        $scope.currentStage = {}

        function startUp() {
            Invoice.get({
                id: invoice.id
            }).$promise.then(function (newInvoice) {
                invoice = newInvoice;
                $scope.invoice = newInvoice;
            });

            if (invoice.callresult) {
                CampaignStage.get({
                    id: invoice.callresult.campaignstageId
                }).$promise.then(function (stage) {
                    $scope.stage = stage;
                });
            }

            Skill.get({
                id: invoice.lead.tfSkillId || invoice.lead.tmSkillId
            }).$promise.then(function (skill) {
                $scope.skill = skill;
            });

            SubSkill.get({
                id: invoice.lead.tfSubSkillId || invoice.lead.tmSubSkillId
            }).$promise.then(function (subskill) {
                $scope.subskill = subskill;
            });

            Invoice.getPayments({
                id: invoice.id
            }).$promise.then(function (payments) {
                $scope.paymentLogs = _.sortBy(payments, 'paymentDate').reverse();
            });

            Invoice.getEvents({
                id: invoice.id
            }).$promise.then(function (events) {
                $scope.auditEvents = events.reverse();
            })

            Campaign.getCampaignStages({
                id: $scope.invoice.campaignId
            }).$promise.then(function (stages) {
                $scope.stages = stages;
                $http.get(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.invoice.leadId + '/campaign/' + $scope.invoice.campaignId).then(function (response) {
                    var lead = response.data;
                    if (lead.campaigns && lead.campaigns.length) {
                        var campaign = lead.campaigns[0]
                        if (campaign.campaignleads && campaign.campaignleads.currentCampaignStageId) {
                            var stage = _.findWhere(stages, {
                                id: campaign.campaignleads.currentCampaignStageId
                            })
                            if (stage) $scope.currentStage = stage
                        } else {
                            $scope.currentStage = {};
                        }
                    } else {
                        $scope.currentStage = {};
                    }
                })
            })

            $http.get(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id + '/notes').then(function (response) {
                $scope.notes = response.data;
            })
        }

        $scope.paymentSourceIsFile = function (source) {
            return source && source.indexOf('.csv') === source.length - 4;
        };

        $scope.downloadFile = function (payment) {
            DownloadUtils.download(APP_SETTINGS.BASE_API_URL + 'paymentlog/download?file=' + payment.source, 'PaymentImport.csv');
        };

        $scope.parseValue = function (val, field) {
            if (field === 'writtenOff') {
                return val && val !== 'false' && val !== '0' ? 'Yes' : 'No'
            }

            if (field === 'amountRemaining' || field === 'grandTotal' || field === 'amount') {
                if (!isNaN(val)) {
                    val = '$' + parseInt(val).toLocaleString()
                    if (val.indexOf('.') > -1) {
                        var split = val.split('.')
                        split[1].padEnd(2, '0')
                        val = split.join('.')
                    }
                }
            }

            if (field === 'paymentDate' && val) {
                val = moment(val).format('M/D/YY')
            }

            return val;
        };

        $scope.disablePayment = function (payment) {
            SweetAlert.swal({
                title: "Disable Payment",
                text: "Are you user you want to disbale this payment?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, disable it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + payment.id, { disabled: true }).then(function () {
                        // we should also update the amount remaining on the invoice to be minus this amount
                        if ($scope.invoice) {
                            var amountRemaining = ($scope.invoice.amountRemaining || 0) + payment.amount
                            $http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id, { amountRemaining }).then(function () {
                                startUp()
                            })
                        } else {
                            startUp()
                        }
                    });
                }
            });
        };

        $scope.changeStage = function () {

            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.leads.changestage.html',
                controller: 'CampaignLeadsChangeStageModalCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    currentStageId: function () {
                        return $scope.currentStage.id;
                    },
                    stages: function () {
                        return $scope.stages;
                    },
                    editItem: function () {
                        return $scope.invoice.lead
                    }
                }
            });

            modalInstance.result.then(function (result) {
                Campaign.moveLeadToCampaignStage({
                    id: $scope.invoice.campaignId
                }, {
                    leadId: $scope.invoice.leadId,
                    newCampaignStageId: result || null
                }).$promise.then(startUp)
            })
        };

        $scope.undo = function (ev) {
            $scope.invoice[ev.field] = ev.fromValue;
            Invoice.update({
                id: $scope.invoice.id
            }, { [ev.field]: ev.fromValue }).$promise.then(function () {
                startUp();
            })
        };

        $scope.alterAmount = function () {
            SweetAlert.swal({
                title: 'Change Amount Remaining',
                text: 'Set the amount Remaining (max ' + $scope.invoice.grandTotal + ')',
                type: 'input',
                confirmButtonText: "Confirm",
                cancelButtonText: "Cancel",
                showCancelButton: true,
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (inputValue) {
                if (inputValue === false) return;
                if (isNaN(inputValue)) return;
                var amount = parseFloat(inputValue)
                if (amount < 0 || amount > $scope.invoice.grandTotal) return;

                $scope.invoice.amountRemaining = amount;
                $scope.invoice.writtenOff = false;
                $scope.invoice.writtenOffAmount = 0;
                $scope.invoice.deleted = false;
                $http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id, angular.copy($scope.invoice)).then(startUp);
            })
        };

        $scope.addPayment = function () {
            SweetAlert.swal({
                title: 'Add Payment',
                text: 'How much do you want Pay? (max ' + $scope.invoice.amountRemaining + ')',
                type: 'input',
                confirmButtonText: "Confirm",
                cancelButtonText: "Cancel",
                showCancelButton: true,
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (inputValue) {
                if (inputValue === false) return;
                if (!parseInt(inputValue)) return;
                var amount = parseFloat(inputValue)
                if (amount < 0 || amount > $scope.invoice.amountRemaining) return;

                $scope.invoice.amountRemaining = $scope.invoice.amountRemaining - amount;
                $http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id, angular.copy($scope.invoice)).then(function (response) {
                    if (response.data.destination) {
                        SweetAlert.info('Payment Received in Full', 'This lead has now been moved to the \'' + response.data.destination + '\' stage')
                    } else if (response.data.destination === null) {
                        SweetAlert.info('Payment Received in Full', 'This lead has now been moved out of this Campaign')
                    }

                    var payment = new PaymentLog();
                    payment.invoiceId = $scope.invoice.id;
                    payment.amount = amount;
                    payment.paymentDate = new Date();
                    payment.actualPaymentDate = new Date();
                    payment.isPaid = true;
                    payment.disabled = false;
                    payment.deleted = false;
                    payment.userId = $rootScope.loggedInUser.id;
                    payment.clientId = $scope.invoice.clientId;
                    payment.campaignId = $scope.invoice.campaignId;
                    payment.leadId = $scope.invoice.leadId;
                    payment.callresultId = $scope.invoice.callresultId;
                    payment.source = 'manual'
                    payment.$save().then(startUp)
                })
            })
        };

        $scope.writeOff = function () {
            SweetAlert.swal({
                title: 'Write Off Invoice',
                text: 'Write off the full remaining amount of ' + $scope.invoice.amountRemaining + ' or a partial amount',
                type: 'input',
                inputValue: $scope.invoice.amountRemaining,
                confirmButtonText: "Confirm",
                cancelButtonText: "Cancel",
                showCancelButton: true,
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (inputValue) {
                if (inputValue === false) return;
                if (!parseInt(inputValue)) return;
                var amount = parseFloat(inputValue)

                Invoice.writeOff({
                    id: $scope.invoice.id
                }, {
                    amount: amount
                }).$promise
                .then(startUp);
            })
        };

        $scope.badPayment = function () {
            SweetAlert.swal({
                title: 'Flag as bad payment',
                text: 'This will move this lead into the bad credit cards stage and reset the amount paid\n\nDo you wish to continue?',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    //set amount paid to 0 and move lead to collections campaign stage
                    $scope.invoice.amountRemaining = invoice.grandTotal;
                    $scope.invoice.writtenOff = false;
                    $scope.invoice.writtenOffAmount = 0;
                    $scope.invoice.sendInvoice = false;
                    $scope.invoice.dueDate = moment().add(14, 'days').toDate();
                    Invoice.update({
                        id: $scope.invoice.id
                    }, angular.copy($scope.invoice));

                    $http.delete(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id + '/payments');

                    var collectionsStage = _.findWhere($scope.stages, {
                        name: 'Bad Credit Cards'
                    });
                    if (collectionsStage) {
                        Campaign.moveLeadToCampaignStage({
                            id: $scope.invoice.campaignId
                        }, {
                            leadId: $scope.invoice.leadId,
                            newCampaignStageId: collectionsStage.id
                        }).$promise.then(startUp);
                    } else {
                        startUp();
                    }
                }
            })
        };

        $scope.changeCard = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/collections/invoices/collections.invoices.history.changecard.html',
                controller: 'InvoiceChangeCardCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    leadId: function () {
                        return $scope.invoice.leadId;
                    },
                    clientId: function () {
                        return $scope.invoice.clientId;
                    }
                }
            });

            modalInstance.result.then(startUp)
        };

        $scope.resetInsallments = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/collections/invoices/collections.invoice.paymentplan.html',
                controller: 'InvoicePaymentPlanCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    invoice: function () {
                        return $scope.invoice;
                    },
                    payments: function () {
                        return angular.copy(_.filter($scope.paymentLogs, function (payment) {
                            return !payment.isPaid && !payment.deleted && !payment.disabled && payment.status === 'pending'
                        }))
                    }
                }
            });

            modalInstance.result.then(function (notes) {
                $scope.invoice.amountRemaining = 0;
                $scope.invoice.writtenOff = false;
                $scope.invoice.writtenOffAmount = 0;
                $scope.invoice.sendInvoice = false;
                $scope.invoice.notes = notes;
                Invoice.update({
                    id: $scope.invoice.id
                }, angular.copy($scope.invoice)).then(startUp);
            })
        };

        $scope.cancelPayments = function () {
            var pending = _.filter($scope.paymentLogs, function (payment) {
                return !payment.isPaid && !payment.deleted && !payment.disabled && payment.status === 'pending'
            })
            if (!pending.length) return;
            SweetAlert.swal({
                title: 'Cancel Payments',
                text: 'Are you sure you want to cancel ' + pending.length + ' scheduled payments?',
                type: "warning",
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (result) {
                if (result) {
                    $http.delete(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id + '/payments').then(function (response) {
                        $scope.invoice.amountRemaining = response.data.amountRemaining;
                        startUp()
                    })
                }
            })
        };

        $scope.addNote = function () {
            SweetAlert.swal({
                title: 'Add Note',
                type: 'input',
                inputValue: '',
                confirmButtonText: "Save",
                cancelButtonText: "Cancel",
                showCancelButton: true,
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (inputValue) {
                if (inputValue === false) return;
                $http.post(APP_SETTINGS.BASE_API_URL + 'invoices/' + $scope.invoice.id + '/notes', {
                    note: inputValue,
                    userId: $rootScope.loggedInUser.id,
                    invoiceId: $scope.invoice.id
                }).then(startUp);
            })
        };

        $scope.getError = function (payment) {
            if (payment.error) return payment.error;
            if (payment.receipt) {
                var receipt = payment.receipt
                if (typeof receipt === 'string') receipt = JSON.parse(receipt)
                if (receipt && receipt.responseMessage) return receipt.responseMessage
            }

            return 'Failed'
        };

        $scope.hasPending = function () {
            var pending = false

            for (var i = 0; i < $scope.paymentLogs.length; i++) {
                var payment = $scope.paymentLogs[i];
                if (payment.status === 'pending' && !payment.deleted && !payment.disabled && !payment.isPaid) pending = true;
            }

            return pending;
        }

        $scope.editRecurring = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/collections/recurring/edit.html',
                controller: 'AdminRecurringPaymentEditCtrl',
                backdrop: 'static',
                keyboard: false,
                size: 'lg',
                resolve: {
                    recurring: function () {
                        return $http.get(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.invoice.leadId + '/recurring').then(function (response) {
                            return response.data;
                        });
                    },
                    disposition: function () {
                        return null;
                    },
                    agent: function () {
                        return false
                    }
                }
            });

            modalInstance.result.then(function () {
                startUp()
            })
        };

        startUp();
    })