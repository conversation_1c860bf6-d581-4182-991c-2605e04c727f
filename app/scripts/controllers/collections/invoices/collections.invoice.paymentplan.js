'use strict';

angular.module('dialerFrontendApp')
    .controller('InvoicePaymentPlanCtrl', function ($scope, $rootScope, $uibModalInstance, $window, $http, APP_SETTINGS, PaymentLog, invoice, payments, _, moment) {
        $scope.amountRemaining = invoice.amountRemaining;
        $scope.invoice = invoice;
        $scope.payments = payments;
        $scope.paymentCount = payments.length;
        $scope.paymentsTotal = 0;
        $scope.paymentEvery = 1;
        $scope.minDate = new Date();
        $scope.maxDate = moment().add(30, 'days');
        $scope.difference = 'correct amount';
        $scope.notes = invoice.notes || '';

        for (var i = 0; i < payments.length; i++) {
            $scope.paymentsTotal += payments[i].amount;
        }

        if (payments.length > 1) {
            var first = payments[0]
            var next = payments[1]
            $scope.paymentEvery = moment(next.paymentDate).diff(moment(first.paymentDate), 'months');
        }


        $scope.dates = {
            initial: false
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.updatePaymentCount = function () {
            var range = Math.ceil($scope.amountRemaining / $scope.paymentCount);
            $scope.paymentsTotal = $scope.amountRemaining;
            $scope.difference = 'correct amount';
            var total = 0;
            var date = $scope.payments.length ? moment($scope.payments[0].paymentDate) : moment();
            $scope.payments = [];

            for (var i = 0; i < $scope.paymentCount; i++) {
                var amount;
                if ((total + range) < $scope.paymentsTotal)
                    amount = range;
                else
                    amount = $scope.paymentsTotal - total;

                if (amount < 0) amount = 0;

                $scope.payments.push({
                    amount, paymentDate: moment(date).toDate()
                });

                total += range

                date = date.add($scope.paymentEvery, 'month');
            }
        };

        $scope.fixPayments = function (index) {
            var difference = $scope.amountRemaining - $scope.paymentsTotal;
            $scope.payments[index].amount += difference;
            $scope.updatePayment();
        };

        $scope.updatePayment = function () {
            var total = 0
            for (let i = 0; i < $scope.payments.length; i++) {
                total += $scope.payments[i].amount;
            }
            $scope.paymentsTotal = total
            try {
                var difference = Math.round((total - $scope.amountRemaining) * 100) / 100
                if (difference === 0)
                    $scope.difference = 'correct amount'
                else if (difference > 0)
                    $scope.difference = '$' + difference + ' too much'
                else
                    $scope.difference = '$' + Math.abs(difference) + ' too little'
            }
            catch (err) {
                console.log(err)
            }

            $scope.paymentsBroken = !!difference;
        };

        if ($scope.paymentCount === 0) {
            $scope.paymentCount = 1;
            $scope.updatePaymentCount();
        }

        $scope.cancel = $uibModalInstance.dismiss;

        $scope.save = function () {
            var payments = []
            for (let i = 0; i < $scope.payments.length; i++) {
                var payment = $scope.payments[i];
                payments.push({
                    amount: payment.amount,
                    paymentDate: payment.paymentDate,
                    isPaid: false,
                    status: 'pending',
                    invoiceId: $scope.invoice.id,
                    leadId: $scope.invoice.leadId,
                    userId: $rootScope.loggedInUser.id,
                    clientId: $scope.invoice.clientId,
                    campaignId: $scope.invoice.campaignId,
                    callresultId: $scope.invoice.callresultId,
                    source: 'installment plan'
                })
            }
            $http.post(APP_SETTINGS.BASE_API_URL + 'paymentlog', payments).then(function () {
                $uibModalInstance.close($scope.notes)
            });
        };

        $scope.updatePayment();
    });