'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('AdminPaymentsCtrl', function ($scope, $rootScope, $uibModal, $interval, $timeout, APP_SETTINGS, $http, moment, PaymentLog, RecurringPayment, SweetAlert, _, clients, campaigns, users) {
        $rootScope.pageTitle = 'Payments';
        $scope.clients = clients;
        $scope.campaigns = campaigns;
        $scope.users = users;
        $scope.filters = {
            startDate: moment().subtract(1, 'month').toDate(),
            endDate: moment().toDate()
        };
        $scope.payments = [];
        $scope.pagination = {
            current: 1,
            total: 0
        };
        $scope.dates = {
            start: false,
            end: false
        };

        $scope.format = 'dd-MMMM-yyyy';

        $scope.openEndDate = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();

            $scope.dates.start = false;
            $scope.dates.end = true;
        };

        $scope.openStartDate = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();

            $scope.dates.end = false;
            $scope.dates.start = true;
        };

        $scope.pageChanged = function (page) {
            $scope.pagination.current = page;
            $scope.search(false);
        };

        $scope.disable = function (index) {
            var payment = $scope.payments[index];
            SweetAlert.swal({
                title: "Disable Payment",
                text: "Are you user you want to disbale this payment?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, disable it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + payment.id, { disabled: true }).then(function () {
                        // we should also update the amount remaining on the invoice to be minus this amount
                        if (payment.invoice) {
                            var amountRemaining = (payment.invoice.amountRemaining || 0) + payment.amount
                            $http.put(APP_SETTINGS.BASE_API_URL + 'invoices/' + payment.invoice.id, { amountRemaining }).then(function () {
                                $scope.search()
                            })
                        } else {
                            $scope.search()
                        }
                    });
                }
            });
        };

        $scope.delete = function (index) {
            var payment = $scope.payments[index];
            SweetAlert.swal({
                title: "Disable Payment",
                text: "Are you user you want to delete this payment?",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + payment.id, { deleted: true }).then(function () {
                        $scope.search();
                    })
                }
            });
        };

        $scope.edit = function (row) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/collections/recurring/edit.html',
                controller: 'AdminRecurringPaymentEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    recurring: function () {
                        return RecurringPayment.get({
                            id: row.recurringpaymentId
                        }).$promise;
                    },
                    disposition: function () {
                        return null;
                    },
                    agent: function () {
                        return false
                    }
                }
            });

            modalInstance.result.then(function (reload) {
                if (reload) {
                    $scope.search();
                }
            });
        };

        ($scope.search = function (reset) {
            if (reset) $scope.pagination.current = 1;
            var queryStr = '?limit=10&offset=' + (($scope.pagination.current - 1) * 10);

            if ($scope.filters.startDate) {
                queryStr += '&startDate=' + moment($scope.filters.startDate).startOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
            if ($scope.filters.endDate) {
                queryStr += '&endDate=' + moment($scope.filters.endDate).endOf('day').format('YYYY-MM-DD HH:mm:ss');
            }
            if ($scope.filters.client) queryStr += '&clientId=' + $scope.filters.client.id;
            if ($scope.filters.campaign) queryStr += '&campaignId=' + $scope.filters.campaign.id;
            if ($scope.filters.user) queryStr += '&userId=' + $scope.filters.user.id;
            if ($scope.filters.filterPaid) queryStr += '&filterPaid=true';
            if ($scope.filters.filterErrors) queryStr += '&filterErrors=true';
            if ($scope.filters.leadId) queryStr += '&leadId=' + $scope.filters.leadId;
            if ($scope.filters.clientRef) queryStr += '&clientRef=' + $scope.filters.clientRef;

            $http.get(APP_SETTINGS.BASE_API_URL + 'paymentlog' + queryStr).then(function (response) {
                $scope.payments = response.data.rows;
                $scope.pagination.total = response.data.count;
            })
        })();
    });