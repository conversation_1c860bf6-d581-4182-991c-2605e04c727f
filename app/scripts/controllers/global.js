'use strict';

angular.module('dialerFrontendApp')
	.controller('GlobalCtrl', function ($window, $rootScope, $scope, $state, $uibModal, $interval, $http, APP_SETTINGS, UserAuthFactory, moment, User, ngAudio) {
		$rootScope.pageTitle = '';

		var timerDetachers = [];

		$rootScope.loggedOut = true;

		$rootScope.forcedExit = false;

		$rootScope.errors = [];
		$rootScope.phone;

		Date.prototype.addDays = function (days) {
			this.setDate(this.getDate() + parseInt(days));
			return this;
		};

		if ($window.sessionStorage.loggedInUser) {
			$rootScope.loggedInUser = JSON.parse($window.sessionStorage.loggedInUser);
		}

		if ($rootScope.loggedInUser) {
			$rootScope.loggedOut = false;
			$window.sessionStorage.loggedInUser = JSON.stringify($rootScope.loggedInUser);
		}

		if ($window.sessionStorage.loginTime) {
			$rootScope.loginTime = $window.sessionStorage.loginTime;
		} else {
			$rootScope.loginTime = moment().format('h:mm a');
		}

		$rootScope.thisYear = moment().format('YYYY')

		// this adds the global scope as a global js obj so tsys event handling can bubble through
		extScope = $rootScope;
		$rootScope.clientId = undefined;
		$rootScope.carderrors = {};
		$rootScope.lastTsys = {
			eventType: undefined,
			event: undefined
		};
		$rootScope.tsysEvent = function (eventType, event) {
			console.log(eventType, event)
			$rootScope.lastTsys = {
				eventType, event
			}
			switch (eventType) {
				case 'FieldValidationErrorEvent':
					$rootScope.carderrors[event.fieldName] = event.message;
					break;
				case 'TokenEvent':
					if (event.message === 'Success') {
						$rootScope.carderrors.master = '';
						$rootScope.cardtoken = event;
					} else {
						$rootScope.carderrors.master = event.message;
						$rootScope.resetTsys();
					}
					break;
				case 'FocusOutEvent':
					$rootScope.carderrors[event.fieldName] = '';
					break;
				case 'FocusOnEvent':
					if ($rootScope.phone && $rootScope.phone.onCall) { $rootScope.phone.pauseRecording() };
					break;
				case 'BlurEvent':
					if ($rootScope.phone && $rootScope.phone.onCall) { $rootScope.phone.resumeRecording() };
					break;
			}
		};

		$rootScope.buildTsys = function (clientId) {
			window.onload = null;
			$http.get(APP_SETTINGS.BASE_API_URL + 'manifest?clientId=' + (clientId || $rootScope.clientId)).then(function (response) {
				var manifest = response.data.manifest;
				var device = response.data.deviceID;
				var domain = response.data.uiDomain || 'https://gateway.transit-pass.com/transit-tsep-web/jsView/'
				var s = document.createElement('script');
				s.src = domain + device + '?' + manifest;
				document.head.appendChild(s);
				setTimeout(function () {
					dispatchEvent(new Event('load'));
				}, 1000)
			});
		};

		$rootScope.clearTsys = function () {
			$rootScope.lastTsys = {
				eventType: undefined,
				event: undefined
			};
			$scope.errors = {};
			$scope.cardtoken = {};
			$('#tsep-cardNum').remove();
			$('#tsep-datepicker').remove();
			$('#tsep-cvv2').remove();
		};

		$rootScope.resetTsys = function () {
			$rootScope.lastTsys = {
				eventType: undefined,
				event: undefined
			};
			$scope.errors = {};
			$scope.cardtoken = {};
			$('#tsep-cardNum').remove();
			$('#tsep-datepicker').remove();
			$('#tsep-cvv2').remove();
			$rootScope.buildTsys();
		};

		$rootScope.checkCardDate = function (lastPaymentDate, date) {
			if (!date && $scope.cardtoken && $scope.cardtoken.expirationDate) {
				date = $scope.cardtoken.expirationDate
			}
			if (!date) return
			if (typeof date === 'string') {
				if (date.length === 6) {
					date = moment(date, 'MMYYYY')
				} else if (date.length === 7) {
					date = moment(date, 'MM/YYYY')
				} else {
					date = moment(date)
				}
			}
			if (moment(lastPaymentDate) > date) {
				return 'last payment date is after expiration date'
			}
			return ''
		};

		$rootScope.waitForTsys = function (cb) {
			if ($rootScope.lastTsys.eventType === 'TokenEvent') return cb();
			var count = 0;
			(function loop() {
				if ($rootScope.lastTsys.eventType === 'TokenEvent') return cb();
				count++
				// if its taken more than 1.5 seconds then there is a problem so return
				if (count >= 15) return cb(true);
				setTimeout(loop, 100);
			})();
		};

		$rootScope.safeApply = function (fn) {
			var phase = this.$root.$$phase;
			if (phase === '$apply' || phase === '$digest') {
				if (fn && (typeof (fn) === 'function')) {
					fn();
				}
			} else {
				this.$apply(fn);
			}
		};

		$scope.isEmptyObject = function (obj) {
			for (var prop in obj) {
				if (Object.prototype.hasOwnProperty.call(obj, prop)) {
					return false;
				}
			}
			return true;
		};

		$rootScope.logout = function () {
			if (!$rootScope.loggedOut) {
				$rootScope.loggedOut = true;
				User.logout({
					id: $rootScope.loggedInUser.id
				}).$promise.then(function () {
					if ($rootScope.preLogoutMethod && typeof $rootScope.preLogoutMethod === 'function') {
						$rootScope.preLogoutMethod(UserAuthFactory.logout);
						$rootScope.preLogoutMethod = null;
					} else {
						UserAuthFactory.logout();
					}
				});
			}
		};

		$rootScope.formatDate = function (str, hideTime, defaultVal) {
			if (str) {
				var date = moment.utc(str);
				date.local();

				if (hideTime) {
					return date.format('MMM-DD-YYYY');
				} else {
					return date.format('MMM-DD-YYYY HH:mm:ss');
				}
			} else {
				return defaultVal;
			}
		};

		$rootScope.formatTime = function (str, withSeconds) {
			if (str) {
				var date = moment.utc(str);
				date.local();
				if (withSeconds) {
					return date.format('HH:mm:ss');
				} else {
					return date.format('HH:mm');
				}
			}
		};

		$rootScope.humanizeTimespan = function (seconds) {
			return window.humanizeDuration(seconds * 1000, {
				round: true
			});
		};

		$rootScope.changePassword = function (withCancel, client, ignoreCurrent) {
			$uibModal.open({
				templateUrl: 'views/common/changepassword.html',
				controller: 'ChangePasswordModalCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					withCancel: function () {
						return withCancel;
					},
					client: function () {
						return client;
					},
					loggedInUser: function () {
						return User.get({
							id: $rootScope.loggedInUser.id
						}).$promise;
					},
					ignoreCurrent: function () {
						return ignoreCurrent;
					}
				}
			});
		};

		$rootScope.humanizeSeconds = function (secs) {
			if (secs > 3599) {
				return moment(secs * 1000).format('H:mm:ss');
			} else {
				return moment(secs * 1000).format('mm:ss');
			}
		};

		$rootScope.playRecording = function (cdr) {
			cdr.playing = true;
			$('#jquery_jplayer_' + cdr.id).jPlayer({
				ready: function () {
					$(this).jPlayer("setMedia", {
						mp3: `https://${cdr.recordingServer}:3000/api/v1/getrecording?apitoken=0bfcc65c-e362-4017-8435-61a5e53cd4ae&recordingLocation=${cdr.recordingLocation}`
					});
					$(this).jPlayer("play", 1);
				},
				stop: function () {
					cdr.playing = false
				},
				cssSelectorAncestor: "#jp_container_" + cdr.id,
				swfPath: "/js",
				supplied: "mp3",
				useStateClassSkin: true,
				autoBlur: false,
				smoothPlayBar: true,
				keyEnabled: true,
				remainingDuration: true,
				toggleDuration: true
			});
			// cdr.audio = ngAudio.play(`https://${cdr.recordingServer}:3000/api/v1/getrecording?apitoken=0bfcc65c-e362-4017-8435-61a5e53cd4ae&recordingLocation=${cdr.recordingLocation}`);
		};

		$rootScope.validateCreditCardNumber = function (value) {
			// accept only digits, dashes or spaces
			if (/[^0-9-\s]+/.test(value)) {
				return false;
			}

			// The Luhn Algorithm. It's so pretty.
			var nCheck = 0,
				nDigit = 0,
				bEven = false;
			value = value.replace(/\D/g, '');

			for (var n = value.length - 1; n >= 0; n--) {
				var cDigit = value.charAt(n);
				nDigit = parseInt(cDigit, 10);

				if (bEven) {
					if ((nDigit *= 2) > 9) {
						nDigit -= 9;
					}
				}

				nCheck += nDigit;
				bEven = !bEven;
			}

			return (nCheck % 10) === 0;
		};

		$rootScope.validateCreditCardDate = function (value) {
			if (!value || value.length < 4) {
				return false;
			}

			value = value + '';

			var month = value.substring(0, 2);
			var year = value.substring(value.length - 2, value.length);

			if (month > 12 || month < 1) {
				return false;
			}

			var now = new Date();
			if (year < (now.getYear() + '').substring(1, 3)) {
				return false;
			}

			return true;
		};

		$rootScope.validateCreditCardPin = function (value) {
			return (value && (value.length === 3 || value.length === 4) && !isNaN(value));
		};

		$rootScope.detectCardType = function (number) {
			var re = {
				electron: /^(4026|417500|4405|4508|4844|4913|4917)\d+$/,
				maestro: /^(5018|5020|5038|5612|5893|6304|6759|6761|6762|6763|0604|6390)\d+$/,
				dankort: /^(5019)\d+$/,
				interpayment: /^(636)\d+$/,
				unionpay: /^(62|88)\d+$/,
				visa: /^4[0-9]{12}(?:[0-9]{3})?$/,
				mastercard: /^5[1-5][0-9]{14}$/,
				amex: /^3[47][0-9]{13}$/,
				diners: /^3(?:0[0-5]|[68][0-9])[0-9]{11}$/,
				discover: /^6(?:011|5[0-9]{2})[0-9]{12}$/,
				jcb: /^(?:2131|1800|35\d{3})\d{11}$/
			};
			if (re.electron.test(number)) {
				return 'Electron';
			} else if (re.maestro.test(number)) {
				return 'Maestro';
			} else if (re.dankort.test(number)) {
				return 'Dankort';
			} else if (re.interpayment.test(number)) {
				return 'Interpayment';
			} else if (re.unionpay.test(number)) {
				return 'Unionpay';
			} else if (re.visa.test(number)) {
				return 'Visa';
			} else if (re.mastercard.test(number)) {
				return 'Mastercard';
			} else if (re.amex.test(number)) {
				return 'American Express';
			} else if (re.diners.test(number)) {
				return 'Diners';
			} else if (re.discover.test(number)) {
				return 'Discover';
			} else if (re.jcb.test(number)) {
				return 'JCB';
			} else {
				return undefined;
			}
		};

		$rootScope.getObjectPropertyByString = function (o, s) {
			try {
				if (!o) {
					return '';
				}
				s = s.replace(/\[(\w+)\]/g, '.$1'); // convert indexes to properties
				s = s.replace(/^\./, ''); // strip a leading dot
				var a = s.split('.');
				for (var i = 0, n = a.length; i < n; ++i) {
					var k = a[i];
					if (k in o) {
						o = o[k];
					} else {
						return;
					}
				}

				if (o && o.length === 24) {
					//probably a date so check it
					var date = new Date(o);
					if (date.toString() !== 'Invalid Date' && date.toString() !== 'Invalid date') {
						return $rootScope.formatDate(date);
					}
				}
				return o;
			} catch (e) {
				return '';
			}
		};

		$rootScope.checkPasswordStrength = function (password) {
			var reg = new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.{8,})');
			return reg.test(password);
		};

		$rootScope.splitOnCamelcase = function (input) {
			if (input && input.replace) {
				return input.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) { return str.toUpperCase(); });
			} else {
				return input;
			}
		};

		$rootScope.parseCardType = function (cardType) {
			switch (cardType) {
				case 'V': return 'VISA';
				case 'M': return 'MASTERCARD';
				case 'R': return 'DISCOVER';
				case 'X': return 'AMERICAN EXPRESS';
				case 'J': return 'JCB';
				case 'I': return 'DINERS CLUB';
				case 'D': return 'DEBIT';
				case 'A': return 'ATM';
				case 'G': return 'PRIVATE LABEL GIFTCARD';
				case 'E': return 'EBT';
				default: return 'UNKNOWN'
			}
		};

		timerDetachers.push(
			$interval(function () {
				if ($rootScope.loggedInUser && !$rootScope.loggedOut) {
					User.keepalive({
						id: $rootScope.loggedInUser.id || 0,
						state: $state.current ? $state.current.name : 'unknown'
					}).$promise.then(function (result) {
						if (!result.success) {
							$rootScope.forcedExit = true;
							$rootScope.logout();
						} else {
							$scope.userSession = result.session;
							if (result.session.state !== 'agent.dashboard' && result.session.timeoutIn && result.session.timeoutIn < 120) {
								$.gritter.add({
									title: 'Timeout',
									text: 'You will be timed out in ' + result.session.timeoutIn + ' seconds',
									time: 2500,
									sticky: false
								});
							}
						}
					}).catch(console.log);
				}
			}, 5000)
		);

		$scope.$on('$destroy', function () {
			window.onbeforeunload = undefined;

			for (var i = 0; i < timerDetachers.length; i++) {
				if (timerDetachers[i]) {
					$interval.cancel(timerDetachers[i]);
				}
			}

			if ($rootScope.loggedInUser) {
				User.logout({
					id: $rootScope.loggedInUser.id || 0
				});
			}
		});
	});