'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportClientLoginCtrl', function (APP_SETTINGS, $window, $scope, $timeout, $rootScope, $uibModal, Report, User, Client, moment, _, SweetAlert) {
		$rootScope.pageTitle = 'Reports | Client';

		Report.getClientReports({
			id: $rootScope.loggedInUser.clientId
		}).$promise.then(function (reports) {
			console.log(reports)
			$scope.reports = reports;
		})

		Client.get({
			id: $rootScope.loggedInUser.clientId
		}).$promise.then(function (client) {
			if (client.reportPasswordRequired && (!client.reportPasswordLastChange || moment(client.reportPasswordLastChange) < moment().subtract(90, 'days'))) {
				$timeout(function () {
					$rootScope.changePassword(false, true, !client.reportPasswordLastChange);
				});
			}
		})

		User.get({
			id: $rootScope.loggedInUser.id
		}).$promise.then(function (user) {
			if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
				$timeout(function () {
					$rootScope.changePassword(false);
				});
			}
		})
	});