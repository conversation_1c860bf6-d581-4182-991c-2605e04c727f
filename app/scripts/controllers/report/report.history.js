'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportHistoryCtrl', function (APP_SETTINGS, $scope, $q, $rootScope, $uibModal, $window, Report, ReportHistory, ReportHistoryAudit, Client, agents, campaigns, clients, report, users, _, SweetAlert) {
		$scope.report = report;
		$rootScope.pageTitle = 'Reports | History | ' + $scope.report.name;

		for (var i = 0; i < $scope.report.reporthistories.length; i++) {
			$scope.report.reporthistories[i].filters = JSON.parse($scope.report.reporthistories[i].filters);
			if ($rootScope.loggedInUser.isClient) {
				$scope.report.reporthistories[i].hide = true;
				if ($scope.report.reporthistories[i].filters instanceof Array && $scope.report.reporthistories[i].filters.length) {
					$scope.report.reporthistories[i].filters.forEach(function (filter) {
						if (filter.field == 'Client') {
							if (filter.value == $rootScope.loggedInUser.client.name) {
								$scope.report.reporthistories[i].hide = false;
							}
						}
					})
				} else if ($scope.report.reporthistories[i].filters) {
					if ($scope.report.reporthistories[i].filters['Client']) {
						if ($scope.report.reporthistories[i].filters['Client'] == $rootScope.loggedInUser.client.id) {
							$scope.report.reporthistories[i].hide = false;
						}
					}
				}
			}
			$scope.report.reporthistories[i].new = _.where($scope.report.reporthistories[i].reporthistoryaudits, {
				userId: $rootScope.loggedInUser.id
			}).length == 0;
		};
		if ($rootScope.loggedInUser.isClient) {
			$scope.report.reporthistories = _.where($scope.report.reporthistories, {
				isClientReady: true,
				hide: false
			});
		}
		$scope.report.definition = JSON.parse($scope.report.definition);
		$scope.filters = [];
		if ($scope.report.definition instanceof Array) {
			if ($scope.report.definition[0].rawQuery) {
				for (var i = 0; i < $scope.report.definition[0].availableFilters.length; i++) {
					$scope.filters.push($scope.report.definition[0].availableFilters[i].model);
				};
			} else {
				if ($scope.report.definition[0].primaryModule.name != "CallResult" && $scope.report.definition[0].primaryModule.name != "Sale") {
					$scope.filters.push([$scope.report.definition[0].primaryModule.name]);
				}
				for (var i = 0; i < $scope.report.definition[0].primaryModule.relatedModules.length; i++) {
					if ($scope.report.definition[0].primaryModule.relatedModules[i].name != "CallResult" && $scope.report.definition[0].primaryModule.relatedModules[i].name != "Sale") {
						$scope.filters.push($scope.report.definition[0].primaryModule.relatedModules[i].name);
					}
				};
			}
		} else {
			if ($scope.report.definition.rawQuery) {
				for (var i = 0; i < $scope.report.definition.availableFilters.length; i++) {
					if ($scope.report.definition.availableFilters[i].model) $scope.filters.push($scope.report.definition.availableFilters[i].model);
				};
			} else {
				if ($scope.report.definition.primaryModule.name != "CallResult" && $scope.report.definition.primaryModule.name != "Sale") {
					$scope.filters.push([$scope.report.definition.primaryModule.name]);
				}
				for (var i = 0; i < $scope.report.definition.primaryModule.relatedModules.length; i++) {
					if ($scope.report.definition.primaryModule.relatedModules[i].name != "CallResult" && $scope.report.definition.primaryModule.relatedModules[i].name != "Sale") {
						$scope.filters.push($scope.report.definition.primaryModule.relatedModules[i].name);
					}
				};
			}
		}

		$scope.email = function (history) {
			var clientName = _.findWhere(history.filters, {
				field: 'Client'
			})

			if (clientName && clientName.value) {
				Client.query().$promise.then(function (clients) {
					var client = _.findWhere(clients, {
						name: clientName.value
					})
					if (client && client.email) {
						ReportHistory.emailReport({
							id: history.id
						}, {
							to: client.email,
							userId: $rootScope.loggedInUser.id
						}).$promise.then(function (result) {
							if (result && !result.error) {
								SweetAlert.swal({
									title: "Success",
									text: 'Email Sent Successfully',
									closeOnConfirm: true,
									type: 'success'
								});
							} else {
								SweetAlert.swal({
									title: "Error",
									text: result.error,
									closeOnConfirm: true,
									type: 'error'
								});
							}
						})
					} else {
						askForEmailAddress(history);
					}
				})
			} else {
				askForEmailAddress(history);
			}
		}

		function askForEmailAddress(history) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/email/report.email.html',
				controller: 'ReportEmailCtrl'
			});

			modalInstance.result.then(function (result) {
				if (result) {
					ReportHistory.emailReport({
						id: history.id
					}, {
						to: result.address,
						userId: $rootScope.loggedInUser.id
					}).$promise.then(function (result) {
						if (result && !result.error && !result.message) {
							SweetAlert.swal({
								title: "Success",
								text: 'Email Sent Successfully',
								closeOnConfirm: true,
								type: 'success'
							});
						} else {
							SweetAlert.swal({
								title: "Error",
								text: result.error,
								closeOnConfirm: true,
								type: 'error'
							});
						}
					})
				}
			});
		}

		$scope.getUser = function (userId) {
			return _.findWhere(users, {
				id: userId
			}).username;
		};

		$scope.updateClientHistory = function (history) {
			ReportHistory.update({
				id: history.id
			}, history);
		};

		$scope.getLink = function (history) {
			var audit = new ReportHistoryAudit();
			audit.isDownloaded = true;
			audit.reporthistoryId = history.id;
			audit.userId = $rootScope.loggedInUser.id;
			audit.$save();

			history.new = false;

			$window.location.href = APP_SETTINGS.BASE_API_URL + 'reporthistories/' + history.id + '?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
		};

		$scope.findFilter = function (repHistory, filter) {
			if (filter) {
				if (repHistory.filters instanceof Array) {
					if (filter === 'CampaignStage') filter = 'Campaign Stage'
					var result = _.findWhere(repHistory.filters, {
						field: filter
					})
					if (result) {
						return result.value;
					} else {
						return '';
					}
				} else {
					if (filter == 'Campaign') {
						var campaign = _.findWhere(campaigns, {
							id: repHistory.filters['Campaign']
						})
						if (campaign) return campaign.name
						return '';
					}

					if (filter == 'Agent') {
						var agent = _.findWhere(agents, {
							id: repHistory.filters['Agent']
						})
						if (agent) return agent.name
						else return '';
					}

					if (filter == 'Client') {
						var client = _.findWhere(clients, {
							id: repHistory.filters['Client']
						});
						if (client) return client.name
						return '';
					}
				}
			}
		}

		$scope.delete = function (history) {
			SweetAlert.swal({
				title: "Delete Report History",
				text: "Are you user you want to delete this report history!",
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#DD6B55",
				confirmButtonText: "Yes, delete it!",
				cancelButtonText: "No, cancel",
				closeOnConfirm: true,
				closeOnCancel: true
			},
				function (isConfirm) {
					if (isConfirm) {
						var historyId = history.id;
						ReportHistory.delete({
							id: historyId
						}, function () {
							$scope.report.reporthistories = $scope.report.reporthistories.filter(function (a) {
								return a.id && a.id !== historyId;
							});
						})
					}
				});
		};
	});