'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportClientHistoryCtrl', function (APP_SETTINGS, $scope, $rootScope, $window, $state, Report, ReportHistoryAudit, reportId, campaigns) {
		$rootScope.pageTitle = 'Reports';

		Report.getClientReportHistory({
			id: reportId,
			clientid: $rootScope.loggedInUser.clientId
		}).$promise.then(function (reporthistories) {
			reporthistories.forEach(function (history) {
				if (history.filters)
					history.filters = JSON.parse(history.filters);
			})
			$scope.reporthistories = reporthistories;
		})

		$scope.getCampaign = function (history) {
			if (history.filters instanceof Array) {
				var result = _.findWhere(history.filters, {
					field: 'Campaign'
				})
				if (result) {
					return result.value;
				} else {
					return '';
				}
			} else {
				var campaign = _.findWhere(campaigns, {
					id: history.filters['Campaign']
				})
				if (campaign) return campaign.name;
				else return '';
			}
		};

		$scope.getLink = function (history) {
			var audit = new ReportHistoryAudit();
			audit.isDownloaded = true;
			audit.reporthistoryId = history.id;
			audit.userId = $rootScope.loggedInUser.id;
			audit.$save();
			history.new = false;

			$window.location.href = APP_SETTINGS.BASE_API_URL + 'reporthistories/' + history.id + '?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
		};

		$scope.goback = function () {
			$state.go('report.clientlogin');
		};
	});