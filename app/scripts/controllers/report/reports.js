'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportCustomCtrl', function (APP_SETTINGS, $window, $scope, $rootScope, $timeout, $uibModal, reportFolder, ReportHistory, User, moment, ReportHistoryAudit, Report, reports, _, SweetAlert) {
		$rootScope.pageTitle = 'Reports | ' + (reportFolder || 'All');
		$scope.reportFolder = reportFolder;
		$scope.anyReports = true;
		$scope.reports = reports;
		for (var i = 0; i < $scope.reports.length; i++) {
			try {
				if (typeof $scope.reports[i].definition == 'string') $scope.reports[i].definition = JSON.parse($scope.reports[i].definition);
			} catch (e) {
				console.log($scope.reports[i])
				console.log(e)
			}
		};
		if (reportFolder) {
			$scope.anyReports = _.where($scope.reports, {
				folder: reportFolder
			}).length > 0;
		}

		User.get({
			id: $rootScope.loggedInUser.id
		}).$promise.then(function (user) {
			if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
				$timeout(function () {
					$rootScope.changePassword(false);
				});
			}
		})

		$scope.run = function (report) {
			var definition = report.definition
			if (definition instanceof Array) {
				definition = definition[0]
			}
			if (definition.rawQuery) {
				var modalInstance = $uibModal.open({
					animation: true,
					keyboard: false,
					templateUrl: 'views/report/filters/report.query.filters.html',
					controller: 'ReportQueryFilterCtrl',
					size: 'lg',
					resolve: {
						report: function () {
							return report;
						}
					}
				});

				modalInstance.result.then(function (result) {
					if (result.id) {
						report.reporthistories.push(result);

						var audit = new ReportHistoryAudit();
						audit.isDownloaded = true;
						audit.reporthistoryId = result.id;
						audit.userId = $rootScope.loggedInUser.id;
						audit.$save();

						var pathToFile = APP_SETTINGS.BASE_API_URL + 'reporthistories/' + result.id + '?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
						window.location.href = pathToFile;
					} else if (result.email) {
						sweetAlert('You will receive an email when this report is ready to download');
					} else {
						if (result.message) {
							sweetAlert("Warning", result.message, "warning");
						} else {
							alert('Error creating report');
						}
					}
				});
			} else {
				var modalInstance = $uibModal.open({
					animation: true,
					keyboard: false,
					templateUrl: 'views/report/filters/report.filters.modal.html',
					controller: 'ReportFilterModalCtrl',
					size: 'lg',
					resolve: {
						report: function () {
							return report;
						}
					}
				});

				modalInstance.result.then(function (result) {
					if (result.id) {
						report.reporthistories.push(result);

						var audit = new ReportHistoryAudit();
						audit.isDownloaded = true;
						audit.reporthistoryId = result.id;
						audit.userId = $rootScope.loggedInUser.id;
						audit.$save();

						var pathToFile = APP_SETTINGS.BASE_API_URL + 'reporthistories/' + result.id + '?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
						window.location.href = pathToFile;
					} else {
						if (result.message) {
							sweetAlert("Warning", result.message, "warning");
						} else if (result.email) {
							sweetAlert('You will receive an email when this report is ready to download');
						} else {
							alert('Error creating report');
						}
					}
				});
			}
		};

		$scope.schedule = function (report) {
			$uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/schedule/report.schedules.html',
				controller: 'ReportScheduleCtrl',
				resolve: {
					report: function () {
						return report;
					}
				}
			});
		};

		$scope.delete = function (report) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/common/messageboxes/dialogModal.html',
				controller: 'DialogModalCtrl',
				resolve: {
					options: function () {
						return {
							yes: 'Yes',
							no: 'No',
							title: 'Delete ' + report.name,
							content: 'Are you sure you want to delete this report?'
						}
					}
				}
			});

			modalInstance.result.then(function (result) {
				if (result) {
					var reportId = report.id;
					report.$delete(function () {
						$scope.reports = $scope.reports.filter(function (a) {
							return a.id && a.id !== reportId;
						});
					});
				}
			});
		};
	});