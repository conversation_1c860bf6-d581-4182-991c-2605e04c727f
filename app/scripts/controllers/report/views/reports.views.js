'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportViewsCtrl', function (APP_SETTINGS, $window, $scope, $rootScope, moment, _, SweetAlert, Client) {
		$rootScope.pageTitle = 'Reports | Views';

		$scope.showWeeklyGoals = false;
		$scope.showExpiredCallbacks = false;
		$scope.showPledges = false;
		$scope.showSales = false;
		$scope.showCollections = false;

		$scope.showAny = false;

		if ($rootScope.loggedInUser.isAdmin || $rootScope.loggedInUser.isSuperManager || $rootScope.loggedInUser.isClientAdmin) {
			$scope.showWeeklyGoals = true;
			$scope.showExpiredCallbacks = true;
			$scope.showPledges = true;
			$scope.showSales = true;
			$scope.showCollections = true;
			$scope.showAny = true;
		} else if ($rootScope.loggedInUser.isSupervisor) {
			$scope.showWeeklyGoals = true;
			$scope.showPledges = true;
			$scope.showSales = true;
			$scope.showCollections = true;
			$scope.showAny = true;
		} else if ($rootScope.loggedInUser.isAgent || $rootScope.loggedInUser.isClientAgent) {
			$scope.showWeeklyGoals = true;
			$scope.showPledges = true;
			$scope.showSales = true;
			$scope.showCollections = true;
			$scope.showAny = true;
		} else if ($rootScope.loggedInUser.isClient) {
			Client.get({
				id: $rootScope.loggedInUser.clientId
			}).$promise.then(function (client) {
				if (client.showReportsAsViews) {
					$scope.showPledges = true;
					$scope.showSales = true;
					$scope.showCollections = true;
					$scope.showAny = true;
				}
			})
		}
	})