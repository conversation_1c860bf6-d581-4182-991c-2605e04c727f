'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportWeeklyGoalsCtrl', function (APP_SETTINGS, $window, $scope, $rootScope, moment, _, Sweet<PERSON>lert, Agent, Campaign, Client, User) {
		$scope.users = [];
		$scope.availableWeeks = [];

		$rootScope.pageTitle = 'Report | Weekly Goals';

		var now = moment().day('Monday');
		var blankTotals = {
			'Sch Hrs': 0,
			'Act Hours': 0,
			'Calls / Hr': 0,
			'Goal': 0,
			'Actual Amt': 0,
			'Goal/hr': 0,
			'Actual/hr': 0,
			'Acq %': 0,
			'Monday Total': 0,
			'Tuesday Total': 0,
			'Wednesday Total': 0,
			'Thursday Total': 0,
			'Friday Total': 0,
			'Saturday Total': 0,
			'Sunday Total': 0,
			'CC Rate #': 0,
			'CC Rate $': 0,
			'Renew Inc': 0,
			'Add-on': 0,
			'Total #': 0,
			'New $': 0,
			'New $ CC': 0,
			'CC $': 0,
			'Unpaid Invoice $': 0,
			'Paid Invoice $': 0
		};

		for (var i = 0; i < 52; i++) {
			var start = moment().day('Monday').subtract(i, 'weeks');
			var end = moment(start).add(6, 'days').endOf('day').utc().format('YYYY-MM-DD HH:mm:ss')
			$scope.availableWeeks.push({
				name: start.format('DD-MMM-YYYY'),
				start: start.startOf('day').utc().format('YYYY-MM-DD HH:mm:ss'),
				end: end
			})
		}

		$scope.selectedWeek = _.first($scope.availableWeeks);

		$scope.getResults = function () {
			$scope.users = [];
			Agent.getWeeklyGoalsView({
				startDate: $scope.selectedWeek.start,
				endDate: $scope.selectedWeek.end
			}).$promise.then(function (results) {
				processResults(results);
			}).catch(function (err) {
				console.log(err.message ? err.message : err);
			})
		};


		function processResults(results) {
			results.rows.forEach(function (result) {
				addResultToUser(result);
			});

			processTotals(results.totals);
		}

		function addResultToUser(result) {
			var user = _.findWhere($scope.users, {
				name: result.Supervisor
			});

			if (!user) {
				user = {
					name: result.Supervisor,
					clients: []
				};
				$scope.users.push(user);
			}

			addResultToClient(user, result);
		}

		function addResultToClient(user, result) {
			var client = _.findWhere(user.clients, {
				name: result['Campaign Name']
			});

			if (client) {
				client.agents.push(result);
			} else {
				client = {
					name: result['Campaign Name'],
					agents: [result]
				};
				user.clients.push(client);
			}
		}

		function processTotals(totals) {
			$scope.grandTotals = angular.copy(blankTotals);
			$scope.users.forEach(function (user) {
				try {
					user.totals = angular.copy(blankTotals);
					user.clients.forEach(function (client) {
						try {
							client.totals = angular.copy(blankTotals);
							client.agents.forEach(function (agent) {
								for (var prop in client.totals) {
									client.totals[prop] += agent[prop];
								}
							})

							if (totals[client.name]) {
								client.totals['Actual/hr'] = client.totals['Actual Amt'] / client.totals['Act Hours'];
								client.totals['Acq %'] = totals[client.name]['Acq %'];
								client.totals['Ren %'] = totals[client.name]['Ren %'];
								client.totals['Lap %'] = totals[client.name]['Lap %'];
								client.totals['CC Rate #'] = totals[client.name]['CC Rate #'];
								client.totals['CC Rate $'] = totals[client.name]['CC Rate $'];
								client.totals['Renew Inc'] = totals[client.name]['Renew Inc'];
								client.totals['Add-on'] = totals[client.name]['Addon %'];
								client.totals['CC $'] = totals[client.name]['CC $'];
								client.totals['Unpaid Invoice $'] = totals[client.name]['Unpaid Invoice $'];
								client.totals['Paid Invoice $'] = totals[client.name]['Paid Invoice $'];
								client.totals['Goal %'] = (client.totals['Actual Amt'] / client.totals['Goal']) * 100;
								client.totals['Goal/hr'] = Math.round(client.totals['Goal'] / (client.totals['Sch Hrs'] || 1));
							}

							for (var prop in user.totals) {
								user.totals[prop] += client.totals[prop];
							}
						} catch (err) {
							console.log(client)
							console.log(err)
						}
					})

					if (totals[user.name]) {
						user.totals['Actual/hr'] = user.totals['Actual Amt'] / user.totals['Act Hours'];
						user.totals['Acq %'] = totals[user.name]['Acq %'];
						user.totals['Ren %'] = totals[user.name]['Ren %'];
						user.totals['Lap %'] = totals[user.name]['Lap %'];
						user.totals['CC Rate #'] = totals[user.name]['CC Rate #'];
						user.totals['CC Rate $'] = totals[user.name]['CC Rate $'];
						user.totals['Renew Inc'] = totals[user.name]['Renew Inc'];
						user.totals['Add-on'] = totals[user.name]['Addon %'];
						user.totals['CC $'] = totals[user.name]['CC $'];
						user.totals['Unpaid Invoice $'] = totals[user.name]['Unpaid Invoice $'];
						user.totals['Paid Invoice $'] = totals[user.name]['Paid Invoice $'];
						user.totals['Goal %'] = (user.totals['Actual Amt'] / user.totals['Goal']) * 100;
						user.totals['Goal/hr'] = Math.round(user.totals['Goal'] / (user.totals['Sch Hrs'] || 1));
					}

					for (var prop in user.totals) {
						$scope.grandTotals[prop] += user.totals[prop];
					}
				} catch (err) {
					console.log(user)
					console.log(err)
				}
			})

			$scope.grandTotals['Actual/hr'] = $scope.grandTotals['Actual Amt'] / $scope.grandTotals['Act Hours'];
			$scope.grandTotals['Acq %'] = totals.grandTotal['Acq %'];
			$scope.grandTotals['Ren %'] = totals.grandTotal['Ren %'];
			$scope.grandTotals['Lap %'] = totals.grandTotal['Lap %'];
			$scope.grandTotals['CC Rate #'] = totals.grandTotal['CC Rate #'];
			$scope.grandTotals['CC Rate $'] = totals.grandTotal['CC Rate $'];
			$scope.grandTotals['Renew Inc'] = totals.grandTotal['Renew Inc'];
			$scope.grandTotals['Add-on'] = totals.grandTotal['Add-on'];
			$scope.grandTotals['CC $'] = totals.grandTotal['CC $'];
			$scope.grandTotals['Unpaid Invoice $'] = totals.grandTotal['Unpaid Invoice $'];
			$scope.grandTotals['Paid Invoice $'] = totals.grandTotal['Paid Invoice $'];
			$scope.grandTotals['Goal %'] = ($scope.grandTotals['Actual Amt'] / $scope.grandTotals['Goal']) * 100;
			$scope.grandTotals['Goal/hr'] = Math.round($scope.grandTotals['Goal'] / ($scope.grandTotals['Sch Hrs'] || 1));
			$scope.grandTotals.user = _.last($scope.users).name;
		}

		$scope.getResults();
	})