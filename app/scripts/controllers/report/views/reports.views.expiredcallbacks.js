'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportViewsExpiredCallbacksCtrl', function(APP_SETTINGS, $scope, $http, $rootScope, $uibModal, Callback, Client, Campaign, CallResult, CampaignStage, SweetAlert) {
		$rootScope.pageTitle = 'Report | Expired Callbacks';
		$scope.callbacks = [];
		$scope.total = 0;
		$scope.page = 1;
		$scope.sortType = 'callback.startDateTime';
		$scope.sortReverse = false;
		$scope.initialLoad = true;

		$scope.setDirection = function (field) {
			$scope.sortType = field;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.page = 1;
			$scope.getCallbacks();
		};

		$scope.pageChanged = function (page) {
			$scope.page = page;
			$scope.getCallbacks();
		};

		$scope.getCallbacks = function () {
			$http.get(APP_SETTINGS.BASE_API_URL + 'callbacks/expired?limit=30&page=' + ($scope.page - 1) + '&orderby=' + $scope.sortType + '&dir=' + ($scope.sortReverse ? 'DESC' : 'ASC')).then(function (response) {
				$scope.callbacks = response.data.rows;
				$scope.total = response.data.count;
				$scope.initialLoad = false;
			}, function(err) {
				console.log(err);
			});
		};

		$scope.getCurrentStage = function (callback) {
			var lead = callback.lead;
			var campaign = callback.campaign;

			if (lead.suppressions && lead.suppressions.length) {
				for (var i = 0; i < lead.suppressions.length; i++) {
					var suppression = lead.suppressions[i];
					if (!suppression.finished && suppression.campaignId === campaign.id) return 'Suppressed'
				}
			}

			var campaignLeads = lead.campaignleads;

			// Should never be the case for callbacks (?)
			if (!campaignLeads || !campaignLeads.length) {
				return 'No Stage'
			}

			var campaignLead = _.findWhere(campaignLeads, {campaignId: parseInt(campaign.id)});
			var currentStage = _.findWhere(campaign.campaignstages, {id: parseInt(campaignLead.currentCampaignStageId)});

			return currentStage ? currentStage.name : 'No Stage';
		};

		$scope.delete = function (callback) {
			SweetAlert.swal({
				title: "Delete Callback",
				text: "Are you sure you want to delete this callback?",
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#DD6B55",
				confirmButtonText: "Yes, delete it!",
				cancelButtonText: "No, cancel",
				closeOnConfirm: true,
				closeOnCancel: true
			  },
			  function (isConfirm) {
				if (isConfirm) {
					Callback.delete({
						id: callback.id
					}).$promise.then(function () {
						$scope.callbacks = $scope.callbacks.filter(cb => cb.id !== callback.id)
					})
				}
			  });
		};

		$scope.reassign = function(callback) {
			if (typeof callback.callAttemptJson == 'string') {
				callback.callAttemptJson = JSON.parse(callback.callAttemptJson);
			}
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
				controller: 'SupervisorCallbackCtrl',
				resolve: {
					callback: function() {
						return Callback.get({
							id: callback.id
						}).$promise;
					},
					lead: function() {
						return callback.lead;
					},
					campaign: function() {
						return callback.campaign;
					},
					agents: function() {
						return Campaign.getAgents({
							id: callback.campaignId
						}).$promise;
					},
					client: function() {
						return Client.get({
							id: callback.campaign.clientId
						}).$promise;
					},
					callresult: function() {
						if (callback.callresultId) {
							return CallResult.get({
								id: callback.callresultId
							}).$promise;
						} else {
							return new CallResult();
						}
					},
					stage: function() {
						return CampaignStage.get({
							id: callback.callAttemptJson.campaignstageId
						}).$promise;
					}
				}
			})

			modalInstance.result.then(function(result) {
				if (result.id) {
					$scope.callbacks = $scope.callbacks.filter(function(a) {
						return a.id && a.id !== callback.id;
					});
				}
			})
		};

		$scope.getCallbacks();
	})