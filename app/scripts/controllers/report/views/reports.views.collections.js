'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportViewsCollectionsCtrl', function (APP_SETTINGS, $window, $scope, $rootScope, moment, _, SweetAlert, User, $uibModal) {
		$rootScope.pageTitle = 'Report | Collections';
		$scope.orderby = {
			payments: 'createdAt',
			exceptions: 'createdAt',
			refusals: 'createdAt',
		};
		$scope.limit = 15;
		$scope.sortReverse = {
			payments: true,
			exceptions: true,
			refusals: true
		};
		$scope.endDate = new Date();
		$scope.startDate = moment().subtract(1, 'day').toDate();

		$scope.pagination = {
			payments: {
				current: 1
			},
			exceptions: {
				current: 1
			},
			refusals: {
				current: 1
			}
		};

		$scope.orderChanged = function (tab, order, direction) {
			$scope.orderby[tab] = order;
			$scope.sortReverse[tab] = direction;
			getResults();
		};

		$scope.pageChanged = function(pageNumber) {
			getResults();
		};

		$scope.showChanges = function (row) {
			var changes = {};
			row.dbChanges.forEach(function (change) {
				if (changes[change.field] !== undefined) {
					changes[change.field].newValue = change.newValue;
				} else {
					var label = resolveField(change.field)
					if (label) {
						changes[change.field] = {
							oldValue: change.previousValue,
							newValue: change.newValue,
							label: label
						};
					}
				}
			})
			$uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/views/reports.views.leadchanges.html',
				controller: 'ReportViewLeadChangesCtrl',
				resolve: {
					changes: function () {
						return changes;
					},
					lead: function () {
						return row.lead;
					}
				}
			})
		};

		function resolveField(field) {
			switch(field) {
				case 'salutation':
					return 'Salutation';
				case 'first_name':
					return 'First Name';
				case 'last_name':
					return 'Last Name';
				case 'suffix':
					return 'Suffix';
				case 'spouse_name':
					return 'Spouse';
				case 'company_name':
					return 'Company';
				case 'address1':
					return 'Address 1';
				case 'address2':
					return 'Address 2';
				case 'address3':
					return 'Address 3';
				case 'city':
					return 'City';
				case 'state':
					return 'State';
				case 'zip':
					return 'Zip';
				case 'phone_home':
					return 'Phone 1';
				case 'phone_mobile':
					return 'Phone 2';
				case 'phone_work':
					return 'Phone 3';
				case 'phone_workmobile':
					return 'Phone 4';
				case 'email':
					return 'Email';
				default: return null;
			}
		}

		function getResults(page) {
			User.getCollections({
				id: $rootScope.loggedInUser.id,
				page: $scope.pagination,
				limit: $scope.limit,
				orderby: $scope.orderby,
				dir: $scope.sortReverse,
				startdate: $scope.startDate,
				enddate: $scope.endDate,
			}).$promise.then(function (results) {
				$scope.payments = results.payments;
				$scope.exceptions = results.exceptions;
				$scope.refusals = results.refusals;
			})
		}

		$scope.columns = {
			payments: [{
				name: 'skill',
				label: 'Reporting Group',
				datetime: false,
				bool: false
			}, {
				name: 'subSkill',
				label: 'Lead Type',
				datetime: false,
				bool: false
			}, {
				name: 'createdAt',
				label: 'Payment Taken Date',
				datetime: true,
				bool: false
			}, {
				name: 'payDate',
				label: 'Pay Date',
				datetime: true,
				bool: false
			}, {
				name: 'payAmount',
				label: 'Payment Amount',
				datetime: false,
				bool: false
			}, {
				name: 'lead.id',
				label: 'Kaos ID',
				datetime: false,
				bool: false
			}, {
				name: 'lead.clientRef',
				label: 'Lead Client Ref',
				datetime: false,
				bool: false
			}, {
				name: 'lead.clientSourceCode',
				label: 'Lead Source Code',
				datetime: false,
				bool: false
			}, {
				name: 'lead.division',
				label: 'Lead Division',
				datetime: false,
				bool: false
			}, {
				name: 'lead.salutation',
				label: 'Lead Salutation',
				datetime: false,
				bool: false
			}, {
				name: 'lead.first_name',
				label: 'Lead First Name',
				datetime: false,
				bool: false
			}, {
				name: 'lead.last_name',
				label: 'Lead Last Name',
				datetime: false,
				bool: false
			}, {
				name: 'decisionMaker',
				label: 'Decision Maker',
				datetime: false,
				bool: false
			}, {
				name: 'lead.lyAmount',
				label: 'Lead LY Amount',
				datetime: false,
				bool: false
			}, {
				name: 'numberOfInstallments',
				label: 'Number of Installments',
				datetime: false,
				bool: false
			}, {
				name: 'installmentsNotes',
				label: 'Installment Notes',
				datetime: false,
				bool: false
			}, {
				name: 'creditCardType',
				label: 'Credit Card Type',
				datetime: false,
				bool: false
			}, {
				name: 'creditCardNumber',
				label: 'Credit Card Number',
				datetime: false,
				bool: false,
				hide: (($rootScope.loggedInUser.isSupervisor || $rootScope.loggedInUser.isAgent) && !$rootScope.loggedInUser.isAdmin)
			}, {
				name: 'creditCardExpDate',
				label: 'Credit Card Exp Date',
				datetime: false,
				bool: false,
				hide: (($rootScope.loggedInUser.isSupervisor || $rootScope.loggedInUser.isAgent) && !$rootScope.loggedInUser.isAdmin)
			}, {
				name: 'creditCardSecurityCode',
				label: 'Credit Card Security Code',
				datetime: false,
				bool: false,
				hide: (($rootScope.loggedInUser.isSupervisor || $rootScope.loggedInUser.isAgent) && !$rootScope.loggedInUser.isAdmin)
			}, {
				name: 'useExistingCreditCard',
				label: 'Use Existing Credit Card',
				datetime: false,
				bool: true
			}, {
				name: 'freeTickets',
				label: 'Free Tickets',
				datetime: false,
				bool: false
			}, {
				name: 'declineBenefits',
				label: 'Decline Benefits',
				datetime: false,
				bool: true
			}, {
				name: 'newMembershipCarrd',
				label: 'New Membership Card',
				datetime: false,
				bool: true
			}, {
				name: 'giftMatchingCompany',
				label: 'Gift Matching Company',
				datetime: false,
				bool: false
			}, {
				name: 'notes',
				label: 'Notes',
				datetime: false,
				bool: false
			}, {
				name: 'requiresFollowUp',
				label: 'Requires Follow-up',
				datetime: false,
				bool: false
			}, {
				name: 'lead.address1',
				label: 'Lead Address 1',
				datetime: false,
				bool: false
			}, {
				name: 'lead.address2',
				label: 'Lead Address 2',
				datetime: false,
				bool: false
			}, {
				name: 'lead.city',
				label: 'Lead City',
				datetime: false,
				bool: false
			}, {
				name: 'lead.state',
				label: 'Lead State',
				datetime: false,
				bool: false
			}, {
				name: 'lead.zip',
				label: 'Lead Zip',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_home',
				label: 'Lead Phone 1',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_mobile',
				label: 'Lead Phone 2',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_work',
				label: 'Lead Phone 3',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_workmobile',
				label: 'Lead Phone 4',
				datetime: false,
				bool: false
			}, {
				name: 'lead.email',
				label: 'Lead Email',
				datetime: false,
				bool: false
			}, {
				name: 'campaign.name',
				label: 'Campaign Name',
				datetime: false,
				bool: false
			}, {
				name: 'campaignstage.name',
				label: 'Campaign Stage',
				datetime: false,
				bool: false
			}, {
				name: 'client.name',
				label: 'Client Name',
				datetime: false,
				bool: false
			}],
			refusals: [{
				name: 'createdAt',
				label: 'Refusal Date',
				datetime: true,
				bool: false
			}, {
				name: 'lead.id',
				label: 'Kaos Id',
				datetime: false,
				bool: false
			}, {
				name: 'lead.clientRef',
				label: 'Client Id',
				datetime: false,
				bool: false
			}, {
				name: 'lead.clientSourceCode',
				label: 'Lead Source Code',
				datetime: false,
				bool: false
			}, {
				name: 'lead.division',
				label: 'Lead Division',
				datetime: false,
				bool: false
			}, {
				name: 'refusalReason',
				label: 'Refusal Reason',
				datetime: false,
				bool: false
			}, {
				name: 'notes',
				label: 'Notes',
				datetime: false,
				bool: false
			}, {
				name: 'decisionMaker',
				label: 'Decision Maker',
				datetime: false,
				bool: false
			}, {
				name: 'lead.salutation',
				label: 'Lead Salutation',
				datetime: false,
				bool: false
			}, {
				name: 'lead.first_name',
				label: 'Lead First Name',
				datetime: false,
				bool: false
			}, {
				name: 'lead.last_name',
				label: 'Lead Last Name',
				datetime: false,
				bool: false
			}, {
				name: 'lead.spouse_name',
				label: 'Lead Spouse Name',
				datetime: false,
				bool: false
			}, {
				name: 'skill',
				label: 'Reporting Group',
				datetime: false,
				bool: false
			}, {
				name: 'subSkill',
				label: 'Lead Type',
				datetime: false,
				bool: false
			}, {
				name: 'lead.address1',
				label: 'Lead Address 1',
				datetime: false,
				bool: false
			}, {
				name: 'lead.address2',
				label: 'Lead Address 2',
				datetime: false,
				bool: false
			}, {
				name: 'lead.city',
				label: 'Lead City',
				datetime: false,
				bool: false
			}, {
				name: 'lead.state',
				label: 'Lead State',
				datetime: false,
				bool: false
			}, {
				name: 'lead.zip',
				label: 'Lead Zip',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_home',
				label: 'Lead Phone 1',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_mobile',
				label: 'Lead Phone 2',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_work',
				label: 'Lead Phone 3',
				datetime: false,
				bool: false
			}, {
				name: 'lead.phone_workmobile',
				label: 'Lead Phone 4',
				datetime: false,
				bool: false
			}, {
				name: 'campaign.name',
				label: 'Campaign Name',
				datetime: false,
				bool: false
			}, {
				name: 'campaignstage.name',
				label: 'Campaign Stage',
				datetime: false,
				bool: false
			}, {
				name: 'client.name',
				label: 'Client Name',
				datetime: false,
				bool: false
			}]
		};

		getResults();
	})