'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportViewsSalesCtrl', function (APP_SETTINGS, $window, $scope, $rootScope, moment, _, SweetAlert, User, $uibModal) {
		$rootScope.pageTitle = 'Report | Sales';
		$scope.orderby = 'createdAt';
		$scope.limit = 50;
		$scope.sortReverse = true;
		$scope.endDate = new Date();
		$scope.startDate = moment().subtract(1, 'day').toDate();

		$scope.pagination = {
            current: 1
        };

		$scope.$watch('orderby', function () {
			$scope.pagination.current = 1;
			getResults(1);
		});

		$scope.$watch('sortReverse', function () {
			$scope.pagination.current = 1;
			getResults(1);
		});

		$scope.pageChanged = function (pageNumber) {
			getResults(pageNumber);
		};

		$scope.showChanges = function (row) {
			var changes = {};
			row.dbChanges.forEach(function (change) {
				if (changes[change.field] !== undefined) {
					changes[change.field].newValue = change.newValue;
				} else {
					var label = resolveField(change.field)
					if (label) {
						changes[change.field] = {
							oldValue: change.previousValue,
							newValue: change.newValue,
							label: label
						};
					}
				}
			})
			$uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/views/reports.views.leadchanges.html',
				controller: 'ReportViewLeadChangesCtrl',
				resolve: {
					changes: function () {
						return changes;
					},
					lead: function () {
						return row.lead;
					}
				}
			})
		};

		function resolveField(field) {
			switch(field) {
				case 'salutation':
					return 'Salutation';
				case 'first_name':
					return 'First Name';
				case 'last_name':
					return 'Last Name';
				case 'suffix':
					return 'Suffix';
				case 'spouse_name':
					return 'Spouse';
				case 'company_name':
					return 'Company';
				case 'address1':
					return 'Address 1';
				case 'address2':
					return 'Address 2';
				case 'address3':
					return 'Address 3';
				case 'city':
					return 'City';
				case 'state':
					return 'State';
				case 'zip':
					return 'Zip';
				case 'phone_home':
					return 'Phone 1';
				case 'phone_mobile':
					return 'Phone 2';
				case 'phone_work':
					return 'Phone 3';
				case 'phone_workmobile':
					return 'Phone 4';
				case 'email':
					return 'Email';
				default: return null;
			}
		}

		function getResults(page) {
			User.getSales({
				id: $rootScope.loggedInUser.id,
				page: page - 1,
				limit: $scope.limit,
				orderby: $scope.orderby,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				startdate: $scope.startDate,
				enddate: $scope.endDate,
			}).$promise.then(function (results) {
				$scope.sales = results.rows;
				$scope.count = results.count;
			})
		}

		getResults(1);
	})