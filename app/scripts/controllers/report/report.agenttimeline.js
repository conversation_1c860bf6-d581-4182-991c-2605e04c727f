'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('ReportAgentTimelineCtrl', function ($scope, $rootScope, $state, $log, Agent, AgentEvents, agents, _) {
    $rootScope.pageTitle = 'Report | Agent Timeline';

    $scope.agents = agents;
    $scope.events = [];
    $scope.currentAgentId = {};

    $scope.$watch('dateFilter', function () {
      getEvents();
    })

    $scope.$watch('agentId', function () {
      getEvents();
    })

    var getEvents = function (_agentId) {
      if(!$scope.agentId) return;

      AgentEvents.getByAgentDay({ id: _agentId }, $scope.dateFilter, function (events) {
        console.log(events);
        $scope.events = [];
        _.each(events, function(_event) {
          var newEvent = {
            title: _event.eventType,
            detail: _event.eventName
          };

          var time = moment(_event.createdAt);
          newEvent.friendlyTime = time.format('h:mm a');
          var session = JSON.parse(_event.additionalInfo);
          if(session.agentStatus && _event.eventType != 'Login') {
            newEvent.color = session.agentStatus.color;
          }
          else {
            newEvent.color = 'black';
          }

          switch(_event.eventType) {
            case "Login":
              newEvent.icon = "user";
              break;
            case "State Change":
              newEvent.icon = "phone";
              break;
            default:
              newEvent.icon = "phone";
          }

          $scope.events.push(newEvent);
        })
      })
    }

    //getEvents(1);

  });
