'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportScheduleEditCtrl', function ($scope, $rootScope, $uibModalInstance, Campaign, report, schedule, moment, clients, agents, campaigns, users) {
		$scope.schedule = schedule;
		$scope.users = users;
		if (report.definition) {
			report.definition = JSON.parse(report.definition);
		}
		if (!schedule.id) {
			$scope.schedule.startDate = new Date();
			$scope.schedule.startTime = new Date();
			$scope.schedule.frequency = 1;
		} else {
			$scope.schedule.filters = JSON.parse($scope.schedule.filters);
		}

		$scope.loadingStages = true;
		$scope.campaignstages = [];

		var selectedUsers = [];
		if (schedule.recipients && schedule.recipients.length) {
			schedule.recipients.split(',').forEach(function (userId) {
				var user = _.findWhere(users, {
					id: parseInt(userId)
				});
				if (user) selectedUsers.push(user);
			})
		}
		schedule.recipients = selectedUsers;

		$scope.clients = clients;
		$scope.agents = agents;

		var allCampaigns = campaigns;
		if ($scope.schedule.filters && $scope.schedule.filters.Client) {
			$scope.campaigns = _.filter(allCampaigns, function (cam) {
				return cam.clientId == $scope.schedule.filters.Client
			})
		} else {
			$scope.campaigns = campaigns;
		}

		$scope.availableFilters = [];

		if (report.definition instanceof Array) {
			if (report.definition[0].availableFilters) {
				report.definition[0].availableFilters.forEach(function (filter) {
					if (filter.model) {
						$scope.availableFilters.push(filter.model);
					} else if (filter.placeholder) {
						$scope.availableFilters.push(filter.name);
					}
				})
			}
		} else {
			if (report.definition.availableFilters) {
				report.definition.availableFilters.forEach(function (filter) {
					if (filter.model) {
						$scope.availableFilters.push(filter.model);
					} else if (filter.placeholder) {
						$scope.availableFilters.push(filter.name);
					}
				})
			}
		}

		if (report.definition.primaryModule) {
			$scope.availableFilters.push(report.definition.primaryModule.name);
			if (report.definition.primaryModule.relatedModules) {
				report.definition.primaryModule.relatedModules.forEach(function (module) {
					$scope.availableFilters.push(module.name);
				})
			}
		}


		$scope.save = function () {
			$scope.schedule.filters = JSON.stringify($scope.schedule.filters);
			$scope.schedule.recipients = $scope.schedule.recipients.map(function (user) {
				return user.id;
			}).join(",");

			if ($scope.schedule.id) {
				$scope.schedule.$update().then(function (result) {
					$uibModalInstance.close(result);
				});
			} else {
				$scope.schedule.reportId = report.id;
				$scope.schedule.$save().then(function (result) {
					$uibModalInstance.close(result);
				});
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};

		$scope.filterCampaigns = function () {
			if ($scope.schedule.filters && $scope.schedule.filters.Client) {
				$scope.campaigns = _.filter(allCampaigns, function (cam) {
					return cam.clientId == $scope.schedule.filters.Client
				})
			} else {
				$scope.campaigns = allCampaigns;
			}
		};

		$scope.openEndDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.startDateOpened = false;
			$scope.endDateOpened = true;
		};

		$scope.openStartDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.endDateOpened = false;
			$scope.startDateOpened = true;
		};

		$scope.format = 'dd-MMMM-yyyy';

		$scope.loadStages = function () {
			if ($scope.availableFilters.indexOf('CampaignStage') === -1) return;
			$scope.loadingStages = true;
			if (!$scope.schedule.filters) $scope.schedule.filters = {};
			$scope.schedule.filters.stage = undefined;
			var campaignId = $scope.schedule.filters.Campaign;
			Campaign.getCampaignStages({
				id: campaignId
			}).$promise.then(function (stages) {
				$scope.loadingStages = false;
				$scope.campaignstages = stages;
			})
		};

		$scope.loadStages();

	})