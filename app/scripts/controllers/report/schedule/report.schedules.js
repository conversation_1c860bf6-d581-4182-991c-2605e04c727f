'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportSchedulesCtrl', function ($scope, $rootScope, $uibModal, reportId, schedules, users, Report, ReportSchedule, SweetAlert, clients, campaigns, Agent, User) {
		$scope.schedules = schedules;
		$scope.filters = [];

		function resolveRecipients() {
			schedules.forEach(function (schedule) {
				if (schedule.filters) {
					if (typeof (schedule.filters) === 'string') schedule.filters = JSON.parse(schedule.filters);
					for (var prop in schedule.filters) {
						if ($scope.filters.indexOf(prop) === -1) {
							$scope.filters.push(prop);
						}
					}
				}
				if (schedule.recipients) {
					var selectedUsers = [];
					schedule.recipients.split(',').forEach(function (userId) {
						var user = _.findWhere(users, {
							id: parseInt(userId)
						})
						if (user) selectedUsers.push(user.name);
					})
					schedule.recipients = selectedUsers.join(', ');
				}
			})
		}

		resolveRecipients();

		$scope.resolveFilter = function (filter, value) {
			if (filter == 'Client') {
				var client = clients.find(function (c) {
					return c.id == value;
				})
				return client ? client.name : value;
			}

			if (filter == 'Campaign') {
				var campaign = campaigns.find(function (c) {
					return c.id == value;
				})
				return campaign ? campaign.name : value;
			}

			if (filter == 'dateRange') {
				return $rootScope.splitOnCamelcase(value);
			}

			return value;
		};

		$scope.new = function () {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/schedule/report.schedules.edit.html',
				controller: 'ReportScheduleEditCtrl',
				resolve: {
					schedule: function () {
						return new ReportSchedule();
					},
					report: function () {
						return Report.get({
							id: reportId
						}).$promise;
					},
					campaigns: function () {
						return campaigns;
					},
					clients: function () {
						return clients;
					},
					agents: function () {
						return Agent.query().$promise;
					},
					users: function () {
						return users;
					}
				}
			});

			modalInstance.result.then(function (result) {
				if (result.id) {
					$scope.schedules.push(result);
					resolveRecipients();
				}
			})
		}

		$scope.edit = function (schedule) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/report/schedule/report.schedules.edit.html',
				controller: 'ReportScheduleEditCtrl',
				resolve: {
					schedule: function () {
						return ReportSchedule.get({
							id: schedule.id
						}).$promise;
					},
					report: function () {
						return Report.get({
							id: reportId
						}).$promise;
					},
					campaigns: function () {
						return campaigns
					},
					clients: function () {
						return clients
					},
					agents: function () {
						return Agent.query().$promise;
					},
					users: function () {
						return users;
					}
				}
			});

			modalInstance.result.then(function (result) {
				if (result.id) {
					$scope.schedules.forEach(function (schedule) {
						if (schedule.id == result.id) {
							for (var prop in schedule) {
								if (schedule.hasOwnProperty(prop)) {
									schedule[prop] = result[prop];
								}
							}
						}
					})
					resolveRecipients();
				}
			})
		}

		$scope.delete = function (schedule) {
			SweetAlert.swal({
				title: "Delete Schedule",
				text: "Are you user you want to delete this schedule?",
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#DD6B55",
				confirmButtonText: "Yes, delete it!",
				cancelButtonText: "No, cancel",
				closeOnConfirm: true,
				closeOnCancel: true
			},
				function (isConfirm) {
					if (isConfirm) {
						var scheduleId = schedule.id;
						ReportSchedule.delete({
							id: schedule.id
						}).$promise.then(function () {
							$scope.schedules = $scope.schedules.filter(function (a) {
								return a.id && a.id !== scheduleId;
							});
						});
					}
				});
		}
	})