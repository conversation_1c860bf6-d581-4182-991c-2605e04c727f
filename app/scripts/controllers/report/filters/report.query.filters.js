'use strict';

angular.module('dialerFrontendApp')
	.controller('ReportQueryFilterCtrl', function (APP_SETTINGS, $scope, $rootScope, $uibModalInstance, $window, report, ReportHistory, Client, Campaign, Agent, moment, _) {
		if (report.definition instanceof Array)
			report.definition = report.definition[0]

		$scope.report = report;
		$scope.campaigns = [];
		$scope.clients = [];
		$scope.agents = [];
		$scope.filters = {
			startDate: new Date().addDays(-7),
			endDate: new Date()
		};
		$scope.loadingStages = true;
		$scope.campaignstages = [];
		var historyFilters = [];
		var replacements = [];

		var allCampaigns = [];

		Client.query().$promise.then(function (clients) {
			$scope.clients = clients;
		});

		Campaign.query().$promise.then(function (campaigns) {
			allCampaigns = campaigns;
			if ($scope.filters.client) {
				$scope.campaigns = _.filter(campaigns, function (cam) {
					return cam.clientId == $scope.filters.client
				})
			} else {
				$scope.campaigns = campaigns;
			}
		});

		Agent.query().$promise.then(function (agents) {
			$scope.agents = agents;
		});

		$scope.campaignAvailable = _.findWhere(report.definition.availableFilters, {
			tableName: 'campaigns'
		});

		$scope.clientAvailable = _.findWhere(report.definition.availableFilters, {
			tableName: 'clients'
		});

		$scope.agentAvailable = _.findWhere(report.definition.availableFilters, {
			tableName: 'agents'
		});

		$scope.stagesAvailable = _.findWhere(report.definition.availableFilters, {
			tableName: 'campaignstages'
		});

		$scope.dateFilters = _.findWhere(report.definition.availableFilters, {
			type: 'datetime'
		});

		if (!$scope.dateFilters) {
			$scope.dateFilters = _.findWhere(report.definition.availableFilters, {
				type: 'date'
			});
		}

		$scope.dates = {
			start: false,
			end: false
		};

		$scope.openEndDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.dates.start = false;
			$scope.dates.end = true;
		};

		$scope.openStartDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.dates.end = false;
			$scope.dates.start = true;
		};

		$scope.format = 'dd-MMMM-yyyy';

		$scope.loadStages = function () {
			if (!$scope.stagesAvailable) return;
			$scope.loadingStages = true;
			$scope.filters.stage = undefined;
			var campaignId = $scope.filters.campaign;
			Campaign.getCampaignStages({
				id: campaignId
			}).$promise.then(function (stages) {
				$scope.loadingStages = false;
				$scope.campaignstages = stages;
			})
		};

		$scope.run = function () {
			var history = new ReportHistory();
			history.userId = $rootScope.loggedInUser.id;
			history.reportId = $scope.report.id;
			history.whereClause = buildWhereClause();
			history.filters = historyFilters;
			history.placeholderReplacements = replacements;
			history.$save(function (result) {
				$uibModalInstance.close(result);
			});
		}

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};

		$scope.filterCampaigns = function () {
			if ($scope.filters.client) {
				$scope.campaigns = _.filter(allCampaigns, function (cam) {
					return cam.clientId == $scope.filters.client
				})
			} else {
				$scope.campaigns = allCampaigns;
			}
		};

		function buildWhereClause() {
			var result = '';
			var filters = [];

			if ($scope.dateFilters) {
				if ($scope.dateFilters.queryTableName) {
					historyFilters.push({
						field: $scope.dateFilters.field,
						value: 'greater than ' + $scope.filters.startDate
					});
					historyFilters.push({
						field: $scope.dateFilters.field,
						value: 'less than ' + $scope.filters.endDate
					});
					filters.push($scope.dateFilters.queryTableName + '.' + $scope.dateFilters.field + " >= '" + moment($scope.filters.startDate).startOf('day').toISOString() + "'");
					filters.push($scope.dateFilters.queryTableName + '.' + $scope.dateFilters.field + " <= '" + moment($scope.filters.endDate).endOf('day').toISOString() + "'");
				} else if ($scope.dateFilters.placeholder) {
					historyFilters.push({
						field: $scope.dateFilters.name,
						value: 'greater than ' + $scope.filters.startDate
					});
					historyFilters.push({
						field: $scope.dateFilters.name,
						value: 'less than ' + $scope.filters.endDate
					});

					replacements.push({
						placeholder: '{{startDate}}',
						value: $scope.dateFilters.local ? moment($scope.filters.startDate).startOf('day').format('YYYY-MM-DD 23:59:59') : moment($scope.filters.startDate).startOf('day').toISOString()
					});

					replacements.push({
						placeholder: '{{endDate}}',
						value: $scope.dateFilters.local ? moment($scope.filters.endDate).endOf('day').format('YYYY-MM-DD 00:00:00') : moment($scope.filters.endDate).endOf('day').toISOString()
					});
				}
			}

			if ($scope.filters.campaign) {
				historyFilters.push({
					field: 'Campaign',
					value: _.findWhere($scope.campaigns, {
						id: $scope.filters.campaign
					}).name
				});

				replacements.push({
					placeholder: '{{campaignId}}',
					value: $scope.filters.campaign
				});

				if ($scope.filters.sisterCampaign) {
					filters.push($scope.campaignAvailable.queryTableName + '.id in (' + $scope.filters.campaign + ', ' + $scope.filters.sisterCampaign + ')');
				}
				else {
					filters.push($scope.campaignAvailable.queryTableName + '.id=' + $scope.filters.campaign + '');
				}
			}
			if ($scope.filters.campaigns && $scope.filters.campaigns.length) {
				var campaigns = _.filter($scope.campaigns, function (c) {
					return $scope.filters.campaigns.indexOf(c.id) > -1
				})
				historyFilters.push({
					field: 'Campaign',
					value: _.map(campaigns, function (c) {
						return c.name
					}).join(', ')
				});

				replacements.push({
					placeholder: '{{campaignId}}',
					value: $scope.filters.campaigns.join()
				});

				filters.push($scope.campaignAvailable.queryTableName + '.id in (' + $scope.filters.campaigns.join() + ')');
			}
			if ($scope.filters.client) {
				historyFilters.push({
					field: 'Client',
					value: _.findWhere($scope.clients, {
						id: $scope.filters.client
					}).name
				});

				replacements.push({
					placeholder: '{{clientId}}',
					value: $scope.filters.client
				});

				filters.push($scope.clientAvailable.queryTableName + '.id=' + $scope.filters.client + '');
			}
			if ($scope.filters.agent) {
				historyFilters.push({
					field: 'Agent',
					value: _.findWhere($scope.agents, {
						id: $scope.filters.agent
					}).name
				});
				filters.push($scope.agentAvailable.queryTableName + '.id=' + $scope.filters.agent + '');
			}

			if ($scope.filters.agents && $scope.filters.agents.length) {
				var agents = _.filter($scope.agents, function (c) {
					return $scope.filters.agents.indexOf(c.id) > -1
				})
				historyFilters.push({
					field: 'Agent',
					value: _.map(agents, function (c) {
						return c.name
					}).join(', ')
				});

				replacements.push({
					placeholder: '{{agentId}}',
					value: $scope.filters.agents.join()
				});

				filters.push($scope.agentAvailable.queryTableName + '.id in (' + $scope.filters.agents.join() + ')');
			}

			if ($scope.filters.stage) {
				historyFilters.push({
					field: 'Campaign Stage',
					value: _.findWhere($scope.campaignstages, {
						id: $scope.filters.stage
					}).name
				});

				replacements.push({
					placeholder: '{{stageId}}',
					value: $scope.filters.stage
				});

				filters.push($scope.stagesAvailable.queryTableName + '.id=' + $scope.filters.stage + '');
			}

			if (filters.length) {
				result = '';
				for (var i = 0; i < filters.length; i++) {
					if (i === 0) {
						result += filters[i];
					} else {
						result += ' and ' + filters[i];
					}
				};
			}

			return result;
		}
	});