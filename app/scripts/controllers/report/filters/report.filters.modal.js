'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportFilterModalCtrl', function (APP_SETTINGS, $scope, $rootScope, $uibModalInstance, $window, report, ReportHistory, Client, Campaign, Agent, moment, _) {
		if (report.definition instanceof Array)
			report.definition = report.definition[0]

		$scope.report = report;
		$scope.campaigns = [];
		$scope.clients = [];
		$scope.agents = [];
		$scope.models = [report.definition.primaryModule.name];
		$scope.fields = [];
		$scope.query = {
			startDate: new Date(),
			endDate: new Date()
		};
		$scope.loadingStages = false;
		$scope.campaignstages = [];
		$scope.filters = {
			all: [{
				field: '',
				operator: '',
				value: ''
			}],
			any: [{
				field: '',
				operator: '',
				value: ''
			}]
		};

		$scope.operators = [{
			label: 'equals',
			value: ''
		}, {
			label: 'does not equal',
			value: 'ne'
		}, {
			label: 'greater than',
			value: 'gt'
		}, {
			label: 'less than',
			value: 'lt'
		}];

		for (var i = 0; i < $scope.report.definition.primaryModule.fields.length; i++) {
			var field = $scope.report.definition.primaryModule.fields[i];
			$scope.fields.push($scope.report.definition.primaryModule.name + '.' + field.fieldName);
		};


		if (report.definition.primaryModule.relatedModules && report.definition.primaryModule.relatedModules.length) {
			for (var i = 0; i < report.definition.primaryModule.relatedModules.length; i++) {
				var related = report.definition.primaryModule.relatedModules[i];
				$scope.models.push(related.name);
				for (var j = 0; j < related.fields.length; j++) {
					var field = related.fields[j];
					$scope.fields.push(related.name + '.' + field.fieldName);
				};
			};
		}

		Client.query().$promise.then(function (clients) {
			$scope.clients = clients;
		});

		Campaign.query().$promise.then(function (campaigns) {
			$scope.campaigns = _.sortBy(campaigns, 'name');
		});

		Agent.query().$promise.then(function (agents) {
			$scope.agents = agents;
		});

		$scope.addFilter = function (isAll) {
			var blankFilter = {
				field: '',
				operator: '',
				value: ''
			};
			if (isAll) {
				$scope.filters.all.push(blankFilter);
			} else {
				$scope.filters.any.push(blankFilter);
			}
		};

		$scope.removeFilter = function (isAll, index) {
			if (isAll) {
				$scope.filters.all.splice(index, 1);
			} else {
				$scope.filters.any.splice(index, 1);
			}
		};

		$scope.openEndDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.startDateOpened = false;
			$scope.endDateOpened = true;
		};

		$scope.openStartDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.endDateOpened = false;
			$scope.startDateOpened = true;
		};

		$scope.format = 'dd-MMMM-yyyy';

		$scope.loadStages = function () {
			if (!$scope.stagesAvailable) return;
			$scope.loadingStages = true;
			$scope.filters.stage = undefined;
			var campaignId = $scope.filters.campaign;
			Campaign.getCampaignStages({
				id: campaignId
			}).$promise.then(function (stages) {
				$scope.loadingStages = false;
				$scope.campaignstages = stages;
			})
		};

		$scope.run = function () {
			var history = new ReportHistory();
			history.userId = $rootScope.loggedInUser.id;
			history.reportId = $scope.report.id;
			history.filters = buildFilters();
			history.$save(function (result) {
				$uibModalInstance.close(result);
			});
		}

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};

		function buildFilters() {
			var result = [];
			if ($scope.query.campaign) {
				result.push({
					model: 'Campaign',
					field: 'id',
					operator: null,
					value: $scope.query.campaign,
					type: 'and',
					friendlyName: _.findWhere($scope.campaigns, {
						id: $scope.query.campaign
					}).name
				});
			}
			if ($scope.query.client) {
				result.push({
					model: 'Client',
					field: 'id',
					operator: null,
					value: $scope.query.client,
					type: 'and',
					friendlyName: _.findWhere($scope.clients, {
						id: $scope.query.client
					}).name
				});
			}
			if ($scope.query.stage) {
				result.push({
					model: 'CampaignStage',
					field: 'id',
					operator: null,
					value: $scope.query.stage,
					type: 'and',
					friendlyName: _.findWhere($scope.campaignstages, {
						id: $scope.query.stage
					}).name
				});
			}
			if ($scope.query.agent) {
				result.push({
					model: 'Agent',
					field: 'id',
					operator: null,
					value: $scope.query.agent,
					type: 'and',
					friendlyName: _.findWhere($scope.agents, {
						id: $scope.query.agent
					}).name
				});
			}
			if ($scope.models.indexOf('CallResult') > -1) {
				result.push({
					model: 'CallResult',
					field: 'createdAt',
					operator: 'gte',
					value: moment($scope.query.startDate).startOf('day').toISOString(),
					type: 'and'
				});

				result.push({
					model: 'CallResult',
					field: 'createdAt',
					operator: 'lte',
					value: moment($scope.query.endDate).endOf('day').toISOString(),
					type: 'and'
				});
			}

			for (var i = 0; i < $scope.filters.all.length; i++) {
				var filter = $scope.filters.all[i];
				if (filter.field) {
					var split = filter.field.split('.');
					result.push({
						model: split[0],
						field: split[1],
						operator: filter.operator,
						value: filter.value,
						type: 'and'
					});
				}
			};

			for (var i = 0; i < $scope.filters.any.length; i++) {
				var filter = $scope.filters.any[i];
				if (filter.field) {
					var split = filter.field.split('.');
					result.push({
						model: split[0],
						field: split[1],
						operator: filter.operator,
						value: filter.value,
						type: 'any'
					});
				}
			};

			return result;
		}
	});