'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportEmailCtrl', function ($scope, $rootScope, $uibModalInstance) {
		$scope.email = {};
		$scope.send = function () {
			$uibModalInstance.close($scope.email);
		}
		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}
	})