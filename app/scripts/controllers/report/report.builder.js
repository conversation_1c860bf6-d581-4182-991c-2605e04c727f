'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ReportBuilderCtrl', function ($scope, $rootScope, $state, Report, _, reportObj, modules) {
		$rootScope.pageTitle = 'Reports | Create';
		$scope.modules = modules;
		$scope.report = {}

		$scope.report.columns = [];
		$scope.report.calculatedColumns = [];
		$scope.report.groupby = [{
			field: '',
			dir: 'DESC'
		}, {
			field: '',
			dir: 'DESC'
		}];
		$scope.report.filters = {
			all: [{
				field: '',
				operator: 'eq',
				value: '',
				operators: []
			}],
			any: [{
				field: '',
				operator: 'eq',
				value: '',
				operators: []
			}]
		}

		var stringOperators = [{
			label: 'equals',
			value: 'eq'
		}, {
			label: 'not equal to',
			value: 'ne'
		}, {
			label: 'starts with',
			value: 'starts'
		}, {
			label: 'ends with',
			value: 'ends'
		}, {
			label: 'contains',
			value: 'contains'
		}, {
			label: 'does not contain',
			value: 'notlike'
		}, {
			label: 'is empty',
			value: 'empty'
		}]

		var intOperators = [{
			label: 'equals',
			value: 'eq'
		}, {
			label: 'not equal to',
			value: 'ne'
		}, {
			label: 'less than',
			value: 'lt'
		}, {
			label: 'less than or equal to',
			value: 'lte'
		}, {
			label: 'greater than',
			value: 'gt'
		}, {
			label: 'greater than or equal to',
			value: 'gte'
		}, {
			label: 'between (#-#)',
			value: 'between'
		}, {
			label: 'not between (#-#)',
			value: 'notBetween'
		}]

		var dateOperators = [{
			label: 'equals',
			value: 'eq'
		}, {
			label: 'not equal to',
			value: 'ne'
		}, {
			label: 'less than',
			value: 'lt'
		}, {
			label: 'greater than',
			value: 'gt'
		}]

		$scope.folders = [{
			name: 'Campaign Reports'
		}, {
			name: 'Agent Reports'
		}, {
			name: 'Custom Reports'
		}];

		$scope.friendlyRelatedModules = [];
		$scope.friendlyColumns = [];
		$scope.columns = [];

		$scope.groupby = [{
			field: '--None--',
			dir: 'ASC'
		}, {
			name: '--None--',
			dir: 'ASC'
		}];

		var primaryModule;

		$scope.updatePrimary = function () {
			primaryModule = _.findWhere($scope.modules, {
				name: $scope.report.primaryModule
			});
			$scope.friendlyRelatedModules = primaryModule.associations;
		};

		$scope.setAvailableColumns = function () {
			var results = [];
			$scope.report.calculatedColumns = [];
			$scope.columns = [];
			for (var i = 0; i < primaryModule.attributes.length; i++) {
				var type = primaryModule.attributes[i].type;
				var field = primaryModule.attributes[i].name;
				var obj = {
					modal: primaryModule.name,
					field: field,
					type: type
				};
				if ((type == 'INTEGER' || type == 'FLOAT' || type == 'DOUBLE') && field != 'id' && field.indexOf('Id') == -1) {
					$scope.report.calculatedColumns.push(obj);
				}
				$scope.columns.push(obj);

				results.push(primaryModule.name + '.' + field);
			};
			if ($scope.report.relatedModules && $scope.report.relatedModules.length) {
				for (var i = 0; i < $scope.report.relatedModules.length; i++) {
					var related = $scope.report.relatedModules[i];
					var module = _.findWhere($scope.modules, {
						name: related
					});
					for (var j = 0; j < module.attributes.length; j++) {
						var type = module.attributes[j].type;
						var field = module.attributes[j].name;
						var obj = {
							modal: related,
							field: field,
							type: type
						};
						if ((type == 'INTEGER' || type == 'FLOAT' || type == 'DOUBLE' || type == 'DOUBLE PRECISION') && field != 'id' && field.indexOf('Id') == -1) {
							$scope.report.calculatedColumns.push(obj);
						}
						$scope.columns.push(obj);

						results.push(related + '.' + module.attributes[j].name);
					};
				};
			}
			$scope.friendlyColumns = results;
		};


		$scope.saveColumns = function () {

		};

		$scope.addFilter = function (isAll) {
			var filter = {
				field: '',
				operator: 'eq',
				value: '',
				operators: []
			};
			if (isAll) {
				$scope.report.filters.all.push(filter);
			} else {
				$scope.report.filters.any.push(filter);
			}
		};

		$scope.removeFilter = function (isAll, index) {
			if (isAll) {
				$scope.report.filters.all.splice(index, 1);
			} else {
				$scope.report.filters.any.splice(index, 1);
			}
		};

		$scope.fieldChanged = function (filter) {
			var modal = filter.field.split('.')[0];
			var field = filter.field.split('.')[1];

			var att = _.findWhere($scope.columns, {
				field: field,
				modal: modal
			});

			if (att) {
				if (att.type == 'INTEGER' || att.type == 'FLOAT' || att.type == 'DOUBLE' || att.type == 'DOUBLE PRECISION') {
					filter.operators = intOperators;
					filter.type = 'number';
				} else if (att.type === 'DATE') {
					filter.operators = dateOperators;
					filter.type = 'date';
				} else {
					filter.operators = stringOperators;
					filter.type = 'text';
				}
			}
		};

		$scope.wizardCompleted = function () {
			//construct the report definition from the $scope.report object
			var rp = $scope.report;
			var reportObject = new Report();
			reportObject.name = rp.name;
			reportObject.folder = rp.folder;
			var report = reportObject.definition = {
				primaryModule: {}
			};

			report.primaryModule.name = rp.primaryModule;
			report.primaryModule.fields = [];
			report.primaryModule.filters = {
				and: [],
				or: []
			};
			report.primaryModule.relatedModules = [];
			report.groupBy = [];

			//get related modules
			if (rp.relatedModules) {
				for (var i = 0; i < rp.relatedModules.length; i++) {
					report.primaryModule.relatedModules.push({
						name: rp.relatedModules[i],
						fields: [],
						filters: {
							and: [],
							or: []
						},
						relatedModules: [],
						groupBy: []
					});
				};
			}

			//get columns
			if (rp.columns) {
				for (var i = 0; i < rp.columns.length; i++) {
					var split = rp.columns[i].split('.');
					var moduleName = split[0];
					var fieldName = split[1];
					if (moduleName == report.primaryModule.name) {
						report.primaryModule.fields.push({
							fieldName: fieldName,
							operation: null
						});
					} else {
						_.findWhere(report.primaryModule.relatedModules, {
							name: moduleName
						}).fields.push({
							fieldName: fieldName,
							operation: null
						});
					}
				};
			}

			//group by
			for (var i = 0; i < rp.groupby.length; i++) {
				var groupby = rp.groupby[i];
				if (groupby.field) {
					var split = groupby.field.split('.');
					var moduleName = split[0];
					var fieldName = split[1];
					report.groupBy.push({
						moduleName: moduleName,
						fieldName: fieldName,
						sortOrder: groupby.dir || 'DESC'
					});
				}
			};

			//calculations
			if (rp.calculatedColumns) {
				for (var i = 0; i < rp.calculatedColumns.length; i++) {
					var column = rp.calculatedColumns[i];
					if (column.sum || column.avg || column.low || column.high) {
						var operation = '';
						if (column.sum) operation = 'sum';
						else if (column.avg) operation = 'avg';
						else if (column.low) operation = 'min';
						else if (column.high) operation = 'max';

						if (report.primaryModule.name == column.modal) {
							var existingColumn = _.findWhere(report.primaryModule.fields, {
								fieldName: column.field
							});
							if (existingColumn) {
								existingColumn.operation = operation;
							} else {
								report.primaryModule.fields.push({
									fieldName: column.field,
									operation: operation
								});
							}
						} else {
							var module = _.findWhere(report.primaryModule.relatedModules, {
								name: column.modal
							});
							var existingColumn = _.findWhere(module.fields, {
								fieldName: column.field
							})
							if (existingColumn) {
								existingColumn.operation = operation;
							} else {
								module.fields.push({
									fieldName: column.field,
									operation: operation
								});
							}
						}
					}
				};
			}

			//filters
			for (var i = 0; i < rp.filters.all.length; i++) {
				var filter = rp.filters.all[i];
				if (filter.field) {
					var flt = calculateFilter(filter);
					if (report.primaryModule.name == flt.moduleName) {
						delete flt.moduleName;
						report.primaryModule.filters.and.push(flt);
					} else {
						var module = _.findWhere(report.primaryModule.relatedModules, {
							name: flt.moduleName
						});
						delete flt.moduleName;
						module.filters.and.push(flt);
					}
				}
			};

			for (var i = 0; i < rp.filters.any.length; i++) {
				var filter = rp.filters.any[i];
				if (filter.field) {
					var flt = calculateFilter(filter);
					if (report.primaryModule.name == flt.moduleName) {
						delete flt.moduleName;
						report.primaryModule.filters.or.push(flt);
					} else {
						var module = _.findWhere(report.primaryModule.relatedModules, {
							name: flt.moduleName
						});
						delete flt.moduleName;
						module.filters.or.push(flt);
					}
				}
			};


			reportObject.definition = JSON.stringify(reportObject.definition);
			reportObject.isSystem = false;

			if (reportObj.id) {
				reportObject.$update({
					id: reportObj.id
				}, function () {
					var location = 'report.all'
					if (reportObject.folder) {
						if (reportObject.folder === 'Custom Reports') location = 'report.custom';
						if (reportObject.folder === 'Agent Reports') location = 'report.agent';
						if (reportObject.folder === 'Campaign Reports') location = 'report.campaign';
					}
					$state.go(location);
				});
			} else {
				reportObject.$save(function () {
					var location = 'report.all'
					if (reportObject.folder) {
						if (reportObject.folder === 'Custom Reports') location = 'report.custom';
						if (reportObject.folder === 'Agent Reports') location = 'report.agent';
						if (reportObject.folder === 'Campaign Reports') location = 'report.campaign';
					}
					$state.go(location);
				});
			}
		};

		function calculateFilter(filter) {
			var result = {};
			var split = filter.field.split('.');
			result.moduleName = split[0];
			result.fieldName = split[1];
			result.operator = filter.operator;
			switch (filter.operator) {
				case 'starts':
					result.operator = 'like';
					result.comparator = filter.value + '%';
					break;
				case 'ends':
					result.operator = 'like';
					result.comparator = '%' + filter.value;
					break;
				case 'contains':
					result.operator = 'like';
					result.comparator = '%' + filter.value + '%';
					break;
				case 'between':
				case 'notBetween':
					result.comparator = filter.value.split('-');
					break;
				case 'empty':
					result.comparator = '';
					break;
				default:
					result.comparator = filter.value;
			}
			return result;
		}

		function uncalculateFilter(module, filter) {
			var result = {
				field: module + '.' + filter.fieldName,
				operator: filter.operator,
				value: '',
				operators: []
			}

			if (filter.operator === 'like') {
				if (filter.comparator[0] === '%' && filter.comparator[filter.comparator.length - 1] === '%') {
					result.operator = 'contains'
					result.value = filter.comparator.substring(1, filter.comparator.length - 2)
				} else if (filter.comparator[0] === '%') {
					result.operator = 'ends'
					result.value = filter.comparator.substring(1, filter.comparator.length - 1)
				} else {
					result.operator = 'starts'
					result.value = filter.comparator.substring(0, filter.comparator.length - 2)
				}
			} else {
				if (filter.comparator === '') {
					result.operator = 'empty'
				} else if (Array.isArray(filter.comparator)) {
					result.value = filter.comparator.join('-')
				} else {
					result.value = filter.comparator
				}
			}

			var att = _.findWhere($scope.columns, {
				field: filter.fieldName,
				modal: module
			});

			if (att) {
				if (att.type == 'INTEGER' || att.type == 'FLOAT' || att.type == 'DOUBLE' || att.type == 'DOUBLE PRECISION') {
					result.operators = intOperators;
					result.type = 'number'
				} else if (att.type == 'DATE') {
					result.operators = dateOperators;
					result.value = new Date(result.value)
					result.type = 'date'
				} else {
					result.operators = stringOperators;
					result.type = 'text'
				}
			}

			return result
		}

		if (reportObj && reportObj.id) {
			$scope.update = true;
			$scope.oldName = reportObj.name;

			var definition = JSON.parse(reportObj.definition);

			$scope.report.primaryModule = definition.primaryModule.name;
			$scope.report.relatedModules = definition.primaryModule.relatedModules.map(r => r.name)

			$scope.updatePrimary();
			$scope.setAvailableColumns();
			$scope.report.folder = reportObj.folder;
			$scope.report.name = reportObj.name;
			$scope.report.description = reportObj.description;
			definition.primaryModule.fields.forEach(col => {
				if (col.operation) {
					var found = _.findWhere($scope.report.calculatedColumns, {
						modal: definition.primaryModule.name,
						field: col.fieldName
					})
					if (found) {
						if (col.operation === 'sum') found.sum = true
						if (col.operation === 'avg') found.avg = true
						if (col.operation === 'min') found.low = true
						if (col.operation === 'max') found.high = true
					}
				} else {
					$scope.report.columns.push(definition.primaryModule.name + '.' + col.fieldName)
				}
			})
			definition.primaryModule.filters.and.forEach((filt, i) => {
				if (!i) $scope.report.filters.all = []
				$scope.report.filters.all.push(uncalculateFilter(definition.primaryModule.name, filt))
			})
			definition.primaryModule.filters.or.forEach((filt, i) => {
				if (!i) $scope.report.filters.any = []
				$scope.report.filters.any.push(uncalculateFilter(definition.primaryModule.name, filt))
			})
			definition.primaryModule.relatedModules.forEach(rm => {
				rm.fields.forEach(col => {
					if (col.operation) {
						var found = _.findWhere($scope.report.calculatedColumns, {
							modal: rm.name,
							field: col.fieldName
						})
						if (found) {
							if (col.operation === 'sum') found.sum = true
							if (col.operation === 'avg') found.avg = true
							if (col.operation === 'min') found.min = true
							if (col.operation === 'max') found.max = true
						}
					} else {
						$scope.report.columns.push(rm.name + '.' + col.fieldName)
					}
				})

				rm.filters.and.forEach(filt => {
					$scope.report.filters.all.push(uncalculateFilter(rm.name, filt))
				})

				rm.filters.or.forEach(filt => {
					$scope.report.filters.any.push(uncalculateFilter(rm.name, filt))
				})
			})

			definition.groupBy.forEach((group, i) => {
				$scope.report.groupby[i] = {
					field: group.moduleName + '.' + group.fieldName,
					dir: group.sortOrder
				}
			})
		}
	});