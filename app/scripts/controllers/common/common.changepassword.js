'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ChangePasswordModalCtrl', function ($scope, $rootScope, $http, APP_SETTINGS, $uibModalInstance, User, SweetAlert, withCancel, loggedInUser, client, ignoreCurrent, Client) {
		$scope.user = {};
		$scope.passwordError = '';
		$scope.newPasswordError = '';
		$scope.repeatPassword = '';
		$scope.withCancel = withCancel;
		$scope.ignoreCurrent = ignoreCurrent;
		$scope.client = client;

		$scope.passwordFormatText = "Password must contain:\nat least 1 uppercase letter\nat least 1 lowercase letter\nat least 1 number\nat least 8 characters";

		var passwordhistory = [];
		if (loggedInUser.passwordhistory) {
			passwordhistory = JSON.parse(loggedInUser.passwordhistory);
		}

		$scope.setpassword = function () {
			$scope.passwordError = '';
			$scope.newPasswordError = '';
			$scope.repeatPassword = '';

			var user = $scope.user;

			if (!user.newPassword) {
				$scope.newPasswordError = 'Password cannot be blank';
				return;
			}

			

			if (!client && passwordhistory.indexOf($scope.user.newPassword) > -1) {
				SweetAlert.swal("Password Duplication", "New password cannot be the same as a previous password", 'warning');
				return;
			}

			if (!$scope.checkPasswordStrength($scope.user.newPassword)) {
				SweetAlert.swal("Password is not strong enough", $scope.passwordFormatText, 'warning');
				return;
			}

			if ($scope.user.newPassword == $scope.user.repeatPassword) {
				var promise
				if(client) {
					promise = $http.get(APP_SETTINGS.BASE_API_URL + 'clients/' + loggedInUser.clientId + '/checkpassword?password=' + $scope.user.currentPassword + '&ignore=' + ignoreCurrent);
				} else {
					promise = User.checkPassword({
						id: loggedInUser.id,
						password: $scope.user.currentPassword
					}).$promise;
				}
				promise.then(function (result) {
					var isGood = client ? result.data.result : result.result
					if (isGood) {
						if(!client) {
							if (user.currentPassword == user.newPassword) {
								$scope.newPasswordError = 'New password must not be the same as the current password';
								return false;
							}
						}

						if(client) {
							Client.get({
								id: loggedInUser.clientId
							}).$promise.then(function (currentUser) {
								currentUser.reportPassword = user.newPassword;
								currentUser.$update();
								$uibModalInstance.close(true);
							})
						} else {
							User.get({
								id: loggedInUser.id
							}).$promise.then(function (currentUser) {
								currentUser.password = user.newPassword;
								currentUser.firstLogin = false;
								currentUser.$update();
								$uibModalInstance.close(true);
							})
						}						
					} else {
						$scope.passwordError = "Wrong Password";
					}
				})
			} else {
				$scope.repeatPassword = 'Passwords do not match';
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.close(false);
		};
	});