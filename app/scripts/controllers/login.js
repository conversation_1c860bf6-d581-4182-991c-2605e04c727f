'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('LoginCtrl', function (_, $state, $scope, $location, $rootScope, moment, $window, $http, UserAuthFactory, AuthenticationFactory) {
    $scope.loginUser = function () {
      var username = $scope.user?.username?.toLowerCase(),
        password = $scope.user?.password;
      $scope.error = '';

      if (username !== undefined && password !== undefined) {
        UserAuthFactory.login(username, password)
          .success(function (data) {
            if (data) {
              if (data.status === 401) {
                //invalid credentials
                $scope.error = data.message;
              } else {
                $scope.error = '';
                AuthenticationFactory.isLogged = true;
                AuthenticationFactory.user = data.user.username;

                $window.sessionStorage.token = data.token;
                $window.sessionStorage.loggedInUser = JSON.stringify(data.user);
                $window.sessionStorage.user = data.user.username; // to fetch the user details on refresh
                $window.sessionStorage.isAdmin = data.user.isAdmin;
                $window.sessionStorage.isSupervisor = data.user.isSupervisor;
                $window.sessionStorage.isClient = data.user.isClient;
                $window.sessionStorage.isSuperManager = data.user.isSuperManager;
                $window.sessionStorage.isAgent = data.user.isAgent;
                $window.sessionStorage.loginTime = moment().format('h:mm a');

                if (moment(data.user.lastPasswordUpdate) < moment().subtract(90, 'days')) {
                  //force password change
                  data.user.firstLogin = true;
                }

                $rootScope.loggedInUser = data.user;

                $rootScope.forcedExit = false;
                $rootScope.loggedOut = false;

                $state.go(data.user.homeState);
              }
            } else {
              console.error('Oops something went wrong!');
            }
          })
          .error(function (err) {
            console.log(err);
            console.error('Oops something went wrong!');
          });
      } else {
        console.warn('Invalid credentials');
      }
    };
  });