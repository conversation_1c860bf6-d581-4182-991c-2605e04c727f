'use strict';

angular.module('dialerFrontendApp')
    .controller('ItemDeleteModalCtrl', function($scope, $rootScope, $uibModalInstance, item, _, SweetAlert) {
        $scope.kaosId;

        $scope.ok = function() {
            if($scope.kaosId == item.leadId) {
                $uibModalInstance.close(true);
            } else {
                SweetAlert.swal({
                    title: "Wrong KAOS Id",
                    text: "You must enter the correct KAOS Id to delete this.",
                    type: "warning",
                    showCancelButton: false,
                    confirmButtonColor: "#DD6B55",
                    confirmButtonText: "Ok",
                    closeOnConfirm: true,
                    closeOnCancel: true
                });
            }
        };

        $scope.cancel = function() {
            $uibModalInstance.dismiss();
        };
    });