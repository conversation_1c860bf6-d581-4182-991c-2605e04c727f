'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('AgentDashboardForceWrapUpModalCtrl', function ($scope, $timeout, $uibModalInstance, dispositions, timeout) {
		var wrapUpTimer;

		$scope.selectedDisposition = _.findWhere(dispositions, { name: 'No Resolution' });
		$scope.dispositions = dispositions;
		
		$scope.wrapUpSecsLeft = timeout;
		$scope.wrapUpStartValue = timeout;
		$scope.wrapUpTimePercentLeft = 100;

        function decrementWrapUpTime() {
            if ($scope.wrapUpSecsLeft) {
                $scope.wrapUpSecsLeft--;
                $scope.wrapUpTimePercentLeft = Math.round(($scope.wrapUpSecsLeft / $scope.wrapUpStartValue) * 100);
                wrapUpTimer = $timeout(decrementWrapUpTime, 1000);
            } else {
                $scope.wrapUp();
            }
        }

        wrapUpTimer = $timeout(decrementWrapUpTime, 1000);

		$scope.wrapUp = function () {
			$uibModalInstance.close($scope.selectedDisposition);
		};
	});