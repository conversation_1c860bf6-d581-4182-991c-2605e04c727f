'use strict';

angular.module('dialerFrontendApp')
    .controller('AgentCallbackCtrl', function ($scope, $uibModalInstance, callback, moment, Callback, CallResult, lead, campaign, client, callresult, stage) {
        $scope.callback = callback;
        $scope.callresult = callresult;
        $scope.client = client;
        $scope.lead = lead;
        $scope.callbackDate = callback.startDateTime || new Date();

        // Default callback window to 0 if it is not set
        let callbackWindowEndDate = new Date().addDays(campaign.callbackWindow ? campaign.callbackWindow : 0);
        let campaignEndDate = moment(campaign.endDate).toDate();
        // Default to campaign `endDate` if stage `endDate` is not defined
        let stageEndDate = stage?.endDate ? moment(stage.endDate).toDate() : campaignEndDate;

        const soonestEndDate = new Date(Math.min(...[callbackWindowEndDate, stageEndDate, campaignEndDate]));

        $scope.dateOptions = {
            minDate: new Date(),
            maxDate: soonestEndDate
        };

        $scope.hours = [];
        for (var i = 0; i < 24; i++) {
            if ((i + '').length == 1) {
                $scope.hours.push('0' + '' + i);
            } else {
                $scope.hours.push('' + i);
            }
        }

        $scope.minutes = [];
        for (var i = 0; i < 60; i += 5) {
            if ((i + '').length == 1) {
                $scope.minutes.push('0' + '' + i);
            } else {
                $scope.minutes.push('' + i);
            }
        }

        if (callback.startDateTime) {
            $scope.startHour = moment.utc(callback.startDateTime).tz(client.timezone).hour().toString().padStart(2, '0')
            $scope.startMinute = moment.utc(callback.startDateTime).tz(client.timezone).minute().toString().padStart(2, '0')
        }
        else {
            $scope.startHour = moment().tz(client.timezone).hour().toString().padStart(2, '0')
            $scope.startMinute = moment().tz(client.timezone).minute().toString().padStart(2, '0')
        }
        
        $scope.calcCallback = function () {
            var startTime = moment($scope.callbackDate).tz(client.timezone)
                .hour($scope.startHour)
                .minute($scope.startMinute)
                .second(0);

            $scope.callbackEndDateTime = startTime.add(2, 'hours').format('HH:mm')
            $scope.localCallbackStartDateTime = moment.utc(callback.startDateTime || moment()).local().format('HH:mm');
            $scope.localCallbackEndDateTime = moment.utc(callback.endDateTime || moment().add(2, 'hours')).local().format('HH:mm');
        };

        $scope.save = function () {
            var startTime = moment($scope.callbackDate).tz(client.timezone)
                .hour($scope.startHour)
                .minute($scope.startMinute)
                .second(0);

            var endTime = moment($scope.callbackDate).tz(client.timezone)
                .hour(parseInt($scope.startHour) + 2)
                .minute($scope.startMinute)
                .second(0);

            callback.startDateTime = startTime.toDate();
            callback.endDateTime = endTime.toDate();
            callback.deleted = false;
            callback.expired = false;
            callback.clientId = campaign.clientId;
            var stage = campaign.initialCampaignStageId;
            if (lead.campaignleads) {
                if (lead.campaignleads.length) {
                    stage = lead.campaignleads[0].currentCampaignStageId
                } else {
                    stage = lead.campaignleads.currentCampaignStageId
                }
            }
            if (!callback.callAttemptJson) {
                callback.callAttemptJson = JSON.stringify({
                    startTime: '00:00:00',
                    endTime: '23:59:59',
                    monday: 1,
                    tuesday: 1,
                    wednesday: 1,
                    thursday: 1,
                    friday: 1,
                    saturday: 1,
                    sunday: 1,
                    isCallback: true,
                    randomSelector: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaaa',
                    campaignstageId: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : campaign.initialCampaignStageId,
                    campaignId: campaign.id,
                    leadId: lead.id
                });
            }

            if (!callresult.id) {
                //create a new callresuslt object
                callresult.wrapup = 'Callback';
                callresult.completed = true;
                callresult.skill = campaign.campaigntype.name == 'Telefunding' ? lead.tfSkill.name : lead.tmSkill.name;
                callresult.subskill = campaign.campaigntype.name == 'Telefunding' ? lead.tfSubSkill.name : lead.tmSubSkill.name;
                callresult.clientId = campaign.clientId;
                callresult.campaignId = campaign.id;
                callresult.callAttemptJson = callback.callAttemptJson;
                callresult.campaignstageId = lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : campaign.initialCampaignStageId;
                callresult.agentId = callback.agentId;
                callresult.leadId = lead.id;
                callresult.$save().then(function (newCallresult) {
                    callback.callresultId = newCallresult.id
                    if (callback.id) {
                        callback.$update().then(function (result) {
                            result.callresult = newCallresult;
                            $uibModalInstance.close(result);
                        })
                    } else {
                        callback.$save().then(function (result) {
                            result.callresult = newCallresult;
                            $uibModalInstance.close(result);
                        });
                    }
                });
            } else {
                //just update the callresult in case the notes changed
                callresult.$update().then(function (newCallresult) {
                    if (callback.id) {
                        callback.$update().then(function (result) {
                            result.callresult = newCallresult;
                            $uibModalInstance.close(result);

                        })
                    } else {
                        callback.$save().then(function (result) {
                            result.callresult = newCallresult;
                            $uibModalInstance.close(result);
                        });
                    }
                });

            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.calcCallback();
    });