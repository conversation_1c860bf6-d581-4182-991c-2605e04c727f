'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('AgentRefusalsCtrl', function ($scope, $rootScope, $uibModal, title, Agent, callresults, Campaign, RefusalReason) {
		$rootScope.pageTitle = title.name + ' | Refusals';
		$scope.totalResults = 0;
		$scope.resultsPerPage = 30;
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.sortType = 'createdAt';
		$scope.refusals = callresults;

		callresults.forEach(function (his) {
			his.isException = (his.wrapup == 'Exception Refusal' ? 'Yes' : 'No');
		})

		$scope.edit = function (item) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/supervisor/editors/supervisor.editors.refusals.html',
				controller: 'EditRefusalModalCtrl',
				size: 'lg',
				resolve: {
					callresult: function () {
						return item;
					},
					campaign: function () {
						return Campaign.get({
							id: item.campaignId
						}).$promise;
					},
					reasons: function () {
						return RefusalReason.query().$promise;
					}
				}
			})

			modalInstance.result.then(function (result) {
				if(result.id) {
					item.wrapup = result.wrapup;
					item.isException = (item.wrapup == 'Exception Refusal'  ? 'Yes' : 'No');
					item.decisionMaker = result.decisionMaker;
					item.refusalReason = result.refusalReason;
				}
			})
		};
	})