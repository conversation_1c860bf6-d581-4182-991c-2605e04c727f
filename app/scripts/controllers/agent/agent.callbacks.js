'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('AgentCallbacksCtrl', function ($scope, $rootScope, callbacks, title, Callback, CallResult, CampaignStage, Client, $uibModal) {
		$rootScope.pageTitle = title.name + ' | Callbacks';
		$scope.sortReverse = true;
		$scope.sortType = 'startDateTime';
		$scope.callbacks = callbacks;

		$scope.edit = function(callback) {
			if (typeof callback.callAttemptJson == 'string') {
				callback.callAttemptJson = JSON.parse(callback.callAttemptJson);
			}
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/agent/editors/agent.editors.callback.html',
				controller: 'AgentCallbackCtrl',
				resolve: {
					callback: function() {
						return Callback.get({
							id: callback.id
						}).$promise;
					},
					callresult: function() {
						return CallResult.get({
							id: callback.callresultId
						}).$promise;
					},
					lead: function() {
						return callback.lead;
					},
					campaign: function() {
						return callback.campaign;
					},
					client: function() {
						return Client.get({
							id: callback.campaign.clientId
						}).$promise;
					},
					stage: function() {
						return CampaignStage.get({
							id: callback.callAttemptJson.campaignstageId
						}).$promise;
					}
				}
			})

			modalInstance.result.then(function(result) {
				if (result.callresult) {
					callback.callresult = result.callresult;
				}
				if (result.id) {
					for (var prop in result) {
						if (result.hasOwnProperty(prop)) {
							callback[prop] = result[prop];
						}
					}
				}
			})
		};
	})