/*global $:false */
/* jshint camelcase: false */
'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('AgentDashboardCtrl', function ($timeout, $interval, $window, APP_SETTINGS, $uibModal, $rootScope, $scope, agentStates, CallRecord, AgentSession, Phone,
        LeadCallAttempts, BroadcastMessage, CallResult, Lead, Agent, Client, RecurringPayment,
        Disposition, CallResultFieldValue, Campaign, CampaignStage, User, _, SweetAlert, Panic, RefusalReason) {
        $rootScope.pageTitle = 'Agent | Dashboard';

        var eventDetachers = [];
        var timerDetachers = [];
        var callPrepTimer = null;

        // $rootScope.waitForTsys(function (err) {
        //     console.log('WAIT FOR ME', err);
        // })

        //these are created so CDR works regardless of order of hangup and wrapup
        var lastLead;
        var lastCallResult;

        User.get({
            id: $rootScope.loggedInUser.id
        }).$promise.then(function (user) {
            if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
                $timeout(function () {
                    $rootScope.changePassword(false);
                });
            }
        })

        $scope.agentStates = agentStates;
        $scope.phone = Phone;
        $rootScope.phone = Phone;
        $scope.timerRunning = false;
        $scope.callPrepTimePercentLeft = 100;
        $scope.callPrepSecondsLeft = 0;
        $scope.newLead = false;
        $scope.pulseHangup = false;
        $scope.badCreditCard = false;
        $scope.inWrapUp = false;
        $scope.notesChanges = false;
        $scope.showRecurring = false;

        $scope.customFields = []

        var users = [];
        User.query().$promise.then(function (results) {
            users = results;
        });

        function saveAgentSession(cb) {
            $scope.agentSession.$save(function (session) {
                //check if session is valid and force a relog if not
                if (!session || session.error) {

                }
                (cb || angular.noop)();
            });
        }

        function decrementCallPrepTime() {
            if ($scope.callPrepSecondsLeft) {
                $scope.callPrepSecondsLeft--;
                $scope.callPrepTimePercentLeft = Math.round(($scope.callPrepSecondsLeft / $scope.callPrepStartValue) * 100);
                callPrepTimer = $timeout(decrementCallPrepTime, 1000);
            } else {
                $scope.onCallPrepCompleted();
            }
        }

        function clearCallPrepTimer() {
            $scope.showCallPrepTimer = false;

            if (callPrepTimer) {
                try {
                    $timeout.cancel(callPrepTimer);
                } catch (e) { } finally {
                    callPrepTimer = null;
                }
            }
        }

        $scope.panicButton = function () {
            $uibModal.open({
                templateUrl: 'views/agent/agent.panicbutton.html',
                controller: 'PanicButtonModalCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    panic: function () {
                        var panicObj = new Panic();
                        panicObj.userId = $rootScope.loggedInUser.id;
                        panicObj.jsonData = {
                            agentSession: $scope.agentSession,
                            inWrapUp: $scope.inWrapUp,
                            onCall: $scope.phone.onCall
                        };
                        panicObj.errors = $rootScope.errors;
                        return panicObj;
                    }
                }
            });
        };

        $scope.startCallPrep = function () {
            $scope.callPrepTimePercentLeft = 100;
            $scope.callPrepSecondsLeft = $scope.callPrepStartValue;
            $scope.showCallPrepTimer = true;
            callPrepTimer = $timeout(decrementCallPrepTime, 1000);
        };

        $scope.onCallPrepCompleted = function (redial) {
            clearCallPrepTimer();
            dialNextAutoDialNumber(redial);
        };

        $scope.flagPreviousNumberAsBad = function () {
            if ($scope.lastDialledNumberType && $scope.lastDialledNumberType.type) {
                try {
                    var tempNumber = $scope.agentSession.currentLead[$scope.lastDialledNumberType.type];
                    if ($scope.agentSession.currentLead.phone_home === tempNumber) $scope.agentSession.currentLead.phone_home = '';
                    if ($scope.agentSession.currentLead.phone_mobile === tempNumber) $scope.agentSession.currentLead.phone_mobile = '';
                    if ($scope.agentSession.currentLead.phone_work === tempNumber) $scope.agentSession.currentLead.phone_work = '';
                    if ($scope.agentSession.currentLead.phone_workmobile === tempNumber) $scope.agentSession.currentLead.phone_workmobile = '';
                } catch (e) {
                    console.log(e)
                }

                Lead.update($scope.agentSession.currentLead, function (result) {
                    saveAgentSession();
                });
                $scope.badNumberButtonDisabled = true;

                //check if all available numbers are set as bad
                //if they are then set wrapup as no resolution
                if (!_.where($scope.diallingOrder, {
                    dialled: false
                }).length) {
                    var disposition = _.findWhere($scope.dispositions, {
                        name: 'No Resolution'
                    });
                    if (disposition) {
                        clearCallPrepTimer();
                        onWrapUpComplete({
                            disposition: disposition
                        });
                    }
                }
            }
        };

        $scope.saveLeadNotes = function () {
            var lead = $scope.agentSession.currentLead;
            Lead.update({ notes: lead.notes, id: lead.id }, function (result) {
                $scope.notesChanges = false;
                saveAgentSession();
            });
        };

        $scope.editLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.agentSession.currentLead.id
                        }).$promise
                    },
                    existingItem: function () {
                        return $scope.agentSession.currentLead
                    },
                    agentId: function () {
                        return $scope.agentSession.agentId
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                _.extend($scope.agentSession.currentLead, lead);

                if (!lead.phone_home && !lead.phone_mobile && !lead.phone_work && !lead.phone_workmobile) {
                    //lead no longer has any valid numbers so call attempts should be scrapped
                    AgentSession.clearLead({
                        id: $scope.agentSession.agentId
                    }).$promise
                        .then(function (session) {
                            $scope.agentSession = session;

                            $scope.agentSession.agentStatus = _.findWhere($scope.agentStates, {
                                name: 'Available'
                            });
                            $scope.agentSession.agentStatusSince = Date.now();

                            saveAgentSession();

                            selectNextLeadToCall();
                        });
                }
            });
        };

        $scope.showTix = function (lead, tix) {
            var tix = 'tix' + tix;
            if (lead[tix + 'Type'] || lead[tix + 'Yr'] || lead[tix + 'Event'] || lead[tix + 'Date'] || lead[tix + 'Cost'] || lead[tix + 'Loc'] || lead[tix + 'Seats'] || lead[tix + 'Addl']) {
                return true;
            }

            return false;
        };

        $scope.hasGiftHistory = function (lead) {
            if (lead.lyAmount || lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount || lead.lastGiftDate || lead.lastGiftAmount || lead.lastGiftReference || lead.lastGiftCC)
                return true;

            if (lead.gift1Amount || lead.gift2Amount || lead.gift3Amount || lead.gift3Amount || lead.gift3Amount)
                return true;

            if (lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount)
                return true;

            return false;
        };

        $scope.hasBuyingHistory = function (lead) {
            if (lead.tix1 || lead.tix2 || lead.tix3 || lead.tix4 || lead.tix5 || lead.tix6 || lead.tix7 || lead.tix8)
                return true;

            if (
                (lead.tix1Type || lead.tix1Event) ||
                (lead.tix2Type || lead.tix2Event) ||
                (lead.tix3Type || lead.tix3Event) ||
                (lead.tix4Type || lead.tix4Event) ||
                (lead.tix5Type || lead.tix5Event) ||
                (lead.tix6Type || lead.tix6Event) ||
                (lead.tix7Type || lead.tix7Event) ||
                (lead.tix8Type || lead.tix8Event) ||
                (lead.tix9Type || lead.tix9Event)
            )
                return true;

            return false;
        };

        $scope.getAskAmount = function (lead) {
            if (!lead) return null;
            var lastAmount = lead.lastGiftAmount;
            var projection = _.findWhere($scope.agentSession.currentCampaignStage.campaign.campaignprojections, {
                subskillId: lead.tfSubSkillId
            });
            if (!projection) return null;
            var defaultAmount = projection.defaultAskAmount;
            var increasePerc = projection.askAmountIncrease;

            if (!defaultAmount && !increasePerc) return null;

            if (!lastAmount) return defaultAmount;

            var increasedAmount = parseInt(lastAmount) + ((increasePerc / 100) * lastAmount);
            if (increasedAmount > defaultAmount) {
                return increasedAmount;
            } else {
                return defaultAmount;
            }
        };

        $scope.rejectAndIdle = function () {
            clearCallPrepTimer();
            $.gritter.removeAll();
            AgentSession.rejectLead({
                id: $scope.agentSession.agentId
            }).$promise
                .then(function (session) {
                    $scope.agentSession = session;
                    $scope.lastCallConnected = null;
                    $scope.leadCallHistory = null;

                    $scope.agentSession.agentStatus = _.findWhere($scope.agentStates, {
                        name: 'Idle'
                    });
                    $scope.agentSession.agentStatusSince = Date.now();

                    saveAgentSession();
                });
        };

        $scope.changeAgentStatus = function (state) {
            if (!$scope.agentSession.agentStatus || $scope.agentSession.agentStatus.id !== state.id) {
                $scope.agentSession.agentStatus = state;
                $scope.agentSession.agentStatusSince = Date.now();

                saveAgentSession();

                if (state.outbound && !$scope.loadingNextCall && !$scope.callInProgress && !$scope.agentSession.currentLead) {
                    selectNextLeadToCall();
                } else if (!state.outbound && $scope.agentSession.currentLead && !$scope.attemptedCallToCurrentLead) {
                    $scope.rejectAndIdle();
                }
            }
        };


        $scope.forceLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/agent/agent.dashboard.override.html',
                controller: 'DashboardLeadOverrideModalCtrl',
                backdrop: 'static'
            });

            modalInstance.result.then(function (leadId) {
                if (leadId) selectNextLeadToCall(leadId);
            });
        };

        $scope.wrapUpCall = function (disposition) {
            if (!$scope.wrapUpComplete) {
                if (disposition) {
                    // Cancel 'call prep' timer for next ringing call (if there's another number)
                    clearCallPrepTimer();
                    $scope.inWrapUp = true;

                    if ((disposition.templateUrl && disposition.controller) &&
                        (disposition.system || (disposition.callresultfields && disposition.callresultfields.length))) {

                        var modalInstance = $uibModal.open({
                            templateUrl: disposition.templateUrl,
                            controller: disposition.controller,
                            backdrop: 'static',
                            openedClass: 'model-open-overflow',
                            size: disposition.size,
                            scope: $scope,
                            resolve: getDispositionObjects(disposition)
                        });

                        modalInstance.result.then(onWrapUpComplete, function () {
                            $scope.inWrapUp = false;
                            if (!$scope.callInProgress) {
                                //they cancelled it so start the wrapup timer again
                                $scope.startCallPrep();
                            }
                        });
                    } else {
                        onWrapUpComplete({
                            disposition: disposition
                        });
                    }
                }
            }
        };

        function getDispositionObjects(disposition) {
            var result = {
                disposition: function () {
                    return disposition;
                },
                lead: function () {
                    if ($scope.agentSession.currentLead) {
                        return Lead.get({
                            id: $scope.agentSession.currentLead.id
                        }).$promise;
                    }
                },
                agentSession: function () {
                    return $scope.agentSession;
                },
                phone: function () {
                    return Phone;
                },
                admin: function () {
                    return false;
                }
            }

            if (disposition.name.indexOf('Sale') > -1) {
                result.products = function () {
                    return Campaign.getProducts({
                        id: $scope.agentSession.currentCampaignStage.campaign.id
                    }).$promise;
                }

                result.client = function () {
                    return Client.get({
                        id: $scope.agentSession.currentCampaignStage.campaign.client.id
                    }).$promise;
                }
            }

            if (disposition.name.indexOf('Pledge') > -1 || disposition.name.indexOf('Recurring Gift') > -1) {
                result.agent = function () { return true };
                result.client = function () {
                    return Client.get({
                        id: $scope.agentSession.currentCampaignStage.campaign.client.id
                    }).$promise;
                }
            }

            if (disposition.name == 'Refusal' || disposition.name == 'Collections Refusal' || disposition.name.indexOf('Invoice') > -1 || disposition.name == 'Write Off' || disposition.name == 'Refusal and Write Off') {
                result.invoices = function () {
                    return Campaign.getLeadInvoices({
                        id: $scope.agentSession.currentCampaignStage.campaign.id,
                        leadid: $scope.agentSession.currentLead.id
                    }).$promise;
                }

                result.reasons = function () {
                    return RefusalReason.query().$promise;
                }
            }

            if (disposition.name.indexOf('Invoice') > -1) {
                result.client = function () {
                    return Client.get({
                        id: $scope.agentSession.currentCampaignStage.campaign.client.id
                    }).$promise;
                }
            }

            if (disposition.name == 'Callback') {
                result.activeDiallingNumber = function () {
                    return $scope.activeDiallingNumber;
                }

                result.client = function () {
                    return Client.get({
                        id: $scope.agentSession.currentCampaignStage.campaign.client.id
                    }).$promise;
                }
            }

            if (disposition.name === 'Recurring Maintenance') {
                result.agent = function () { return true };
                result.recurring = function () {
                    var found = $scope.agentSession.currentLead.paymentlogs.filter(pl => pl.recurringpaymentId)
                    if (found) {
                        return RecurringPayment.get({
                            id: found[found.length - 1].recurringpaymentId
                        }).$promise;
                    } else {
                        return {}
                    }
                }
            }

            return result;
        }

        $scope.getTrainingDocUrl = function (doc) {
            if (doc && doc.link) return doc.link;
            if (!$scope.agentSession.currentCampaignStage || !$scope.agentSession.currentCampaignStage.campaign) return null;
            return 'trainingdocs/' + $scope.agentSession.currentCampaignStage.campaign.id + '/' + doc.path.replace(/^.*[\\\/]/, '');
        };

        $scope.getUser = function (userId) {
            var user = _.findWhere(users, {
                id: userId
            });
            return (user ? user.name : 'Unknown');
        };

        $scope.showGivingHistory = function (lead) {
            if (!lead) return false;

            if (lead.lyAmount || lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount || lead.lastGiftDate || lead.lastGiftAmount || lead.lastGiftReference || lead.lastGiftCC)
                return true;

            if (lead.gift1Amount || lead.gift2Amount || lead.gift3Amount || lead.gift3Amount || lead.gift3Amount)
                return true;

            if (lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount)
                return true;

            return false;
        };

        $scope.showBuyingHistory = function (lead) {
            if (!lead) return false;

            if (lead.tix1 || lead.tix2 || lead.tix3 || lead.tix4 || lead.tix5 || lead.tix6 || lead.tix7)
                return true;

            if (lead.tix1Type || lead.tix2Type || lead.tix3Type || lead.tix4Type || lead.tix5Type || lead.tix6Type || lead.tix7Type || lead.tix8Type || lead.tix9Type)
                return true;

            return false;
        };

        $scope.clientDateTime = function () {
            if (!$scope.agentSession.currentCampaignStage) return '';
            return moment().tz($scope.agentSession.currentCampaignStage.campaign.client.timezone).format('HH:mm');
        };

        $scope.showTix = function (lead, tix) {
            var tix = 'tix' + tix;
            if (lead[tix + 'Type'] || lead[tix + 'Yr'] || lead[tix + 'Event'] || lead[tix + 'Date'] || lead[tix + 'Cost'] || lead[tix + 'Loc'] || lead[tix + 'Seats'] || lead[tix + 'Addl']) {
                return true;
            }

            return false;
        };

        $scope.makeCall = function (number, callerId, dontRecord, type) {
            $scope.activeDiallingNumber = {
                number,
                type
            };
            console.log('makeCall', number, callerId, dontRecord);
            $scope.phone.makeCall(number, callerId, dontRecord);
        };

        function submitCallLog() {
            try {
                var start = moment($scope.lastCallStarted);
                var connected = moment($scope.lastCallConnected);
                var ended = moment($scope.lastCallCompleted);

                var cdr = new CallRecord();

                cdr.startDateTime = $scope.lastCallStarted;
                cdr.connectedDateTime = $scope.lastCallConnected;
                cdr.endDateTime = $scope.lastCallCompleted;
                cdr.connectedDurationSecs = $scope.lastCallConnected ? ended.diff(connected, 'seconds') : 0;
                cdr.ringDurationSecs = $scope.lastCallConnected ? connected.diff(start, 'seconds') : ended.diff(start, 'seconds');
                cdr.totalDurationSecs = ended.diff(start, 'seconds');
                cdr.callTo = $scope.activeDiallingNumber.number;
                cdr.callFrom = $scope.agentSession.agent.device.extension;
                cdr.callerId = $scope.agentSession.currentCampaignStage.callerId || $scope.agentSession.currentCampaignStage.campaign.defaultCallerId;
                cdr.callFromType = 'extension';
                cdr.callToType = $scope.activeDiallingNumber.type;
                cdr.phoneType = $scope.phone.PHONE_TYPE;
                cdr.direction = 'outgoing';

                if ($scope.phone.callUuid) {
                    cdr.callUuid = $scope.phone.callUuid;
                }
                if ($scope.phone.recordingLocation) {
                    cdr.recordingLocation = $scope.phone.recordingLocation;
                    cdr.recordingServer = $scope.phone.credentials.server;
                }

                cdr.leadId = $scope.agentSession.currentLead ? $scope.agentSession.currentLead.id : lastLead.id;
                cdr.callresultId = $scope.agentSession.currentCallResult ? $scope.agentSession.currentCallResult.id : lastCallResult.id;
                cdr.agentId = $scope.agentSession.agent.id;
                cdr.campaignId = $scope.agentSession.currentCampaignStage.campaign.id;
                cdr.campaignstageId = $scope.agentSession.currentCampaignStage.id;

                cdr.$save();
            } catch (e) {
                console.warn('Error submitting CDR');
                console.error(e);
            }
        }

        function onWrapUpComplete(resultObj) {
            $scope.inWrapUp = false;
            if ($scope.agentSession.currentCallResult) {
                $scope.agentSession.inWrapUp = false;
                $scope.wrapUpComplete = true;
                $scope.leadCallHistory = null;

                if (resultObj && resultObj.callResultData) {
                    _.extend($scope.agentSession.currentCallResult, resultObj.callResultData);
                }

                if (resultObj && resultObj.wrapUpNotes) {
                    $scope.agentSession.currentCallResult.notes = resultObj.wrapUpNotes;
                }

                if (resultObj && resultObj.disposition) {
                    // Check if lead should be exhausted

                    var csd = resultObj.disposition.campaignstagedispositions;
                    if (csd.transitionToCampaignStageId) {
                        var nextStageId = null

                        if (csd.transitionCutOffDate) {
                            if (moment(csd.transitionCutOffDate).valueOf() > Date.now()) {
                                nextStageId = csd.transitionToCampaignStageId;
                            } else {
                                nextStageId = csd.transitionCutOffDateDispositionId || null;
                            }
                        } else {
                            nextStageId = csd.transitionToCampaignStageId;
                        }

                        if (nextStageId) {
                            Campaign.moveLeadToCampaignStage({
                                id: $scope.agentSession.currentCampaignStage.campaign.id
                            }, {
                                leadId: $scope.agentSession.currentCallResult.leadId,
                                newCampaignStageId: nextStageId
                            });
                        } else {
                            if (resultObj.disposition.exhaustLead) {
                                Campaign.moveLeadToCampaignStage({
                                    id: $scope.agentSession.currentCampaignStage.campaign.id
                                }, {
                                    leadId: $scope.agentSession.currentCallResult.leadId,
                                    newCampaignStageId: null
                                });
                            }
                        }
                    } else {
                        var updateObj = {
                            leadId: $scope.agentSession.currentCallResult.leadId,
                            newCampaignStageId: null
                        }
                        if (resultObj.disposition.exhaustLead) {
                            Campaign.moveLeadToCampaignStage({
                                id: $scope.agentSession.currentCampaignStage.campaign.id
                            }, updateObj);
                        }
                    }

                    // Save custom call result field values
                    if (resultObj.disposition.callresultfields && resultObj.disposition.callresultfields.length) {
                        saveCallResultFieldValues(resultObj.disposition.callresultfields);
                    }

                    var wrapUpDuration = Date.now() - $scope.lastCallCompleted;
                    var callResult = angular.copy($scope.agentSession.currentCallResult);

                    callResult.wrapupduration = Math.floor(wrapUpDuration / 1000);
                    callResult.wrapup = resultObj.disposition ? resultObj.disposition.name : '';
                    if (callResult.wrapup == 'Refusal') {
                        if ($scope.agentSession.currentCampaignStage.name == 'Collections' || $scope.agentSession.currentCampaignStage.name == 'Bad Credit Cards') {
                            callResult.wrapup = 'Collections Standard Refusal';
                        } else {
                            callResult.wrapup = 'Standard Refusal';
                        }
                    }

                    if (callResult.wrapup == 'Pledge') {
                        callResult.wrapup = (callResult.paymentType == 'Invoice' ? 'Pledge Invoice' : 'Pledge Credit Card');
                    }
                    callResult.agentId = $scope.agentSession.agentId;
                    callResult.campaignId = $scope.agentSession.currentCampaignStage.campaign.id;
                    callResult.clientId = $scope.agentSession.currentCampaignStage.campaign.client.id;

                    if ($scope.agentSession.currentCampaignStage.campaign.campaigntype.name == 'Telefunding') {
                        if ($scope.agentSession.currentLead.tfSkill) {
                            callResult.skill = $scope.agentSession.currentLead.tfSkill.name;
                        }
                        if ($scope.agentSession.currentLead.tfSubSkill) {
                            callResult.subSkill = $scope.agentSession.currentLead.tfSubSkill.name;
                        }
                    } else {
                        if ($scope.agentSession.currentLead.tmSkill) {
                            callResult.skill = $scope.agentSession.currentLead.tmSkill.name;
                        }
                        if ($scope.agentSession.currentLead.tmSubSkill) {
                            callResult.subSkill = $scope.agentSession.currentLead.tmSubSkill.name;
                        }
                    }

                    callResult.completed = true;
                    $scope.agentSession.currentCallResult.completed = true;

                    saveAgentSession();

					var dcu = nowPlusHours(csd.dontContactLeadForHours);

					try {
						if (resultObj.lastDate && moment(dcu) < moment(resultObj.lastDate))
							dcu = resultObj.lastDate;
						}
					catch (err) {}

                    Lead.update({
                        id: $scope.agentSession.currentLead.id
                    }, {
                        dontContactUntil: dcu
                    })
                        .$promise
                        .finally(function () {
                            CallResult.update({
                                id: callResult.id
                            }, callResult).$promise
                                .then(function () {
                                    updateStats();
                                    if ($scope.agentSession.agentStatus.outbound && !$scope.loadingNextCall && !$scope.callInProgress) {
                                        selectNextLeadToCall();
                                    } else {
                                        lastLead = angular.copy($scope.agentSession.currentLead);
                                        lastCallResult = angular.copy($scope.agentSession.currentCallResult);
                                        $scope.agentSession.currentLead = null;
                                        $scope.agentSession.currentCallResult = null;
                                        saveAgentSession();
                                    }
                                });
                        });
                }
            } else {
                //
            }
        }

        function nowPlusHours(hours) {
            var d = new Date()

            if (hours)
                d.setHours(d.getHours() + hours)

            return d;
        }

        function saveCallResultFieldValues(fields) {
            _.each(fields, function (field) {
                var value = '';

                if (field.callresultfieldtype.multipleOptions && field.callresultfieldtype.multipleResults) {
                    var values = [];
                    _.each(field.callresultfieldoptions, function (option) {
                        if (option.selected) {
                            values.push(option.value);
                        }
                    });

                    value = values.join(',');
                } else {
                    value = field.result;
                }

                new CallResultFieldValue({
                    value: value,
                    callresultfieldId: field.id,
                    callresultId: $scope.agentSession.currentCallResult.id
                })
                    .$save();
            });
        }

        function resetTimerDuration() {
            $scope.$broadcast('timer-clear');
        }

        function selectNextLeadToCall(leadId) {
            /*jshint camelcase: false */
            $scope.agentSession.currentCallResult = null;
            $scope.agentSession.currentLead = null;

            var currentCampaign;
            var currentCampaignStage;
            if ($scope.agentSession.currentCampaignStage) {
                currentCampaign = $scope.agentSession.currentCampaignStage.campaign;
                currentCampaignStage = $scope.agentSession.currentCampaignStage;
            }

            saveAgentSession(function () {
                //  TODO: sort out this shit-list of sporadic logic dumped against the scope
                $scope.loadingNextCall = true;
                $scope.leadCallHistory = null;
                $scope.attemptedCallToCurrentLead = false;
                $scope.wrapUpComplete = false;
                $scope.lastDialledNumberType = null;
                $scope.nextNumberTypeToDial = null;
                $scope.activeDiallingNumber = null;
                $scope.diallingOrder = [];
                $scope.newLead = false;

                resetTimerDuration();

                AgentSession.nextCall({
                    id: $rootScope.loggedInUser.agentId,
                    leadId: leadId
                }, function (updatedAgentSession) {
                    try {
                        if (updatedAgentSession && !updatedAgentSession.error) {
                            $scope.notesChanges = false;
                            $scope.agentSession = updatedAgentSession;

                            if (updatedAgentSession.currentCampaignStage) {
                                $scope.dispositions = updatedAgentSession.currentCampaignStage.dispositions;
                                //HACK 
                                //update this list to combine the refusal dispositions and pledge dispositions
                                $scope.dispositions = _.filter($scope.dispositions, function (disposition) {
                                    return disposition.name != 'Exception Refusal' && disposition.name != 'Collections Exception Refusal' && disposition.name != 'Pledge Invoice';
                                });

                                //rename the remaining dispositions to merge them
                                $scope.dispositions.forEach(function (dis) {
                                    if (dis.name == 'Standard Refusal' || dis.name == 'Collections Standard Refusal') {
                                        dis.name = 'Refusal';
                                    }
                                    if (dis.name == 'Pledge Credit Card') {
                                        dis.name = 'Pledge';
                                    }
                                })

                                $scope.showRecurring = false;
                                if ($scope.dispositions.some(d => d.name === 'Recurring Maintenance')) {
                                    // check if there some recurring payments in the payments
                                    if (!$scope.agentSession.currentLead.paymentlogs.some(pl => pl.recurringpaymentId)) {
                                        $scope.dispositions = $scope.dispositions.filter(d => d.name !== 'Recurring Maintenance')
                                    } else {
                                        $scope.showRecurring = true;
                                    }
                                }
                            }

                            $scope.newLead = true;
                            $scope.badCreditCard = false;
                            $scope.customFields = []

                            var lead = updatedAgentSession.currentLead;
                            if (lead && lead.customFields) {
                                try {
                                    if (typeof lead.customFields === 'string') {
                                        lead.customFields = JSON.parse(lead.customFields)
                                    }
                                    var keys = Object.keys(lead.customFields);
                                    if (keys && keys.length) {
                                        _.each(keys, function (key) {
                                            if (key.indexOf('TICKET INFO') === 0 && lead.customFields[key]) {
                                                var infoNo = key.substr(11, 2);
                                                var field = _.findWhere($scope.customFields, { key: infoNo });
                                                if (!field) {
                                                    field = { key: infoNo, ADDL: '', COST: '', DATE: '', EVENT: '', LOC: '', SEATS: '', TYPE: '', YEAR: '' };
                                                    $scope.customFields.push(field);
                                                }
                                                field[key.replace('TICKET INFO' + infoNo + ' ', '')] = lead.customFields[key];
                                            }
                                        })
                                    }
                                } catch (e) {

                                }
                            }

                            //check if this lead has been flagged as bad credit card payment
                            if (updatedAgentSession.currentCampaignStage.name == 'Collections' && updatedAgentSession.currentLead.invoices && updatedAgentSession.currentLead.invoices.length) {
                                var invoices = updatedAgentSession.currentLead.invoices;
                                var invoice = _.last(invoices = _.sortBy(invoices, function (invoice) {
                                    return invoice.createdAt;
                                }));
                                if (invoice.invoiceType == 'Credit Card' && invoice.amountRemaining) {
                                    $scope.badCreditCard = true;
                                }
                            }

                            updateCampaignMaterials();

                            if ($scope.agentSession.currentLead && $scope.agentSession.currentCallResult) {
                                lastLead = angular.copy($scope.agentSession.currentLead);
                                lastCallResult = angular.copy($scope.agentSession.currentCallResult);
                                getCallHistoryForLeadId($scope.agentSession.currentLead.id);

                                $('.gritter-item-wrapper').remove();

                                // if this record is a call back flag as such
                                if ($scope.agentSession.currentCallResult.isCallback) {
                                    $.gritter.add({
                                        title: 'Callback',
                                        text: 'This call is a callback',
                                        time: '',
                                        sticky: true
                                    });
                                }

                                if (!currentCampaign || currentCampaign.id !== updatedAgentSession.currentCampaignStage.campaign.id) {
                                    $.gritter.add({
                                        title: 'Campaign Change',
                                        text: 'Moved to ' + updatedAgentSession.currentCampaignStage.campaign.name + ' campaign',
                                        class_name: 'campaignSwitchClass',
                                        time: 5000
                                    });
                                    $('.campaignSwitchClass').find('.gritter-item').css('background-color', '#D44E2E');
                                }
                                else if (!currentCampaignStage || currentCampaignStage.id !== updatedAgentSession.currentCampaignStage.id) {
                                    $.gritter.add({
                                        title: 'Campaign Stage Change',
                                        text: 'Moved to ' + updatedAgentSession.currentCampaignStage.name + ' campaign stage',
                                        class_name: 'campaignStageSwitchClass',
                                        time: 5000
                                    });
                                    $('.campaignStageSwitchClass').find('.gritter-item').css('background-color', '#f7f69e').css('color', '#000');
                                }

                                if ($scope.autoDialEnabled) {
                                    _.each(APP_SETTINGS.PHONE_ORDER, function (numberType) {
                                        var number = $scope.agentSession.currentLead[numberType];

                                        if (number && !_.findWhere($scope.diallingOrder, {
                                            number: number
                                        })) {
                                            $scope.diallingOrder.push({
                                                type: numberType,
                                                number: number,
                                                dialled: false
                                            });
                                        }
                                    });

                                    if ($scope.agentSession.currentCallResult.isCallback && $scope.agentSession.callback && $scope.agentSession.callback.phone && $scope.diallingOrder.length) {
                                        var callback = $scope.agentSession.callback;
                                        if ($scope.diallingOrder[0].type != callback.phone) {
                                            var index = -1;
                                            $scope.diallingOrder.forEach(function (dial, i) {
                                                if (dial.type == callback.phone) {
                                                    index = i;
                                                }
                                            })
                                            if (index > -1) {
                                                arraymove($scope.diallingOrder, index, 0);
                                            }
                                        }
                                    }

                                    if ($scope.diallingOrder.length) {
                                        continueAutoDial();
                                    } else {
                                        // TODO: lead has no numbers (I know, wtf right!), tell the agent that we pitty the foool
                                    }
                                }
                            } else {
                                // TODO: no lead found, probably should alert the agent of this fact!
                            }
                        } else {
                            if (updatedAgentSession.error) {
                                $scope.lastCallConnected = null;
                                $scope.leadCallHistory = null;

                                $scope.agentSession.agentStatus = _.findWhere($scope.agentStates, {
                                    name: 'Idle'
                                });
                                $scope.agentSession.agentStatusSince = Date.now();

                                saveAgentSession();
                                SweetAlert.swal('Warning', updatedAgentSession.error, 'warning');
                            }
                            // TODO something bad has happened, should probably inform the agent and try and do something about it!
                        }
                    } finally {
                        $scope.loadingNextCall = false;
                    }
                },
                    function (err) {
                        SweetAlert.swal('Warning', err.error, 'warning');
                        $scope.loadingNextCall = false;
                    });
            });
        }

        function continueAutoDial() {
            $scope.badNumberButtonDisabled = false;

            var nextNumber = _.findWhere($scope.diallingOrder, {
                dialled: false
            });

            if (nextNumber) {
                $scope.agentSession.callState = 'Awaiting Dial';
                saveAgentSession();
                $scope.nextNumberTypeToDial = nextNumber;
                $scope.activeDiallingNumber = nextNumber;
                $scope.startCallPrep();
            } else {
                $scope.activeDiallingNumber = null;
                $scope.nextNumberTypeToDial = null;

                if (!$scope.wrapUpComplete) {
                    // Allow them a short time to redial the previous number or flag it as bad
                    $scope.startCallPrep();

                    $scope.agentSession.callState = 'Awaiting Wrap-Up';
                    saveAgentSession();
                } else {
                    // Must be in a non-calling state
                    $scope.agentSession.callState = 'Idle';
                    saveAgentSession();
                }
            }
        }

        function dialNextAutoDialNumber(redial) {
            var numberToDial;

            if (redial) {
                if ($scope.lastDialledNumberType) {
                    $scope.activeDiallingNumber = $scope.lastDialledNumberType;
                    numberToDial = $scope.lastDialledNumberType.number;
                }
            } else {
                if ($scope.nextNumberTypeToDial) {
                    $scope.activeDiallingNumber = $scope.nextNumberTypeToDial;
                    numberToDial = $scope.nextNumberTypeToDial.number;
                    $scope.lastDialledNumberType = $scope.nextNumberTypeToDial;
                    $scope.nextNumberTypeToDial.dialled = true;
                } else {
                    // final warning shot, they've had time to flag last number as bad, redial or wrap up, now time to kick ass
                    forceWrapUp();
                }
            }

            if (numberToDial && $scope.agentSession.currentCampaignStage) {
                $scope.lastCallConnected = null;
                $scope.phone.makeCall(numberToDial,
                    ($scope.agentSession.currentCampaignStage.callerId || $scope.agentSession.currentCampaignStage.campaign.defaultCallerId)
                    , $scope.agentSession.currentCampaignStage.campaign.dontRecord);
            }
        }

        function forceWrapUp() {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/agent/agent.dashboard.forcewrapup.modal.html',
                controller: 'AgentDashboardForceWrapUpModalCtrl',
                backdrop: 'static',
                keyboard: false,
                size: 'sm',
                resolve: {
                    dispositions: function () {
                        return $scope.dispositions;
                    },
                    timeout: function () {
                        return 30;
                    }
                }
            });

            modalInstance.result.then(function (disposition) {
                $scope.wrapUpCall(disposition);
            });
        }

        function getCallHistoryForLeadId(leadId) {
            Lead.getCallHistory({
                id: leadId
            }).$promise
                .then(function (results) {
                    $scope.leadCallHistory = [];

                    var tyAmount = parseInt(($scope?.agentSession?.currentLead?.tyAmount || '').replace('$','').replace(',',''))
                    var calcTyAmount = false

                    if (!tyAmount) {
                        tyAmount = 0
                        calcTyAmount = true
                    }

                    results.forEach(function (result) {
                        switch (result.wrapup) {
                            case 'Standard Refusal':
                            case 'Exception Refusal':
                                result.notes = 'Refusal Reason: ' + result.refusalReason + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                                break;
                            case 'Pledge V2':
                            case 'Pledge V3':
                            case 'Pledge Credit Card':
                            case 'Pledge Invoice':
                                result.notes = 'Pledge Amount: $' + result.giftAmount + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                                if (result.installmentHistory) {
                                    // show the installments
                                    result.notes += '\nInstallments: ' + result.installmentHistory
                                    if (result.installmentError) result.notes += '\nInstallment Error: ' + result.installmentError
                                } else if (result.installmentNotes) {
                                    result.notes += '\nInstallments: ' + result.installmentNotes
                                }
                                break;
                            case 'Recurring Payment':
                            case 'Recurring Gift':
                                var notes = result.notes;
                                result.notes = "";
                                if (result.recurringpayments && result.recurringpayments.length > 0) {
                                    result.notes = 'Recurring Amount: $:' + result.recurringpayments[0].amount + '\nInstallment Every: ' + result.recurringpayments[0].unit + '\nFirst Payment: ' + moment(result.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                                }
                                result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');

                                break;
                            case 'Recurring Maintenance':
                                var notes = result.notes;
                                result.notes = "";
                                var recurring = results.slice().reverse().find(r => r.recurringpayments && r.recurringpayments.length)
                                if (recurring) {
                                    result.notes = 'Recurring Amount: $:' + recurring.recurringpayments[0].amount + '\nInstallment Every: ' + recurring.recurringpayments[0].unit + '\nFirst Payment: ' + moment(recurring.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                                }

                                result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');
                                break;
                        }

                        try {
                            if (calcTyAmount && result.campaignId === $scope.agentSession.currentCampaignStage.campaign.id)
                                tyAmount += (result.giftAmount || 0)
                        }
                        catch (err) {
                            console.error(err)
                        }

                        $scope.leadCallHistory.push(result);
                    });
                    // results = _.filter(results, function (result) {
                    //     return result.agent;
                    // });
                    // $scope.leadCallHistory = results;

                    if ($scope?.agentSession?.currentLead)
						$scope.agentSession.currentLead.tyAmount = '$' + tyAmount
                });
        }

        $scope.pagination = {
            agent: {
                current: 1
            },
            campaign: {
                current: 1
            },
            skill: {
                current: 1
            }
        };

        $scope.messages = {
            campaignMessages: [],
            skillMessages: [],
            agentMessages: []
        };

        $scope.newMessages = {
            campaign: false,
            skill: false,
            agent: false
        };

        function updateCampaignMaterials() {
            if ($scope.agentSession.currentCampaignStage &&
                $scope.agentSession.currentCampaignStage.campaign) {
                var agentObj = _.findWhere($scope.agentSession.currentCampaignStage.agents, {
                    id: $scope.agentSession.agentId
                })
                Agent.getMyMessages({
                    id: $scope.agentSession.agentId,
                    campaignId: $scope.agentSession.currentCampaignStage.campaign.id,
                    stageId: $scope.agentSession.currentCampaignStage ? $scope.agentSession.currentCampaignStage.id : null,
                    skills: agentObj ? JSON.parse(agentObj.campaignstageagents.agentskills) : []
                }).$promise
                    .then(function (messages) {
                        if (messages) {
                            if (messages.campaignMessages && messages.campaignMessages.length) {
                                if ($scope.messages.campaignMessages && $scope.messages.campaignMessages.length) {
                                    if ($scope.messages.campaignMessages[0].id != messages.campaignMessages[0].id) {
                                        $scope.newMessages.campaign = true;
                                        $scope.pagination.campaign.current = 1;
                                    }
                                } else {
                                    $scope.newMessages.campaign = true;
                                    $scope.pagination.campaign.current = 1;
                                }
                            }

                            if (messages.skillMessages && messages.skillMessages.length) {
                                if ($scope.messages.skillMessages && $scope.messages.skillMessages.length) {
                                    if ($scope.messages.skillMessages[0].id != messages.skillMessages[0].id) {
                                        $scope.newMessages.skill = true;
                                        $scope.pagination.skill.current = 1;
                                    }
                                } else {
                                    $scope.newMessages.skill = true;
                                    $scope.pagination.skill.current = 1;
                                }
                            }

                            if (messages.agentMessages && messages.agentMessages.length) {
                                if ($scope.messages.agentMessages && $scope.messages.agentMessages.length) {
                                    if ($scope.messages.agentMessages[0].id != messages.agentMessages[0].id) {
                                        $scope.newMessages.agent = true;
                                        $scope.pagination.agent.current = 1;
                                    }
                                } else {
                                    $scope.newMessages.agent = true;
                                    $scope.pagination.agent.current = 1;
                                }
                            }
                        }
                        $scope.messages = messages;
                    });

                Campaign.getDocs({
                    id: $scope.agentSession.currentCampaignStage.campaign.id
                }).$promise
                    .then(function (trainingDocs) {
                        trainingDocs.forEach(function (doc) {
                            doc.name = doc.name && !doc.link ? doc.name.substr(0, doc.name.lastIndexOf('.')) : doc.name;
                        })
                        $scope.campaignTrainingDocs = trainingDocs.reverse();
                    });
            }
        }

        function updateStats() {
            Agent.getCallCountToday({
                id: $scope.agentSession.agentId
            }).$promise
                .then(function (stat) {
                    $scope.callCountToday = stat.result;
                });

            Agent.getTotalGiftAmountToday({
                id: $scope.agentSession.agentId
            }).$promise
                .then(function (stat) {
                    if (!stat || !stat.result) {
                        $scope.totalGiftAmountToday = 0;
                    } else {
                        $scope.totalGiftAmountToday = stat.result;
                    }
                });

            Agent.getAvgGiftPerCallToday({
                id: $scope.agentSession.agentId
            }).$promise
                .then(function (stat) {
                    if (!stat || !stat.result) {
                        $scope.avgGiftPerCallToday = 0;
                    } else {
                        $scope.avgGiftPerCallToday = stat.result;
                    }
                });

            Agent.getAvgWrapUpDurationToday({
                id: $scope.agentSession.agentId
            }).$promise
                .then(function (stat) {
                    if (!stat || !stat.result) {
                        $scope.avgWrapUpDurationToday = '00:00';
                    } else {
                        $scope.avgWrapUpDurationToday = $scope.humanizeSeconds(stat.result);
                    }
                });
        }

        function onCallStatusChange(e) {
            $scope.agentSession.callState = e.label;
            saveAgentSession();
        }

        function onCallStarted() {
            // $scope.lastCallConnected = null;
            $scope.callInProgress = true;
            $scope.attemptedCallToCurrentLead = true;
            $scope.lastCallStarted = Date.now();
        }

        function onCallEnded() {
            if ($scope.callInProgress) {
                stopDurationTimer();

                $scope.pulseHangup = false;

                $scope.lastCallCompleted = Date.now();

                submitCallLog();

                $scope.callInProgress = false;

                if ($scope.wrapUpComplete) {
                    $scope.agentSession.currentLead = null;

                    if ($scope.agentSession.agentStatus.outbound) {
                        selectNextLeadToCall();
                    }
                } else if ($scope.inWrapUp) {
                    //this would happen if the other side hungup whilst the user was in wrapup
                    $scope.agentSession.callState = 'Awaiting Wrapup';
                } else {
                    if ($scope.autoDialEnabled) {
                        continueAutoDial();
                    } else {
                        $scope.agentSession.callState = 'Awaiting Dial';
                        saveAgentSession();
                    }
                }
            }
        }

        function onCallConnected() {
            $scope.lastCallConnected = Date.now();
        }

        function onRinging() {
            startDurationTimer();
        }

        function startDurationTimer() {
            $scope.$broadcast('timer-start');
            $scope.timerRunning = true;
        }

        function stopDurationTimer() {
            $scope.$broadcast('timer-stop');
            $scope.timerRunning = false;
        }

        eventDetachers.push(
            $scope.$on('$stateChangeStart', function (event) {
                if (!Phone.onCall) {
                    return;
                }

                if ($rootScope.forcedExit) {
                    return;
                }

                var answer = confirm('Leaving this page will end the call in progress!\n\nAre you sure you want to leave?')

                if (!answer) {
                    event.preventDefault();
                }
            })
        );

        $scope.$on('$destroy', function () {
            window.onbeforeunload = undefined;

            for (var i = 0; i < eventDetachers.length; i++) {
                if (eventDetachers[i]) {
                    eventDetachers[i]();
                }
            }

            for (var i = 0; i < timerDetachers.length; i++) {
                if (timerDetachers[i]) {
                    $interval.cancel(timerDetachers[i]);
                }
            }

            Phone.deinit();
        });

        $scope.agentSession = new AgentSession();
        $scope.agentSession.agentId = $rootScope.loggedInUser.agentId;
        $scope.agentSession.userId = $rootScope.loggedInUser.id;
        $scope.agentSession.id = $rootScope.loggedInUser.agentId;

        saveAgentSession(function () {
            $scope.autoDialEnabled = $scope.agentSession.agent.autoDial;
            $scope.callPrepStartValue = $scope.agentSession.agent.callPrepTime || 10;

            if ($scope.agentSession.currentCampaignStage) {
                $scope.dispositions = $scope.agentSession.currentCampaignStage.dispositions;
                //update this list to combine the refusal dispositions
                $scope.dispositions = _.filter($scope.dispositions, function (disposition) {
                    return disposition.name != 'Exception Refusal' && disposition.name !== 'Collections Exception Refusal';
                });

                $scope.dispositions.forEach(function (dis) {
                    if (dis.name == 'Standard Refusal' || dis.name == 'Collections Standard Refusal') {
                        dis.name = 'Refusal';
                    }
                })
            }

            if (!$scope.agentSession.agentStatus) {
                var state = _.findWhere($scope.agentStates, {
                    id: $scope.agentSession.agent.defaultAgentStateId
                }) || $scope.agentStates[0];
                $scope.changeAgentStatus(state);
            }

            // Initate phone
            $scope.phone.init({
                server: $rootScope.loggedInUser.agent.device.server,
                extension: $rootScope.loggedInUser.agent.device.extension,
                password: $rootScope.loggedInUser.agent.device.password,
                display: $rootScope.loggedInUser.agent.device.name
            });
        });

        // TODO: Not a huge fan of this, there must be a better way... to be reviewed!
        $rootScope.preLogoutMethod = function (cb) {
            $scope.agentSession.id = $scope.agentSession.agentId;
            return $scope.agentSession.$delete(cb || angular.noop);
        };

        window.onbeforeunload = function (event) {
            if (!Phone.onCall) {
                return;
            }

            var message = 'Leaving this page will end the call in progress!';

            if (!$rootScope.forcedExit) {
                if (typeof event == 'undefined') {
                    event = window.event;
                }

                if (event) {
                    event.returnValue = message;
                }

                return message;
            }
        };

        timerDetachers.push(
            $interval(function () {
                AgentSession.keepAlive({
                    id: $scope.agentSession.agentId
                }).$promise.then(function (result) {
                    if (result.session) {
                        if (result.session.timeoutIn && result.session.timeoutIn < 120) {
                            $.gritter.add({
                                title: 'Timeout',
                                text: 'You will be timed out in ' + result.session.timeoutIn + ' seconds',
                                time: 2500,
                                sticky: false
                            });
                        }
                        if (!$scope.agentSession.agentStatus || (result.session.agentStatus && result.session.agentStatus.name !== $scope.agentSession.agentStatus.name))
                            $scope.agentSession.agentStatus = result.session.agentStatus;
                    }
                });
            }, 5000)
        );

        timerDetachers.push(
            $interval(function () {
                if ($scope.phone.onCall && $scope.wrapUpComplete) {
                    $scope.pulseHangup = !$scope.pulseHangup
                } else {
                    $scope.pulseHangup = false;
                }
            }, 2000)
        );

        timerDetachers.push($interval(updateCampaignMaterials, 15000));

        updateStats();

        $scope.phone.on($scope, 'callstarted', onCallStarted);
        $scope.phone.on($scope, 'callconnected', onCallConnected);
        $scope.phone.on($scope, 'callended', onCallEnded);
        // $scope.phone.on($scope, 'unregistered', onCallEnded); - TODO: Not sure if this is a good idea or not - possibly handle differently?
        $scope.phone.on($scope, 'callringing', onRinging);
        $scope.phone.on($scope, 'callstatuschange', onCallStatusChange);

        function arraymove(arr, fromIndex, toIndex) {
            var element = arr[fromIndex];
            arr.splice(fromIndex, 1);
            arr.splice(toIndex, 0, element);
        }
    });