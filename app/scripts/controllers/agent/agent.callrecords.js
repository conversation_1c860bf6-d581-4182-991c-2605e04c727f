'use strict';

angular.module('dialerFrontendApp')
	.controller('AgentCallrecordsCtrl', function ($scope, $rootScope, CallRecord, moment, _, filters, title) {
		$rootScope.pageTitle = title.name  + ' | Call Records';
		$scope.sortType = 'createdAt'
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.callRecords = [];
		$scope.totalCalls = 0;
		$scope.perPage = 20;

		$scope.pageChanged = function (newPageNumber) {
			getResultsPage(newPageNumber);
		};

		$scope.filterChanged = function (filter) {
			$scope.sortType = filter;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.pagination.current = 1;
			getResultsPage(1);
		};

		function getResultsPage(pageNumber) {
			CallRecord.query({
				page: pageNumber - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: filters,
				limit: $scope.perPage
			}).$promise.then(function (result) {
				$scope.callRecords = result.data.callRecords;
				$scope.totalCalls = result.data.count;
			})
		}

		getResultsPage(1);
	})