'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('AgentPledgesCtrl', function ($scope, $rootScope, $uibModal, Agent, callresults, title, CallResult, SweetAlert) {
		$rootScope.pageTitle = title.name + ' | Pledges';
		$scope.totalResults = 0;
		$scope.resultsPerPage = 30;
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.sortType = 'createdAt';
		$scope.pledges = callresults;

		$scope.edit = function (item) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/supervisor/editors/supervisor.editors.pledges.html',
				controller: 'EditPledgeModalCtrl',
				size: 'lg',
				resolve: {
					callresult: function () {
						return item;
					}
				}
			})

			modalInstance.result.then(function (result) {
				if (result.id) {
					for (var prop in result) {
						if (result.hasOwnProperty(prop)) {
							item[prop] = result[prop];
						}
					}
				}
			})
		};

		$scope.delete = function (item) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/agent/agent.item.delete.html',
				controller: 'ItemDeleteModalCtrl',
				size: 'sm',
				resolve: {
					item: function () {
						return item;
					}
				}
			})

			modalInstance.result.then(function (result) {
				if (result) {
					CallResult.removePledge({
						id: item.id
					}).$promise.then(function () {
						SweetAlert.swal('Deletion Complete, you will need to manually move this lead to the correct stage');
						$scope.pledges = $scope.pledges.filter(function (a) {
							return a.id && a.id !== item.id;
						});
					});
				}
			})
		};
	})