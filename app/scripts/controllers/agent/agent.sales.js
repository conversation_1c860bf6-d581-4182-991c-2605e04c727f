'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('AgentSalesCtrl', function ($scope, $rootScope, $uibModal, Agent, Campaign, Client, callresults, title, CallResult, SweetAlert) {
		$rootScope.pageTitle = title.name + ' | Sales';
		$scope.totalResults = 0;
		$scope.resultsPerPage = 30;
		$scope.sortReverse = true;
		$scope.pagination = {
			current: 1
		};
		$scope.sortType = 'createdAt';
		$scope.callresults = callresults;

		$scope.edit = function (item) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/supervisor/editors/supervisor.editors.sales.html',
				controller: 'EditSaleModalCtrl',
				size: 'lg',
				resolve: {
					callresult: function () {
						return item;
					},
					client: function () {
						return Client.get({
							id: item.clientId
						}).$promise;
					},
					products: function () {
						return Campaign.getProducts({
							id: item.campaignId
						}).$promise;
					}
				}
			})

			modalInstance.result.then(function (result) {
				if (result.id) {
					for (var prop in result) {
						if (result.hasOwnProperty(prop)) {
							item[prop] = result[prop];
						}
					}
				}
			})
		};

		$scope.delete = function (item) {
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/agent/agent.item.delete.html',
				controller: 'ItemDeleteModalCtrl',
				size: 'sm',
				resolve: {
					item: function () {
						return item;
					}
				}
			})

			modalInstance.result.then(function (result) {
				if (result) {
					CallResult.removeSale({
						id: item.id
					}).$promise.then(function () {
						SweetAlert.swal('Deletion Complete, you will need to manually move this lead to the correct stage');
						$scope.callresults = $scope.callresults.filter(function (a) {
							return a.id && a.id !== item.id;
						});
					});
				}
			})
		};

	})