'use strict';

angular.module('dialerFrontendApp')
    .controller('CollectionRefusalDispositionModalCtrl', function ($scope, $uibModalInstance, $uibModal, disposition, agentSession, lead, invoices, Invoice, SweetAlert, _) {

        $scope.notes = '';

        $scope.verify = {
            contactDetails: false
        };

        if (invoices.length) {
            $scope.invoice = invoices[invoices.length - 1];
            var count = invoices.length;
            while ($scope.invoice.amountRemaining == 0 && count) {
                count--;
                $scope.invoice = invoices[count];
            }
        } else {
            $scope.invoice = {};
        }

        $scope.refusalReasons = [];
        $scope.lead = lead;
        $scope.formData = {};
        var standardRefusals = [
            'Cannot pay',
            'Did not agree',
            'Changed mind',
            'Hang up'
        ];
        var exceptionRefusals = [
            'Already paid',
            'Deceased - all parties on record',
            'Do not call again',
        ];

        $scope.refusalReasons = standardRefusals.concat(exceptionRefusals)

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function (Lead) {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.ok = function () {
            var verificationResult = performVerification();

            var disposition = {};
            if (standardRefusals.indexOf($scope.refusalReason) > -1) {
                //standard refusal
                disposition = angular.copy(_.findWhere(agentSession.currentCampaignStage.dispositions, {
                    name: 'Refusal'
                }));
                if (!disposition) {
                    disposition = angular.copy(_.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Collections Refusal'
                    }));
                }
                if (!disposition) {
                    disposition = angular.copy(_.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Standard Refusal'
                    }));
                }
                disposition.name = 'Standard Refusal';
            } else {
                //exception refusal
                disposition = angular.copy(_.findWhere(agentSession.currentCampaignStage.dispositions, {
                    name: 'Collections Exception Refusal'
                }));
                disposition.name = 'Exception Refusal';
            }

            //writeoff remaining balance
            Invoice.writeOff({
                id: $scope.invoice.id
            }, {
                amount: null
            }).$promise
            .finally(_ => {
                if (verificationResult === true) {
                    var result = {
                        refusalReason: $scope.refusalReason,
                        decisionMaker: $scope.formData.decisionMaker
                    };

                    $uibModalInstance.close({
                        disposition: disposition,
                        wrapUpNotes: $scope.notes,
                        callResultData: result
                    });
                } else {
                    var verificationMsg = verificationResult.join('\n - ');
                    SweetAlert.swal({
                        title: "Invalid",
                        text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                    });
                }
            });
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        function performVerification() {
            var errors = [];

            if (!$scope.formData.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (!$scope.refusalReason) {
                errors.push('Please select a refusal reason.');
            }

            return (errors.length ? errors : true);
        }
    });