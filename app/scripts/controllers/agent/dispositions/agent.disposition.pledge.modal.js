'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('PledgeDispositionModalCtrl', function ($scope, $uibModal, $uibModalInstance, disposition, moment, Lead, client, lead, agentSession, admin, _, SweetAlert, Invoice, PaymentLog, phone) {
        // Set form options and defaults
        $scope.verify = {};
        $scope.formData = {
            freeTickets: 0,
            payDate: new Date(),
            paymentType: 'Credit Card',
            declineBenefits: false
        };

        $scope.notes = '';
        $scope.lead = lead;
        $scope.client = client;
        $scope.cards = [];
        $scope.phone = phone;
        $scope.client = client;
        $scope.admin = admin;

        if ($scope.client.additionalInfo) {
            var cardInfo = JSON.parse($scope.client.additionalInfo);
            for (var key in cardInfo) {
                if (cardInfo[key])
                    $scope.cards.push(key);
            }
        }

        $scope.changePaymentType = function () {
            if ($scope.formData.paymentType == 'Credit Card') {
                $scope.formData.payDate = new Date();
            } else {
                $scope.formData.payDate = new Date().addDays(30);
            }
        }

        $scope.ok = function () {
            var verificationResult = performVerification();

            if (verificationResult === true) {
                var fd = $scope.formData;

                if (fd.paymentType == 'Credit Card') {
                    if (fd.numberOfInstallments) {
                        fd.payDate = new Date();
                    }

                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Pledge'
                    });

                } else {
                    fd.numberOfInstallments = null;
                    fd.installmentNotes = null;
                    fd.creditCardNumber = null;
                    fd.creditCardType = null;
                    fd.creditCardDate = null;

                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Pledge Invoice'
                    });
                }


                if (client.defaultInvoiceType == 'email' && $scope.lead.email && !$scope.verify.sendByMail) {
                    $scope.formData.paymentMethod = 'Email';
                } else {
                    $scope.formData.paymentMethod = 'Paper';
                }

                var result = {};
                result.giftAmount = fd.giftAmount;
                result.freeTickets = fd.freeTickets;
                result.paymentType = fd.paymentType;
                result.creditCardType = fd.creditCardType;
                result.creditCardNumber = fd.creditCardNumber;
                result.creditCardExpDate = fd.creditCardDate;
                result.installmentNotes = fd.installmentNotes;
                result.requiresFollowUp = fd.requiresFollowUp;
                result.payDate = fd.payDate;
                result.decisionMaker = fd.decisionMaker;
                result.giftMatchingCompany = fd.giftMatchingCompany;
                result.declineBenefits = fd.declineBenefits;
                result.invoiceMethod = fd.paymentMethod;
                result.grandTotal = fd.giftAmount;
                result.numberOfInstallments = fd.numberOfInstallments;
                result.newMembershipCard = fd.newMembershipCard;
                result.useExistingCreditCard = fd.useExistingCC;
                if (result.useExistingCreditCard && result.paymentType == 'Credit Card') {
                    if (!result.creditCardDate) result.creditCardDate = lead.existingCCExp;
                    result.creditCardType = lead.existingCCType;
                    result.creditCardNumber = lead.existingCCDigits;
                }

                var invoice = new Invoice();
                invoice.callresultId = agentSession.currentCallResult.id;
                invoice.grandTotal = result.grandTotal;
                invoice.amountRemaining = result.paymentType != 'Credit Card' ? result.grandTotal : 0;
                invoice.deliveryMethod = fd.paymentMethod;
                invoice.dueDate = fd.payDate;
                invoice.requestCount = 0;
                invoice.invoiceType = result.paymentType;
                invoice.clientId = agentSession.currentCampaignStage.campaign.client.id;
                invoice.campaignId = agentSession.currentCampaignStage.campaign.id;
                invoice.leadId = lead.id;
                invoice.sendInvoice = result.paymentType != 'Credit Card';

                invoice.$save().then(function (inv) {
                    if (result.paymentType == 'Credit Card') {
                        // create the payment log now
                        var payment = new PaymentLog();
                        payment.invoiceId = inv.id;
                        payment.amount = result.grandTotal;
                        payment.paymentDate = new Date();
                        payment.isPaid = true;
                        payment.actualPaymentDate = new Date();
                        payment.status = 'paid';
                        payment.deleted = false;
                        payment.disabled = false;
                        payment.leadId = lead.id;
                        payment.userId = agentSession.userId;
                        payment.clientId = agentSession.currentCampaignStage.campaign.client.id;
                        payment.campaignId = agentSession.currentCampaignStage.campaign.id;
                        payment.callresultId = agentSession.currentCallResult.id;
                        payment.source = 'pledge'
                        payment.$save();
                    }
                });

                Lead.update({
                    id: lead.id
                }, {
                    agentPortfolioTag: agentSession.agent.name
                })
                .$promise
                .finally(function () {
                    $uibModalInstance.close({
                        disposition: disposition,
                        callResultData: result,
                        wrapUpNotes: $scope.notes
                    });
                });
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.minDate = new Date();
        $scope.maxDate = new Date().addDays(90);
        $scope.invoiceMaxDate = new Date().addDays(30);

        $scope.format = 'dd-MMMM-yyyy';

        $scope.popup1 = {
            opened: false
        };

        $scope.openDatePicker = function () {
            $scope.popup1.opened = true;
        };

        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            if (!fd.giftAmount || (fd.giftAmount !== fd.verifyGiftAmount)) {
                errors.push('Gift amount must be verified and match.');
            }

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (fd.paymentType === 'Credit Card') {
                if (!fd.useExistingCC) {
                    if (!fd.creditCardType || !fd.creditCardNumber || !fd.creditCardDate) {
                        errors.push('Please complete all credit card information.');
                    }

                    if (fd.creditCardNumber?.length !== 4) {
                        errors.push('Invalid last 4 CC digits.');
                    }

                    if (!$scope.validateCreditCardDate(fd.creditCardDate)) {
                        errors.push('Invalid credit card expiry date');
                    }
                } else {
                    if (!fd.creditCardDate) {
                        errors.push('Please complete all credit card information.');
                    }

                    if (!$scope.validateCreditCardDate(fd.creditCardDate)) {
                        errors.push('Invalid credit card expiry date');
                    }
                }
            }

            if (!$scope.verify.giftAmount) {
                errors.push('Please confirm you have verified the gift amount.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the donor details.');
            }

            if (!$scope.verify.emailAddress) {
                errors.push('Please confirm you have asked the donor for their email address.');
            }

            if (!$scope.verify.invoiceDelivery && fd.paymentMethod === 'Invoice' && $scope.lead.email && !$scope.verify.sendByMail) {
                errors.push('Please confirm you have informed the donor about the delivery method for the invoice (email / postal).');
            }

            return (errors.length ? errors : true);
        }
    });