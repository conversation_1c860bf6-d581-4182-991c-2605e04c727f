'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('SaleDispositionModalCtrl', function ($scope, $uibModal, $uibModalInstance, disposition, moment, Lead, Sale, Invoice, client, products, lead, agentSession, _, SweetAlert, phone, PaymentLog) {
        var callResult = agentSession.currentCallResult;
        if (!callResult || !callResult.id) {
            SweetAlert.swal({
                title: "Error",
                text: 'Something went wrong, please close this Sale and try again'
            });
        }
        var result = {};
        $scope.phone = phone;

        // Set form options and defaults
        var paymentMonths = getPaymentMonths();
        $scope.paymentMonths = paymentMonths.options;
        $scope.verify = {};
        $scope.formData = {
            freeTickets: 0,
            paymentMonth: paymentMonths.selected,
            declineBenefits: false
        };

        $scope.notes = '';
        $scope.products = _.sortBy(products, 'order_by');
        $scope.orderItems = [new Sale()];
        $scope.cards = [];
        $scope.lead = lead;
        $scope.client = client;

        if ($scope.client.additionalInfo) {
            var cardInfo = JSON.parse($scope.client.additionalInfo);
            for (var key in cardInfo) {
                if (cardInfo[key])
                    $scope.cards.push(key);
            };
        } else {
            $scope.cards = [];
        }

        var getProduct = function (order) {
            return _.findWhere($scope.products, {
                series: order.series,
                seats: order.seats,
                days: order.dayOfWeek
            });
        };

        $scope.addNewOrder = function () {
            $scope.orderItems.push(new Sale());
        };

        $scope.updateCost = function (order) {
            var product = getProduct(order);
            if (product) {
                order.costEach = product.price;
                order.feePerTicket = product.feePerTicket;
            }
            $scope.updateTotal(order);
        };

        $scope.checkvalues = function (order) {
            order.possibleDays = _.where($scope.products, {
                series: order.series,
                seats: order.seats
            });
            
            $scope.updateCost(order)
        };

        $scope.getVenue = function (order) {
            var product = getProduct(order);
            if (product) {
                return product.venue;
            }
        };

        $scope.updateTotal = function (order) {
            var product = getProduct(order);
            if (product) {
                order.productCode = product.productCode;
                order.tix_sub = product.tix_sub;
                if (order.seatCount > 0) {
                    order.subtotal = (order.costEach + order.feePerTicket) * order.seatCount;
                } else {
                    order.subtotal = 0;
                }
            } else {
                order.subtotal = 0;
            }
            order.salesTax = client.salesTax;

            $scope.updateGrandTotal();
        };

        $scope.removeOrder = function (order) {
            var index = $scope.orderItems.indexOf(order);
            if (index > -1) {
                $scope.orderItems.splice(index, 1);
            }
            $scope.updateGrandTotal();
        };

        $scope.updateGrandTotal = function () {
            $scope.subtotal = 0;

            _.each($scope.orderItems, function (order) {
                if (order.subtotal !== undefined)
                    $scope.subtotal += order.subtotal;
            })

            if ($scope.subtotal === 0) {
                var subWithTax = 0;
            } else {
                var subWithTax = $scope.subtotal + (($scope.client.salesTax / 100) * $scope.subtotal);
            }

            if ($scope.addOnGiftEnabled) {
                $scope.giftAmountTotal = $scope.giftAmount;

                if (!$scope.giftAmount && $scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                } else if ($scope.giftAmount && !$scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                } else {
                    $scope.grandTotal = subWithTax + $scope.giftAmount;
                }
            } else {
                $scope.giftAmountTotal = 0;

                if ($scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                }
            }

            $scope.grandTotal += $scope.client.orderFee;
            if ($scope.grandTotal)
                $scope.grandTotal = $scope.grandTotal.toFixed(2);
        };



        $scope.ok = function () {
            var verificationResult = performVerification();

            if (verificationResult === true) {
                var fd = $scope.formData;

                if ($scope.addOnGiftEnabled) {
                    result.giftAmount = $scope.giftAmount;
                    result.giftMatchingCompany = fd.giftMatchingCompany;
                }
                result.freeTickets = $scope.freeTix;
                result.paymentType = 'Credit Card';
                result.creditCardType = fd.creditCardType;
                result.creditCardNumber = fd.creditCardNumber;
                result.creditCardExpDate = fd.creditCardDate;
                result.decisionMaker = fd.decisionMaker;
                result.declineBenefits = $scope.declineBenfits;
                result.saleAmount = $scope.subtotal;
                result.grandTotal = $scope.grandTotal;
                result.newMembershipCard = fd.newMembershipCard;
                result.useExistingCreditCard = fd.useExistingCC;
                result.installmentNotes = fd.installmentNotes;
                result.numberOfInstallments = fd.numberOfInstallments;
                if (result.useExistingCreditCard) {
                    result.creditCardDate = lead.existingCCExp;
                    result.creditCardType = lead.existingCCType;
                    result.creditCardNumber = lead.existingCCDigits;
                }

                //save order to sales table
                for (var i = 0; i < $scope.orderItems.length; i++) {
                    var order = $scope.orderItems[i];
                    order.callresultId = callResult.id;
                    order.campaignId = agentSession.currentCampaignStage.campaign.id;
                    order.clientId = agentSession.currentCampaignStage.campaign.client.id;
                    order.agentId = agentSession.agentId;
                    order.campaignstageId = agentSession.currentCampaignStage.id;
                    order.leadId = agentSession.currentLead.id;
                    order.$save();
                };

                var invoice = new Invoice();
                invoice.callresultId = callResult.id;
                invoice.grandTotal = result.grandTotal;
                invoice.amountRemaining = 0;
                invoice.requestCount = 0;
                invoice.invoiceType = result.paymentType;
                invoice.clientId = agentSession.currentCampaignStage.campaign.client.id;
                invoice.campaignId = agentSession.currentCampaignStage.campaign.id;
                invoice.leadId = lead.id;
                invoice.sendInvoice = false;

                invoice.$save().then(function (inv) {
                    if (result.paymentType == 'Credit Card') {
                        // create the payment log now
                        var payment = new PaymentLog();
                        payment.invoiceId = inv.id;
                        payment.amount = result.grandTotal;
                        payment.paymentDate = new Date();
                        payment.isPaid = true;
                        payment.actualPaymentDate = new Date();
                        payment.status = 'paid';
                        payment.deleted = false;
                        payment.disabled = false;
                        payment.leadId = lead.id;
                        payment.userId = agentSession.userId;
                        payment.clientId = agentSession.currentCampaignStage.campaign.client.id;
                        payment.campaignId = agentSession.currentCampaignStage.campaign.id;
                        payment.callresultId = callResult.id;
                        payment.source = 'sale'
                        payment.$save();
                    }
                });

                $uibModalInstance.close({
                    disposition: disposition,
                    callResultData: result,
                    wrapUpNotes: $scope.notes
                });
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            $scope.orderItems.forEach(function (order, index) {
                if (!order.series || !order.seats || !order.dayOfWeek) {
                    errors.push('Sale item ' + (index + 1) + ' is not complete');
                }
            })

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (!$scope.formData.useExistingCC) {
                if ($scope.formData.creditCardNumber?.length !== 4) {
                    errors.push('Invalid last 4 CC digits.');
                }

                if (!$scope.validateCreditCardDate($scope.formData.creditCardDate)) {
                    errors.push('Invalid credit card expiry date.');
                }

                if (!$scope.formData.creditCardType || !$scope.formData.creditCardNumber || !$scope.formData.creditCardDate) {
                    errors.push('Please complete all credit card information.');
                }
            } else {
                if (!$scope.validateCreditCardDate(fd.creditCardDate)) {
                    errors.push('Invalid credit card expiry date');
                }
            }

            if ($scope.acceptAddon && $scope.addOnGiftEnabled) {
                if (!$scope.giftAmount || ($scope.giftAmount !== $scope.verifyGiftAmount)) {
                    errors.push('Gift amount must be verified and match.');
                }
            }

            if (!$scope.acceptAddon) {
                errors.push('Please confirm you have asked about the add-on gift.');
            }

            if (!$scope.acceptSaleAmount) {
                errors.push('Please confirm you have verified the grand total cost.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the lead details.');
            }

            if (!$scope.acceptEmailAddress) {
                errors.push("Please confirm patron's email address.");
            }

            return (errors.length ? errors : true);
        }

        function getPaymentMonths() {
            var results = [];
            var dt = moment();
            var today = moment();

            for (var i = 0; i < 4; i++) {
                results.push(dt.format('MMMM'));
                dt.add(1, 'M');
            }

            return {
                options: results,
                selected: (today.date() > 10 ? today.add(1, 'M').format('MMMM') : today.format('MMMM'))
            };
        }
    });