'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('CustomDispositionModalCtrl', function ($scope, $rootScope, $uibModalInstance, $uibModal, disposition, agentSession, phone, lead, Lead, SweetAlert, _) {
        $scope.disposition = disposition;
        $scope.notes = '';
        $scope.lead = lead;
        $scope.phone = phone;

        $scope.ok = function () {
            if ($scope.disposition.validateLead && !$scope.confirmedLeadDetails) {
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please Validate Lead'
                });
            } else {
                $uibModalInstance.close({ disposition: disposition, wrapUpNotes: $scope.notes });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.confirmedLeadDetails = false;

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };
    });