'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('WriteOffDispositionModalCtrl', function ($scope, $uibModalInstance, $uibModal, disposition, invoices, agentSession, lead, Invoice, SweetAlert, _) {
        // Set form options and defaults
        $scope.notes = '';

        $scope.verify = {
            contactDetails: false
        };

        $scope.confirmedLeadDetails = false;
        $scope.lead = lead;
        $scope.formData = {
            notes: '',
            decisionMaker: ''
        };

        if (invoices.length) {
            $scope.invoice = invoices[invoices.length - 1];
            var count = invoices.length;
            while ($scope.invoice.amountRemaining == 0 && count) {
                count--;
                $scope.invoice = invoices[count];
            }
        } else {
            $scope.invoice = {};
        }

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function (Lead) {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.ok = function () {
            var verificationResult = performVerification();

            if (verificationResult === true) {
                var result = {
                    decisionMaker: $scope.formData.decisionMaker
                };

                if ($scope.invoice && $scope.invoice.id) {
					Invoice.writeOff({
						id: $scope.invoice.id
					}, {
						amount: null
					}).$promise
					.then(updatedInvoice => {
						$scope.invoice.writtenOff = updatedInvoice.writtenOff
						$scope.invoice.writtenOffAmount = updatedInvoice.writtenOffAmount
						$scope.invoice.amountRemaining = updatedInvoice.amountRemaining
						$scope.invoice.sendInvoice = updatedInvoice.sendInvoice

                        $uibModalInstance.close({
                            disposition: disposition,
                            wrapUpNotes: $scope.formData.notes,
                            callResultData: result
                        });
					});
                }
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        function performVerification() {
            var errors = [];

            if (!$scope.formData.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (!$scope.formData.notes) {
                errors.push('Please select a refusal reason.');
            }

            return (errors.length ? errors : true);
        }
    });