'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('SaleDispositionV3Ctrl', function ($scope, $uibModal, $rootScope, $http, $uibModalInstance, APP_SETTINGS, disposition, moment, Lead, Sale, Invoice, client, products, lead, agentSession, _, SweetAlert, phone) {
        var callResult = agentSession.currentCallResult;
        var result = {};
        $scope.phone = phone;

        // Set form options and defaults
        var paymentMonths = getPaymentMonths();
        $scope.paymentMonths = paymentMonths.options;
        $scope.verify = {};
        $scope.formData = {
            freeTickets: 0,
            paymentMonth: paymentMonths.selected,
            declineBenefits: false,
            initialPaymentDate: new Date()
        };


        $rootScope.client = client;

        $scope.notes = '';
        $scope.products = _.sortBy(products, 'order_by');
        $scope.orderItems = [new Sale()];
        $scope.cards = [];
        $scope.lead = lead;
        $scope.client = client;
        $scope.digits = undefined;
        $scope.validtoken = false;
        $scope.tokenerror = 'No Card on File';
        $scope.cardToken = lead.cardtoken || {};
        $rootScope.clientId = client.id;
        $rootScope.cardtoken = {};
        if ($scope.cardToken.maskedCardNumber) {
            $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
        }

        if (lead.cardtoken && lead.cardtoken.expirationDate) {
            var expirationDate = moment(lead.cardtoken.expirationDate, 'MMYYYY')
            $scope.cardToken.expirationDate = expirationDate.format('MM/YYYY')
            if (expirationDate > moment()) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
            } else {
                $scope.tokenerror = 'Saved Card Expired';
            }
        }

        if (!$scope.validtoken) {
            $rootScope.resetTsys();
        }

        $scope.payments = {
            count: 1,
            every: 1,
            unit: 'months',
            values: [{
                amount: 0,
                date: new Date()
            }]
        };

        $scope.format = 'dd-MMMM-yyyy';
        $scope.initialOptions = {
            minDate: new Date(),
            maxDate: moment().add(90, 'days').toDate()
        };
        $scope.dates = {
            initial: false,
        };

        $scope.carderrors = {};

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);

        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            $scope.cardToken = $rootScope.cardtoken;
        }, true);

        if ($scope.client.additionalInfo) {
            var cardInfo = JSON.parse($scope.client.additionalInfo);
            for (var key in cardInfo) {
                if (cardInfo[key])
                    $scope.cards.push(key);
            };
        } else {
            $scope.cards = [];
        }

        $scope.checkvalues = function (order) {
            order.possibleDays = _.where($scope.products, {
                series: order.series,
                seats: order.seats
            });
        };

        $scope.addNewOrder = function () {
            $scope.orderItems.push(new Sale());
        };

        $scope.updateCost = function (order) {
            var product = getProduct(order);
            if (product) {
                order.costEach = product.price;
                order.feePerTicket = product.feePerTicket;
            }
            $scope.updateTotal(order);
            $scope.updatePaymentCount();
        };

        $scope.updateTotal = function (order) {
            var product = getProduct(order);
            order.productCode = product.productCode;
            order.tix_sub = product.tix_sub;
            order.salesTax = client.salesTax;
            if (product) {
                if (order.seatCount > 0) {
                    order.subtotal = (order.costEach + order.feePerTicket) * order.seatCount;
                } else {
                    order.subtotal = 0;
                }
            } else {
                order.subtotal = 0;
            }

            $scope.updateGrandTotal();
        };

        $scope.removeOrder = function (order) {
            var index = $scope.orderItems.indexOf(order);
            if (index > -1) {
                $scope.orderItems.splice(index, 1);
            }
            $scope.updateGrandTotal();
        };

        var getProduct = function (order) {
            return _.findWhere($scope.products, {
                series: order.series,
                seats: order.seats,
                days: order.dayOfWeek
            });
        };

        $scope.updateGrandTotal = function () {
            $scope.subtotal = 0;

            _.each($scope.orderItems, function (order) {
                if (order.subtotal !== undefined)
                    $scope.subtotal += order.subtotal;
            })

            if ($scope.subtotal === 0) {
                var subWithTax = 0;
            } else {
                var subWithTax = $scope.subtotal + (($scope.client.salesTax / 100) * $scope.subtotal);
            }

            if ($scope.addOnGiftEnabled) {
                $scope.giftAmountTotal = $scope.giftAmount;

                if (!$scope.giftAmount && $scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                } else if ($scope.giftAmount && !$scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                } else {
                    $scope.grandTotal = subWithTax + $scope.giftAmount;
                }
            } else {
                $scope.giftAmountTotal = 0;

                if ($scope.subtotal) {
                    $scope.grandTotal = subWithTax;
                }
            }

            $scope.grandTotal += $scope.client.orderFee;
            if ($scope.grandTotal)
                $scope.grandTotal = $scope.grandTotal.toFixed(2);
        };



        $scope.ok = function () {
            var verificationResult = performVerification();
            var lastDate = null;

            if (verificationResult === true) {
                var fd = $scope.formData;
                var promise;

                if ($scope.formData.splitPayments) {
                    // generate all the future payments
                    var payments = []
                    for (let i = 0; i < $scope.payments.values.length; i++) {
                        var payment = $scope.payments.values[i];
                        payments.push({
                            amount: payment.amount,
                            paymentDate: payment.date,
                            isPaid: false,
                            status: 'pending',
                            leadId: lead.id,
                            userId: $rootScope.loggedInUser.id,
                            clientId: client.id,
                            campaignId: agentSession.currentCallResult.campaignId,
                            callresultId: agentSession.currentCallResult.id
                        })
                        lastDate = payment.date
                    }
                    promise = $http.post(APP_SETTINGS.BASE_API_URL + 'paymentlog', payments)
                } else {
                    if ($scope.digits) $scope.digits = $scope.digits.trim();
                    promise = $http.post(APP_SETTINGS.BASE_API_URL + 'callresults/' + agentSession.currentCallResult.id + '/sale', {
                        amount: $scope.grandTotal,
                        digits: $scope.digits,
                        clientId: client.id
                    })
                    // take payment now
                }

                promise.then(response => {
                    if (!response.data.Success) {
                        var verificationMsg = response.data.error
                        return SweetAlert.swal({
                            title: "Error Processing Payment",
                            text: verificationMsg
                        });
                    }

                    if ($scope.addOnGiftEnabled) {
                        result.giftAmount = $scope.giftAmount;
                        result.giftMatchingCompany = fd.giftMatchingCompany;
                    }
                    result.freeTickets = $scope.freeTix;
                    result.paymentType = 'Credit Card';
                    result.creditCardType = fd.creditCardType;
                    result.creditCardNumber = fd.creditCardNumber;
                    result.creditCardExpDate = fd.creditCardDate;
                    result.creditCardSecurityCode = fd.creditCardPin;
                    result.decisionMaker = fd.decisionMaker;
                    result.declineBenefits = $scope.declineBenfits;
                    result.saleAmount = $scope.subtotal;
                    result.grandTotal = $scope.grandTotal;
                    result.newMembershipCard = fd.newMembershipCard;
                    result.useExistingCreditCard = fd.useExistingCC;
                    result.installmentNotes = fd.installmentNotes;
                    result.numberOfInstallments = fd.numberOfInstallments;
                    if (result.useExistingCreditCard) {
                        result.creditCardPin = '';
                        result.creditCardDate = lead.existingCCExp;
                        result.creditCardType = lead.existingCCType;
                        result.creditCardNumber = lead.existingCCDigits;
                    }

                    //save order to sales table
                    for (var i = 0; i < $scope.orderItems.length; i++) {
                        var order = $scope.orderItems[i];
                        order.callresultId = callResult.id;
                        order.campaignId = agentSession.currentCampaignStage.campaign.id;
                        order.clientId = agentSession.currentCampaignStage.campaign.client.id;
                        order.agentId = agentSession.agentId;
                        order.campaignstageId = agentSession.currentCampaignStage.id;
                        order.leadId = agentSession.currentLead.id;
                        order.$save();
                    };

                    var invoice = new Invoice();
                    invoice.callresultId = callResult.id;
                    invoice.grandTotal = result.grandTotal;
                    invoice.amountRemaining = 0;
                    invoice.requestCount = 0;
                    invoice.invoiceType = result.paymentType;
                    invoice.clientId = agentSession.currentCampaignStage.campaign.client.id;
                    invoice.campaignId = agentSession.currentCampaignStage.campaign.id;
                    invoice.leadId = lead.id;
                    invoice.sendInvoice = false;

                    invoice.$save().then(function (inv) {
                        if (response.data.payment) {
                            $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + response.data.payment.id, {
                                invoiceId: inv.id
                            }).catch(angular.noop)
                        } else {
                            $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/callresult/' + agentSession.currentCallResult.id, {
                                invoiceId: inv.id
                            }).catch(angular.noop)
                        }
                    });
                    
                    Lead.update({
                        id: lead.id
                    }, {
                        agentPortfolioTag: agentSession.agent.name
                    })
                    .$promise
                    .finally(function () {
                        $uibModalInstance.close({
                            disposition: disposition,
                            callResultData: result,
                            lastDate: lastDate,
                            wrapUpNotes: $scope.notes
                        });
                    });
                })
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;
                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.updatePaymentCount = function () {
            try {
                $scope.payments.values = []
                var range = Math.ceil($scope.grandTotal / $scope.payments.count);
                $scope.payments.total = $scope.grandTotal;
                $scope.payments.difference = 'correct amount';
                var total = 0, date = moment($scope.formData.initialPaymentDate);
                for (var i = 0; i < $scope.payments.count; i++) {
                    var amount;
                    if ((total + range) < $scope.grandTotal)
                        amount = range;
                    else
                        amount = $scope.grandTotal - total;

                    if (amount < 0) amount = 0;

                    $scope.payments.values.push({
                        amount, date: moment(date).toDate()
                    });

                    total += range

                    date = date.add($scope.payments.every, $scope.payments.unit);
                }
            } catch (err) {
                console.log(err)
            }
        };

        $scope.updatePayment = function () {
            var total = 0
            for (let i = 0; i < $scope.payments.values.length; i++) {
                total += $scope.payments.values[i].amount;
            }
            $scope.payments.total = total
            try {
                var difference = total - $scope.formData.giftAmount;
                if (difference === 0)
                    $scope.payments.difference = 'correct amount'
                else if (difference > 0)
                    $scope.payments.difference = '$' + difference + ' too much'
                else
                    $scope.payments.difference = '$' + difference + ' too little'
            }
            catch (err) {
                console.log(err)
            }

            $scope.paymentsBroken = !!difference;
        };

        $scope.fixPayments = function (index) {
            var difference = $scope.grandTotal - $scope.payments.total;
            $scope.payments.values[index].amount += difference;
            $scope.updatePayment();
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.minDate = new Date();
        $scope.maxDate = new Date().addDays(90);

        $scope.format = 'dd-MMMM-yyyy';

        $scope.popup1 = {
            opened: false
        };

        $scope.openDatePicker = function () {
            $scope.popup1.opened = true;
        };

        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            $scope.orderItems.forEach(function (order, index) {
                if (!order.series || !order.seats || !order.dayOfWeek) {
                    errors.push('Sale item ' + (index + 1) + ' is not complete');
                }
            })

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if ($scope.tokenerror) {
                errors.push($scope.tokenerror)
            }

            if ($scope.acceptAddon && $scope.addOnGiftEnabled) {
                if (!$scope.giftAmount || ($scope.giftAmount !== $scope.verifyGiftAmount)) {
                    errors.push('Gift amount must be verified and match.');
                }
            }

            if (!$scope.acceptAddon) {
                errors.push('Please confirm you have asked about the add-on gift.');
            }

            if (!$scope.acceptSaleAmount) {
                errors.push('Please confirm you have verified the grand total cost.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the lead details.');
            }

            if (!$scope.acceptEmailAddress) {
                errors.push("Please confirm patron's email address.");
            }

            if ($scope.paymentsBroken) {
                errors.push('Split payments do not add up.')
            }

            if ($scope.formData.splitPayments && $scope.cardToken) {
                var lastDate = $scope.payments.values[$scope.payments.values.length - 1].date;
                var dateErr = $rootScope.checkCardDate(lastDate, $scope.cardToken.expiry);
                if (dateErr) {
                    errors.push(dateErr);
                }
            }

            return (errors.length ? errors : true);
        }

        function getPaymentMonths() {
            var results = [];
            var dt = moment();
            var today = moment();

            for (var i = 0; i < 4; i++) {
                results.push(dt.format('MMMM'));
                dt.add(1, 'M');
            }

            return {
                options: results,
                selected: (today.date() > 10 ? today.add(1, 'M').format('MMMM') : today.format('MMMM'))
            };
        }
    });