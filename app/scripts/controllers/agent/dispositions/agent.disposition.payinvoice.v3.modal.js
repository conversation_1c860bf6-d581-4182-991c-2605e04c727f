'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('InvoicePaymentDispositionV3ModalCtrl', function ($scope, $rootScope, $http, $q, APP_SETTINGS, $uibModalInstance, $uibModal, disposition, moment, agentSession, invoices, lead, client, admin, Lead, Invoice, CallResult, SweetAlert, Campaign, phone) {
        $scope.lead = lead;
        var stages = [];
        $scope.testingTsys = false;

        $scope.phone = phone;
        $scope.admin = admin;
        $scope.saving = false;

        $scope.existingCard = angular.copy(lead.cardtoken || {});

        //get the newest invoice that has an amount remaining greater than 0
        if (invoices.length) {
            $scope.invoice = invoices[invoices.length - 1];
            var count = invoices.length;
            while ($scope.invoice.amountRemaining == 0 && count) {
                count--;
                $scope.invoice = invoices[count];
            }
        } else {
            $scope.invoice = {};
        }

        $scope.numberOfInstallments = 0;
        if ($scope.invoice.callresult) {
            $scope.numberOfInstallments = $scope.invoice.callresult.numberOfInstallments;
        }

        $scope.failReason = '';
        if ($scope.invoice.paymentlogs && $scope.invoice.paymentlogs.length) {
            var lastPayment = $scope.invoice.paymentlogs.reverse().find(function (pl) {
                return !!pl.error && pl.error !== 'Previous Payment Failed'
            })
            if (lastPayment) {
                $scope.failReason = lastPayment.error
            }

            if (!$scope.numberOfInstallments) {
                $scope.numberOfInstallments = $scope.invoice.paymentlogs.filter(pl => pl.callresultId === $scope.invoice.callresult.id).length;
            }
        }

        $scope.payments = {
            count: 1,
            every: 1,
            unit: 'months',
            values: [{
                amount: 0,
                date: new Date()
            }]
        };

        $scope.notes = '';

        var firstLoad = true;
        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            // dont do this the first time as the cardtoken gets reset to blank immediately and we lose the stored card
            if (!firstLoad) {
                $scope.cardToken = $rootScope.cardtoken;
                if ($scope.cardToken.tsepToken && !$scope.validtoken && !$scope.saving) {
                    // just save it so the user doesnt have to
                    $scope.saveCard();
                }
            }
            firstLoad = false;
        }, true);

        Campaign.getCampaignStages({
            id: agentSession.currentCampaignStage.campaign.id
        }).$promise.then(function (_stages) {
            stages = _stages;
        })

        var originalAmountRemaining = $scope.invoice.amountRemaining || 0;

        $scope.client = client;
        $scope.cards = [];
        $scope.writeoff = false;

        $scope.verify = {
            sendByMail: false,
            leftToPay: false
        };

        if ($scope.client.additionalInfo) {
            var cardInfo = JSON.parse($scope.client.additionalInfo);
            for (var key in cardInfo) {
                if (cardInfo[key])
                    $scope.cards.push(key);
            };
        }

        $scope.formData = {
            paymentMethod: 'Credit Card',
            payAmount: $scope.invoice.amountRemaining || 0,
            originalAmount: $scope.invoice.callresult ? $scope.invoice.callresult.grandTotal : 0,
            initialPaymentDate: new Date()
        };
        $rootScope.clientId = client.id;


        $scope.digits = undefined;
        $scope.validtoken = false;
        $scope.tokenerror = 'No Card on File';
        $scope.expired = false;
        $scope.cardToken = lead.cardtoken || {};
        $rootScope.cardtoken = {};
        if ($scope.cardToken.maskedCardNumber) {
            $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
        }

        $scope.cardToken.cardHolderName = lead.first_name + ' ' + lead.last_name;

        if (lead.cardtoken && lead.cardtoken.expirationDate) {
            var expirationDate = moment(lead.cardtoken.expirationDate, 'MMYYYY')
            $scope.cardToken.expirationDate = expirationDate.format('MM/YYYY')
            if (expirationDate > moment()) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
            } else {
                $scope.expired = true;
                $scope.tokenerror = 'Saved Card Expired';
            }
        }

        if (!$scope.validtoken) {
            $rootScope.resetTsys();
        }

        $scope.format = 'dd-MMMM-yyyy';
        $scope.initialOptions = {
            minDate: new Date(),
            maxDate: moment().add(90, 'days').toDate()
        };
        $scope.dates = {
            initial: false,
        };

        $scope.carderrors = {};

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);

        var firstLoad = true;
        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            if (!firstLoad) $scope.cardToken = $rootScope.cardtoken;
            firstLoad = false;
        }, true);

        $scope.updateAmount = function () {
            if ($scope.invoice.callresult.grandTotal > $scope.formData.originalAmount) {
                $scope.invoice.amountRemaining = originalAmountRemaining - ($scope.invoice.callresult.grandTotal - $scope.formData.originalAmount);
            } else if ($scope.invoice.callresult.grandTotal < $scope.formData.originalAmount) {
                $scope.invoice.amountRemaining = originalAmountRemaining + ($scope.formData.originalAmount - $scope.invoice.callresult.grandTotal);
            } else {
                $scope.invoice.amountRemaining = originalAmountRemaining;
            }
        };

        $scope.ok = function () {
            if ($scope.formData.paymentType === 'Credit Card' && !$scope.validtoken) {
                $scope.disableOkButton = true;
                $scope.testingTsys = true;
                // wait for tsys to fire the TokenEvent event which means its checked the card
                $rootScope.waitForTsys(function (err) {
                    if (err) {
                        // the wait has timed out after 1.5 seconds, carry on as the verifiction will tidy this up
                    }
                    $scope.testingTsys = false;

                    okPressed();
                })
            } else {
                okPressed();
            }
        };

        function okPressed() {
            var verificationResult = performVerification();
            var lastDate = null;

            if (verificationResult === true) {
                var promise = $q.when(true);
                var fd = $scope.formData;

                if ($scope.formData.splitPayments) {
                    // generate all the future payments
                    var payments = []
                    for (let i = 0; i < $scope.payments.values.length; i++) {
                        var payment = $scope.payments.values[i];
                        payments.push({
                            amount: payment.amount,
                            paymentDate: payment.date,
                            isPaid: false,
                            status: 'pending',
                            leadId: lead.id,
                            userId: $rootScope.loggedInUser.id,
                            clientId: client.id,
                            campaignId: agentSession.currentCallResult.campaignId,
                            invoiceId: $scope.invoice.id,
                            callresultId: agentSession.currentCallResult.id
                        })
                    }
                    try {
                        lastDate = $scope.payments.values[$scope.payments.values.length - 1].date;
                    }
                    catch (e) {}
                    promise = $http.post(APP_SETTINGS.BASE_API_URL + 'paymentlog', payments)
                } else {
                    if ($scope.digits) $scope.digits = $scope.digits.trim();
                    promise = $http.post(APP_SETTINGS.BASE_API_URL + 'callresults/' + agentSession.currentCallResult.id + '/sale', {
                        invoiceId: $scope.invoice.id,
                        amount: fd.payAmount,
                        digits: $scope.digits,
                        clientId: client.id
                    })
                    // take payment now
                }

                promise.then(function (response) {
                    if (response && response.data) {
                        if (!response.data.Success) {
                            $rootScope.safeApply(function () {
                                $scope.disableOkButton = false;
                            });
                            var verificationMsg = response.data.error
                            return SweetAlert.swal({
                                title: "Error Processing Payment",
                                text: verificationMsg
                            });
                        }
                    }

                    if (client.defaultInvoiceType == 'email' && lead.email && !$scope.verify.sendByMail) {
                        $scope.formData.paymentMethod = 'Email';
                    } else {
                        $scope.formData.paymentMethod = 'Paper';
                    }

                    var result = {};
                    result.paymentType = 'Credit Card';
                    result.payAmount = fd.payAmount;
                    result.creditCardType = fd.creditCardType;
                    result.creditCardNumber = fd.creditCardNumber;
                    result.creditCardExpDate = fd.creditCardDate;
                    result.creditCardSecurityCode = fd.creditCardPin;
                    result.decisionMaker = fd.decisionMaker;

                    if (fd.originalAmount > $scope.invoice.callresult.grandTotal) {
                        $scope.invoice.callresult.giftAmount = fd.originalAmount;
                        $scope.invoice.grandTotal = fd.originalAmount;
                        $scope.invoice.sendInvoice = true;
                        CallResult.update({
                            id: $scope.invoice.callresult.id
                        }, $scope.invoice.callresult);
                    } else if (fd.originalAmount < $scope.invoice.callresult.grandTotal) {
                        $scope.invoice.grandTotal = fd.originalAmount;
                        $scope.invoice.sendInvoice = true;
                    }

                    $scope.invoice.amountRemaining = $scope.invoice.amountRemaining - fd.payAmount;

                    if ($scope.invoice.amountRemaining) {
                        if ($scope.writeoff && !$scope.formData.splitPayments) {
                            writeOff();
                            // disposition.exhaustLead = true;
                        } else {
                            $scope.invoice.sendInvoice = true;
                            // disposition.exhaustLead = false;
                            // disposition.campaignstagedispositions.transitionToCampaignStageId = null;
                        }
                    } else {
                        $scope.invoice.sendInvoice = false;
                        // disposition.exhaustLead = true;
                        // var stage = _.findWhere(stages, {
                        //     name: 'Thank you'
                        // });

                        // if (stage)
                        //     disposition.campaignstagedispositions.transitionToCampaignStageId = stage.id;
                    }

                    Invoice.update({
                        id: $scope.invoice.id
                    }, $scope.invoice);

                    $uibModalInstance.close({
                        disposition: disposition,
                        callResultData: result,
                        wrapUpNotes: $scope.notes,
                        lastDate: lastDate
                    });
                }).catch(err => {
                    $rootScope.safeApply(function () {
                        $scope.disableOkButton = false;
                    });
                    console.log(err)
                    if (err) {
                        var verificationMsg = err.data
                        SweetAlert.swal({
                            title: "Error Processing Payment",
                            text: verificationMsg
                        });
                        throw (err)
                    }
                })
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $scope.saving = true;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.saving = false;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;

                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.saving = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.auth = function () {
            var body = {
                amount: $scope.amount,
                creditCardNumber: $scope.creditCard.number,
                creditCardDate: $scope.creditCard.expiry,
                creditCardPin: $scope.creditCard.pin
            }

            $scope.response = '';
            $scope.error = '';

            var request = {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/authpayment',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: body
            }
            $http(request).then(function (response) {
                if (response.data.Success === false) {
                    $scope.error = response.data.message
                } else {
                    if (response.data.SaleResponse.status === 'PASS') {
                        $scope.response = response.data.SaleResponse.responseMessage
                    } else {
                        $scope.error = response.data.SaleResponse.responseMessage
                    }
                }
            }, function (response) {
                $scope.error = response.data.message
            })
        };

        $scope.updatePaymentCount = function () {
            try {
                $scope.payments.values = []
                var range = Math.ceil($scope.formData.payAmount / $scope.payments.count);
                $scope.payments.total = $scope.formData.payAmount;
                $scope.payments.difference = 'correct amount';
                var total = 0, date = moment($scope.formData.initialPaymentDate);
                for (var i = 0; i < $scope.payments.count; i++) {
                    var amount;
                    if ((total + range) < $scope.formData.payAmount)
                        amount = range;
                    else
                        amount = $scope.formData.payAmount - total;

                    if (amount < 0) amount = 0;

                    $scope.payments.values.push({
                        amount, date: moment(date).toDate()
                    });

                    total += range

                    date = date.add($scope.payments.every, $scope.payments.unit);
                }
            } catch (err) {
                console.log(err)
            }
        };

        $scope.updatePayment = function () {
            var total = 0
            for (let i = 0; i < $scope.payments.values.length; i++) {
                total += $scope.payments.values[i].amount;
            }
            $scope.payments.total = total
            try {
                var difference = Math.round((total - $scope.formData.payAmount) * 100) / 100
                if (difference === 0)
                    $scope.payments.difference = 'correct amount'
                else if (difference > 0)
                    $scope.payments.difference = '$' + difference + ' too much'
                else
                    $scope.payments.difference = '$' + Math.abs(difference) + ' too little'
            }
            catch (err) {
                console.log(err)
            }

            $scope.paymentsBroken = !!difference;
        };

        $scope.fixPayments = function (index) {
            var difference = $scope.formData.payAmount - $scope.payments.total;
            $scope.payments.values[index].amount += difference;
            $scope.updatePayment();
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        function writeOff() {
            if ($scope.invoice && $scope.invoice.id) {
                Invoice.writeOff({
                    id: $scope.invoice.id
                }, {
                    amount: null
                }).$promise
                .then(updatedInvoice => {
                    $scope.invoice.writtenOff = updatedInvoice.writtenOff
                    $scope.invoice.writtenOffAmount = updatedInvoice.writtenOffAmount
                    $scope.invoice.amountRemaining = updatedInvoice.amountRemaining
                    $scope.invoice.sendInvoice = updatedInvoice.sendInvoice
                });
            }
        }

        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            if (!fd.payAmount) {
                errors.push('Invalid Pay Amount')
            }

            if (originalAmountRemaining - fd.payAmount < 0) {
                errors.push('Pay Amount is too high')
            }

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if ($scope.payments.difference !== 'correct amount' && $scope.formData.splitPayments && !$scope.writeOff) {
                errors.push('If you have an amount remaining and split payments you must write off the remaining amount');
            }

            if ($scope.tokenerror) {
                errors.push($scope.tokenerror);
            }

            if (!$scope.lead.address1) {
                errors.push('Lead Address Line 1 Required');
            }

            if (!$scope.lead.zip) {
                errors.push('Lead Zip Code Required');
            }

            if (!$scope.verify.leftToPay) {
                errors.push('Please confirm you have verified the amount still to pay.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the contact details.');
            }

            if ($scope.paymentsBroken) {
                errors.push('Split payments do not add up.');
            }

            if ($scope.formData.splitPayments && $scope.cardToken) {
                var lastDate = $scope.payments.values[$scope.payments.values.length - 1].date;
                var dateErr = $rootScope.checkCardDate(lastDate, $scope.cardToken.expiry || $scope.cardToken.expirationDate);
                if (dateErr) {
                    errors.push(dateErr);
                }
            }


            return (errors.length ? errors : true);
        }
    })