'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('InvoicePaymentDispositionModalCtrl', function ($scope, $uibModalInstance, $uibModal, disposition, moment, agentSession, invoices, lead, client, Lead, Invoice, CallResult, SweetAlert, Campaign, PaymentLog, phone) {
		$scope.lead = lead;
		var stages = [];

		$scope.phone = phone;

		//get the newest invoice that has an amount remaining greater than 0
		if (invoices.length) {
			$scope.invoice = invoices[invoices.length - 1];
			var count = invoices.length;
			while ($scope.invoice.amountRemaining == 0 && count) {
				count--;
				$scope.invoice = invoices[count];
			}
		} else {
			$scope.invoice = {};
		}

		Campaign.getCampaignStages({
			id: agentSession.currentCampaignStage.campaign.id
		}).$promise.then(function (_stages) {
			stages = _stages;
		})

		var originalAmountRemaining = $scope.invoice.amountRemaining;

		$scope.client = client;
		$scope.cards = [];

		$scope.verify = {
			sendByMail: false,
			leftToPay: false
		};

		if ($scope.client.additionalInfo) {
			var cardInfo = JSON.parse($scope.client.additionalInfo);
			for (var key in cardInfo) {
				if (cardInfo[key])
					$scope.cards.push(key);
			};
		}

		$scope.formData = {
			paymentMethod: 'Credit Card',
			payAmount: $scope.invoice.amountRemaining,
			originalAmount: $scope.invoice.callresult.grandTotal
		};

		$scope.updateAmount = function () {
			if ($scope.invoice.callresult.grandTotal > $scope.formData.originalAmount) {
				$scope.invoice.amountRemaining = originalAmountRemaining - ($scope.invoice.callresult.grandTotal - $scope.formData.originalAmount);
			} else if ($scope.invoice.callresult.grandTotal < $scope.formData.originalAmount) {
				$scope.invoice.amountRemaining = originalAmountRemaining + ($scope.formData.originalAmount - $scope.invoice.callresult.grandTotal);
			} else {
				$scope.invoice.amountRemaining = originalAmountRemaining;
			}
		};

		$scope.ok = function () {
			var verificationResult = performVerification();

			if (verificationResult === true) {
				var fd = $scope.formData;

				if (client.defaultInvoiceType == 'email' && lead.email && !$scope.verify.sendByMail) {
					$scope.formData.paymentMethod = 'Email';
				} else {
					$scope.formData.paymentMethod = 'Paper';
				}

				var result = {};
				result.paymentType = 'Credit Card';
				result.payAmount = fd.payAmount;
				result.creditCardType = fd.creditCardType;
				result.creditCardNumber = fd.creditCardNumber;
				result.creditCardExpDate = fd.creditCardDate;
				result.decisionMaker = fd.decisionMaker;

				if (fd.originalAmount > $scope.invoice.callresult.grandTotal) {
					$scope.invoice.callresult.giftAmount = fd.originalAmount;
					$scope.invoice.grandTotal = fd.originalAmount;
					$scope.invoice.sendInvoice = true;
					CallResult.update({
						id: $scope.invoice.callresult.id
					}, $scope.invoice.callresult);
				} else if (fd.originalAmount < $scope.invoice.callresult.grandTotal) {
					$scope.invoice.grandTotal = fd.originalAmount;
					$scope.invoice.sendInvoice = true;
				}

				$scope.invoice.amountRemaining = $scope.invoice.amountRemaining - fd.payAmount;

				if ($scope.invoice.amountRemaining) {
					$scope.invoice.sendInvoice = true;
					// disposition.exhaustLead = false;
					// disposition.campaignstagedispositions.transitionToCampaignStageId = null;
				} else {
					$scope.invoice.sendInvoice = false;
					// disposition.exhaustLead = true;
					// disposition.campaignstagedispositions.transitionToCampaignStageId = _.findWhere(stages, {
					// 	name: 'Thank you'
					// }).id;
				}

				Invoice.update({
					id: $scope.invoice.id
				}, $scope.invoice)

				// create a payment log
				var payment = new PaymentLog();
				payment.invoiceId = $scope.invoice.id;
				payment.amount = fd.payAmount;
				payment.paymentDate = new Date();
				payment.isPaid = true;
				payment.actualPaymentDate = new Date();
				payment.status = 'paid';
				payment.deleted = false;
				payment.disabled = false;
				payment.leadId = lead.id;
				payment.userId = agentSession.userId;
				payment.clientId = agentSession.currentCampaignStage.campaign.client.id;
				payment.campaignId = agentSession.currentCampaignStage.campaign.id;
				payment.callresultId = agentSession.currentCallResult.id;
				payment.source = 'pay invoice'
				payment.$save();

				$uibModalInstance.close({
					disposition: disposition,
					callResultData: result,
					wrapUpNotes: $scope.notes
				});
			} else {
				var verificationMsg = verificationResult.join('\n - ');
				SweetAlert.swal({
					title: "Invalid",
					text: 'Please correct the following error(s):\n\n - ' + verificationMsg
				});
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};

		$scope.validateLead = function () {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/lead/admin.lead.edit.html',
				controller: 'LeadModalEditCtrl',
				backdrop: 'static',
				size: 'lg',
				keyboard: false,
				resolve: {
					editItem: function () {
						return Lead.get({
							id: $scope.lead.id
						}).$promise;
					},
					existingItem: function () {
						return $scope.lead;
					},
					agentId: function () {
						return agentSession.agentId;
					}
				}
			});

			modalInstance.result.then(function (lead) {
				$scope.lead = lead;
				$scope.confirmedLeadDetails = true;
			})
		};

		function performVerification() {
			var errors = [];
			var fd = $scope.formData;

			if (!fd.decisionMaker) {
				errors.push('Decision Maker must be filled in.');
			}

			if (!fd.creditCardType || !fd.creditCardNumber || !fd.creditCardDate) {
				errors.push('Please complete all credit card information.');
			}

			if (fd.creditCardNumber?.length !== 4) {
				errors.push('Invalid last 4 CC digits.');
			}

			if (!$scope.validateCreditCardDate(fd.creditCardDate)) {
				errors.push('Invalid credit card expiry date');
			}

			if (!$scope.verify.leftToPay) {
				errors.push('Please confirm you have verified the amount still to pay.');
			}

			if (!$scope.confirmedLeadDetails) {
				errors.push('Please confirm the contact details.');
			}


			return (errors.length ? errors : true);
		}
	})