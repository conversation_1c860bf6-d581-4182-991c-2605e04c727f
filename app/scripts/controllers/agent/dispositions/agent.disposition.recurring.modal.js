'use strict';

angular.module('dialerFrontendApp')
    .controller('RecurringDispositionCtrl', function ($scope, $rootScope, $http, $uibModal, $uibModalInstance, APP_SETTINGS, disposition, moment, Lead, client, lead, agentSession, admin, _, SweetAlert, Invoice, PaymentLog, RecurringPayment, phone) {
        $scope.verify = {};
        $scope.testingTsys = false;
        $scope.formData = {
            freeTickets: 0,
            giftAmount: 0,
            payDate: new Date(),
            paymentType: 'Credit Card',
            initialPaymentDate: new Date(),
            declineBenefits: false
        };
        $scope.nextPayment;
        $scope.lastPayment;

        $scope.daysOfMonth = [];
        var tempDate = moment().startOf('month')
        for (var i = 1; i < 29; i++) {
            $scope.daysOfMonth.push({
                value: i,
                label: tempDate.format('Do')
            });
            tempDate.add(1, 'day');
        }

        $rootScope.client = client;
        $scope.saving = false;
        $scope.admin = admin;

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);

        var firstLoad = true;
        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            // dont do this the first time as the cardtoken gets reset to blank immediately and we lose the stored card
            if (!firstLoad) {
                $scope.cardToken = $rootScope.cardtoken;
                if ($scope.cardToken.tsepToken && !$scope.validtoken && !$scope.saving) {
                    // just save it so the user doesnt have to
                    $scope.saveCard();
                }
            }
            firstLoad = false;
        }, true);

        $scope.notes = '';
        $scope.lead = lead;
        $scope.client = client;
        $scope.phone = phone;
        $scope.validtoken = false;
        $scope.disableOkButton = false;
        $scope.digits = undefined;
        $scope.tokenerror = 'No Card on File';
        $scope.cardToken = client.allowExistingCC ? lead.cardtoken || {} : {};
        $scope.existingCard = angular.copy(lead.cardtoken || {});
        $rootScope.cardtoken = {};
        if ($scope.cardToken.maskedCardNumber) {
            $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
        }
        $rootScope.clientId = client.id;

        $scope.cardToken.cardHolderName = lead.first_name + ' ' + lead.last_name;

        if (client.allowExistingCC && lead.cardtoken && lead.cardtoken.expirationDate) {
            var expirationDate = moment(lead.cardtoken.expirationDate, 'MMYYYY')
            $scope.cardToken.expirationDate = expirationDate.format('MM/YYYY')
            if (expirationDate > moment()) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
            } else {
                $scope.tokenerror = 'Saved Card Expired';
            }
        }

        if (!$scope.validtoken) {
            $rootScope.resetTsys();
        }

        $scope.recurring = {
            amount: 0,
            every: 1,
            unit: 'month',
            firstPayment: new Date()
        };

        $scope.format = 'dd-MMMM-yyyy';
        $scope.initialOptions = {
            minDate: new Date(),
            maxDate: moment().add(30, 'days').toDate()
        };
        $scope.dates = {
            initial: false,
        };
        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.carderrors = {};


        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $scope.saving = true;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.saving = false;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;

                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.saving = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        function okPressed() {
            var lastDate = null;
            var verificationResult = performVerification();
            $rootScope.safeApply(function () {
                $scope.disableOkButton = false;
            });

            if (verificationResult === true) {
                var fd = $scope.formData;

                $rootScope.safeApply(function () {
                    $scope.disableOkButton = true;
                });

                if (client.defaultInvoiceType == 'email' && $scope.lead.email && !$scope.verify.sendByMail) {
                    $scope.formData.paymentMethod = 'Email';
                } else if (client.paperInvoices) {
                    $scope.formData.paymentMethod = 'Paper';
                }

                var result = {};
                if ($scope.recurring.unit === 'month') {
                    result.giftAmount = $scope.recurring.amount * 12;
                } else if ($scope.recurring.unit === 'week') {
                    result.giftAmount = $scope.recurring.amount * 52;
                } else if ($scope.recurring.unit === 'quarter') {
                    result.giftAmount = $scope.recurring.amount * 4;
                } else if ($scope.recurring.unit === 'day') {
                    result.giftAmount = $scope.recurring.amount * 365;
                }
                result.freeTickets = fd.freeTickets;
                result.paymentType = 'Credit Card';
                result.requiresFollowUp = fd.requiresFollowUp;
                result.decisionMaker = fd.decisionMaker;
                result.giftMatchingCompany = fd.giftMatchingCompany;
                result.declineBenefits = fd.declineBenefits;
                result.grandTotal = fd.giftAmount;
                result.newMembershipCard = fd.newMembershipCard;
                result.useExistingCreditCard = fd.useExistingCC;
                if (result.useExistingCreditCard && result.paymentType == 'Credit Card') {
                    if (!result.creditCardDate) result.creditCardDate = lead.existingCCExp;
                    result.creditCardType = lead.existingCCType;
                    result.creditCardNumber = lead.existingCCDigits;
                }

                var invoice = new Invoice();
                invoice.callresultId = agentSession.currentCallResult.id;
                invoice.grandTotal = $scope.recurring.amount;
                invoice.amountRemaining = result.paymentType != 'Credit Card' ? result.grandTotal : 0;
                invoice.deliveryMethod = fd.paymentMethod;
                invoice.dueDate = fd.payDate;
                invoice.requestCount = 0;
                invoice.invoiceType = result.paymentType;
                invoice.clientId = agentSession.currentCampaignStage.campaign.client.id;
                invoice.campaignId = agentSession.currentCampaignStage.campaign.id;
                invoice.leadId = lead.id;
                invoice.sendInvoice = result.paymentType != 'Credit Card';

                var nextPayment = new PaymentLog();
                nextPayment.amount = $scope.recurring.amount;
                nextPayment.paymentDate = $scope.recurring.firstPayment;
                nextPayment.isPaid = false;
                nextPayment.status = 'pending';
                nextPayment.leadId = lead.id;
                nextPayment.userId = $rootScope.loggedInUser.id;
                nextPayment.clientId = client.id;
                nextPayment.campaignId = agentSession.currentCallResult.campaignId;
                nextPayment.callresultId = agentSession.currentCallResult.id;
                nextPayment.source = 'recurring';

                var recurring = new RecurringPayment();
                recurring.amount = $scope.recurring.amount;
                recurring.every = $scope.recurring.every;
                recurring.unit = $scope.recurring.unit;
                recurring.firstPayment = $scope.recurring.firstPayment;
                recurring.completed = 0;
                recurring.isCancelled = false;
                recurring.data = {};
                recurring.leadId = lead.id;
                recurring.campaignId = agentSession.currentCallResult.campaignId;
                recurring.clientId = client.id;
                recurring.callresultId = agentSession.currentCallResult.id;

                invoice.$save().then(function () {
                    // create the recurring payment then the next payment
                    recurring.invoiceId = invoice.id;
                    recurring.$save().then(function () {
                        // set the id to the next payment and save
                        nextPayment.recurringpaymentId = recurring.result.id;
                        nextPayment.invoiceId = invoice.id;
                        nextPayment.$save().catch(angular.noop)
                    })
                }).catch(function (err) { });


                $uibModalInstance.close({
                    disposition: disposition,
                    callResultData: result,
                    lastDate: lastDate,
                    wrapUpNotes: $scope.notes
                });
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.ok = function () {
            if (!$scope.validtoken) {
                $scope.disableOkButton = true;
                $scope.testingTsys = true;
                // wait for tsys to fire the TokenEvent event which means its checked the card
                $rootScope.waitForTsys(function (err) {
                    if (err) {
                        // the wait has timed out after 1.5 seconds, carry on as the verifiction will tidy this up
                    }
                    $scope.testingTsys = false;

                    okPressed();
                })
            } else {
                okPressed();
            }
        };


        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            if (!$scope.recurring.amount) {
                errors.push('Gift amount must be verified.');
            }

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if ($scope.tokenerror) {
                errors.push($scope.tokenerror)
            }

            if (!$scope.lead.address1) {
                errors.push('Lead Address Line 1 Required');
            }

            if (!$scope.lead.zip) {
                errors.push('Lead Zip Code Required');
            }

            if (!$scope.verify.giftAmount) {
                errors.push('Please confirm you have verified the gift amount.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the donor details.');
            }

            if (!$scope.verify.emailAddress) {
                errors.push('Please confirm you have asked the donor for their email address.');
            }

            if (!$scope.lead.email) {
                errors.push('Lead must have an email address to send an Invoice')
            }

            if ($scope.cardToken) {
                var dateToCheck = moment().add(6, 'months')
                var dateErr = $rootScope.checkCardDate(dateToCheck, $scope.cardToken.expiry || $scope.cardToken.expirationDate);
                if (dateErr) {
                    errors.push(dateErr);
                }
            }

            return (errors.length ? errors : true);
        }

    })