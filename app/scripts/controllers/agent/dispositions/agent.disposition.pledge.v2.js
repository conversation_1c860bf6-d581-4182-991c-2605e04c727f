'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('PledgeDispositionV2ModalCtrl', function ($scope, $rootScope, $q, $http, $uibModal, $uibModalInstance, APP_SETTINGS, disposition, moment, Lead, client, lead, agentSession, admin, _, SweetAlert, Invoice, PaymentLog, phone) {
        // Set form options and defaults
        $scope.verify = {};
        $scope.formData = {
            freeTickets: 0,
            payDate: new Date(),
            paymentType: 'Credit Card',
            initialPaymentDate: new Date(),
            declineBenefits: false
        };

        $rootScope.client = client;

        $scope.notes = '';
        $scope.lead = lead;
        $scope.client = client;
        $scope.cards = [];
        $scope.phone = phone;
        $scope.client = client;
        $scope.admin = admin;
        $scope.validtoken = false;
        $scope.disableOkButton = false;
        $scope.digits = undefined;
        $scope.tokenerror = 'No Card on File';
        $scope.cardToken = lead.cardtoken || {};
        $rootScope.cardtoken = {};
        if ($scope.cardToken.maskedCardNumber) {
            $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
        }
        $rootScope.clientId = client.id;

        $scope.cardToken.cardHolderName = lead.first_name + ' ' + lead.last_name;

        if (lead.cardtoken && lead.cardtoken.expirationDate) {
            var expirationDate = moment(lead.cardtoken.expirationDate, 'MMYYYY')
            $scope.cardToken.expirationDate = expirationDate.format('MM/YYYY')
            if (expirationDate > moment()) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
            } else {
                $scope.tokenerror = 'Saved Card Expired';
            }
        }

        if (!$scope.validtoken) {
            $rootScope.resetTsys();
        }

        $scope.payments = {
            count: 1,
            every: 1,
            unit: 'months',
            values: [{
                amount: 0,
                date: new Date()
            }]
        };

        $scope.format = 'dd-MMMM-yyyy';
        $scope.initialOptions = {
            minDate: new Date(),
            maxDate: moment().add(90, 'days').toDate()
        };
        $scope.dates = {
            initial: false,
        };

        $scope.carderrors = {};

        $scope.$watch(function () {
            return $rootScope.carderrors;
        }, function () {
            $scope.carderrors = $rootScope.carderrors;
        }, true);

        $scope.$watch(function () {
            return $rootScope.cardtoken;
        }, function () {
            $scope.cardToken = $rootScope.cardtoken;
        }, true);

        if ($scope.client.additionalInfo) {
            var cardInfo = JSON.parse($scope.client.additionalInfo);
            for (var key in cardInfo) {
                if (cardInfo[key])
                    $scope.cards.push(key);
            }
        }

        $scope.changePaymentType = function () {
            if ($scope.formData.paymentType == 'Credit Card') {
                $scope.formData.payDate = new Date();
                $rootScope.resetTsys();
            } else {
                $scope.formData.payDate = new Date().addDays(30);
            }
        }

        $scope.ok = function () {
            var verificationResult = performVerification();
            var lastDate = null;

            if (verificationResult === true) {
                var promise = $q.when(true);
                var fd = $scope.formData;

                $rootScope.safeApply(function () {
                    $scope.disableOkButton = true;
                });

                if (fd.paymentType == 'Credit Card') {
                    if (fd.numberOfInstallments) {
                        fd.payDate = new Date();
                    }

                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Pledge'
                    });

                    if (!disposition) {
                        disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                            name: 'Pledge V2'
                        });
                    }

                    if ($scope.formData.splitPayments) {
                        // generate all the future payments
                        var payments = []
                        for (let i = 0; i < $scope.payments.values.length; i++) {
                            var payment = $scope.payments.values[i];
                            payments.push({
                                amount: payment.amount,
                                paymentDate: payment.date,
                                isPaid: false,
                                status: 'pending',
								source: 'installment plan',
                                leadId: lead.id,
                                userId: $rootScope.loggedInUser.id,
                                clientId: client.id,
                                campaignId: agentSession.currentCallResult.campaignId,
                                callresultId: agentSession.currentCallResult.id
                            })
                            lastDate = payment.date
                        }
                        promise = $http.post(APP_SETTINGS.BASE_API_URL + 'paymentlog', payments)
                    } else {
                        if ($scope.digits) $scope.digits = $scope.digits.trim();
                        promise = $http.post(APP_SETTINGS.BASE_API_URL + 'callresults/' + agentSession.currentCallResult.id + '/sale', {
                            amount: $scope.formData.giftAmount,
                            digits: $scope.digits,
                            clientId: client.id
                        })
                        // take payment now
                    }
                } else {
                    fd.numberOfInstallments = null;
                    fd.installmentNotes = null;
                    fd.creditCardNumber = null;
                    fd.creditCardType = null;
                    fd.creditCardPin = null;
                    fd.creditCardDate = null;

                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Pledge Invoice'
                    });
                }

                promise.then(response => {
                    if (response && response.data) {
                        if (!response.data.Success) {
                            $rootScope.safeApply(function () {
                                $scope.disableOkButton = false;
                            });
                            var verificationMsg = response.data.error
                            return SweetAlert.swal({
                                title: "Error Processing Payment",
                                text: verificationMsg
                            });
                        }
                    }
                    if (client.defaultInvoiceType == 'email' && $scope.lead.email && !$scope.verify.sendByMail) {
                        $scope.formData.paymentMethod = 'Email';
                    } else if (client.paperInvoices) {
                        $scope.formData.paymentMethod = 'Paper';
                    }

                    var result = {};
                    result.giftAmount = fd.giftAmount;
                    result.freeTickets = fd.freeTickets;
                    result.paymentType = fd.paymentType;
                    result.creditCardType = fd.creditCardType;
                    result.creditCardNumber = fd.creditCardNumber;
                    result.creditCardExpDate = fd.creditCardDate;
                    result.creditCardSecurityCode = fd.creditCardPin;
                    result.installmentNotes = fd.installmentNotes;
                    result.requiresFollowUp = fd.requiresFollowUp;
                    result.payDate = fd.payDate;
                    result.decisionMaker = fd.decisionMaker;
                    result.giftMatchingCompany = fd.giftMatchingCompany;
                    result.declineBenefits = fd.declineBenefits;
                    result.invoiceMethod = fd.paymentMethod;
                    result.grandTotal = fd.giftAmount;
                    result.numberOfInstallments = fd.numberOfInstallments;
                    result.newMembershipCard = fd.newMembershipCard;
                    result.useExistingCreditCard = fd.useExistingCC;
                    if (result.useExistingCreditCard && result.paymentType == 'Credit Card') {
                        if (!result.creditCardDate) result.creditCardDate = lead.existingCCExp;
                        result.creditCardType = lead.existingCCType;
                        result.creditCardNumber = lead.existingCCDigits;
                    }

                    var invoice = new Invoice();
                    invoice.callresultId = agentSession.currentCallResult.id;
                    invoice.grandTotal = result.grandTotal;
                    invoice.amountRemaining = result.paymentType != 'Credit Card' ? result.grandTotal : 0;
                    invoice.deliveryMethod = fd.paymentMethod;
                    invoice.dueDate = fd.payDate;
                    invoice.requestCount = 0;
                    invoice.invoiceType = result.paymentType;
                    invoice.clientId = agentSession.currentCampaignStage.campaign.client.id;
                    invoice.campaignId = agentSession.currentCampaignStage.campaign.id;
                    invoice.leadId = lead.id;
                    invoice.sendInvoice = result.paymentType != 'Credit Card';

                    invoice.$save().then(function () {
                        if (response && response.data && response.data.payment) {
                            $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/' + response.data.payment.id, {
                                invoiceId: invoice.id
                            }).catch(angular.noop)
                        } else {
                            $http.put(APP_SETTINGS.BASE_API_URL + 'paymentlog/callresult/' + agentSession.currentCallResult.id, {
                                invoiceId: invoice.id
                            }).catch(angular.noop)
                        }
                    }).catch(function (err) { });


                    $uibModalInstance.close({
                        disposition: disposition,
                        callResultData: result,
                        lastDate: lastDate,
                        wrapUpNotes: $scope.notes
                    });
                }).catch(err => {
                    $rootScope.safeApply(function () {
                        $scope.disableOkButton = false;
                    });
                    console.log(err)
                    if (err) {
                        var verificationMsg = err.data
                        SweetAlert.swal({
                            title: "Error Processing Payment",
                            text: verificationMsg
                        });
                    }
                })


            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.saveCard = function () {
            $scope.digits = undefined;
            $http.post(APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/cardtoken', $rootScope.cardtoken).then(function (response) {
                $scope.validtoken = true;
                $scope.tokenerror = '';
                $scope.digits = $rootScope.cardtoken.cvv2;

                $scope.cardToken = response.data.token;
                if ($scope.cardToken.maskedCardNumber) {
                    $scope.cardToken.cardNumber = "************" + $scope.cardToken.maskedCardNumber;
                }
                $rootScope.clearTsys();
            }, function (error) {
                $scope.validtoken = false;
                $scope.tokenerror = error.data.error;
            })
        };

        $scope.clearCard = function () {
            $http.delete(APP_SETTINGS.BASE_API_URL + 'leads/' + $scope.lead.id + '/cardtoken').catch(angular.noop)
            $scope.cardToken = {};
            $scope.validtoken = false;
            $scope.tokenerror = 'No Card on File';
            $rootScope.resetTsys();
        };

        $scope.auth = function () {
            var body = {
                amount: $scope.amount,
                creditCardNumber: $scope.creditCard.number,
                creditCardDate: $scope.creditCard.expiry,
                creditCardPin: $scope.creditCard.pin
            }

            $scope.response = '';
            $scope.error = '';

            var request = {
                method: 'POST',
                url: APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/authpayment',
                headers: {
                    'Content-Type': 'application/json'
                },
                data: body
            }
            $http(request).then(function (response) {
                if (response.data.Success === false) {
                    $scope.error = response.data.message
                } else {
                    if (response.data.SaleResponse.status === 'PASS') {
                        $scope.response = response.data.SaleResponse.responseMessage
                    } else {
                        $scope.error = response.data.SaleResponse.responseMessage
                    }
                }
            }, function (response) {
                $scope.error = response.data.message
            })
        };

        $scope.updatePaymentCount = function () {
            try {
                $scope.payments.values = []
                var range = Math.ceil($scope.formData.giftAmount / $scope.payments.count);
                $scope.payments.total = $scope.formData.giftAmount;
                $scope.payments.difference = 'correct amount';
                var total = 0, date = moment($scope.formData.initialPaymentDate);
                for (var i = 0; i < $scope.payments.count; i++) {
                    var amount;
                    if ((total + range) < $scope.formData.giftAmount)
                        amount = range;
                    else
                        amount = $scope.formData.giftAmount - total;

                    if (amount < 0) amount = 0;

                    $scope.payments.values.push({
                        amount, date: moment(date).toDate()
                    });

                    total += range

                    date = date.add($scope.payments.every, $scope.payments.unit);
                }
            } catch (err) {
                console.log(err)
            }
        };

        $scope.updatePayment = function () {
            var total = 0
            for (let i = 0; i < $scope.payments.values.length; i++) {
                total += $scope.payments.values[i].amount;
            }
            $scope.payments.total = total
            try {
                var difference = total - $scope.formData.giftAmount;
                if (difference === 0)
                    $scope.payments.difference = 'correct amount'
                else if (difference > 0)
                    $scope.payments.difference = '$' + difference + ' too much'
                else
                    $scope.payments.difference = '$' + difference + ' too little'
            }
            catch (err) {
                console.log(err)
            }

            $scope.paymentsBroken = !!difference;
        };

        $scope.fixPayments = function (index) {
            var difference = $scope.formData.giftAmount - $scope.payments.total;
            $scope.payments.values[index].amount += difference;
            $scope.updatePayment();
        };

        $scope.openDate = function (index, $event) {
            $event.preventDefault();
            $event.stopPropagation();
            // index += '';
            $scope.dates[index] = true;
            for (var prop in $scope.dates) {
                if (prop != index) {
                    $scope.dates[prop] = false;
                }
            }
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.minDate = new Date();
        $scope.maxDate = new Date().addDays(90);
        $scope.invoiceMaxDate = new Date().addDays(30);

        $scope.format = 'dd-MMMM-yyyy';

        $scope.popup1 = {
            opened: false
        };

        $scope.openDatePicker = function () {
            $scope.popup1.opened = true;
        };

        function performVerification() {
            var errors = [];
            var fd = $scope.formData;

            if (!fd.giftAmount || (fd.giftAmount !== fd.verifyGiftAmount)) {
                errors.push('Gift amount must be verified and match.');
            }

            if (!fd.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (fd.paymentType === 'Credit Card') {
                if ($scope.tokenerror) {
                    errors.push($scope.tokenerror)
                }
            }

            if (!$scope.verify.giftAmount) {
                errors.push('Please confirm you have verified the gift amount.');
            }

            if (!$scope.confirmedLeadDetails) {
                errors.push('Please confirm the donor details.');
            }

            if (!$scope.verify.emailAddress) {
                errors.push('Please confirm you have asked the donor for their email address.');
            }

            if (!$scope.verify.invoiceDelivery && fd.paymentMethod === 'Invoice' && $scope.lead.email && !$scope.verify.sendByMail) {
                errors.push('Please confirm you have informed the donor about the delivery method for the invoice (email / postal).');
            }

            if ($scope.paymentsBroken) {
                errors.push('Split payments do not add up.')
            }

            if ($scope.formData.splitPayments) {
                if ($scope.payments.every > 3 || $scope.payments.every < 1) {
                    errors.push('Payments must be with 1-3 months of each other')
                }
            }

            if ($scope.formData.splitPayments && $scope.cardToken) {
                var lastDate = $scope.payments.values[$scope.payments.values.length - 1].date;
                var dateErr = $rootScope.checkCardDate(lastDate, $scope.cardToken.expiry || $scope.cardToken.expirationDate);
                if (dateErr) {
                    errors.push(dateErr);
                }
            }

            return (errors.length ? errors : true);
        }
    });