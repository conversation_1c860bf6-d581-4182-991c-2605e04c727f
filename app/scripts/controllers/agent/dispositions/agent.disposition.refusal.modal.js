'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('RefusalDispositionModalCtrl', function ($scope, $uibModalInstance, $uibModal, disposition, reasons, moment, agentSession, lead, SweetAlert, _) {
        // Set form options and defaults
        $scope.notes = '';

        $scope.verify = {
            contactDetails: false
        };

        $scope.lead = lead;
        $scope.formData = {};
        var standardRefusals = [];
        var exceptionRefusals = [];

        if (agentSession.currentCampaignStage.campaign.campaigntype.name === 'Telefunding') {
            standardRefusals = _.where(reasons, {
                exception: false,
                telefunding: true
            });

            exceptionRefusals = _.where(reasons, {
                exception: true,
                telefunding: true
            });

            if (agentSession.currentCampaignStage.name !== '2nd Appeal') {
                exceptionRefusals.push({
                    name: 'Already gave this fiscal year'
                })
            }
        } else {
            standardRefusals = _.where(reasons, {
                exception: false,
                telemarketing: true
            });

            exceptionRefusals = _.where(reasons, {
                exception: true,
                telemarketing: true
            });

            if (agentSession.currentCampaignStage.name !== '2nd Appeal') {
                exceptionRefusals.push({
                    name: 'Already subscribed this season'
                })
            }
        }

        $scope.refusalReasons = standardRefusals.concat(exceptionRefusals);

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function (Lead) {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.confirmedLeadDetails = true;
            })
        };

        $scope.ok = function () {
            var verificationResult = performVerification();


            if (verificationResult === true) {
                if (_.findWhere(standardRefusals, {
                    name: $scope.refusalReason.name
                })) {
                    //standard refusal
                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Refusal'
                    });
                } else {
                    //exception refusal
                    disposition = _.findWhere(agentSession.currentCampaignStage.dispositions, {
                        name: 'Exception Refusal'
                    });

                }
                var result = {
                    refusalReason: $scope.refusalReason.name,
                    decisionMaker: $scope.formData.decisionMaker
                };

                $uibModalInstance.close({
                    disposition: disposition,
                    wrapUpNotes: $scope.notes,
                    callResultData: result
                });
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        function performVerification() {
            var errors = [];

            if (!$scope.formData.decisionMaker) {
                errors.push('Decision Maker must be filled in.');
            }

            if (!$scope.refusalReason) {
                errors.push('Please select a refusal reason.');
            }

            return (errors.length ? errors : true);
        }
    });