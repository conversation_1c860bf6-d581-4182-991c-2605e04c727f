'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('CallbackDispositionModalCtrl', function ($scope, $rootScope, $uibModalInstance, $uibModal, disposition, moment, lead, Callback, agentSession, client, activeDiallingNumber, Lead, SweetAlert) {
        $scope.callbackDate = new Date().addDays(1);
        $scope.notes = '';

        // $scope.timeFrom = moment({
        //     hour: moment().tz(client.timezone).hour(),
        //     minute: 0
        // }).toDate();

        $scope.hours = [];
        for (var i = 0; i < 24; i++) {
            if ((i + '').length == 1) {
                $scope.hours.push('0' + '' + i);
            } else {
                $scope.hours.push('' + i);
            }
        }

        $scope.minutes = [];
        for (var i = 0; i < 60; i += 5) {
            if ((i + '').length == 1) {
                $scope.minutes.push('0' + '' + i);
            } else {
                $scope.minutes.push('' + i);
            }
        }

        var currentHour = moment().tz(client.timezone).hour() + '';
        if (currentHour.length == 1) {
            $scope.startHour = '0' + '' + currentHour;
        } else {
            $scope.startHour = currentHour;
        }

        $scope.startMinute = '00';

        $scope.lead = lead;
        $scope.minDate = new Date();

        if (agentSession && agentSession.currentCampaignStage) {
            if (moment(agentSession.currentCampaignStage.endDate) < moment(new Date().addDays(agentSession.currentCampaignStage.campaign.callbackWindow || 14))) {
                $scope.maxDate = moment(agentSession.currentCampaignStage.endDate).toDate();
            } else {
                $scope.maxDate = new Date().addDays(agentSession.currentCampaignStage.campaign.callbackWindow || 14);
            }
        } else {
            $scope.maxDate = new Date().addDays(agentSession.currentCampaignStage.campaign.callbackWindow || 14);
        }

        $scope.phone = (activeDiallingNumber ? activeDiallingNumber.type : '');

        $scope.client = client;

        $scope.callbackEndDateTime;
        $scope.localCallbackStartDateTime;
        $scope.localCallbackEndDateTime;

        $scope.calcCallback = function () {
            var startTime = moment().tz(client.timezone).hour($scope.startHour).minute($scope.startMinute);
            $scope.callbackEndDateTime = angular.copy(startTime).add(2, 'hours').format('HH:mm');

            $scope.localCallbackStartDateTime = moment(startTime.toDate()).format('HH:mm');
            $scope.localCallbackEndDateTime = moment(startTime.toDate()).add(2, 'hours').format('HH:mm');
        };

        $scope.ok = function () {
            var verificationResult = performVerification();

            if (verificationResult === true) {
                if (moment($scope.callbackDate).format('DD-MM-YY') == moment().format('DD-MM-YY')) {
                    SweetAlert.swal({
                            title: "Alert",
                            text: "You are setting the callback for today, are you sure you want to do this?",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "Yes",
                            cancelButtonText: "No",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                        function (isConfirm) {
                            if (isConfirm) {
                                createCallBack();
                            }
                        });
                } else {
                    createCallBack();
                }
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        function createCallBack() {
            var timeDiff = parseInt(moment.tz(client.timezone).format('Z'));

            var start = moment($scope.callbackDate).format('YYYY-MM-DD');
            var end = moment($scope.callbackDate).format('YYYY-MM-DD');
            var startHour = parseInt($scope.startHour) - timeDiff;
            if (startHour > 23) {
                start = moment($scope.callbackDate).add(1, 'day').format('YYYY-MM-DD');
                end = moment($scope.callbackDate).add(1, 'day').format('YYYY-MM-DD');
                startHour = startHour - 24
            } else if (startHour < 0) {
                start = moment($scope.callbackDate).subtract(1, 'day').format('YYYY-MM-DD');
                end = moment($scope.callbackDate).subtract(1, 'day').format('YYYY-MM-DD');
                startHour = startHour + 24
            }
            var endHour = startHour + 2;

            startHour = '' + startHour;
            if (endHour == 24) endHour = 0;
            if (endHour == 25) endHour = 1;


            if (endHour == 0 || endHour == 1) {
                end = moment(start).add(1, 'day').format('YYYY-MM-DD');
            }

            endHour = '' + endHour;

            if (startHour.length == 1) startHour = '0' + startHour;
            if (endHour.length == 1) endHour = '0' + endHour;

            start = start + 'T' + startHour + ':' + $scope.startMinute + ':00';
            end = end + 'T' + endHour + ':' + $scope.startMinute + ':00';

            var callback = new Callback({
                startDateTime: start,
                endDateTime: end,
                phone: $scope.phone,
                agentId: agentSession.agentId,
                callresultId: agentSession && agentSession.currentCallResult ? agentSession.currentCallResult.id : null,
                clientId: agentSession.currentCampaignStage.campaign.clientId,
                leadId: lead ? lead.id : null,
                callAttemptJson: agentSession.currentCallResult.callAttemptJson,
                campaignId: agentSession && agentSession.currentCampaignStage && agentSession.currentCampaignStage.campaign ? agentSession.currentCampaignStage.campaign.id : null,
                deleted: false
            });

            callback.$save(function () {
                $uibModalInstance.close({
                    disposition: disposition,
                    wrapUpNotes: $scope.notes
                });
            });
        }

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.openDatePicker = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();

            $scope.datePickerOpened = true;
        };

        $scope.format = 'dd-MMMM-yyyy';

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
            })
        };

        function performVerification() {
            var errors = [];

            return (errors.length ? errors : true);
        }

        $scope.calcCallback();
    });