'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('InvoiceRequestedDispositionModalCtrl', function ($scope, $uibModalInstance, $uibModal, disposition, client, moment, agentSession, lead, invoices, _, Invoice, Lead, SweetAlert) {
        $scope.lead = lead;
        $scope.client = client;

        $scope.verify = {};

        //get the newest invoice that has an amount remaining greater than 0
        if (invoices.length) {
            $scope.invoice = invoices[invoices.length - 1];
            var count = invoices.length;
            while ($scope.invoice.amountRemaining == 0 && count) {
                count--;
                $scope.invoice = invoices[count];
            }
        } else {
            $scope.invoice = {};
        }

        $scope.numberOfInstallments = 0;
        if ($scope.invoice.callresult) {
            $scope.numberOfInstallments = $scope.invoice.callresult.numberOfInstallments;
        }

        $scope.failReason = '';
        if ($scope.invoice.paymentlogs && $scope.invoice.paymentlogs.length) {
            var lastPayment = $scope.invoice.paymentlogs.reverse().find(function (pl) {
                return !!pl.error && pl.error !== 'Previous Payment Failed'
            })
            if (lastPayment) {
                $scope.failReason = lastPayment.error
            }

            if (!$scope.numberOfInstallments) {
                $scope.numberOfInstallments = $scope.invoice.paymentlogs.filter(pl => pl.callresultId === $scope.invoice.callresult.id).length;
            }
        }

        $scope.originalAmount = $scope.invoice.callresult ? $scope.invoice.callresult.grandTotal : 0;


        $scope.ok = function () {
            var verificationResult = performVerification();

            if (verificationResult === true) {
                var result = {};

                $scope.invoice.requestCount++;
                $scope.invoice.dueDate = moment().add(30, 'days').toDate();
                $scope.invoice.sendInvoice = true;

                Invoice.update({
                    id: $scope.invoice.id
                }, $scope.invoice);

                result.invoiceMethod = $scope.invoice.deliveryMethod;
                result.payDate = $scope.invoice.dueDate;

                $uibModalInstance.close({
                    disposition: disposition,
                    callResultData: result,
                    wrapUpNotes: $scope.notes
                });
            } else {
                var verificationMsg = verificationResult.join('\n - ');
                SweetAlert.swal({
                    title: "Invalid",
                    text: 'Please correct the following error(s):\n\n - ' + verificationMsg
                });
            }
        };

        $scope.cancel = function () {
            $uibModalInstance.dismiss();
        };

        $scope.validateLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: $scope.lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return $scope.lead;
                    },
                    agentId: function () {
                        return agentSession.agentId;
                    }
                }
            });

            modalInstance.result.then(function (lead) {
                $scope.lead = lead;
                $scope.verify.confirmedLeadDetails = true;
            })
        };

        function performVerification() {
            var errors = [];

            if (!$scope.verify.leftToPay) {
                errors.push('Please confirm you have verified the amount left to pay.');
            }

            if (!$scope.verify.confirmedLeadDetails) {
                errors.push('Please confirm the donor details.');
            }

            return (errors.length ? errors : true);
        }
    })