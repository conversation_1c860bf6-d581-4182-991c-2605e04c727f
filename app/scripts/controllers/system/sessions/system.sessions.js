'use strict';

angular.module('dialerFrontendApp')
	.controller('SessionsCtrl', function ($window, $rootScope, $scope, $interval, users, agents, User, AgentSession, SweetAlert, moment, _) {
		$rootScope.pageTitle = 'System | Sessions';

		$scope.users = users;
		$scope.agents = agents;

		$scope.userSessions = [];
		$scope.agentSessions = [];

		var timerDetachers = [];

		timerDetachers.push(
			$interval(function () {
				getUserSessions();
			}, 5000)
		);

		timerDetachers.push(
			$interval(function () {
				getAgentSessions();
			}, 5000)
		);

		$scope.findUser = function (userId) {
			if (userId === 0) {
				return 'Superadmin';
			}

			var user = _.findWhere($scope.users, {
				id: userId
			});

			if (user) {
				return user.name;
			} else {
				return 'Unknown - please reload page';
			}
		};

		$scope.findAgent = function (agentId) {
			var agent = _.findWhere($scope.agents, {
				id: agentId
			});

			if (agent) {
				return agent.name;
			} else {
				return 'Unknown - please reload page';
			}
		};

		$scope.findAgentDevice = function (agentId) {
			var agent = _.findWhere($scope.agents, {
				id: agentId
			});

			if (agent && agent.device) {
				return agent.device.extension + (agent.device.server ? '@' + agent.device.server : '') + ' (' + agent.device.type + ')';
			} else {
				return 'Unknown - please reload page';
			}
		};

		$scope.findAgentUser = function (agentId) {
			var user = _.findWhere($scope.users, {
				agentId: agentId
			});

			if (user) {
				return user.id == 0 ? 'Superadmin' : user.name;
			} else {
				return 'Unknown - please reload page';
			}
		};

		$scope.epochTime = function (ticks) {
			return moment(ticks).format('MMM-DD-YYYY HH:mm:ss');
		};

		$scope.endAgentSession = function (session) {
			SweetAlert.swal({
					title: "End Session",
					text: "Are you user you want to end user session " + session.id + "!",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, end it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function (isConfirm) {
					if (isConfirm) {
						var user = _.findWhere($scope.users, {
							agentId: session.id
						});

						if (user) {
							User.logout({
								id: user.id
							});
						}

					}
				});
		};

		$scope.endUserSession = function (session) {
			SweetAlert.swal({
					title: "Delete Device",
					text: "Are you user you want to end agent session " + session.id + "!",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, end it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function (isConfirm) {
					if (isConfirm) {
						User.logout({
							id: session.id
						});
					}
				});
		};

		$scope.$on('$destroy', function () {
			window.onbeforeunload = undefined;

			for (var i = 0; i < timerDetachers.length; i++) {
				if (timerDetachers[i]) {
					$interval.cancel(timerDetachers[i]);
				}
			}
		});

		function getUserSessions() {
			User.getAllLoggedInUsers().$promise.then(function (sessions) {
				$scope.userSessions = sessions;
			});
		}

		function getAgentSessions() {
			AgentSession.query().$promise.then(function (sessions) {
				$scope.agentSessions = sessions;
			});
		}

		getUserSessions();
		getAgentSessions();
	})