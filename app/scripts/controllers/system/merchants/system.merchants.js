'use strict';

angular.module('dialerFrontendApp').controller('MerchantsCtrl', function ($window, $rootScope, $scope, $uibModal, $http, APP_SETTINGS, Client, SweetAlert, moment, _) {
    $rootScope.pageTitle = 'System | Merchants';

    $scope.merchants = [];
    $scope.testing = false;

    $scope.getMerchants = function () {
        $http.get(APP_SETTINGS.BASE_API_URL + 'merchant').then(response => {
            $scope.merchants = response.data
        })
    };
    $scope.getMerchants();

    $scope.$watchCollection('lastTsys', function (newValue, oldValue) {
        if (!$scope.testing || !newValue.event) return;
        $scope.testing = false;
        if (newValue.eventType === 'ErrorEvent') {
            SweetAlert.swal("Failure!", newValue.event.message, "error");
        } else if (newValue.event) {
            SweetAlert.swal("Success!", "You are ready to go!", "success");
        }
    });

    $scope.delete = function (merchant) {
        SweetAlert.swal({
            title: "Delete Merchant",
            text: "Are you user you want to delete " + (merchant.client ? merchant.client.name : 'this merchant') + "!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, cancel",
            closeOnConfirm: true,
            closeOnCancel: true
        },
            function (isConfirm) {
                if (isConfirm) {
                    $http.delete(APP_SETTINGS.BASE_API_URL + 'merchant/' + merchant.id).then(() => {
                        $scope.getMerchants();
                    })
                }
            });
    };

    $scope.test = function (merchant) {
        if ($scope.testing) return;
        $scope.testing = true;
        $rootScope.clearTsys();
        $rootScope.buildTsys(merchant.clientId);
        setTimeout(function () {
            if ($scope.testing) {
                $scope.testing = false;
                SweetAlert.swal("Success!", "You are ready to go!", "success");
            }
        }, 1500)
    };

    $scope.edit = function (merchant) {
        var modalInstance = $uibModal.open({
            animation: true,
            keyboard: false,
            templateUrl: 'views/system/merchants/system.merchants.edit.html',
            controller: 'MerchantsEditCtrl',
            resolve: {
                merchant: function () {
                    return merchant || { developerID: '003022G900' }
                },
                clients: function () {
                    return Client.query();
                }
            }
        });

        modalInstance.result.then(function () {
            $scope.getMerchants();
        });
    };

    $scope.generate = function (merchant) {
        var modalInstance = $uibModal.open({
            animation: true,
            keyboard: false,
            templateUrl: 'views/system/merchants/system.merchants.generate.html',
            controller: 'MerchantsGenerateCtrl',
            resolve: {
                merchant: function () {
                    return merchant || {}
                }
            }
        });

        modalInstance.result.then(function () {
            $scope.getMerchants();
        });
    };
})