'use strict';

angular.module('dialerFrontendApp').controller('MerchantsEditCtrl', function ($window, $uibModalInstance, $scope, $http, APP_SETTINGS, merchant, clients) {
    $scope.merchant = angular.copy(merchant);
    $scope.clients = clients;

    $scope.save = function () {
        if ($scope.merchant.id) {
            $http.put(APP_SETTINGS.BASE_API_URL + 'merchant/' + $scope.merchant.id, $scope.merchant).then(function () {
                $uibModalInstance.close();
            })
        } else {
            $http.post(APP_SETTINGS.BASE_API_URL + 'merchant', $scope.merchant).then(function () {
                $uibModalInstance.close();
            })
        }
    };

    $scope.cancel = function () {
        $uibModalInstance.close();
    };
})