'use strict';

angular.module('dialerFrontendApp').controller('MerchantsGenerateCtrl', function ($window, $uibModalInstance, $scope, $http, APP_SETTINGS, merchant) {
    $scope.userID = '';
    $scope.password = '';
    $scope.merchant = merchant;


    $scope.save = function () {
        if ($scope.merchant.id) {
            $http.put(APP_SETTINGS.BASE_API_URL + 'merchant/' + $scope.merchant.id + '/refresh', {
                userID: $scope.userID,
                password: $scope.password
            }).then(function (result) {
                $uibModalInstance.close(result);
            })
        }
    };

    $scope.cancel = function () {
        $uibModalInstance.close();
    };
})