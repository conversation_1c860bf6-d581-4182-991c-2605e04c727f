'use strict';

angular.module('dialerFrontendApp')
	.controller('EmailCtrl', function ($window, $rootScope, $scope, $state, email, System) {
		$rootScope.pageTitle = 'System | Email';
		$scope.email = email.value || {
			host: '',
			port: 22,
			secure: false,
			from: '',
			auth: {
				user: '',
				pass: ''
			}
		};

		$scope.save = function () {
			if($scope.email.service) {
				delete $scope.email.host;
				delete $scope.email.port;
				delete $scope.email.secure;
			}

			System.update({
				key: 'email'
			}, {
				value: $scope.email
			}).$promise.then(function (result) {
				$state.go('supervisor.campaigns');
			})
		}
	})