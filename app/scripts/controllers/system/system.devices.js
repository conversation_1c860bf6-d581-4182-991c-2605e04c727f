'use strict';

angular.module('dialerFrontendApp')
	.controller('DevicesCtrl', function ($window, $rootScope, $scope, devices, SweetAlert) {
		$rootScope.pageTitle = 'System | Devices';

		$scope.devices = devices;

		$scope.deleteDevice = function (device) {
			SweetAlert.swal({
					title: "Delete Device",
					text: "Are you user you want to delete " + device.extension + "!",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, delete it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function (isConfirm) {
					if (isConfirm) {
						device.$delete(function () {
							$scope.devices = $scope.devices.filter(function (a) {
								return a.id && a.id !== device.id;
							});
						});
					}
				});
		};
	})