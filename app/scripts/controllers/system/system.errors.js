'use strict';

angular.module('dialerFrontendApp')
	.controller('ErrorsCtrl', function ($window, $rootScope, $scope, errors, SweetAlert) {
		$rootScope.pageTitle = 'System | Errors';

		$scope.errors = errors;

		$scope.deleteError = function (error) {
			SweetAlert.swal({
					title: "Delete Device",
					text: "Are you user you want to delete this error!",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, delete it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function (isConfirm) {
					if (isConfirm) {
						error.$delete(function () {
							$scope.errors = $scope.errors.filter(function (a) {
								return a.id && a.id !== error.id;
							});
						});
					}
				});
		};
	})