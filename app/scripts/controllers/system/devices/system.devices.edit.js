'use strict';

angular.module('dialerFrontendApp')
	.controller('EditDevicesCtrl', function ($window, $state, $rootScope, $scope, Device, editItem) {
		$scope.update = !!editItem.id;
		$scope.editItem = editItem;
		$rootScope.pageTitle = 'System | ' + ($scope.update ? 'Update' : 'Create') + ' Device';

		$scope.phones = [{
			type:'MockPhone',
			name: 'Mock Phone'
		}, {
			type:'WebRTC',
			name: 'WebRTC Phone'
		}, {
			type:'Verto',
			name: 'Verto Web Phone'
		}, {
			type:'Softphone',
			name: 'SIP Softphone'
		}];

		$scope.ok = function () {
			if($scope.update) {
				$scope.editItem.$update().then(function () {
					$state.go('system.devices');
				})					
				
			} else {
				$scope.editItem.$save().then(function () {
					$state.go('system.devices');	
				})
			}			
		};

		$scope.cancel = function () {
			$state.go('system.devices');	
		}
	})