'use strict'

angular.module('dialerFrontendApp')
	.controller('CampaignStageFlowModalEditCtrl', function ($scope, $uibModalInstance, campaigns, editItem, workflows, CampaignStage, moment, _) {
		$scope.stages = [];
		campaigns.forEach(function (campaign) {
			if(moment(campaign.endDate) > moment()) {
				campaign.campaignstages.forEach(function (stage) {
					$scope.stages.push({
						id: stage.id,
						name: stage.name + ' - '  + campaign.name
					})	
				})		
			}
					
		})

		$scope.editItem = editItem;
		$scope.workflows = workflows;

		for (var i = 0; i < $scope.editItem.dispositions.length; i++) {
			var flow = _.find($scope.workflows, function (workflow) {
				return workflow.dispositionId == $scope.editItem.dispositions[i].id;
			});

			if (flow) {
				$scope.editItem.dispositions[i].destination = flow.transitionToCampaignStageId;
				$scope.editItem.dispositions[i].dontContactLeadForHours = (flow.dontContactLeadForHours === undefined || flow.dontContactLeadForHours === null) ? 24 : flow.dontContactLeadForHours;
				$scope.editItem.dispositions[i].transitionCutOffDate = flow.transitionCutOffDate;
				$scope.editItem.dispositions[i].transitionCutOffDateDispositionId = flow.transitionCutOffDateDispositionId;
				if(flow.dontContactLeadForHours % 24 == 0) {
					$scope.editItem.dispositions[i].dontContactLeadForHours = flow.dontContactLeadForHours / 24;
					$scope.editItem.dispositions[i].delayType = 'days';
				}
			}
		};

		$scope.save = function () {
			var workflows = _.map($scope.editItem.dispositions, function (disposition) {
				return {
					stageId: editItem.id,
					dispositionId: disposition.id,
					destination: disposition.destination || null,
					transitionCutOffDate: disposition.transitionCutOffDate,
					transitionCutOffDateDispositionId: disposition.transitionCutOffDateDispositionId,
					dontContactLeadForHours: disposition.delayType ? 24 * disposition.dontContactLeadForHours : disposition.dontContactLeadForHours
				};
			});

			workflows.forEach(function (workflow) {
				if (!workflow.transitionCutOffDate) {
					workflow.transitionCutOffDate = null;
					workflow.transitionCutOffDateDispositionId = null;
				}
			})

			CampaignStage.setWorkflows({
				id: $scope.editItem.id
			}, workflows).$promise.then(function (res) {
				$uibModalInstance.close($scope.editItem);
			});
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};
	});