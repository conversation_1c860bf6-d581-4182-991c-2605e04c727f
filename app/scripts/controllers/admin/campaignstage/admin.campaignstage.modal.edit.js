'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('CampaignStageModalEditCtrl', function ($scope, $uibModalInstance, Campaign, CampaignStage, skills, agents, dispositions, editItem, _) {
    $scope.agents = agents;
    $scope.dispositions = dispositions;
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.stages = [];

    $scope.skills = skills;

    if ($scope.editItem.blacklistedSkills) {
      $scope.editItem.blacklistedSkills = JSON.parse($scope.editItem.blacklistedSkills);
    }

    Campaign.get({
      id: editItem.campaignId
    }).$promise.then(function (campaign) {
      $scope.campaign = campaign;
    });

    Campaign.getCampaignStages({
      id: editItem.campaignId
    }).$promise.then(function (stages) {
      $scope.stages = _.filter(stages, function (row) {
        return row.id !== editItem.id
      });
    });

    $scope.availableDispositions = _.filter($scope.dispositions, function (a) {
      return !_.find($scope.editItem.dispositions, {
        id: a.id
      });
    });
    $scope.selectedDispositions = angular.copy($scope.editItem.dispositions) || [];

    $scope.availableAgents = _.filter($scope.agents, function (a) {
      return !_.find($scope.editItem.agents, {
        id: a.id
      });
    });
    $scope.selectedAgents = angular.copy($scope.editItem.agents) || [];

    $scope.addAgentToSelected = function () {
      if ($scope.highlightedAvailableAgent && $scope.highlightedAvailableAgent.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableAgent.length; i++) {
          var agentToMove = _.findWhere($scope.availableAgents, {
            id: $scope.highlightedAvailableAgent[i]
          });

          if (agentToMove) {
            $scope.availableAgents = _.without($scope.availableAgents, agentToMove);
            $scope.selectedAgents.push(agentToMove);
          }
        }
      }
    };

    $scope.addAgentToAvailable = function () {
      if ($scope.highlightedSelectedAgent && $scope.highlightedSelectedAgent.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedAgent.length; i++) {
          var agentToMove = _.findWhere($scope.selectedAgents, {
            id: $scope.highlightedSelectedAgent[i]
          });

          if (agentToMove) {
            $scope.selectedAgents = _.without($scope.selectedAgents, agentToMove);
            $scope.availableAgents.push(agentToMove);
          }
        }
      }
    };

    $scope.addDispositionToSelected = function () {
      if ($scope.highlightedAvailableDisposition && $scope.highlightedAvailableDisposition.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableDisposition.length; i++) {
          var dispositionToMove = _.findWhere($scope.availableDispositions, {
            id: $scope.highlightedAvailableDisposition[i]
          });

          if (dispositionToMove) {
            $scope.availableDispositions = _.without($scope.availableDispositions, dispositionToMove);
            $scope.selectedDispositions.push(dispositionToMove);
          }
        }
      }
    };

    $scope.addDispositionToAvailable = function () {
      if ($scope.highlightedSelectedDisposition && $scope.highlightedSelectedDisposition.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedDisposition.length; i++) {
          var dispositionToMove = _.findWhere($scope.selectedDispositions, {
            id: $scope.highlightedSelectedDisposition[i]
          });

          if (dispositionToMove) {
            $scope.selectedDispositions = _.without($scope.selectedDispositions, dispositionToMove);
            $scope.availableDispositions.push(dispositionToMove);
          }
        }
      }
    };

    $scope.save = function () {
      $scope.editItem.agents = $scope.selectedAgents;
      $scope.editItem.dispositions = $scope.selectedDispositions;

      if ($scope.editItem.blacklistedSkills) {
        $scope.editItem.blacklistedSkills = JSON.stringify($scope.editItem.blacklistedSkills);
      }

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $uibModalInstance.close($scope.editItem);
        });
      } else {
        $scope.editItem.$save(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };

    $scope.openEndDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.startDateOpened = false;
      $scope.endDateOpened = true;
    };

    $scope.openStartDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.endDateOpened = false;
      $scope.startDateOpened = true;
    };

    $scope.mindate = new Date();

    $scope.format = 'dd-MMMM-yyyy';
  });