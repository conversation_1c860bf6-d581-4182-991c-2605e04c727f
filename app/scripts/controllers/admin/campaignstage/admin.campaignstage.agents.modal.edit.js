'use strict'

angular.module('dialerFrontendApp')
	.controller('CampaignStageAgentsModalEditCtrl', function ($scope, $uibModal, $uibModalInstance, agents, campaignStageAgents, editItem, skills, CampaignStage, _) {
		$scope.availableSkills = skills;
		$scope.selectedAgents = [];

		$scope.campaignStageAgents = _.map(campaignStageAgents, function (csa) {
			if (csa.campaignstageagents && csa.campaignstageagents.agentskills) {
				csa.campaignstageagents.agentskills = JSON.parse(csa.campaignstageagents.agentskills);
			}
			else {
				csa.campaignstageagents = {
					agentskills: []
				};
			}

			return csa;
		});

		$scope.availableAgents = [];
		_.each(agents, function (aa) {
			var agent = _.findWhere($scope.campaignStageAgents, { id: aa.id });

			if (!agent) {
				aa.campaignstageagents = {
					agentskills: []
				};
				$scope.availableAgents.push(aa);
			}
		});

		$scope.addAgents = function (agents) {
			if (agents && agents.length) {
				$scope.campaignStageAgents = $scope.campaignStageAgents.concat(agents);
				$scope.availableAgents = _.filter($scope.availableAgents, function (aa) {
					return !_.findWhere(agents, { id: aa.id });
				});
				$scope.selectedAgents = [];
			}
		};

		$scope.removeAgent = function (agent) {
			$scope.campaignStageAgents = _.filter($scope.campaignStageAgents, function (csa) {
				return csa.id !== agent.id;
			});
			$scope.availableAgents.push(agent);
		};

		$scope.save = function () {
			$scope.campaignStageAgents = _.map($scope.campaignStageAgents, function (csa) {
				csa.campaignstageagents.agentskills = JSON.stringify(csa.campaignstageagents.agentskills);
				return csa;
			});

			CampaignStage.setAgents({ id: editItem.id }, $scope.campaignStageAgents)
				.$promise
				.then($uibModalInstance.close);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};
	});