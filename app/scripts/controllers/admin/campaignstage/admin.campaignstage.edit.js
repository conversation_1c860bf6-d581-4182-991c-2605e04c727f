'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('CampaignStageEditCtrl', function ($scope, $stateParams, $window, $location, Campaign, CampaignStage, dispositions, skills, editItem, _) {
    if (!editItem.campaignId) {
      editItem.campaignId = $stateParams.id;
    }

    $scope.dispositions = dispositions;
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.skills = skills;
    $scope.campaign = {};
    $scope.stages = []

    Campaign.get({
      id: editItem.campaignId
    }).$promise.then(function (campaign) {
      $scope.campaign = campaign;
    });

    Campaign.getCampaignStages({
      id: editItem.campaignId
    }).$promise.then(function (stages) {
      $scope.stages = _.filter(stages, function (row) {
        return row.id !== editItem.id
      });
    });

    if ($scope.editItem.blacklistedSkills) {
      $scope.editItem.blacklistedSkills = JSON.parse($scope.editItem.blacklistedSkills);
    }

    var returnUrl = '/admin/campaigns/' + editItem.campaignId + '/stages';

    $scope.availableDispositions = _.filter($scope.dispositions, function (a) {
      return !_.find($scope.editItem.dispositions, {
        id: a.id
      });
    });
    $scope.selectedDispositions = angular.copy($scope.editItem.dispositions) || [];

    $scope.addDispositionToSelected = function () {
      if ($scope.highlightedAvailableDisposition && $scope.highlightedAvailableDisposition.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableDisposition.length; i++) {
          var dispositionToMove = _.findWhere($scope.availableDispositions, {
            id: $scope.highlightedAvailableDisposition[i]
          });

          if (dispositionToMove) {
            $scope.availableDispositions = _.without($scope.availableDispositions, dispositionToMove);
            $scope.selectedDispositions.push(dispositionToMove);
          }
        }
      }
    };

    $scope.addDispositionToAvailable = function () {
      if ($scope.highlightedSelectedDisposition && $scope.highlightedSelectedDisposition.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedDisposition.length; i++) {
          var dispositionToMove = _.findWhere($scope.selectedDispositions, {
            id: $scope.highlightedSelectedDisposition[i]
          });

          if (dispositionToMove) {
            $scope.selectedDispositions = _.without($scope.selectedDispositions, dispositionToMove);
            $scope.availableDispositions.push(dispositionToMove);
          }
        }
      }
    };

    $scope.save = function () {
      $scope.editItem.dispositions = $scope.selectedDispositions;

      if ($scope.editItem.blacklistedSkills) {
        $scope.editItem.blacklistedSkills = JSON.stringify($scope.editItem.blacklistedSkills);
      }

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path(returnUrl);
          $window.scrollTo(0, 0);
        });
      } else {
        $scope.editItem.$save(function () {
          $location.path(returnUrl);
          $window.scrollTo(0, 0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path(returnUrl);
      $window.scrollTo(0, 0);
    };

    $scope.openEndDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.startDateOpened = false;
      $scope.endDateOpened = true;
    };

    $scope.openStartDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.endDateOpened = false;
      $scope.startDateOpened = true;
    };

    $scope.format = 'dd-MMMM-yyyy';
  });