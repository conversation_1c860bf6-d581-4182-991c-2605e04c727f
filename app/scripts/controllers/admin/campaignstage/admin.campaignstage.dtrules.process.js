'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CampaignStageDTRuleProcessCtrl', function ($scope, $q, $timeout, $uibModalInstance, campaignStage, skills, existingRules, _, CampaignStage, CampaignStageDateTimeRule) {
		$scope.campaignStage = campaignStage;
		$scope.processingComplete = false;

		var changes = $scope.changes = {
			toDelete: [],
			toCreate: [],
			toUpdate: [],
			toDecrement: [],
			toIncrement: []
		};

		skills.forEach(function (skill) {
			if (skill.campaignStageDTRules && skill.campaignStageDTRules.length) {
				skill.campaignStageDTRules.forEach(function (dtRule) {
					// Check how many attempts there were before changes
					var existingQuantity = 0;
					if (dtRule.id) {
						var existingRule = _.findWhere(existingRules, { id: dtRule.id });
						if (existingRule) {
							existingQuantity = existingRule.quantity;
						}
					}

					if (!dtRule.quantity && existingQuantity) {
						// If an existing rule has had it's attempts decreased to zero then flag for deletion and to remove the previous quantity of call attempts
						changes.toDelete.push({ rule: dtRule });
						changes.toDecrement.push({ rule: dtRule, amount: existingQuantity });
					}
					else {
						if (dtRule.quantity && !dtRule.id) {
							// If this is a new rule then create the campaigndatetimerule and flag for incremented call attempts
							changes.toCreate.push({ rule: dtRule });
							changes.toIncrement.push({ rule: dtRule, amount: dtRule.quantity });
						}
						else {
							// If this is an existing rule, determine what the difference is between previous and existing call attempt numbers
							if (dtRule.id) {
								var difference = dtRule.quantity - existingQuantity;

								if (difference > 0) {
									changes.toIncrement.push({ rule: dtRule, amount: difference });
									changes.toUpdate.push({ rule: dtRule });
								} else if (difference < 0) {
									changes.toDecrement.push({ rule: dtRule, amount: (difference * -1) });
									changes.toUpdate.push({ rule: dtRule });
								}
							}
						}
					}
				});
			}
		});

		if (!changes.toDelete.length && !changes.toCreate.length && !changes.toUpdate.length && !changes.toDecrement.length && !changes.toIncrement.length) {
			// hack to actually close the modal whilst the controller is still being created
			// - tried firing on opened and rendered promise but doesn't work, so need to do this to process on next tick
			// TODO: maybe check for changes in the edit controller and don't even launch the modal if there's nothing to do??
			$timeout(function () {
				$uibModalInstance.close($scope.changes);
			}, 1);
		}
		else {
			processChanges(changes);
		}

		function processChanges(changes) {
			var promises = [];
			var toDeletePromises = [];

			changes.toDelete.forEach(function (item) {
				toDeletePromises.push(deleteRule(item.rule))
			});
			changes.toCreate.forEach(function (item) {
				promises.push(createRule(item.rule));
			});
			changes.toUpdate.forEach(function (item) {
				promises.push(updateRule(item.rule));
			});
			changes.toDecrement.forEach(function (item) {
				promises.push(decrementCallAttempts(item.rule, item.amount));
			});
			changes.toIncrement.forEach(function (item) {
				promises.push(incrementCallAttempts(item.rule, item.amount));
			});

			$q.all(toDeletePromises).then(function () {
				$q.all(promises).then(function () {
					$scope.processingComplete = true;
				});
			})
		}

		function deleteRule(rule) {
			return CampaignStageDateTimeRule.delete({ id: rule.id }).$promise;
		}

		function updateRule(rule) {
			return CampaignStageDateTimeRule.update({ id: rule.id }, { quantity: rule.quantity }).$promise;
		}

		function createRule(rule) {
			return new CampaignStageDateTimeRule({
				subskillId: rule.subskillId,
				datetimerulesetId: rule.datetimerulesetId,
				quantity: rule.quantity,
				campaignstageId: campaignStage.id,
				startDate: rule.startDate,
				endDate: rule.endDate,
				uuid: rule.uuid
			})
				.$save();
		}

		function decrementCallAttempts(rule, amount) {
			return CampaignStage.removeCallAttempts({
				id: rule.campaignstageId
			}, {
				quantity: amount,
				dateTimeRule: rule.datetimeruleset,
				subskillId: rule.subskillId,
				uuid: rule.uuid
			});
		}

		function incrementCallAttempts(rule, amount) {
			return CampaignStage.addCallAttempts({
				id: rule.campaignstageId
			}, {
				quantity: amount,
				dateTimeRule: rule.datetimeruleset,
				subskillId: rule.subskillId,
				startDate: rule.startDate,
				endDate: rule.endDate,
				uuid: rule.uuid
			});
		}

		$scope.finish = function () {
			$uibModalInstance.close($scope.changes);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};
	});