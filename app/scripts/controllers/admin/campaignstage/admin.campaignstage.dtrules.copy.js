'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CampaignStageDTRuleCopyCtrl', function ($scope, rfc4122, $uibModalInstance, skills, skill) {
		$scope.skills = [];
		$scope.skill = skill;

		skills.forEach(function (skill) {
			if (skill.campaignStageDTRules && skill.campaignStageDTRules.length) {
				$scope.skills.push(skill);
			}
		})
		$scope.selectedSkill;

		$scope.save = function () {
			var newDtRules = angular.copy($scope.selectedSkill.campaignStageDTRules);
			newDtRules.forEach(function (rule) {
				rule.subskill = skill;
				rule.subskillId = skill.id;
				rule.uuid = rfc4122.v4();
				delete rule.id;
			})
			$uibModalInstance.close(newDtRules);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};
	});