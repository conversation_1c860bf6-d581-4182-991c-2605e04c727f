'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('CampaignStageDTRuleCreateCtrl', function ($scope, rfc4122, $uibModalInstance, editItem, dtRules) {
  		$scope.editItem = editItem;
  		$scope.dtRules = dtRules;

  		if ($scope.dtRules && $scope.dtRules.length) {
  			$scope.editItem.datetimeruleset = $scope.dtRules[0];
  		}

		$scope.openEndDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.startDateOpened = false;
			$scope.endDateOpened = true;
		};

		$scope.openStartDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();

			$scope.endDateOpened = false;
			$scope.startDateOpened = true;
		};

		$scope.decrementRuleQuantity = function () {
			$scope.editItem.quantity--;
		};

		$scope.incrementRuleQuantity = function () {
			$scope.editItem.quantity++;
		};

		$scope.format = 'dd-MMMM-yyyy';

		$scope.save = function () {
			if ($scope.editItem.endDate) {
				$scope.editItem.endDate.setHours(23);
				$scope.editItem.endDate.setMinutes(59);
				$scope.editItem.endDate.setSeconds(59);
			}
			$scope.editItem.uuid = rfc4122.v4();
			$scope.editItem.datetimerulesetId = $scope.editItem.datetimeruleset.id;
			$uibModalInstance.close($scope.editItem);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		};
  });