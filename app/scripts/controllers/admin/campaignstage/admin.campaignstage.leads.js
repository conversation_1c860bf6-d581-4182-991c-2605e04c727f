'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CampaignStageLeadsCtrl', function ($scope, $rootScope, $stateParams, $window, $location, $uibModal, CampaignStage, Callback, Client, Lead, Campaign, CampaignLeads, CallResult, SweetAlert, stage, _) {
		$rootScope.pageTitle = `${stage.campaign.name} | ${stage.name} | Leads`;
		$scope.leads = [];
		$scope.totalLeads = 0;
		$scope.leadsPerPage = 30;
		$scope.sortType = "lead.last_name";
		$scope.sortReverse = false;
		$scope.hideSearch = true;
		$scope.currentFilters = {};

		var campaignStages = [];
		Campaign.getCampaignStages({
			id: stage.campaignId
		}).$promise.then(function (stages) {
			campaignStages = stages;
		})

		$scope.pagination = {
			current: 1
		};

		$scope.displayedColumns = [{
			label: 'KAOS Id',
			field: 'lead.id'
		}, {
			label: 'Client Ref',
			field: 'lead.clientRef'
		}, {
			label: 'First Name',
			field: 'lead.first_name'
		}, {
			label: 'Last Name',
			field: 'lead.last_name'
		}, {
			label: 'Phone 1',
			field: 'lead.phone_home'
		}, {
			label: 'Phone 2',
			field: 'lead.phone_work'
		}, {
			label: 'Phone 3',
			field: 'lead.phone_mobile'
		}, {
			label: 'Phone 4',
			field: 'lead.phone_workmobile'
		}, {
			label: 'Contact Delay',
			field: 'lead.dontContactUntil'
		}];

		getResultsPage(1);

		$scope.pageChanged = function (newPageNumber) {
			getResultsPage(newPageNumber);
		};

		$scope.filterChanged = function (column) {
			$scope.sortType = column;
			$scope.sortReverse = !$scope.sortReverse;
			getResultsPage(1);
		};

		$scope.search = function () {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/campaign/admin.campaign.leads.search.html',
				controller: 'CampaignLeadsSearchModalCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					filters: function () {
						return $scope.currentFilters;
					},
					campaignId: function () {
						return stage.campaignId
					}
				}
			})

			modalInstance.result.then(function (filters) {
				$scope.filters = filters;
				$scope.pagination.current = 1;
				getResultsPage(1);
			})
		};

		$scope.editLead = function (lead) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/lead/admin.lead.edit.html',
				controller: 'LeadModalEditCtrl',
				backdrop: 'static',
				size: 'lg',
				keyboard: false,
				resolve: {
					editItem: function () {
						return Lead.get({
							id: lead.lead.id
						}).$promise;
					},
					existingItem: function () {
						return lead.lead;
					},
					agentId: function () {
						return $rootScope.loggedInUser.agentId || 0;
					}
				}
			});

			modalInstance.result.then(function (result) {
				var existing = _.find($scope.leads, function (lead) {
					return lead.lead.id = result.id
				})

				updateLeadInfo(existing.lead, result);
			})
		};

		$scope.removeLead = function (lead) {
			SweetAlert.swal({
				title: "Remove Lead from Campaign",
				text: "Are you user you want to remove " + lead.lead.first_name + "!",
				type: "warning",
				showCancelButton: true,
				confirmButtonColor: "#DD6B55",
				confirmButtonText: "Yes, remove them!",
				cancelButtonText: "No, cancel",
				closeOnConfirm: true,
				closeOnCancel: true
			},
				function (isConfirm) {
					if (isConfirm) {
						CampaignLeads.delete({
							id: stage.campaignId,
							leadId: lead.lead.id
						}).$promise.then(function () {
							$scope.leads = $scope.leads.filter(function (c) {
								return c.lead.id && c.lead.id !== lead.lead.id;
							});
						})
					}
				});
		};

		$scope.changeStage = function (lead) {
			//throw up a modal asking for the destination
			//on return call Campaign.moveLeadToCampaignStage
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/campaign/admin.campaign.leads.changestage.html',
				controller: 'CampaignLeadsChangeStageModalCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					currentStageId: function () {
						return stage.id;
					},
					stages: function () {
						return campaignStages;
					},
					editItem: function () {
						return lead;
					}
				}
			});

			modalInstance.result.then(function (result) {
				Campaign.moveLeadToCampaignStage({
					id: stage.campaignId
				}, {
					leadId: lead.lead.id,
					newCampaignStageId: result || null
				}).$promise.then(function (res) {
					$scope.leads = $scope.leads.filter(function (c) {
						return c.lead.id && c.lead.id !== lead.lead.id;
					});
				})
			})
		};

		$scope.createCallback = function (lead) {
			$uibModal.open({
				templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
				controller: 'SupervisorCallbackCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					callback: function () {
						return new Callback();
					},
					callresult: function () {
						return new CallResult();
					},
					lead: function () {
						return lead.lead;
					},
					campaign: function () {
						return Campaign.get({
							id: stage.campaignId
						}).$promise;
					},
					agents: function () {
						return Campaign.getAgents({
							id: stage.campaignId
						}).$promise;
					},
					client: function () {
						return Client.get({
							id: stage.campaign.clientId
						}).$promise;
					},
					stage: function () {
						return stage;
					}
				}
			});
		};

		$scope.friendly = function (field) {
			var result = _.findWhere($scope.displayedColumns, {
				field: 'lead.' + field
			});

			if (result) {
				return result.label;
			} else {
				return '';
			}
		};

		$scope.isEmptyObject = function (obj) {
			for (var prop in obj) {
				if (Object.prototype.hasOwnProperty.call(obj, prop)) {
					return false;
				}
			}
			return true;
		};

		$scope.generateDisposition = function (lead) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/lead/admin.lead.disposition.html',
				controller: 'LeadDispositionModalCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					lead: function () {
						return lead.lead;
					},
					stage: function () {
						return Campaign.getStageDispositions({
							id: stage.campaignId,
							stageid: stage.id
						}).$promise;
					},
					callResult: function () {
						return CallResult.save({
							leadId: lead.lead.id,
							campaignstageId: stage.id,
							campaignId: stage.campaignId,
							clientId: stage.campaign.clientId,
							callAttemptJson: JSON.stringify({
								startTime: '00:00:00',
								endTime: '23:59:59',
								monday: 1,
								tuesday: 1,
								wednesday: 1,
								thursday: 1,
								friday: 1,
								saturday: 1,
								sunday: 1,
								isCallback: true,
								randomSelector: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaaa',
								campaignstageId: stage.id,
								campaignId: stage.campaignId,
								leadId: lead.lead.id
							})
						}).$promise;
					},
					campaign: function () {
						return Campaign.get({
							id: stage.campaignId
						}).$promise;
					},
					client: function () {
						return Client.get({
							id: stage.campaign.clientId
						}).$promise;
					}
				}
			});

			modalInstance.result.then(function () {
				getResultsPage($scope.pagination.current);
			})
		}

		function updateLeadInfo(lead, info) {
			for (var prop in info) {
				if (lead.hasOwnProperty(prop)) {
					lead[prop] = info[prop];
				}
			}
		}

		function getResultsPage(pageNumber) {
			CampaignStage.getLeads({
				id: stage.id,
				page: pageNumber - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: $scope.filters
			}).$promise.then(function (result) {
				$scope.leads = result.data.leads;
				$scope.totalLeads = result.data.count;
			})
		}
	})