'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('CampaignStageDTRulesModalEditCtrl', function ($scope, $uibModal, $uibModalInstance, editItem, dateTimeRules, campaignStageSkills, campaignStageDTRules, CampaignStageDateTimeRule) {
    $scope.dtRules = dateTimeRules;
    $scope.editItem = editItem;
    $scope.campaignStageSkills = campaignStageSkills;
    $scope.existingRules = angular.copy(campaignStageDTRules);

    _.each($scope.campaignStageSkills, function (skill) {
      skill.campaignStageDTRules = _.where(campaignStageDTRules, {
        subskillId: skill.id
      });
    });

    $scope.addNewDateTimeRule = function () {
      var modalInstance = $uibModal.open({
        templateUrl: 'views/admin/datetimeruleset/admin.datetimeruleset.edit.html',
        controller: 'DateTimeRuleSetModalEditCtrl',
        backdrop: 'static',
        keyboard: false,
        resolve: {
          editItem: function (DateTimeRuleSet) {
            var newItem = new DateTimeRuleSet();

            newItem.startTime = new Date();
            newItem.startTime.setHours(9);
            newItem.startTime.setMinutes(0);
            newItem.startTime.setSeconds(0);

            newItem.endTime = new Date();
            newItem.endTime.setHours(17);
            newItem.endTime.setMinutes(0);
            newItem.endTime.setSeconds(0);

            return newItem;
          }
        }
      });

      modalInstance.result.then(function (dtRule) {
        $scope.dtRules.push(dtRule);
      });
    };

    $scope.decrementRuleQuantity = function (campaignStageDTRule) {
      if (campaignStageDTRule.quantity) {
        campaignStageDTRule.quantity--;
      } else {
        campaignStageDTRule.quantity = 0;
      }
    };

    $scope.incrementRuleQuantity = function (campaignStageDTRule) {
      if (campaignStageDTRule.quantity) {
        campaignStageDTRule.quantity++;
      } else {
        campaignStageDTRule.quantity = 1;
      }
    };

    $scope.addCallAttempts = function (skill) {
      // var dtRule = new CampaignStageDateTimeRule();
      // dtRule.editMode = true;
      // dtRule.quantity = 1;
      // dtRule.datetimeruleset = $scope.dtRules[0];
      // skill.campaignStageDTRules.push(dtRule);
      var modalInstance = $uibModal.open({
        templateUrl: 'views/admin/campaignstage/admin.campaignstage.dtrules.create.html',
        controller: 'CampaignStageDTRuleCreateCtrl',
        backdrop: 'static',
        keyboard: false,
        size: 'md',
        resolve: {
          editItem: function () {
            return {
              campaignstageId: $scope.editItem.id,
              subskill: skill,
              subskillId: skill.id,
              quantity: 1
            };
          },
          dtRules: function () {
            return $scope.dtRules;
          }
        }
      });

      modalInstance.result.then(function (dtRule) {
        skill.campaignStageDTRules.push(dtRule);
      });
    };

    $scope.copyFromRule = function (skill) {
      var modalInstance = $uibModal.open({
        templateUrl: 'views/admin/campaignstage/admin.campaignstage.dtrules.copy.html',
        controller: 'CampaignStageDTRuleCopyCtrl',
        backdrop: 'static',
        keyboard: false,
        resolve: {
          skills: function () {
            return $scope.campaignStageSkills;
          },
          skill: function () {
            return skill;
          }
        }
      });

      modalInstance.result.then(function (rules) {
        skill.campaignStageDTRules = rules;
      });
    };

    $scope.save = function () {
      var modalInstance = $uibModal.open({
        templateUrl: 'views/admin/campaignstage/admin.campaignstage.dtrules.process.html',
        controller: 'CampaignStageDTRuleProcessCtrl',
        backdrop: 'static',
        keyboard: false,
        size: 'md',
        resolve: {
          campaignStage: function () {
            return $scope.editItem;
          },
          skills: function () {
            return $scope.campaignStageSkills;
          },
          existingRules: function () {
            return $scope.existingRules;
          }
        }
      });

      modalInstance.result.then(function (results) {
        $uibModalInstance.close($scope.editItem);
      });
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });