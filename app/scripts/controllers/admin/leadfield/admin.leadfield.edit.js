'use strict';

angular.module('dialerFrontendApp')
  .controller('LeadFieldEditCtrl', function ($scope, $rootScope, $uibModalInstance, $location, $window, editItem) {
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;

    $scope.save = function () {
      if ($scope.update) {
        $scope.editItem.$update(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });