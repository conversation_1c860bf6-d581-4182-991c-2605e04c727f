'use strict';

angular.module('dialerFrontendApp')
	.controller('CallResultFieldEditCtrl', function ($scope, $q, $uibModal, $rootScope, $location, $window, editItem, callResultFieldGroups, callResultFieldTypes, _, CallResultFieldOption) {
		$rootScope.pageTitle = 'Configure | Call Result Field';

		$scope.update = !!editItem.id;
		$scope.editItem = editItem;

		$scope.editItem.callresultfieldoptions = editItem.callresultfieldoptions || [];

		if ($scope.update) {
			$scope.selectedFieldGroup = _.findWhere(callResultFieldGroups, { id: $scope.editItem.callresultfieldgroupId });
			$scope.selectedFieldType = _.findWhere(callResultFieldTypes, { id: $scope.editItem.callresultfieldtypeId });
		}

		$scope.callResultFieldGroups = callResultFieldGroups;
		$scope.callResultFieldTypes = callResultFieldTypes;

		$scope.$watch('selectedFieldGroup', function (newValue) {
			if (newValue) {
				$scope.editItem.callresultfieldgroupId = newValue.id;
			}
			else {
				$scope.editItem.callresultfieldgroupId = null;
			}
		});

		$scope.$watch('selectedFieldType', function (newValue) {
			if (newValue) {
				$scope.editItem.callresultfieldtypeId = newValue.id;
			}
			else {
				$scope.editItem.callresultfieldtypeId = null;
			}
		});

		var saveOptions = function () {
			if ($scope.editItem.callresultfieldoptions.length) {
				return $q(function (resolve, reject) {
					var completed = 0;
					var completedHandler = function () {
						completed++;
						if (completed === $scope.editItem.callresultfieldoptions.length) {
							resolve();
						}
					};

					_.each($scope.editItem.callresultfieldoptions, function (option) {
						if (option.id) {
							CallResultFieldOption.update(option)
								.$promise
								.then(completedHandler);
						}
						else {
							option.$save(completedHandler);
						}
					});
				});
			}
			else {
				return $q.when();
			}
		}

		$scope.deleteOption = function (option) {
			var callResultFieldOptionId = option.id;
			option.$delete(function () {
				$scope.editItem.callresultfieldoptions = $scope.editItem.callresultfieldoptions.filter(function (c) {
					return c.id && c.id !== callResultFieldOptionId;
				});
			});
		};

		$scope.addOption = function () {
			$scope.editItem.callresultfieldoptions.push(new CallResultFieldOption());
		};

		$scope.addNewGroup = function () {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/callresultfield/admin.callresultfieldgroup.edit.html',
				controller: 'CallResultFieldGroupEditCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					editItem: function (CallResultFieldGroup) {
							return new CallResultFieldGroup();
					}
				}
			});

			modalInstance.result.then(function (group) {
				$scope.callResultFieldGroups.push(group);
				$scope.selectedFieldGroup = group;
			});
		};

		$scope.save = function () {
			saveOptions().then(function () {
				if ($scope.update) {
					$scope.editItem.$update(function () {
						$location.path('/admin/callresultfields');
						$window.scrollTo(0,0);
					});
				}
				else {
					$scope.editItem.$save(function () {
						$location.path('/admin/callresultfields');
						$window.scrollTo(0,0);
					});
				}
			});
		};

		$scope.cancel = function () {
			$location.path('/admin/callresultfields');
			$window.scrollTo(0,0);
		};
	});