'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminCallResultFieldsCtrl', function ($scope, $rootScope, $state, $log, CallResultField) {
    $rootScope.pageTitle = 'Configure | Call Result Fields';

    var callResultFields = CallResultField.query(function () {
      $scope.callResultFields = callResultFields;
    });

    $scope.deleteField = function(field) {
      var callResultFieldId = field.id;
      field.$delete(function () {
        $scope.callResultFields = $scope.callResultFields.filter(function (c) {
          return c.id && c.id !== callResultFieldId;
        });
      });
    };
  });
