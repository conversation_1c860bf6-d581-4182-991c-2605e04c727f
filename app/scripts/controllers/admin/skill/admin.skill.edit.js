
'use strict';

angular.module('dialerFrontendApp')
  .controller('SkillEditCtrl', function ($scope, $rootScope, $location, $window, editItem, DateTimeRuleSet, _) {
    $rootScope.pageTitle = 'Configure | Lead Type';

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;

    if (!editItem.priority)
      editItem.priority = 99;



    $scope.save = function () {
      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path('/admin/skills');
          $window.scrollTo(0,0);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $location.path('/admin/skills');
          $window.scrollTo(0,0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/skills');
      $window.scrollTo(0,0);
    };
  });