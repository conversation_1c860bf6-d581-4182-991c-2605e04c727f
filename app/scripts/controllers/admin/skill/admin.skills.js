'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminSkillsCtrl', function ($scope, $rootScope, $log, $uibModal, SubSkill, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Lead Types';

    var skills = SubSkill.query(function () {
      $scope.skills = skills;
    });

    $scope.deleteSkill = function (skill) {
      SweetAlert.swal({
          title: "Delete Lead Type",
          text: "Are you user you want to delete " + skill.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var skillId = skill.id;
            skill.$delete(function () {
              $scope.skills = $scope.skills.filter(function (l) {
                return l.id && l.id !== skillId;
              });
            });
          }
        });
    };
  });