'use strict';

angular.module('dialerFrontendApp')
  .controller('ClientEditCtrl', function ($scope, $rootScope, $location, $window, $http, editItem, costings, campaigns, FileUploader, ClientCosting, APP_SETTINGS, SweetAlert, Client, $uibModal) {
    $rootScope.pageTitle = 'Configure | Clients';

    $scope.creditCards = ['American Express', 'Dankort', 'Diners', 'Discover', 'Electron', 'Interpayment', 'JCB', 'Maestro', 'Mastercard', 'Unionpay', 'Visa'];

    $scope.costings = costings;
    $scope.campaigns = campaigns;

    $scope.addCosting = function () {
      $scope.costings.push(new ClientCosting());
    }

    $scope.signatureUploader = new FileUploader({
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      }
    });

    $scope.clientLogoUploader = new FileUploader({
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      }
    });

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.timezones = moment.tz.names();
    if (!$scope.editItem.additionalInfo) {
      $scope.editItem.cards = {};
      for (var i = 0; i < $scope.creditCards.length; i++) {
        var card = $scope.creditCards[i];
        $scope.editItem.cards[card] = true;
      };
    } else {
      $scope.editItem.cards = JSON.parse($scope.editItem.additionalInfo);
    }

    if (!$scope.editItem.defaultInvoiceType) {
      $scope.editItem.defaultInvoiceType = "email";
    }

    $scope.resetPassword = function () {
      SweetAlert.swal({
        title: "Reset Client Report Password",
        text: "Are you user you want to reset the password for " + editItem.name + "!",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "Yes, reset it!",
        cancelButtonText: "No, cancel",
        closeOnConfirm: true,
        closeOnCancel: true
      },
      function (isConfirm) {
        if (isConfirm) {
          $http.put(APP_SETTINGS.BASE_API_URL + 'clients/' + editItem.id + '/resetpassword').catch(() => {});
        }
      });
      // $uibModal.open({
      //   animation: true,
      //   keyboard: false,
      //   templateUrl: 'views/admin/client/admin.client.resetpassword.html',
      //   controller: 'ClientResetPasswordCtrl',
      //   resolve: {
      //     clientId: function () {
      //       return editItem.id;
      //     }
      //   }
      // });
    };

    if (!$scope.editItem.timezone)
      $scope.editItem.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    $scope.demoInvoice = function () {
      Client.getDemoInvoice({
        id: $scope.editItem.id
      }).$promise.then(function (result) {
        if (result.error) {
          SweetAlert.swal("Error", result.error, "error");
        } else if (result.id) {
          $window.location.href = APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + result.id + '/pdf?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
        } else {
          SweetAlert.swal("Success", "Creation Complete", "success");
        }
      })
    };

    $scope.save = function () {
      if ($scope.signatureUploader.queue.length) {
        $scope.signatureUploader.queue[0].url = APP_SETTINGS.BASE_API_URL + 'clients/' + $scope.editItem.id + '/uploadsignature';
        $scope.signatureUploader.queue[0].upload();
      }

      if ($scope.clientLogoUploader.queue.length) {
        $scope.clientLogoUploader.queue[0].url = APP_SETTINGS.BASE_API_URL + 'clients/' + $scope.editItem.id + '/uploadlogo';
        $scope.clientLogoUploader.queue[0].upload();
      }

      $scope.costings.forEach(function (costing) {
        if (costing.id) {
          //update
          ClientCosting.update({
            id: costing.id
          }, costing);
        } else {
          //create
          costing.clientId = $scope.editItem.id;
          ClientCosting.save(costing);
        }
      })

      $scope.editItem.additionalInfo = JSON.stringify($scope.editItem.cards);
      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path('/admin/clients');
          $window.scrollTo(0, 0);
        });
      } else {
        $scope.editItem.$save(function () {
          $location.path('/admin/clients');
          $window.scrollTo(0, 0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/clients');
      $window.scrollTo(0, 0);
    };
  });