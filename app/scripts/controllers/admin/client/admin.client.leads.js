'use strict';

angular.module('dialerFrontendApp')
    .controller('ClientLeadCtrl', function ($scope, $rootScope, leads) {
        $rootScope.pageTitle = 'Configure | Client | Leads';

        $scope.leads = leads;

        $scope.displayedColumns = [{
            label: 'KAOS Id',
            field: 'id'
        }, {
            label: 'Client Ref',
            field: 'clientRef'
        }, {
            label: 'First Name',
            field: 'first_name'
        }, {
            label: 'Last Name',
            field: 'last_name'
        }, {
            label: 'Phone 1',
            field: 'phone_home'
        }, {
            label: 'Phone 2',
            field: 'phone_work'
        }, {
            label: 'Phone 3',
            field: 'phone_mobile'
        }, {
            label: 'Phone 4',
            field: 'phone_workmobile'
        }, {
            label: 'TF Lead Type',
            field: 'tfSubSkill.name'
        }, {
            label: 'TM Lead Type',
            field: 'tmSubSkill.name'
        }];
    });