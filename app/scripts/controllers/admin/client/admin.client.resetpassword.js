'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('ClientResetPasswordCtrl', function ($scope, clientId, $uibModalInstance, Client) {
        $scope.password = '';
        $scope.reset = function () {
            Client.get({
                id: clientId
            }).$promise.then(function (client) {
                client.reportPassword = $scope.password;
                client.$update();
                $uibModalInstance.close(true);
            })
        };		

        $scope.cancel = function () {
			$uibModalInstance.close(false);
		};
	});