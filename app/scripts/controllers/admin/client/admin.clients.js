'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminClientsCtrl', function ($scope, $rootScope, $state, $uibModal, clients, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Clients';
    $scope.clients = clients;

    $scope.deleteClient = function (client) {
      SweetAlert.swal({
          title: "Delete Client",
          text: "Are you user you want to delete " + client.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var clientId = client.id;
            client.$delete(function () {
              $scope.clients = $scope.clients.filter(function (c) {
                return c.id && c.id !== clientId;
              });
            });
          }
        });
    };
  });