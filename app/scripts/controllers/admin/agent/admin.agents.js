'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminAgentsCtrl', function ($scope, $rootScope, $uibModal, Agent, AgentSkills, _, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Agents';

    var agents = Agent.query(function () {
      $scope.agents = agents;
    });

    $scope.deleteAgent = function (agent) {
      SweetAlert.swal({
          title: "Delete Agent",
          text: "Are you user you want to delete " + agent.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var agentId = agent.id;
            agent.$delete(function () {
              $scope.agents = $scope.agents.filter(function (a) {
                return a.id && a.id !== agentId;
              });
            });
          }
        });
    };
  });