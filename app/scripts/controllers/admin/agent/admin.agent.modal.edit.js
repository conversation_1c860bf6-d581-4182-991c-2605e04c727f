'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AgentModalEditCtrl', function ($scope, $uibModalInstance, editItem, skills, agentStates, AgentSkills, _) {
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.skills = skills;
    $scope.agentStates = agentStates;

    $scope.skills = _.map($scope.skills, function (skill) {
      skill.checked = !!_.find($scope.editItem.skills, function (s) {
        return s.name === skill.name;
      });
      return skill;
    });

    $scope.save = function () {
      $scope.editItem.skills = _.filter($scope.skills, function (skill) {
        return skill.checked;
      });

      if ($scope.update) {
        $scope.editItem.$update(
          function () {
            $uibModalInstance.close($scope.editItem);
          },
          function (error) {
            console.log(error);
          }
        );
      }
      else {
        $scope.editItem.$save(
          function () {
            $uibModalInstance.close($scope.editItem);
          },
          function (error) {
            console.log(error);
          }
        );
      }      
    };

    $scope.openHireDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.startDateOpened = true;
      $scope.endDateOpened = false;
    };

    $scope.openTermDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.endDateOpened = true;
      $scope.startDateOpened = false;
    };

    $scope.format = 'dd-MMMM-yyyy';

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });
