'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AgentEditCtrl', function ($scope, $location, $rootScope, $window, $uibModal, editItem, agentStates, agents, devices, clients, User, _, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Agents';

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.agentStates = agentStates;
    $scope.agents = agents;
    $scope.clients = clients;

    if(!$scope.update) {
      editItem.campaignSwitchInterval = 30;
    }

    //remove devices already assigned to other agents (except the current agent if editing)
    $scope.agents.forEach(function (agent) {
      if (agent.id != editItem.id) {
        devices = _.reject(devices, function (device) {
          return device.id == agent.deviceId
        })
      }
    })
    $scope.devices = devices;

    $scope.extensionError = {
      text: ''
    };
    $scope.agentError = {
      text: ''
    };

    if ($scope.update) {
      $scope.agents = _.filter($scope.agents, function (agent) {
        if (agent.extension !== $scope.editItem.extension)
          return agent;
      })
    } else {
      $scope.editItem.autoDial = true;
      $scope.editItem.callPrepTime = 10;
    }

    if (!$scope.editItem.defaultAgentStateId) {
      $scope.editItem.defaultAgentStateId = _.findWhere($scope.agentStates, {
        name: 'Idle'
      }).id;
    }

    $scope.save = function () {
      if($scope.editItem.hireDate instanceof Date) {
        $scope.editItem.hireDate = new Date($scope.editItem.hireDate.setUTCHours(0, 0, 0, 0));
      }

      if($scope.editItem.termDate instanceof Date) {
        $scope.editItem.termDate = new Date($scope.editItem.termDate.setUTCHours(23, 59, 59, 0));
      }
      

      if (_.filter($scope.agents, function (agent) {
          if (agent.name == $scope.editItem.name)
            return agent;
        }).length > 0) {
        $scope.agentError.text = "Name Already Exists";
        return false;
      }

      if ($scope.update) {
        $scope.editItem.$update(function (agent) {
          promptForNewUser(agent);
        });
      } else {
        $scope.editItem.$save(function (agent) {
          promptForNewUser(agent);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/agents');
      $window.scrollTo(0, 0);
    };

    $scope.openHireDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.startDateOpened = true;
      $scope.endDateOpened = false;
    };

    $scope.openTermDate = function ($event) {
      $event.preventDefault();
      $event.stopPropagation();

      $scope.endDateOpened = true;
      $scope.startDateOpened = false;
    };

    $scope.format = 'dd-MMMM-yyyy';

    var promptForNewUser = function (agent) {
      User.query().$promise
        .then(function (users) {
          if (_.find(users, function (user) {
              return user.agentId == agent.id;
            })) {
            $location.path('/admin/agents');
            $window.scrollTo(0, 0);
          } else {
            //prompt to create a new user
            var modalInstance = $uibModal.open({
              animation: true,
              keyboard: false,
              templateUrl: 'views/common/messageboxes/dialogModal.html',
              controller: 'DialogModalCtrl',
              resolve: {
                options: function () {
                  return {
                    yes: 'Yes',
                    no: 'No',
                    title: 'Create User',
                    content: 'Do you want to create a user for this agent?'
                  }
                }
              }
            });

            modalInstance.result.then(function (result) {
              if (result) {
                var user = new User();

                user.username = agent.name.toLowerCase();
                user.password = Math.random().toString(36).slice(-8);
                user.name = agent.name;
                user.homeState = 'agent.dashboard';
                user.showStats = true;
                user.isAdmin = false;
                user.isSupervisor = false;
                if (agent.clientId) {
                  user.clientId = agent.clientId;
                  user.isClientAgent = true;
                } else {
                  user.isAgent = true;
                }
                user.agentId = agent.id;
                user.email = agent.email;
                user.firstLogin = true;

                user.$save().then(function (obj) {
                  User.sendPassword({
                    id: obj.id
                  });
                });
              }

              $location.path('/admin/agents');
              $window.scrollTo(0, 0);
            });
          }
        })


    };
  })