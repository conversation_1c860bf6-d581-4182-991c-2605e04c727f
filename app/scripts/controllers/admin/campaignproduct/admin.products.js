'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('CampaignProductsCtrl', function ($scope, $rootScope, $uibModal, campaign, Campaign, CampaignProduct, SweetAlert) {
    $rootScope.pageTitle = campaign.name + ' | Campaign Products';

    $scope.products = [];
    $scope.campaign = campaign;
    $scope.leads = [];
    $scope.totalProducts = 0;
    $scope.productsPerPage = 30;
    $scope.sortType = "order_by";
    $scope.sortReverse = false;
    $scope.pagination = {
      current: 1
    };


    $scope.uploadProducts = function () {
      var modalInstance = $uibModal.open({
        animation: true,
        keyboard: false,
        templateUrl: 'views/admin/campaignproducts/admin.products.upload.html',
        controller: 'UploadProductsModalCtrl',
        size: 'lg',
        resolve: {
          campaign: function () {
            return $scope.campaign;
          }
        }
      });

      modalInstance.result.then(function (result) {
        getResultsPage(1);
      });
    };

    $scope.deleteProduct = function (clientProduct) {
      SweetAlert.swal({
          title: "Delete Product",
          text: "Are you user you want to delete " + clientProduct.productCode + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var clientProductId = clientProduct.id;
            CampaignProduct.delete({
              id: clientProductId
            },function () {
              $scope.products = $scope.products.filter(function (c) {
                return c.id && c.id !== clientProductId;
              });
              $scope.totalProducts--;
            });
          }
        });
    };

    $scope.clearProducts = function () {
      SweetAlert.swal({
          title: "Clear Products",
          text: "Are you user you want to remove all products?\r\nNew products will need to be uploaded",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes",
          cancelButtonText: "No",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            Campaign.deleteProducts({
              id: campaign.id
            }).$promise.then(function (result) {
              console.log(result);
              $scope.products = [];
              $scope.totalProducts = 0;
            })
          }
        });
    };

    $scope.pageChanged = function (newPageNumber) {
      getResultsPage(newPageNumber);
    };

    $scope.filterChanged = function (field) {
      $scope.sortType = field;
      $scope.sortReverse = !$scope.sortReverse;
      $scope.pagination.current = 1;
      getResultsPage(1);
    };

    function getResultsPage(pageNumber) {
      Campaign.getProductsWithPaging({
        id: $scope.campaign.id,
        page: pageNumber - 1,
        orderby: $scope.sortType,
        dir: $scope.sortReverse ? 'DESC' : 'ASC'
      }).$promise.then(function (result) {
        $scope.products = result.data.products;
        $scope.totalProducts = result.data.count;
      })
    }

    getResultsPage(1);
  });