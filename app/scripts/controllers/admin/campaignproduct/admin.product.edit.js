'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignProductEditCtrl', function ($scope, $rootScope, $location, $window, editItem, campaign) {
    $rootScope.pageTitle = 'Configure | Campaign Products | Edit';

    $scope.campaign = campaign;
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.editItem.campaignId = campaign.id;

    $scope.save = function () {
      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path('/admin/campaigns/' + campaign.id + '/products');
          $window.scrollTo(0,0);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $location.path('/admin/campaigns/' + campaign.id + '/products');
          $window.scrollTo(0,0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/campaigns/' + campaign.id + '/products');
      $window.scrollTo(0,0);
    };
  });