'use strict'

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('UploadProductsModalCtrl', function ($scope, $window, $timeout, $uibModalInstance, campaign, FileUploader, APP_SETTINGS, System) {
		$scope.campaign = campaign;

		$scope.uploader = new FileUploader({
			url : APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadproducts',
			headers: {
				'X-Access-Token': $window.sessionStorage.token,
				'X-Key': $window.sessionStorage.user
			},
			onCompleteItem: function (item, response) {
				(function updateProgress(taskId) {
					System.getThreadTaskProgress({
							taskId: taskId
						}).$promise
						.then(function (result) {
							item.progressPercent = result.percentComplete;
							if (item.progressPercent < 100 && !$scope.$$destroyed) {
								$timeout(function () {
									updateProgress(taskId)
								}, 1000);
							}
						});
				})(response.taskId);
			}
		});

		$scope.finish = function () {
			$uibModalInstance.close(true);
		}
	})
