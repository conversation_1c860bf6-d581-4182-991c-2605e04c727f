'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminDispositionsCtrl', function ($scope, $rootScope, $state, $log, $uibModal, Disposition, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Dispositions';

    var dispositions = Disposition.query(function () {
      $scope.dispositions = dispositions;
    });

    $scope.deleteDisposition = function (disposition) {
      SweetAlert.swal({
          title: "Delete Disposition",
          text: "Are you user you want to delete " + disposition.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var dispositionId = disposition.id;
            disposition.$delete(function () {
              $scope.dispositions = $scope.dispositions.filter(function (d) {
                return d.id && d.id !== dispositionId;
              });
            });
          }
        });
    };
  });