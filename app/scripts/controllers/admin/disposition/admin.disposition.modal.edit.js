'use strict';

angular.module('dialerFrontendApp')
  .controller('DispositionModalEditCtrl', function ($scope, $uibModalInstance, editItem, callResultFields) {
    $scope.callResultFields = callResultFields;

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    
    $scope.availableCRFs = _.filter($scope.callResultFields, function (a) {
      return !_.find($scope.editItem.callresultfields, { id: a.id });
    });
    $scope.selectedCRFs = angular.copy($scope.editItem.callresultfields) || [];

    $scope.addCRFToSelected = function () {
      if ($scope.highlightedAvailableCRF && $scope.highlightedAvailableCRF.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableCRF.length; i++) {
          var crfToMove = _.findWhere($scope.availableCRFs, { id: $scope.highlightedAvailableCRF[i] });
          
          if (crfToMove) {
            $scope.availableCRFs = _.without($scope.availableCRFs, crfToMove);
            $scope.selectedCRFs.push(crfToMove);
          }
        }
      }
    };

    $scope.addCRFToAvailable = function () {
      if ($scope.highlightedSelectedCRF && $scope.highlightedSelectedCRF.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedCRF.length; i++) {
          var crfToMove = _.findWhere($scope.selectedCRFs, { id: $scope.highlightedSelectedCRF[i] });
          
          if (crfToMove) {
            $scope.selectedCRFs = _.without($scope.selectedCRFs, crfToMove);
            $scope.availableCRFs.push(crfToMove);
          }
        }
      }
    };

    $scope.save = function () {
      $scope.editItem.callresultfields = $scope.selectedCRFs;

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });