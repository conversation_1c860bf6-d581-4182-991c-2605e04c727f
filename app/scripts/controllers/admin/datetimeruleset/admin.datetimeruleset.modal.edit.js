'use strict';

angular.module('dialerFrontendApp')
  .controller('DateTimeRuleSetModalEditCtrl', function ($scope, $uibModalInstance, editItem) {
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;

    if ($scope.editItem.endTime.getHours) {
      $scope.endTimeObj = $scope.editItem.endTime;
    }
    else {
      $scope.endTimeObj = new Date();
      $scope.endTimeObj.setHours(parseInt($scope.editItem.endTime.substr(0,2)));
      $scope.endTimeObj.setMinutes(parseInt($scope.editItem.endTime.substr(3,2)));
      $scope.endTimeObj.setSeconds(0);
    }

    if ($scope.editItem.startTime.getHours) {
      $scope.startTimeObj = $scope.editItem.startTime;
    }
    else {
      $scope.startTimeObj = new Date();
      $scope.startTimeObj.setHours(parseInt($scope.editItem.startTime.substr(0,2)));
      $scope.startTimeObj.setMinutes(parseInt($scope.editItem.startTime.substr(3,2)));
      $scope.startTimeObj.setSeconds(0);
    }

    $scope.save = function () {
      $scope.editItem.endTime = $scope.endTimeObj.toTimeString().substr(0,8);
      $scope.editItem.startTime = $scope.startTimeObj.toTimeString().substr(0,8);

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });