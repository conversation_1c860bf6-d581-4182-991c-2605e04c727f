'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminDateTimeRuleSetsCtrl', function ($scope, $rootScope, $state, $uibModal, $log, DateTimeRuleSet, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Date/Time Rules';

    var dateTimeRuleSets = DateTimeRuleSet.query(function () {
      $scope.dateTimeRuleSets = dateTimeRuleSets;
    });

    $scope.getDaysOfWeekAsString = function (dateTimeRuleSet) {
      var days = [];

      if (dateTimeRuleSet.monday) {
        days.push('Monday');
      }
      if (dateTimeRuleSet.tuesday) {
        days.push('Tuesday');
      }
      if (dateTimeRuleSet.wednesday) {
        days.push('Wednesday');
      }
      if (dateTimeRuleSet.thursday) {
        days.push('Thursday');
      }
      if (dateTimeRuleSet.friday) {
        days.push('Friday');
      }
      if (dateTimeRuleSet.saturday) {
        days.push('Saturday');
      }
      if (dateTimeRuleSet.sunday) {
        days.push('Sunday');
      }

      if (days.length === 2 && days.indexOf('Saturday') >= 0 && days.indexOf('Sunday') >= 0) {
        return 'Weekends';
      }
      if (days.length === 5 && days.indexOf('Saturday') === -1 && days.indexOf('Sunday') === -1) {
        return 'Weekdays';
      }
      if (days.length === 7) {
        return 'Every day';
      }

      return days.join(', ');
    };

    $scope.deleteDateTimeRuleSet = function (dateTimeRuleSet) {
      SweetAlert.swal({
          title: "Delete Rule",
          text: "Are you user you want to delete " + dateTimeRuleSet.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var dateTimeRuleSetId = dateTimeRuleSet.id;
            dateTimeRuleSet.$delete(function () {
              $scope.dateTimeRuleSets = $scope.dateTimeRuleSets.filter(function (c) {
                return c.id && c.id !== dateTimeRuleSetId;
              });
            });
          }
        });
    };
  });