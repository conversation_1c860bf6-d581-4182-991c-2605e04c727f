'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminUsersCtrl', function ($scope, $rootScope, $log, User, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Users';

    var users = User.query(function () {
      $scope.users = users;
    });

    $scope.deleteUser = function (user) {
      SweetAlert.swal({
          title: "Delete User",
          text: "Are you user you want to delete " + user.username + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var userId = user.id;
            user.$delete(function () {
              $scope.users = $scope.users.filter(function (l) {
                return l.id && l.id !== userId;
              });
            });
          }
        });
    }

    // $scope.deleteUser = function (user) {
    //   var modalInstance = $uibModal.open({
    //     animation: true,
    //     keyboard: false,
    //     templateUrl: 'views/common/messageboxes/dialogModal.html',
    //     controller: 'DialogModalCtrl',
    //     resolve: {
    //       options: function () {
    //         return {
    //           yes: 'Yes',
    //           no: 'No',
    //           title: 'Delete ' + user.username,
    //           content: 'Are you sure you want to delete this user?'
    //         }
    //       }
    //     }
    //   });

    //   modalInstance.result.then(function (result) {
    //     if (result) {
    //       var userId = user.id;
    //       user.$delete(function () {
    //         $scope.users = $scope.users.filter(function (l) {
    //           return l.id && l.id !== userId;
    //         });
    //       });
    //     }
    //   });
    // };
  });