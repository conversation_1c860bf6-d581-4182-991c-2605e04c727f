'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('UserEditCtrl', function ($scope, $location, $rootScope, $window, editItem, agents, clients, User, FileUploader, APP_SETTINGS, _, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Users';

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.agents = agents;
    $scope.clients = clients;
    $scope.errors = {};
    $scope.passwordFormatText = "Password must contain:\nat least 1 uppercase letter\nat least 1 lowercase letter\nat least 1 number\nat least 8 characters";
    var users = [];

    $scope.uploader = new FileUploader({
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      }
    });

    User.query(function (result) {
      users = _.filter(result, function (user) {
        return user.id !== $scope.editItem.id;
      });

      for (var i = 0; i < $scope.agents.length; i++) {
        var agent = $scope.agents[i];
        var existingUser = _.findWhere(users, {
          agentId: agent.id
        })
        if (existingUser)
          agent.assigned = true;
        else
          agent.assigned = false;
      };
    });

    $scope.save = function () {
      if($scope.editItem.password != '--PLACEHOLDER--'  && !$rootScope.checkPasswordStrength($scope.editItem.password)) {
        SweetAlert.swal("Password is not strong enough", $scope.passwordFormatText, 'warning');
        return;
      }

      $scope.editItem.showStats = true;
      $scope.editItem.username = $scope.editItem.username.toLowerCase();

      if (_.findWhere(users, {
          username: $scope.editItem.username
        })) {
        $scope.errors.username = 'Username already exists';
        return;
      }

      if ($scope.editItem.isAdmin) {
        if ($scope.editItem.isSupervisor) {
          $scope.editItem.homeState = 'supervisor.campaigns';
        } else {
          $scope.editItem.homeState = 'admin.clients';
        }
      } else if ($scope.editItem.isSupervisor || $scope.editItem.isClientAdmin) {
        $scope.editItem.homeState = 'supervisor.campaigns';
      } else if ($scope.editItem.isAgent || $scope.editItem.isClientAgent) {
        $scope.editItem.homeState = 'agent.dashboard';
      } else if ($scope.editItem.isClient) {
        $scope.editItem.homeState = 'report.clientlogin';
      }

      if (!$scope.editItem.isAgent && !$scope.editItem.isClientAgent) {
        $scope.editItem.agentId = null;
      }

      if ($scope.update) {
        $scope.editItem.$update(function () {
          if ($scope.uploader.queue.length) {
            $scope.uploader.queue[0].url = APP_SETTINGS.BASE_API_URL + 'users/' + $scope.editItem.id + '/uploadavatar';
            $scope.uploader.queue[0].upload();
          }
          if ($rootScope.loggedInUser.id == $scope.editItem.id) {
            //is the current user so reload the scope user as this one
            User.get({
              id: $scope.editItem.id
            }).$promise.then(function (user) {
              $rootScope.loggedInUser = user;
              $location.path('/admin/users');
              $window.scrollTo(0, 0);
            })
          } else {
            $location.path('/admin/users');
            $window.scrollTo(0, 0);
          }

        });
      } else {
        $scope.editItem.$save(function () {
          if ($scope.uploader.queue.length) {
            $scope.uploader.queue[0].url = APP_SETTINGS.BASE_API_URL + 'users/' + $scope.editItem.id + '/uploadavatar';
            $scope.uploader.queue[0].upload();
          }
          $location.path('/admin/users');
          $window.scrollTo(0, 0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/users');
      $window.scrollTo(0, 0);
    };
  });