'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminCampaignTypesCtrl', function ($scope, $rootScope, $uibModal, CampaignType, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Skill Sets';

    var campaignTypes = CampaignType.query(function () {
      $scope.campaignTypes = campaignTypes;
    });

    $scope.deleteCampaignType = function (type) {
      SweetAlert.swal({
          title: "Delete Campaign Type",
          text: "Are you user you want to delete " + type.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var typeId = type.id;
            type.$delete(function () {
              $scope.campaignTypes = $scope.campaignTypes.filter(function (c) {
                return c.id && c.id !== typeId;
              });
            });
          }
        });
    };
  });