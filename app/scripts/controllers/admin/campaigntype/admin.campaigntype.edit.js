'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignTypeEditCtrl', function ($scope, $rootScope, $location, $window, editItem, skills, _) {
    $rootScope.pageTitle = 'Configure | Skill Set';

    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.skills = skills;

    $scope.availableSkills = _.filter($scope.skills, function (a) {
      return !_.find($scope.editItem.skills, { id: a.id });
    });
    $scope.selectedSkills = angular.copy($scope.editItem.skills) || [];

    $scope.addSkillToSelected = function () {
      if ($scope.highlightedAvailableSkill && $scope.highlightedAvailableSkill.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableSkill.length; i++) {
          var skillToMove = _.findWhere($scope.availableSkills, { id: $scope.highlightedAvailableSkill[i] });
          
          if (skillToMove) {
            $scope.availableSkills = _.without($scope.availableSkills, skillToMove);
            $scope.selectedSkills.push(skillToMove);
          }
        }
      }
    };

    $scope.addSkillToAvailable = function () {
      if ($scope.highlightedSelectedSkill && $scope.highlightedSelectedSkill.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedSkill.length; i++) {
          var skillToMove = _.findWhere($scope.selectedSkills, { id: $scope.highlightedSelectedSkill[i] });
          
          if (skillToMove) {
            $scope.selectedSkills = _.without($scope.selectedSkills, skillToMove);
            $scope.availableSkills.push(skillToMove);
          }
        }
      }
    };

    $scope.save = function () {
      $scope.editItem.skills = $scope.selectedSkills;

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path('/admin/campaigntypes');
          $window.scrollTo(0,0);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $location.path('/admin/campaigntypes');
          $window.scrollTo(0,0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/campaigntypes');
      $window.scrollTo(0,0);
    };
  });