'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignTypeModalEditCtrl', function ($scope, $uibModalInstance, editItem, skills, _) {
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;
    $scope.skills = skills;

    $scope.availableSkills = _.filter($scope.skills, function (a) {
      return !_.find($scope.editItem.skills, { id: a.id });
    });
    $scope.selectedSkills = angular.copy($scope.editItem.skills) || [];

    $scope.addSkillToSelected = function () {
      if ($scope.highlightedAvailableSkill && $scope.highlightedAvailableSkill.length > 0) {
        for (var i = 0; i < $scope.highlightedAvailableSkill.length; i++) {
          var skillToMove = _.findWhere($scope.availableSkills, { id: $scope.highlightedAvailableSkill[i] });
          
          if (skillToMove) {
            $scope.availableSkills = _.without($scope.availableSkills, skillToMove);
            $scope.selectedSkills.push(skillToMove);
          }
        }
      }
    };

    $scope.addSkillToAvailable = function () {
      if ($scope.highlightedSelectedSkill && $scope.highlightedSelectedSkill.length > 0) {
        for (var i = 0; i < $scope.highlightedSelectedSkill.length; i++) {
          var skillToMove = _.findWhere($scope.selectedSkills, { id: $scope.highlightedSelectedSkill[i] });
          
          if (skillToMove) {
            $scope.selectedSkills = _.without($scope.selectedSkills, skillToMove);
            $scope.availableSkills.push(skillToMove);
          }
        }
      }
    };

    $scope.save = function () {
      $scope.editItem.skills = $scope.selectedSkills;

      if ($scope.update) {
        $scope.editItem.$update(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $uibModalInstance.close($scope.editItem);
        });
      }
    };

    $scope.cancel = function () {
      $uibModalInstance.dismiss('cancel');
    };
  });