'use strict'

angular.module('dialerFrontendApp')
	.controller('LeadCallHistoryCtrl', function ($scope, $rootScope, $window, $http, $location, lead, history, Lead, CallResult, _) {
		$rootScope.pageTitle = `${lead.id} - ${lead.first_name} ${lead.last_name} | Call History`;
		$scope.lead = lead;
		$scope.history = history;
		$scope.history.forEach(function (result) {
			result.expand = false;
		});

		var colors = ['navy-bg', 'blue-bg', 'lazur-bg', 'yellow-bg'];
		var campaignToColor = [];

		$scope.wrapupIcon = function(wrapup) {
			switch(wrapup) {
				case "Pledge Credit Card":
				case "Sale":
					return 'fa-credit-card';
				case "Standard Refusal":
					return 'fa-thumbs-o-down';
				case "Exception Refusal":
					return 'fa-ban';
				case "Callback":
					return 'fa-clock-o';
				case "No Resolution":
					return 'fa-arrow-right';
				default:
					return 'fa-phone';
			}
		};

		$scope.campaignColor = function (campaign) {
			var color = _.findWhere(campaignToColor, {
				campaign: campaign
			})

			if(color) {
				return color.color
			} else {
				var newColor = {
					campaign: campaign,
					color: colors[0] || 'navy-bg'
				}
				campaignToColor.push(newColor);
				colors = colors.splice(0, 1);
				return newColor.color;
			}
		};
	});

