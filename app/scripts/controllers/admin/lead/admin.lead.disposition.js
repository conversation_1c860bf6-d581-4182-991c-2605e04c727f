'use strict'

angular.module('dialerFrontendApp')
	.controller('LeadDispositionModalCtrl', function ($scope, $rootScope, $uibModalInstance, $uibModal, $http, APP_SETTINGS, stage, lead, client, campaign, callResult, Campaign, CallResult, Lead, CampaignStage, RefusalReason, _) {
		$scope.dispositions = _.filter(angular.copy(stage.dispositions), function (disposition) {
			return disposition.name != 'Exception Refusal' && disposition.name != 'Collections Exception Refusal' && disposition.name != 'Pledge Invoice' && disposition.name != 'No Resolution' && disposition.name != 'Callback';
		});

		$scope.dispositions.forEach(function (dis) {
			if (dis.name == 'Standard Refusal') dis.name = 'Refusal';
			if (dis.name == 'Collections Standard Refusal') dis.name = 'Collections Refusal';
			if (dis.name == 'Pledge Credit Card') dis.name = 'Pledge';
		})

		stage.dispositions.forEach(function (dis) {
			if (dis.name == 'Standard Refusal') dis.name = 'Refusal';
			if (dis.name == 'Collections Standard Refusal') dis.name = 'Collections Refusal';
			if (dis.name == 'Pledge Credit Card') dis.name = 'Pledge';
		})

		$scope.editItem = lead;

		if (!lead.recurringpayments || !lead.recurringpayments.length) {
			$scope.dispositions = $scope.dispositions.filter(function (dis) {
				return dis.name.indexOf('Recurring Maintenance') === -1
			})
		}

		$scope.agents = stage.agents;
		$scope.data = {
			createdAt: new Date(),
			time: new Date()
		}
		var fakeAgentSession = {
			currentCallResult: angular.copy(callResult),
			currentCampaignStage: {
				id: callResult.campaignstageId,
				dispositions: stage.dispositions,
				campaign: {
					id: callResult.campaignId,
					client: client,
					campaigntypeId: campaign.campaigntypeId,
					campaigntype: campaign.campaigntype
				}
			},
			currentLead: lead
		};

		$scope.ok = function () {
			var disposition = _.findWhere($scope.dispositions, {
				id: $scope.data.disposition
			});

			var dateTime = moment($scope.data.createdAt);
			dateTime.hour($scope.data.time.getHours()).minute($scope.data.time.getMinutes());
			fakeAgentSession.currentCallResult.disposition = disposition;
			fakeAgentSession.currentCallResult.createdAt = dateTime.toDate();
			fakeAgentSession.currentCallResult.agentId = $scope.data.agent;
			fakeAgentSession.agentId = $scope.data.agent;
			fakeAgentSession.agent = _.findWhere($scope.agents, { 
				id: $scope.data.agent
			});

			callResult.agentId = $scope.data.agent;

			if (disposition.templateUrl && disposition.controller) {
				var modalInstance = $uibModal.open({
					templateUrl: disposition.templateUrl,
					controller: disposition.controller,
					backdrop: 'static',
					keyboard: true,
					size: disposition.size || 'md',
					resolve: BuildResolveObj(disposition)
				});

				modalInstance.result.then(function (result) {
					wrapupComplete(result).then(function () {
						$uibModalInstance.close();
					}).catch(function (err) {
						console.log(err);
					})
				});
			} else {
				wrapupComplete({
					disposition: disposition
				}).then(function () {
					$uibModalInstance.close();
				}).catch(function (err) {
					console.log(err);
				})
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};

		function wrapupComplete(resultObj) {
			return new Promise(function (resolve, reject) {
				if (resultObj && resultObj.callResultData) {
					_.extend(callResult, resultObj.callResultData);
				}

				if (resultObj && resultObj.wrapUpNotes) {
					callResult.notes = resultObj.wrapUpNotes;
				}

				if (resultObj && resultObj.disposition) {
					var csd = resultObj.disposition.campaignstagedispositions;
					if (csd.transitionToCampaignStageId) {
						var nextStageId = null

						if (csd.transitionCutOffDate) {
							if (moment(csd.transitionCutOffDate).valueOf() < Date.now()) {
								nextStageId = csd.transitionToCampaignStageId;
							} else {
								nextStageId = csd.transitionCutOffDateDispositionId || null;
							}
						} else {
							nextStageId = csd.transitionToCampaignStageId;
						}

						if (nextStageId) {
							Campaign.moveLeadToCampaignStage({
								id: campaign.id
							}, {
								leadId: lead.id,
								newCampaignStageId: nextStageId
							});
						} else {
							if (resultObj.disposition.exhaustLead) {
								CampaignStage.exhaustLead({
									id: stage.id,
									leadId: lead.id
								});
							}
						}
					} else {
						if (resultObj.disposition.exhaustLead) {
							CampaignStage.exhaustLead({
								id: stage.id,
								leadId: lead.id
							});
						}
					}

					callResult.wrapupduration = 0;
					callResult.wrapup = resultObj.disposition ? resultObj.disposition.name : '';
					if (callResult.wrapup == 'Refusal') {
						if (stage.name == 'Collections') {
							callResult.wrapup = 'Collections Standard Refusal';
						} else {
							callResult.wrapup = 'Standard Refusal';
						}
					}

					if (callResult.wrapup == 'Pledge') {
						callResult.wrapup = (callResult.paymentType == 'Invoice' ? 'Pledge Invoice' : 'Pledge Credit Card');
					}

					if (campaign.campaigntype.name == 'Telefunding') {
						if (lead.tfSkill) {
							callResult.skill = lead.tfSkill.name;
						}
						if (lead.tfSubSkill) {
							callResult.subSkill = lead.tfSubSkill.name;
						}
					} else {
						if (lead.tmSkill) {
							callResult.skill = lead.tmSkill.name;
						}
						if (lead.tmSubSkill) {
							callResult.subSkill = lead.tmSubSkill.name;
						}
					}

					callResult.completed = true;

					var dcu = nowPlusHours(csd.dontContactLeadForHours);

					try {
						if (resultObj.lastDate && moment(dcu) < moment(resultObj.lastDate))
							dcu = resultObj.lastDate;
						}
					catch (err) {}

					Lead.update({
						id: lead.id
					}, {
						dontContactUntil: dcu
					})
						.$promise
						.finally(function () {
							CallResult.update({
								id: callResult.id
							}, callResult).$promise
								.then(function () {
									resolve();
								})
								.catch(reject);
						});
				}
			})
		}

		function nowPlusHours(hours) {
			var d = new Date()

			if (hours)
				d.setHours(d.getHours() + hours)

			return d;
		}

		function BuildResolveObj(disposition) {
			var resolveOjb = {
				disposition: function () {
					return fakeAgentSession.currentCallResult.disposition;
				},
				lead: function () {
					return lead;
				},
				agentSession: function () {
					return fakeAgentSession;
				},
				phone: function () {
					return null;
				},
				admin: function () {
					return true;
				}
			}

			if (disposition.name.indexOf('Sale') > -1) {
				resolveOjb.products = function () {
					return Campaign.getProducts({
						id: campaign.id
					}).$promise;
				}

				resolveOjb.client = function () {
					return client;
				}
			}

			if (disposition.name.indexOf('Pledge') > -1 || disposition.name.indexOf('Recurring') > -1) {
				resolveOjb.client = function () {
					return client;
				}
			}

			if (disposition.name.indexOf('Recurring Maintenance') > -1) {
				resolveOjb.agent = function () { return true; };
				resolveOjb.recurring = function () {
					return $http.get(APP_SETTINGS.BASE_API_URL + 'leads/' + lead.id + '/recurring').then(function (response) {
						return response.data;
					})
				}
			}

			if (disposition.name == 'Refusal' || disposition.name == 'Collections Refusal' || disposition.name.indexOf('Invoice') > -1 || disposition.name == 'Refusal and Write Off') {
				resolveOjb.invoices = function () {
					return Campaign.getLeadInvoices({
						id: campaign.id,
						leadid: lead.id
					}).$promise;
				}

				resolveOjb.reasons = function () {
					return RefusalReason.query().$promise;
				}
			}

			if (disposition.name.indexOf('Invoice') > -1) {
				resolveOjb.client = function () {
					return client;
				}
			}

			if (disposition.name == 'Callback') {
				resolveOjb.activeDiallingNumber = function () {
					return '';
				}

				resolveOjb.client = function () {
					return client;
				}
			}

			return resolveOjb;
		}
	})