'use strict'

angular.module('dialerFrontendApp')
	.controller('LeadAuditHistoryCtrl', function ($scope, $rootScope, $window, $location, lead, history, Lead, LeadAudit, _) {
		$rootScope.pageTitle = `${lead.id} - ${lead.first_name} ${lead.last_name} | Audit History`;
		$scope.lead = lead;
		$scope.history = history;

		$scope.undoAudit = function (audit) {
			var thisLead = $scope.lead;
			Lead.get({
				id: audit.leadId
			}, function (lead) {
				lead[audit.field] = audit.previousValue;
				lead.$update(function () {

					//create new audit event for the roll back 
					var newAudit = new LeadAudit()
					newAudit.field = audit.field
					newAudit.previousValue = audit.newValue;
					newAudit.newValue = audit.previousValue;
					newAudit.leadId = audit.leadId;
					newAudit.userId = $rootScope.loggedInUser.id;

					//add the new audit to the current array for viewing
					newAudit.$save(function (newAudit) {
						newAudit.user = $rootScope.loggedInUser;
						$scope.history.push(newAudit);
					});

					//update the scope details if the name as changed for the view in the table
					if (audit.field == "first_name") {
						thisLead.first_name == newAudit.newValue;
					} else if (audit.field == "last_name") {
						thisLead.first_name == newAudit.last_name;
					}
				})
			})

		}
	});