'use strict'

angular.module('dialerFrontendApp')
	.controller('LeadModalEditCtrl', function ($scope, $rootScope, $window, $location, $uibModalInstance, editItem, existingItem, agentId, Lead, LeadAudit, _, Agent) {		
		$scope.editItem = editItem;
		$scope.existingItem = existingItem;
		$scope.agentId = agentId;
		$scope.agents = []

		$scope.editItem.dontContactUntilObj = editItem.dontContactUntil ? new Date(editItem.dontContactUntil) : null

		if ($scope.editItem.agentPortfolioTag === undefined)
			$scope.editItem.agentPortfolioTag = null

		Agent.query().$promise.then(function (agents) {
			$scope.agents = agents;
		});

        $scope.dateOpened = false;
		$scope.openDate = function ($event) {
			$event.preventDefault();
			$event.stopPropagation();
			$scope.dateOpened = true;
		};

		const isAdminOrClientAdmin = $rootScope.loggedInUser.isAdmin || $rootScope.loggedInUser.isClientAdmin;

		$scope.fields = [ 
			{ name: "clientRef", label: "Client Id", type: "text", disabled: !isAdminOrClientAdmin},
			{ name: "first_name", label: "First Name", type: "text", disabled: false },
			{ name: "last_name", label: "Last Name", type: "text", disabled: false },
			{ name: "salutation", label: "Salutation", type: "text", disabled: false },
			{ name: "suffix", label: "Suffix", type: "text", disabled: false },
			{ name: "spouse_name", label: "Spouse", type: "text", disabled: false },
			{ name: "company_name", label: "Company", type: "text", disabled: false },
			{ name: "address1", label: "Address 1", type: "text", disabled: false },
			{ name: "address2", label: "Address 2", type: "text", disabled: false },
			{ name: "address3", label: "Address 3", type: "text", disabled: false },
			{ name: "city", label: "City", type: "text", disabled: false },
			{ name: "state", label: "State", type: "text", disabled: false },
			{ name: "zip", label: "Zip", type: "text", disabled: false },
			{ name: "phone_home", label: "Phone 1", type: "tel", disabled: false },
			{ name: "phone_mobile", label: "Phone 2", type: "tel", disabled: false },
			{ name: "phone_work", label: "Phone 3", type: "tel", disabled: false },
			{ name: "phone_workmobile", label: "Phone 4", type: "tel", disabled: false },
			{ name: "email", label: "Email", type: "email", disabled: false }
		];
		
		if (window.location.href.indexOf('agent') === -1) {
			$scope.fields.push({ name: "notes", label: "Notes", type: "textarea", disabled: false })
			$scope.fields.push({ name: "dontContactUntil", label: "Lead Delay", disabled: false })
		}

		$scope.save = function () {
			$scope.editItem.dontContactUntil = $scope.editItem.dontContactUntilObj ? $scope.editItem.dontContactUntilObj.toISOString() : null
			$scope.editItem.$update(function () {
				//audit(); moved to server side
				$uibModalInstance.close($scope.editItem);
			})
		}

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		}

		// var audit = function () {
		// 	for (var i = 0; i < $scope.fields.length; i++) {
		// 		var field = $scope.fields[i].name;
		// 		if($scope.existingItem[field] != $scope.editItem[field]) {
		// 			var audit = new LeadAudit();
		// 			audit.field = field;
		// 			audit.previousValue = $scope.existingItem[field];
		// 			audit.newValue = $scope.editItem[field];
		// 			audit.leadId = $scope.editItem.id;
		// 			audit.userId = $rootScope.loggedInUser.id;

		// 			audit.$save();
		// 		}
		// 	};
		// }
	})
