'use strict'

angular.module('dialerFrontendApp')
    .controller('LeadAnalysisCtrl', function ($scope, $window, $rootScope, $http, lead, _, APP_SETTINGS, Lead, Campaign, CampaignStage, Client, CallResult, Callback, <PERSON><PERSON><PERSON>t, Agent, $uibModal, moment) {
        $rootScope.pageTitle = `Lead ${lead.id} - ${lead.first_name} ${lead.last_name} | Analysis`;

        $scope.lead = lead;
        $scope.history = lead.callresults || [];
        $scope.audits = lead.leadaudits;
        $scope.invoices = lead.invoices;
        $scope.callbacks = lead.callbacks;
        $scope.leadUpdates = lead.leadupdates || [];
        $scope.campaignLeadUpdates = lead.campaignleadupdates || [];
        $scope.campaigns = [];
        $scope.suppressions = lead.suppressions;

        $scope.customFields = [];
        if (lead.customFields && typeof lead.customFields === 'object') {
            try {
                var keys = Object.keys(lead.customFields);
                if (keys && keys.length) {
                    _.each(keys, function (key) {
                        if (key.indexOf('TICKET INFO') === 0 && lead.customFields[key]) {
                            var infoNo = key.substr(11, 2);
                            var field = _.findWhere($scope.customFields, { key: infoNo });
                            if (!field) {
                                field = { key: infoNo, ADDL: '', COST: '', DATE: '', EVENT: '', LOC: '', SEATS: '', TYPE: '', YEAR: '' };
                                $scope.customFields.push(field);
                            }
                            field[key.replace('TICKET INFO' + infoNo + ' ', '')] = lead.customFields[key];
                        }
                    })
                }
            } catch (e) {

            }
        }

        function getLatestCampaign (campaignLeads) {
            var latestCampaign = null;
            if (campaignLeads && campaignLeads.length) {
                campaignLeads.forEach(function (cl) {
                    if (!latestCampaign || moment(cl.createdAt) > moment(latestCampaign.createdAt)) {
                        latestCampaign = cl;
                    }
                })
            }
            return latestCampaign;
        }

        $scope.tyAmount = parseFloat((lead.tyAmount || '').replace('$','').replace(',','')) || 0

        if (!$scope.tyAmount) {
            var latestCampaign = getLatestCampaign(lead.campaignLeads)

            if (latestCampaign) {
                $scope.history.forEach(function (result) {
                    if (result.campaignId != latestCampaign.campaignId) return
                    $scope.tyAmount += parseFloat(result.giftAmount) || 0
                })
            }
        }

        $scope.getStage = function (update, id) {
            if (!id) return 'No Stage'
            var stage = _.findWhere(update.campaign.campaignstages, { id: parseInt(id) });
            return stage ? stage.name : 'No Stage';
        };

        $scope.back = function () {
            $window.history.back();
        };

        $scope.parseField = function (field) {
            switch (field) {
                case 'clientRef':
                    return 'Client Id'
                case 'first_name':
                    return 'First Name'
                case 'last_name':
                    return 'Last Name'
                case 'salutation':
                    return 'Salutation'
                case 'suffix':
                    return 'Suffix'
                case 'spouse_name':
                    return 'Spouse'
                case 'company_name':
                    return 'Company Name'
                case 'address1':
                    return 'Address 1'
                case 'address2':
                    return 'Address 2'
                case 'address3':
                    return 'Address 3'
                case 'city':
                    return 'City'
                case 'state':
                    return 'State'
                case 'zip':
                    return 'Zip'
                case 'phone_home':
                    return 'Phone 1'
                case 'phone_mobile':
                    return 'Phone 2'
                case 'phone_work':
                    return 'Phone 3'
                case 'phone_workmobile':
                    return 'Phone 4'
                case 'email':
                    return 'Email'
            }
            return field;
        };

        $scope.history.forEach(function (result) {
            switch (result.wrapup) {
                case 'Standard Refusal':
                case 'Exception Refusal':
                    result.notes = 'Refusal Reason: ' + result.refusalReason + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                    break;
                case 'Pledge V2':
                case 'Pledge V3':
                case 'Pledge Credit Card':
                case 'Pledge Invoice':
                    result.notes = 'Pledge Amount: $' + result.giftAmount + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                    if (result.installmentHistory) {
                        // show the installments
                        result.notes += '\nInstallments: ' + result.installmentHistory
                        if (result.installmentError) result.notes += '\nInstallment Error: ' + result.installmentError
                    }
                    break;
                case 'Sale':
                    result.notes = 'Sale Amount: $' + result.grandTotal + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                    break;
                case 'Recurring Payment':
                case 'Recurring Gift':
                    var notes = result.notes;
                    result.notes = "";
                    if (result.recurringpayments && result.recurringpayments.length > 0) {
                        result.notes = 'Recurring Amount: $:' + result.recurringpayments[0].amount + '\nInstallment Every: ' + result.recurringpayments[0].unit + '\nFirst Payment: ' + moment(result.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                    }
                    result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');

                    break;
                case 'Recurring Maintenance':
                    result.notes = "";
                    var notes = result.notes;
                    var recurring = $scope.history.slice().reverse().find(r => r.recurringpayments && r.recurringpayments.length)
                    if (recurring) {
                        result.notes = 'Recurring Amount: $:' + recurring.recurringpayments[0].amount + '\nInstallment Every: ' + recurring.recurringpayments[0].unit + '\nFirst Payment: ' + moment(recurring.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                    }

                    result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');
                    break;
            }
        });

        Lead.getCallAttempts({
            id: lead.id
        }).$promise.then(function (result) {
            var campaigns = []

            if (result.campaigns) {
                //they have no call attempts so just show the stage they are in (or no stage)
                campaigns = result.campaigns;
            } else {
                result.callattempts.forEach(ca => {
                    try {
                        var rule = _.findWhere(result.rules, {
                            uuid: ca.createdFromDTUuid
                        })
                        if (!rule) rule = {
                            datetimeruleset: {
                                name: 'DELETED'
                            }
                        };
                        var existingCampaign = _.findWhere(campaigns, {
                            id: ca.campaignId
                        })
                        if (existingCampaign) {
                            var existingStage = _.findWhere(existingCampaign.stages, {
                                id: ca.campaignstageId
                            })
                            if (existingStage) {
                                var existingRule = _.findWhere(existingStage.rules, {
                                    id: rule.datetimerulesetId
                                })
                                if (existingRule) {
                                    existingRule.count++;
                                } else {
                                    existingStage.rules.push({
                                        id: rule.datetimerulesetId,
                                        name: rule.datetimeruleset.name,
                                        count: 1
                                    })
                                }
                            } else {
                                existingCampaign.stages.push({
                                    id: ca.campaignstageId,
                                    name: ca.campaignstage.name,
                                    agents: ca.campaignstage.agents,
                                    rules: [{
                                        id: rule.datetimerulesetId,
                                        name: rule.datetimeruleset.name,
                                        count: 1
                                    }]
                                })
                            }
                        } else {
                            campaigns.push({
                                id: ca.campaignId,
                                name: ca.campaign.name,
                                type: ca.campaign.campaigntype.name,
                                initialCampaignStageId: ca.campaign.initialCampaignStageId,
                                stages: [{
                                    id: ca.campaignstageId,
                                    name: ca.campaignstage.name,
                                    agents: ca.campaignstage.agents,
                                    rules: rule ? [{
                                        id: rule.datetimerulesetId,
                                        name: rule.datetimeruleset.name,
                                        count: 1
                                    }] : []
                                }]
                            })
                        }
                    } catch (err) {
                        console.log(err)
                    }
                })

                campaigns.forEach(campaign => {
                    campaign.stages.forEach(stage => {
                        stage.agents.forEach(agent => {
                            if (agent.campaignstageagents.agentskills) {
                                agent.campaignstageagents.agentskills = JSON.parse(agent.campaignstageagents.agentskills)
                            } else {
                                agent.campaignstageagents.agentskills = []
                            }
                            agent.available = !!_.find(agent.campaignstageagents.agentskills, function (skillId) {
                                return skillId == (campaign.type == 'Telefunding' ? lead.tfSubSkillId : lead.tmSubSkillId)
                            });
                            if (agent.available) stage.validAgents = true;
                        })
                    })
                })
            }


            $scope.campaigns = campaigns;
        })

        $scope.showTix = function (lead, tix) {
            var tix = 'tix' + tix;
            if (lead[tix + 'Type'] || lead[tix + 'Yr'] || lead[tix + 'Event'] || lead[tix + 'Date'] || lead[tix + 'Cost'] || lead[tix + 'Loc'] || lead[tix + 'Seats'] || lead[tix + 'Addl']) {
                return true;
            }

            return false;
        };

        $scope.hasGiftHistory = function () {
            if (lead.lyAmount || lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount || lead.lastGiftDate || lead.lastGiftAmount || lead.lastGiftReference || lead.lastGiftCC)
                return true;

            if (lead.gift1Amount || lead.gift2Amount || lead.gift3Amount || lead.gift3Amount || lead.gift3Amount)
                return true;

            if (lead.lap1Amount || lead.lap2Amount || lead.lap3Amount || lead.lap4Amount)
                return true;

            return false;
        };

        $scope.hasBuyingHistory = function () {
            if (lead.tix1 || lead.tix2 || lead.tix3 || lead.tix4 || lead.tix5 || lead.tix6 || lead.tix7 || lead.tix8)
                return true;

            if (
                (lead.tix1Type || lead.tix1Event) ||
                (lead.tix2Type || lead.tix2Event) ||
                (lead.tix3Type || lead.tix3Event) ||
                (lead.tix4Type || lead.tix4Event) ||
                (lead.tix5Type || lead.tix5Event) ||
                (lead.tix6Type || lead.tix6Event) ||
                (lead.tix7Type || lead.tix7Event) ||
                (lead.tix8Type || lead.tix8Event) ||
                (lead.tix9Type || lead.tix9Event)
            )
                return true;

            return false;
        };

        $scope.hasInteractionHistory = function () {
            return !!((lead.interaction1Date || lead.interaction1Type || lead.interaction1Detail) ||
                (lead.interaction2Date || lead.interaction2Type || lead.interaction2Detail) ||
                (lead.interaction3Date || lead.interaction3Type || lead.interaction3Detail) ||
                (lead.interaction4Date || lead.interaction4Type || lead.interaction4Detail) ||
                (lead.interaction5Date || lead.interaction5Type || lead.interaction5Detail) ||
                (lead.interaction6Date || lead.interaction6Type || lead.interaction6Detail) ||
                (lead.interaction7Date || lead.interaction7Type || lead.interaction7Detail) ||
                (lead.interaction8Date || lead.interaction8Type || lead.interaction8Detail))
        };

        $scope.editLead = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return lead;
                    },
                    agentId: function () {
                        return $rootScope.loggedInUser.agentId || 0;
                    }
                }
            });

            modalInstance.result.then(function (result) {
                $scope.lead = result;
            })
        };

        $scope.editCallback = function (callback) {
            var modalInstance = $uibModal.open({
                animation: true,
                keyboard: false,
                templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
                controller: 'SupervisorCallbackCtrl',
                resolve: {
                    callback: function () {
                        return Callback.get({
                            id: callback.id
                        }).$promise;
                    },
                    callresult: function () {
                        if (callback.callresultId) {
                            return CallResult.get({
                                id: callback.callresultId
                            }).$promise;
                        } else {
                            return new CallResult();
                        }
                    },
                    lead: function () {
                        return lead;
                    },
                    campaign: function () {
                        return Campaign.get({
                            id: callback.campaignId
                        }).$promise;
                    },
                    agents: function () {
                        return Campaign.getAgents({
                            id: callback.campaignId
                        }).$promise;
                    },
                    client: function () {
                        return Client.get({
                            id: lead.clientId
                        }).$promise;
                    },
                    stage: function () {
                        return CampaignStage.get({
                            id: JSON.parse(callback.callAttemptJson).campaignstageId
                        }).$promise;
                    }
                }
            })

            modalInstance.result.then(function (result) {
                if (result.callresult) {
                    callback.callresult = result.callresult;
                }
                if (result.id) {
                    for (var prop in result) {
                        if (result.hasOwnProperty(prop)) {
                            callback[prop] = result[prop];
                        }
                    }
                }
            })
        };

        $scope.disableCallback = function (callback) {
            SweetAlert.swal({
                title: "Disable Callback",
                text: "Are you user you want to disable this callback?\r\nThis will allow the lead to be contacted again normally",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, disable it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
                function (isConfirm) {
                    callback.deleted = true;
                    Callback.update({
                        id: callback.id
                    }, callback);
                });
        };

        $scope.enableCallback = function (callback) {
            SweetAlert.swal({
                title: "Enable Callback",
                text: "Are you user you want to enable this callback?\r\nThis will stop the lead to be contacted again normally",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, enable it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
                function (isConfirm) {
                    callback.deleted = false;
                    Callback.update({
                        id: callback.id
                    }, callback);
                });
        };

        $scope.createCallback = function () {
            var campaign = getActiveCampaign($scope.campaigns);
            var modalInstance = $uibModal.open({
                animation: true,
                keyboard: false,
                templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
                controller: 'SupervisorCallbackCtrl',
                resolve: {
                    callback: function () {
                        return new Callback();
                    },
                    callresult: function () {
                        return new CallResult();
                    },
                    lead: function () {
                        return lead;
                    },
                    campaign: function () {
                        if (campaign) {
                            return Campaign.get({
                                id: campaign.id
                            }).$promise;
                        } else {
                            return null;
                        }
                    },
                    agents: function () {
                        return Campaign.getAgents({
                            id: campaign.id
                        }).$promise;
                    },
                    client: function () {
                        return Client.get({
                            id: lead.clientId
                        }).$promise;
                    },
                    stage: function () {
                        if (campaign) {
                            return CampaignStage.get({
                                id: campaign.initialCampaignStageId
                            }).$promise;
                        } else {
                            return null;
                        }
                    }
                }
            })

            modalInstance.result.then(function (result) {
                result.campaign = campaign;
                Agent.get({
                    id: result.agentId
                }).$promise.then(function (agent) {
                    result.agent = agent;
                    $scope.callbacks.push(result);
                })
            })
        };

        $scope.isCallbackInFuture = function (callback) {
            return (moment() < moment(callback.endDateTime))
        };

        $scope.isCallbackForNow = function (callback) {
            return (moment() < moment(callback.endDateTime) && moment() < moment(callback.startDateTime))
        };

        $scope.undoAudit = function (audit) {
            SweetAlert.swal({
                title: "Undo Change",
                text: `Are you user you want to change ${$scope.parseField(audit.field)} from ${audit.newValue || '--BLANK--'} back to ${audit.previousValue || '--BLANK--'}?`,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, undo it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
                function (isConfirm) {
                    lead[audit.field] = audit.previousValue;
                    lead.$update();
                    $scope.audits.push({
                        field: audit.field,
                        previousValue: audit.newValue,
                        newValue: audit.previousValue,
                        createdAt: (new Date()).toString(),
                        user: $rootScope.loggedInUser
                    });
                });
        };

        function getActiveCampaign(campaigns) {
            if (!campaigns || !campaigns.length) return null;

            var campaign = null;

            if (campaigns.length == 1) {
                campaign = campaigns[0]
            } else {
                var moveOn = false;
                //check if both campaigns are active, if only 1 is then assume that one
                campaigns.forEach(function (c) {
                    if (moveOn) return
                    if (moment(c.endDate) > moment()) {
                        if (!campaign) {
                            campaign = c
                        } else {
                            //damn they are both valid so null campaignId and move on
                            moveOn = true;
                            campaign = null;
                        }
                    }
                })

                moveOn = false;

                if (!campaign) {
                    //now check for a campaign where the lead is in a stage
                    campaigns.forEach(function (c) {
                        if (c.stages && c.stages.length) {
                            if (!campaign) {
                                campaign = c
                            } else {
                                //damn they are in a stage for both campaigns so move on
                                moveOn = true;
                                campaign = null;
                            }
                        }
                    })
                }

                if (!campaign) {
                    //maybe we should ask them which campaign they want it raised on in that case
                    //i'll do this later i think
                }
            }
            return campaign;
        }
    });