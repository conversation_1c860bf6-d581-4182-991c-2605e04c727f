'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminRefusalReasonsCtrl', function ($scope, $rootScope, $state, $uibModal, reasons, RefusalReason, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Refusal Reasons';
    $scope.reasons = reasons;

    $scope.addReason = function() {
      var modalInstance = $uibModal.open({
        animation: true,
        templateUrl: 'views/admin/refusalreason/admin.refusalreason.edit.html',
        controller: 'RefusalReasonEditCtrl',
        resolve: {
          editItem: function() {
            return new RefusalReason();
          }
        }
      });

      modalInstance.result.then(function(result) {
        $scope.reasons.push(result);
      })
    };

    $scope.editReason = function (reason) {
      var modalInstance = $uibModal.open({
        animation: true,
        templateUrl: 'views/admin/refusalreason/admin.refusalreason.edit.html',
        controller: 'RefusalReasonEditCtrl',
        resolve: {
          editItem: function () {
            return angular.copy(reason);
          }
        }
      });

      modalInstance.result.then(function (result) {
        var reason = _.findWhere($scope.reasons, {
          id: result.id
        })
        if (reason) {
          reason.name = result.name;
          reason.exception = result.exception;
          reason.telefunding = result.telefunding;
          reason.telemarketing = result.telemarketing;
        }
      })
    };

    $scope.deleteReason = function (reason) {
      SweetAlert.swal({
          title: "Delete Refusal Reason",
          text: "Are you user you want to delete " + reason.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var reasonId = reason.id;
            reason.$delete(function () {
              $scope.reasons = $scope.reasons.filter(function (c) {
                return c.id && c.id !== reasonId;
              });
            });
          }
        });
    };
  });