'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignStageCtrl', function ($scope, $rootScope, campaign, stages, CampaignStage, SweetAlert) {
    $rootScope.pageTitle = campaign.name + ' | Stages';

    $scope.campaign = campaign;
    $scope.campaignStages = stages;

    $scope.campaignStages.forEach(function (stage) {
      getStageLeadCount(stage);
    })

    $scope.deleteLeads = function (stage) {
      SweetAlert.swal({
          title: "Delete Leads",
          text: "Are you user you want to delete all leads in " + stage.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete them!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            CampaignStage.deleteLeads({
              id: stage.id
            }).$promise.then(function (results) {
              console.log(results);
            })
          }
        });
    };

    $scope.deleteCampaignStage = function (campaignStage) {
      SweetAlert.swal({
          title: "Delete Stage",
          text: "Are you user you want to delete " + campaignStage.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var campaignStageId = campaignStage.id;
            campaignStage.$delete(function () {
              $scope.campaignStages = $scope.campaignStages.filter(function (cs) {
                return cs.id && cs.id !== campaignStageId;
              });
            });
          }
        });
    };

    $scope.resetLeads = function (stage) {
      SweetAlert.swal({
          title: "Reset Stage",
          text: "Are you user you want to reset the lead delays on " + stage.name + "?",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes",
          cancelButtonText: "No",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            CampaignStage.resetLeads({
              id: stage.id
            }).$promise.then(function (results) {
              console.log(results);
            })
          }
        });
    };

    function getStageLeadCount(stage) {
      CampaignStage.getLeadCount({
        id: stage.id
      }).$promise.then(function (result) {
        stage.leadCount = result.count;
      })
    }
  });