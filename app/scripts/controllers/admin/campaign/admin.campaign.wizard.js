'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
    .controller('CampaignWizardCtrl', function ($scope, $state, $q, $window, $rootScope, $uibModal, $http, moment, WizardHandler, Campaign,
        Agent, Disposition, Skill, CampaignProjections, CampaignStage, Client, users, editItem, System, $timeout,
        clients, campaignTypes, campaignStages, FileUploader, APP_SETTINGS, _, SweetAlert, SubSkill) {
        $scope.update = !!editItem.id;

        $rootScope.pageTitle = editItem.id ? editItem.name + ' | Edit' : 'Create Campaign';

        SubSkill.query().$promise.then(function (subskills) {
            $scope.subskills = subskills;
        });

        $scope.updateOnly = false;
        $scope.updateOnlyChange = function (ev) {
            var url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads'
            if (ev.target.checked) {
                url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads?updateOnly=true'
            }
            $scope.uploader.url = url
            for (var i = 0; i < $scope.uploader.queue.length; i++) {
                var element = $scope.uploader.queue[i];
                element.url = url;
            }
        };

        $scope.uploader = new FileUploader({
            headers: {
                'X-Access-Token': $window.sessionStorage.token,
                'X-Key': $window.sessionStorage.user
            },
            onCompleteItem: function (item, response) {
                (function updateProgress(history) {
                    var promise
                    if (history.taskId) {
                        promise = System.getThreadTaskProgress({
                            taskId: taskId
                        }).$promise
                    } else {
                        promise = $http.get(APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploads/' + history.id)
                    }
                    promise.then(function (result) {
                        result = result.data || result
                        item.progressPercent = result.percentComplete || 0;
                        console.log(item.progressPercent)
                        if (item.progressPercent < 100 && !$scope.$$destroyed) {
                            $timeout(function () {
                                updateProgress(history)
                            }, 1000);
                        } else if (!$scope.$$destroyed) {
                            $scope.uploads.push(result)
                        }
                    });
                })(response);
            }
        });

        $scope.campaign = editItem;
        $scope.clients = clients;
        $scope.campaignTypes = campaignTypes;
        $scope.campaignStages = campaignStages;
        $scope.weeksRemaining = 0;
        $scope.leftToGoal = 0;
        $scope.users = _.filter(users, function (user) {
            return user.isAdmin || user.isSupervisor || user.isClientAdmin;
        });
        $scope.agentTargets = {};

        if ($scope.campaign.id) {
            $scope.currentCampaignType = _.findWhere($scope.campaignTypes, {
                id: $scope.campaign.campaigntypeId
            }).name;
        } else {
            $scope.currentCampaignType = '';
        }

        var getAgents = function () {
            var deferred = $q.defer();
            Campaign.getAgents({
                id: $scope.campaign.id
            }).$promise.then(function (results) {
                $scope.agents = results;
                deferred.resolve();
            });
            return deferred.promise;
        };

        if (!$scope.update) {
            //set defaults
            $scope.campaign.startDate = moment().day(1).toDate();
            $scope.campaign.endDate = moment($scope.campaign.startDate).add(1, 'year').toDate();
            $scope.campaign.owningUserId = $rootScope.loggedInUser.id;
        } else {
            $scope.campaign.startDate = moment.utc($scope.campaign.startDate).add('4', 'hours').toDate();
            $scope.campaign.endDate = moment.utc($scope.campaign.endDate).subtract('7', 'hours').endOf('day').toDate();
        }

        $scope.startDateDisabled = function (date, mode) {
            return (mode === 'day' && date.getDay() !== 1);
        };

        $scope.addNewCampaignStage = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.edit.html',
                controller: 'CampaignStageModalEditCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    editItem: function (CampaignStage) {
                        var stage = new CampaignStage();
                        stage.campaignId = $scope.campaign.id;
                        return stage;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    dispositions: function (Disposition) {
                        return Disposition.query().$promise;
                    },
                    skills: function (Campaign) {
                        return Campaign.getLeadSubTypes({
                            id: $scope.campaign.id
                        }).$promise;
                    }
                }
            });

            modalInstance.result.then(function (campaignStage) {
                $scope.campaignStages.push(campaignStage);
                if ($scope.campaignStages.length === 1) {
                    $scope.campaign.initialCampaignStageId = campaignStage.id;
                    $scope.saveCampaign();
                }
            });
        };

        $scope.editCampaignStage = function (stage) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.edit.html',
                controller: 'CampaignStageModalEditCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    editItem: function (CampaignStage) {
                        // reload stage to ensure that dependencies are load
                        return CampaignStage.get({
                            id: stage.id
                        }).$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    dispositions: function (Disposition) {
                        return Disposition.query().$promise;
                    },
                    skills: function (Campaign) {
                        return Campaign.getLeadSubTypes({
                            id: $scope.campaign.id
                        }).$promise;
                    }
                }
            });

            modalInstance.result.then(function (campaignStage) {
                for (var i = 0; i < $scope.campaignStages.length; i++) {
                    if ($scope.campaignStages[i].id === campaignStage.id) {
                        $scope.campaignStages[i] = campaignStage;
                    }
                }
            });
        };

        $scope.deleteCampaignStage = function (stage) {
            SweetAlert.swal({
                title: "Delete Stage",
                text: "Are you user you want to delete " + stage.name + "!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
                function (isConfirm) {
                    if (isConfirm) {
                        var stageId = stage.id;
                        CampaignStage.delete({
                            id: stageId
                        }, function () {
                            $scope.campaignStages = $scope.campaignStages.filter(function (cs) {
                                return cs.id && cs.id !== stageId;
                            });

                            if (!$scope.campaign.initialCampaignStageId || $scope.campaign.initialCampaignStageId === stageId) {
                                if ($scope.campaignStages.length > 0) {
                                    $scope.campaign.initialCampaignStageId = $scope.campaignStages[0].id;
                                } else {
                                    $scope.campaign.initialCampaignStageId = null;
                                }
                            }
                        })
                    }
                });
        };

        $scope.setStageFlow = function (stage) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.workflow.edit.html',
                controller: 'CampaignStageFlowModalEditCtrl',
                backdrop: 'static',
                keyboard: false,
                size: 'lg',
                resolve: {
                    editItem: function (CampaignStage) {
                        return CampaignStage.get({
                            id: stage.id
                        }).$promise;
                    },
                    campaigns: function () {
                        return Client.getCampaigns({
                            id: $scope.campaign.clientId
                        }).$promise;
                    },
                    workflows: function (CampaignStage) {
                        return CampaignStage.getWorkflows({
                            id: stage.id
                        }).$promise;
                    }
                }
            });
        };

        $scope.setStageAgents = function (stage) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.agents.edit.html',
                controller: 'CampaignStageAgentsModalEditCtrl',
                backdrop: 'static',
                keyboard: false,
                size: 'lg',
                resolve: {
                    editItem: function (CampaignStage) {
                        return CampaignStage.get({
                            id: stage.id
                        }).$promise;
                    },
                    campaignStageAgents: function (CampaignStage) {
                        return CampaignStage.getAgents({
                            id: stage.id
                        }).$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    skills: function (Campaign) {
                        return Campaign.getLeadSubTypes({
                            id: $scope.campaign.id
                        }).$promise;
                    }
                }
            });
        };

        $scope.setStageDiallingRules = function (stage) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.dtrules.edit.html',
                controller: 'CampaignStageDTRulesModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function (CampaignStage) {
                        return CampaignStage.get({
                            id: stage.id
                        }).$promise;
                    },
                    campaignStages: function (CampaignStage) {
                        return Campaign.getCampaignStages({
                            id: stage.campaignId
                        }).$promise;
                    },
                    dateTimeRules: function (DateTimeRuleSet) {
                        return DateTimeRuleSet.query().$promise;
                    },
                    campaignStageSkills: function (Campaign) {
                        return Campaign.getLeadSubTypes({
                            id: $scope.campaign.id
                        }).$promise;
                    },
                    campaignStageDTRules: function (CampaignStage) {
                        return CampaignStage.getDateTimeRules({
                            id: stage.id
                        }).$promise;
                    }
                }
            });
        };

        $scope.canImportLeads = function () {
            if ($scope.campaign.id) {
                if ($scope.updateOnly) {
                    $scope.uploader.url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads?updateOnly=true';
                } else {
                    $scope.uploader.url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads';
                }

                Campaign.getUploads({
                    id: $scope.campaign.id
                }).$promise.then(function (uploads) {
                    $scope.uploads = uploads.reverse();
                    for (var i = 0; i < $scope.uploads.length; i++) {
                        getStatus($scope.uploads[i]);
                    }
                })
                return true;
            } else {
                return false;
            }
        };

        function getStatus(upload) {
            if (upload.taskId) {
                System.getThreadTaskProgress({
                    taskId: upload.taskId
                }).$promise
                    .then(function (result) {
                        upload.result = result;
                    })
            }
        }

        $scope.saveCampaign = function () {
            $scope.campaign.dispositions = $scope.selectedDispositions;
            if ($scope.campaign.startDate instanceof Date) {
                $scope.campaign.startDate = moment($scope.campaign.startDate).startOf('day').add('4', 'hours').toDate();
            }
            if ($scope.campaign.endDate instanceof Date) {
                $scope.campaign.endDate = moment($scope.campaign.endDate).subtract(6, 'hours').endOf('day').toDate();
            }

            $scope.currentCampaignType = _.findWhere($scope.campaignTypes, {
                id: $scope.campaign.campaigntypeId
            }).name;

            var deferred = $q.defer();
            if ($scope.update) {
                $scope.campaign.$update(function () {
                    deferred.resolve(true);
                });
            } else {
                //create defaults
                $scope.campaign.$save(function () {
                    createDefaultCampaignStages().then(function () {
                        $rootScope.safeApply(function () {
                            $scope.update = true;
                        });
                        deferred.resolve(true);
                    });
                });
            }

            return deferred.promise;
        };

        $scope.checkLeadImportStatus = function () {
            var deferred = $q.defer();

            Campaign.getUploads({
                id: $scope.campaign.id
            }).$promise.then(function (uploads) {
                if (!uploads || !uploads.length) {
                    return deferred.resolve(true);
                }
                var upload = _.last(uploads);
                if (upload.taskId) {
                    System.getThreadTaskProgress({
                        taskId: upload.taskId
                    }).$promise.then(function (result) {
                        if (result.percentComplete < 100) {
                            SweetAlert.swal({
                                title: "Lead Import still in progress",
                                text: "Continuing could impact other campaign setup pages\n\nAre you sure you want to continue",
                                type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#DD6B55",
                                confirmButtonText: "Yes, continue",
                                cancelButtonText: "No, I'll wait",
                                closeOnConfirm: true,
                                closeOnCancel: true
                            },
                                function (isConfirm) {
                                    if (!isConfirm) {
                                        WizardHandler.wizard().goTo('Import Leads');
                                        deferred.resolve(false);
                                    } else {
                                        deferred.resolve(true);
                                    }
                                });
                        } else {
                            deferred.resolve(true);
                        }
                    });
                } else {
                    if (!upload.percentComplete || upload.percentComplete < 100) {
                        SweetAlert.swal({
                            title: "Lead Import still in progress",
                            text: "Continuing could impact other campaign setup pages\n\nAre you sure you want to continue",
                            type: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "Yes, continue",
                            cancelButtonText: "No, I'll wait",
                            closeOnConfirm: true,
                            closeOnCancel: true
                        },
                            function (isConfirm) {
                                if (!isConfirm) {
                                    WizardHandler.wizard().goTo('Import Leads');
                                    deferred.resolve(false);
                                } else {
                                    deferred.resolve(true);
                                }
                            });
                    } else {
                        deferred.resolve(true);
                    }
                }
            })

            return deferred.promise;
        };

        $scope.wizardCompleted = function () {
            if ($rootScope.loggedInUser.isAdmin) {
                $state.go('admin.campaigns');
            } else {
                $state.go('supervisor.campaigns');
            }
        };

        $scope.dates = {
            start: false,
            end: false
        };

        $scope.openEndDate = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();

            $scope.dates.start = false;
            $scope.dates.end = true;
        };

        $scope.openStartDate = function ($event) {
            $event.preventDefault();
            $event.stopPropagation();

            $scope.dates.end = false;
            $scope.dates.start = true;
        };

        $scope.format = 'dd-MMMM-yyyy';

        $scope.canEnterCampaignProjections = function () {
            var deferred = $q.defer();

            $scope.checkLeadImportStatus().then(function (result) {
                if (!result) {
                    return deferred.resolve(false);
                } else {
                    $scope.loadCampaignProjections().then(function () {
                        return deferred.resolve(true);
                    })
                }
            })

            return deferred.promise;
        }

        $scope.loadCampaignSkills = function () {
            var deferred = $q.defer();

            Campaign.getLeadSubTypes({
                id: $scope.campaign.id
            }).$promise.then(function (skills) {
                $scope.campaignSkills = skills;
                deferred.resolve(true);
            })

            return deferred.promise;
        };

        $scope.addCampaignSkill = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.skills.html',
                controller: 'CampaignWizardAddSkill',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    skills: function () {
                        return SubSkill.query().$promise;
                    }
                }
            });

            modalInstance.result.then(function (skill) {
                var existing = _.findWhere($scope.skillProjections, {
                    id: skill.id
                })
                if (!existing) {
                    skill.projection = {};
                    $scope.skillProjections.push(skill)
                }
            });
        };

        $scope.loadCampaignProjections = function () {
            var deferred = $q.defer();

            Campaign.getLeadTypeAndGroup({
                id: $scope.campaign.id
            }).$promise.then(function (skills) {
                Campaign.getProjections({
                    id: $scope.campaign.id
                }).$promise.then(function (projections) {
                    projections.forEach(function (proj) {
                        var skill = _.findWhere(skills, {
                            id: proj.subskillId
                        });

                        if (skill) {
                            skill.projection = proj;
                        } else {
                            skill = _.findWhere($scope.subskills, {
                                id: proj.subskillId
                            });
                            if (skill) {
                                skill.projection = proj;
                                skills.push(skill);
                            }
                        }
                    })

                    $scope.skillProjections = skills;

                    $scope.updateProjectionTotals();
                    deferred.resolve(true);
                })
            }).catch(function (err) {
                console.log(err);
            })

            return deferred.promise;
        };

        $scope.updateProjectionTotals = function () {
            $scope.projectionTotals = {
                preAvg: 0,
                preQuant: 0,
                preTotal: 0,
                secondTotal: 0,
                grandAvg: 0,
                grandQuant: 0,
                grandTotal: 0
            }

            var skillCount = 0;

            $scope.campaign.goals = {};

            $scope.skillProjections.forEach(function (skill) {
                if (skill.projection) {
                    $scope.projectionTotals.preAvg += skill.projection.projectedAvgValue || 0;
                    $scope.projectionTotals.preTotal += (skill.projection.projectedQuantity || 0) * (skill.projection.projectedAvgValue || 0);
                    $scope.projectionTotals.preQuant += skill.projection.projectedQuantity || 0;

                    if (skill.reportingGroup) {
                        if ($scope.campaign.goals[skill.reportingGroup] === undefined) {
                            $scope.campaign.goals[skill.reportingGroup] = {
                                goal: (skill.projection.projectedQuantity || 0) * (skill.projection.projectedAvgValue || 0),
                                projectedQuantity: skill.projection.clientAvgValue || 0
                            }
                        } else {
                            $scope.campaign.goals[skill.reportingGroup].goal += (skill.projection.projectedQuantity || 0) * (skill.projection.projectedAvgValue || 0);
                            $scope.campaign.goals[skill.reportingGroup].projectedQuantity += skill.projection.clientAvgValue || 0;
                        }
                    }
                }
            })

            $scope.projectionTotals.secondTotal = $scope.campaign.secondAppealProjectedQuantity * $scope.campaign.secondAppealProjectedAvgValue;

            $scope.projectionTotals.grandQuant = $scope.projectionTotals.preQuant + ($scope.campaign.secondAppealProjectedQuantity || 0);
            $scope.projectionTotals.grandTotal = $scope.projectionTotals.preTotal + $scope.projectionTotals.secondTotal;
            $scope.projectionTotals.grandAvg = $scope.projectionTotals.preAvg + $scope.secondAppealProjectedAvgValue;

            $scope.campaign.goal = $scope.projectionTotals.grandTotal;
        };

        $scope.saveProjections = function () {
            for (var i = 0; i < $scope.skillProjections.length; i++) {
                var skill = $scope.skillProjections[i];
                if (skill.projection) {
                    var projection = new CampaignProjections();
                    projection.clientQuantity = skill.projection.clientQuantity;
                    projection.clientAvgValue = skill.projection.clientAvgValue;
                    projection.projectedRR = skill.projection.projectedRR;
                    projection.projectedQuantity = skill.projection.projectedQuantity;
                    projection.projectedAvgValue = skill.projection.projectedAvgValue;
                    projection.campaignId = $scope.campaign.id;
                    projection.subskillId = skill.id;
                    projection.id = skill.projection.id;
                    projection.defaultAskAmount = skill.projection.defaultAskAmount;
                    projection.askAmountIncrease = skill.projection.askAmountIncrease;
                    projection.$save();
                }
            }
        };

        var getWeeks = function () {
            var offset = moment().utcOffset();
            var start = moment($scope.campaign.startDate).day(1).utc();
            var end = moment($scope.campaign.endDate);
            var now = moment().utc();

            $scope.weeks = [];
            var count = 1;
            while (start <= end) {
                var nextWeek = moment(start).add(1, 'weeks');
                var week = {
                    start: start,
                    end: nextWeek,
                    name: 'Week ' + count + ' (' + moment(start).add(offset, 'minutes').format('DD MMM YY') + ' - ' + moment(nextWeek).add(offset, 'minutes').subtract(1, 'day').format('DD MMM YY') + ')',
                    agents: []
                };
                for (var i = 0; i < $scope.agents.length; i++) {
                    var agent = $scope.agents[i];
                    var threshold = 100;
                    if (agent.hourlyRate == 11) {
                        threshold = 60;
                    } else if (agent.hourlyRate == 13) {
                        threshold = 80;
                    }
                    var stats = {
                        agent: agent,
                        target: {
                            thresholdPerHour: threshold,
                            level: 1,
                            scheduledHours: 0,
                            impactHours: 0,
                            threshold: 0,
                            goal: 0,
                            notes: '',
                            overrideGoal: 0
                        },
                        changed: true
                    };
                    stats.target.impactHours = stats.target.level * stats.target.scheduledHours;
                    week.agents.push(stats);
                }
                $scope.weeks.push(week);
                if (now >= start && now <= nextWeek) {
                    $scope.agentTargets.currentWeek = week;
                }
                start = nextWeek;
                count++;
            }

            if (!$scope.agentTargets.currentWeek) {
                $scope.agentTargets.currentWeek = _.last($scope.weeks);
            }
            getAgentTargets();
            return true;
        };

        $scope.getAgentTargetPrerequisities = function () {
            return getAgents()
                .then(getCampaignProgress)
                .then(getWeeks);
        };

        $scope.nextWeek = function () {
            for (var i = 0; i < $scope.weeks.length; i++) {
                if ($scope.agentTargets.currentWeek.name == $scope.weeks[i].name) {
                    if (i < $scope.weeks.length - 1)
                        $scope.agentTargets.currentWeek = $scope.weeks[i + 1];

                    $scope.updateAgentTotals();
                    return;
                }
            }
        };

        $scope.previousWeek = function () {
            for (var i = 0; i < $scope.weeks.length; i++) {
                if ($scope.agentTargets.currentWeek.name == $scope.weeks[i].name) {
                    if (i > 0)
                        $scope.agentTargets.currentWeek = $scope.weeks[i - 1];

                    $scope.updateAgentTotals();
                    return;
                }
            }
        };

        $scope.updateAgentTotals = function (isManual) {
            var week = $scope.agentTargets.currentWeek;

            week.agents.forEach(function (agent) {
                agent.target.impactHours = agent.target.scheduledHours * agent.target.level;
                if (isManual) agent.changed = true;
            })

            week.totalImpactHours = 0;
            week.agents.forEach(function (agent) {
                week.totalImpactHours += agent.target.impactHours || 0;
            })

            week.agents.forEach(function (agent) {
                agent.target.goal = ($scope.weeklyTarget / week.totalImpactHours) * agent.target.impactHours;
            })

            week.totalHours = 0;
            week.totalThreshold = 0;
            week.totalGoal = 0;
            week.totalOverride = 0;
            week.agents.forEach(function (agent) {
                week.totalHours += agent.target.scheduledHours || 0;
                week.totalThreshold += agent.target.thresholdPerHour * agent.target.scheduledHours;
                week.totalGoal += agent.target.goal || 0;
                week.totalOverride += agent.target.overrideGoal || 0;
            })
        };

        $scope.saveAgentTargets = function ($event) {
            var updatedTargets = [];
            var newTargets = [];
            for (var i = 0; i < $scope.weeks.length; i++) {
                var week = $scope.weeks[i];
                for (var j = 0; j < week.agents.length; j++) {
                    var agent = week.agents[j];
                    if (agent.changed) {
                        var target = {
                            id: agent.target.id,
                            agentId: agent.agent.id,
                            campaignId: $scope.campaign.id,
                            start: week.start,
                            end: week.end,
                            level: agent.target.level,
                            scheduledHours: agent.target.scheduledHours,
                            goal: agent.target.goal,
                            overrideGoal: agent.target.overrideGoal,
                            notes: agent.target.notes
                        };

                        if (target.id) {
                            updatedTargets.push(target);
                        } else if (target.goal && target.scheduledHours) {
                            newTargets.push(target);
                        }

                    }
                }
            }

            var promises = [];

            if (updatedTargets.length) {
                for (var i = 0; i < updatedTargets.length; i++) {
                    promises.push(Campaign.updateAgentTarget(updatedTargets[i]));
                }
            }
            if (newTargets.length) {
                promises.push(Campaign.createAgentTarget(newTargets));
            }

            if (promises.length) {
                return $q.all(promises).then(function () {
                    getAgentTargets();
                    return $q.when(true);
                });
            } else {
                return true;
            }
        }

        var getAgentTargets = function () {
            Campaign.getAgentTargets({
                id: $scope.campaign.id
            }).$promise.then(function (results) {
                var offset = moment().utcOffset();
                var start = moment($scope.campaign.startDate).day(1);
                var end = moment($scope.campaign.endDate);
                var now = moment().utc();

                $scope.weeksRemaining = Math.ceil(end.diff(now, 'days') / 7);
                $scope.weeklyTarget = (($scope.campaign.goal - $scope.leftToGoal) / $scope.weeksRemaining).toFixed(0);

                for (var i = 0; i < results.length; i++) {
                    var result = results[i];
                    var start = moment(result.start).day('monday').startOf('day').format('YYYY-MM-DD');
                    var end = moment(result.end).day('sunday').endOf('day').format('YYYY-MM-DD');
                    var week = _.find($scope.weeks, function (wk) {
                        return start === moment(wk.start).day('monday').startOf('day').format('YYYY-MM-DD') && end == moment(wk.end).day('sunday').endOf('day').format('YYYY-MM-DD');
                        // return wk.start.isSame(start, 'day') && wk.end.isSame(end, 'day');
                    });
                    if (week) {
                        var agent = _.find(week.agents, function (ag) {
                            return ag.agent.id == result.agent.id;
                        })
                        if (agent) {
                            agent.changed = false;
                            agent.target.level = result.level;
                            agent.target.scheduledHours = result.scheduledHours;
                            agent.target.goal = result.goal;
                            agent.target.overrideGoal = result.overrideGoal;
                            agent.target.notes = result.notes;
                            agent.target.id = result.id;
                            agent.target.impactHours = agent.target.level * agent.target.scheduledHours;

                            var min = agent.target.thresholdPerHour * agent.target.scheduledHours;
                            if (agent.target.goal <= min) {
                                agent.target.goal = min;
                            }
                        }
                    }
                }

                $scope.updateAgentTotals();
            });
        }

        var getCampaignProgress = function () {
            var deffered = $q.defer();
            Campaign.getProgress({
                id: $scope.campaign.id
            }).$promise.then(function (results) {
                if ($scope.currentCampaignType == "Telefunding") {
                    $scope.leftToGoal = results.giftAmount;
                } else {
                    $scope.leftToGoal = results.saleAmount;
                }

                deffered.resolve();
            });

            return deffered.promise;
        };

        var createDefaultCampaignStages = function () {
            //create default campaignStages
            var deffered = $q.defer();
            Disposition.query(function (result) {
                var dispositions = result;
                var completed = 0;
                var campaignType = _.findWhere($scope.campaignTypes, {
                    id: $scope.campaign.campaigntypeId
                });
                var promises = [];

                var firstStage = new CampaignStage();
                firstStage.name = "1st Appeal";
                firstStage.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    firstStage.startDate = new Date($scope.campaign.startDate);
                } else {
                    firstStage.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    firstStage.endDate = new Date($scope.campaign.endDate);
                } else {
                    firstStage.endDate = $scope.campaign.endDate;
                }
                firstStage.leadContactIntervalHours = 36;
                if (campaignType.name == "Telesales") {
                    firstStage.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "Sale" || disposition.name == "No Resolution";
                    });
                } else {
                    firstStage.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Pledge Credit Card" || disposition.name == "Pledge Invoice" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "No Resolution";
                    });
                }
                firstStage.campaignId = $scope.campaign.id;
                promises.push(firstStage.$save());

                var secondAsk = new CampaignStage();
                secondAsk.name = "Reapproach";
                secondAsk.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    secondAsk.startDate = new Date($scope.campaign.startDate);
                } else {
                    secondAsk.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    secondAsk.endDate = new Date($scope.campaign.endDate);
                } else {
                    secondAsk.endDate = $scope.campaign.endDate;
                }
                secondAsk.leadContactIntervalHours = 36;
                if (campaignType.name == "Telesales") {
                    secondAsk.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Sale" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "No Resolution";
                    });
                } else {
                    secondAsk.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Pledge Credit Card" || disposition.name == "Pledge Invoice" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "No Resolution";
                    });
                }
                secondAsk.campaignId = $scope.campaign.id;
                promises.push(secondAsk.$save());

                var collections = new CampaignStage();
                collections.name = "Collections";
                collections.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    collections.startDate = new Date($scope.campaign.startDate);
                } else {
                    collections.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    collections.endDate = new Date($scope.campaign.endDate);
                } else {
                    collections.endDate = $scope.campaign.endDate;
                }
                collections.leadContactIntervalHours = 36;
                collections.dispositions = _.filter(dispositions, function (disposition) {
                    return disposition.name == "Callback" || disposition.name == "Invoice Requested" || disposition.name == "Invoice Payment" || disposition.name == "No Resolution" || disposition.name == "Collections Standard Refusal" || disposition.name == "Collections Exception Refusal";
                });
                collections.campaignId = $scope.campaign.id;
                promises.push(collections.$save());

                var badCC = new CampaignStage();
                badCC.name = "Bad Credit Cards";
                badCC.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    badCC.startDate = new Date($scope.campaign.startDate);
                } else {
                    badCC.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    badCC.endDate = new Date($scope.campaign.endDate);
                } else {
                    badCC.endDate = $scope.campaign.endDate;
                }
                badCC.leadContactIntervalHours = 36;
                badCC.dispositions = _.filter(dispositions, function (disposition) {
                    return disposition.name == "Callback" || disposition.name == "Invoice Requested" || disposition.name == "Invoice Payment" || disposition.name == "No Resolution" || disposition.name == "Collections Standard Refusal" || disposition.name == "Collections Exception Refusal";
                });
                badCC.campaignId = $scope.campaign.id;
                promises.push(badCC.$save());

                var thankyou = new CampaignStage();
                thankyou.name = "Thank you";
                thankyou.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    thankyou.startDate = new Date($scope.campaign.startDate);
                } else {
                    thankyou.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    thankyou.endDate = new Date($scope.campaign.endDate);
                } else {
                    thankyou.endDate = $scope.campaign.endDate;
                }
                thankyou.leadContactIntervalHours = 36;
                if (campaignType.name == "Telesales") {
                    thankyou.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Sale" || disposition.name == "No Resolution" || disposition.name == "Completed Thank You";
                    });
                } else {
                    thankyou.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Pledge Credit Card" || disposition.name == "Pledge Invoice" || disposition.name == "No Resolution" || disposition.name == "Completed Thank You";
                    });
                }
                thankyou.campaignId = $scope.campaign.id;
                promises.push(thankyou.$save());

                var secondAppeal = new CampaignStage();
                secondAppeal.name = "2nd Appeal";
                secondAppeal.isSystem = true;
                if ($scope.campaign.startDate instanceof Date) {
                    secondAppeal.startDate = new Date($scope.campaign.startDate);
                } else {
                    secondAppeal.startDate = $scope.campaign.startDate;
                }
                if ($scope.campaign.endDate instanceof Date) {
                    secondAppeal.endDate = new Date($scope.campaign.endDate);
                } else {
                    secondAppeal.endDate = $scope.campaign.endDate;
                }
                secondAppeal.leadContactIntervalHours = 36;
                if (campaignType.name == "Telesales") {
                    secondAppeal.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Sale" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "No Resolution";
                    });
                } else {
                    secondAppeal.dispositions = _.filter(dispositions, function (disposition) {
                        return disposition.name == "Callback" || disposition.name == "Pledge Credit Card" || disposition.name == "Pledge Invoice" || disposition.name == "Standard Refusal" || disposition.name == "Exception Refusal" || disposition.name == "No Resolution";
                    });
                }
                secondAppeal.campaignId = $scope.campaign.id;
                promises.push(secondAppeal.$save());

                $q.all(promises).then(function (stages) {
                    $scope.campaignStages = stages;
                    $scope.campaign.initialCampaignStageId = stages[0].id;
                    $scope.campaign.$update(function () {
                        createFlow().then(function () {
                            deffered.resolve(true);
                        });
                    });
                });
            })

            return deffered.promise;
        }

        var createFlow = function () {
            var promises = [];

            for (var i = 0; i < $scope.campaignStages.length; i++) {
                var stage = $scope.campaignStages[i];

                switch (stage.name) {
                    case "1st Appeal":
                        promises.push(set1stAppealStageFlow(stage));
                        break;
                    case "Reapproach":
                        promises.push(set2ndAskStageFlow(stage));
                        break;
                    case "Bad Credit Cards":
                    case "Collections":
                        promises.push(setCollectionsStageFlow(stage));
                        break;
                    case "Thank you":
                        promises.push(setThankyouStageFlow(stage));
                        break;
                    case "2nd Appeal":
                        promises.push(set2ndAppealStageFlow(stage));
                        break;
                }
            };

            return $q.all(promises);
        }

        var set1stAppealStageFlow = function (stage) {
            var workflows = [];

            for (var i = 0; i < stage.dispositions.length; i++) {
                var disposition = stage.dispositions[i];
                var workflow = {};
                workflow.stageId = stage.id;
                workflow.dispositionId = disposition.id;
                workflow.dontContactLeadForHours = 24;

                var wfStage = null;

                switch (disposition.name) {
                    case "Pledge Credit Card":
                    case "Sale":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Thank you"
                        });
                        break;
                    case "Pledge Invoice":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Collections"
                        });
                        break;
                    case "Standard Refusal":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Reapproach"
                        });
                        break;
                }

                if (wfStage)
                    workflow.destination = wfStage.id;

                workflows.push(workflow);
            };

            return CampaignStage.setWorkflows({
                id: stage.id
            }, workflows).$promise;
        }

        var set2ndAskStageFlow = function (stage) {
            var workflows = [];

            for (var i = 0; i < stage.dispositions.length; i++) {
                var disposition = stage.dispositions[i];
                var workflow = {};
                workflow.stageId = stage.id;
                workflow.dispositionId = disposition.id;
                workflow.dontContactLeadForHours = 24;

                var wfStage = null;

                switch (disposition.name) {
                    case "Pledge Credit Card":
                    case "Sale":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Thank you"
                        });
                        break;
                    case "Pledge Invoice":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Collections"
                        });
                        break;
                }

                if (wfStage)
                    workflow.destination = wfStage.id;

                workflows.push(workflow);
            };

            return CampaignStage.setWorkflows({
                id: stage.id
            }, workflows).$promise;
        }

        var setCollectionsStageFlow = function (stage) {
            var workflows = [];

            for (var i = 0; i < stage.dispositions.length; i++) {
                var disposition = stage.dispositions[i];
                var workflow = {};
                workflow.stageId = stage.id;
                workflow.dispositionId = disposition.id;
                workflow.dontContactLeadForHours = 24;

                var wfStage = null;

                switch (disposition.name) {
                    case "Collections Standard Refusal":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "2nd Appeal"
                        });
                        break;
                    case "Invoice Payment":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Thank you"
                        });
                        break;
                }

                if (wfStage)
                    workflow.destination = wfStage.id;

                workflows.push(workflow);
            };

            return CampaignStage.setWorkflows({
                id: stage.id
            }, workflows).$promise;
        }

        var setThankyouStageFlow = function (stage) {
            var workflows = [];

            for (var i = 0; i < stage.dispositions.length; i++) {
                var disposition = stage.dispositions[i];
                var workflow = {};
                workflow.stageId = stage.id;
                workflow.dispositionId = disposition.id;
                workflow.dontContactLeadForHours = 24;

                var wfStage = null;

                switch (disposition.name) {
                    case "Pledge Credit Card":
                    case "Sale":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Thank you"
                        });
                        break;
                    case "Pledge Invoice":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Collections"
                        });
                        break;
                    case "Completed Thank You":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "2nd Appeal"
                        });
                        break;
                }

                if (wfStage)
                    workflow.destination = wfStage.id;

                workflows.push(workflow);
            };

            return CampaignStage.setWorkflows({
                id: stage.id
            }, workflows).$promise;
        }

        var set2ndAppealStageFlow = function (stage) {
            var workflows = [];

            for (var i = 0; i < stage.dispositions.length; i++) {
                var disposition = stage.dispositions[i];
                var workflow = {};
                workflow.stageId = stage.id;
                workflow.dispositionId = disposition.id;
                workflow.dontContactLeadForHours = 24;

                var wfStage = null;

                switch (disposition.name) {
                    case "Pledge Credit Card":
                    case "Sale":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Thank you"
                        });
                        break;
                    case "Pledge Invoice":
                        wfStage = _.findWhere($scope.campaignStages, {
                            name: "Collections"
                        });
                        break;
                }

                if (wfStage)
                    workflow.destination = wfStage.id;

                workflows.push(workflow);
            };

            return CampaignStage.setWorkflows({
                id: stage.id
            }, workflows).$promise;
        }
    });