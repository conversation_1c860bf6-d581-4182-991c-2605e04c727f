'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CampaignInvoicesCtrl', function ($scope, $rootScope, $uibModal, $window, APP_SETTINGS, invoices, Invoice, SweetAlert) {
		$rootScope.pageTitle = 'Invoices';
		$scope.pagination = {
			current: 1
		};
		$scope.invoices = invoices;
		$scope.sortReverse = false;
		$scope.sortType = 'lead.id';

		$scope.generate = function (invoice) {
			Invoice.generate({
				id: invoice.id
			}).$promise.then(function (result) {
				if (result.error) {
					sweetAlert("Error", result.error, "error");
				} else if (result.id) {
					$window.location.href = APP_SETTINGS.BASE_API_URL + 'invoicehistory/' + result.id + '/pdf?access_token=' + $window.sessionStorage.token + '&x_key=' + $window.sessionStorage.user;
				} else {
					sweetAlert("Success", "Creation Complete", "success");
				}
			})
		};

		$scope.badPayment = function (invoice) {
			SweetAlert.swal({
				title: 'Flag as bad payment',
				text: 'This will move this lead into collections and reset the amount paid\n\nDo you wish to continue?',
				type: 'warning',
				showCancelButton: true,
				confirmButtonText: "Yes",
				cancelButtonText: "No",
				closeOnConfirm: true,
				closeOnCancel: true
			}, function (isConfirm) {
				if (isConfirm) {
					//set amount paid to 0 and move lead to collections campaign stage
					invoice.amountRemaining = invoice.grandTotal;
					invoice.writtenOff = false;
					invoice.writtenOffAmount = 0;
					invoice.sendInvoice = false;
					Invoice.update({
						id: invoice.id
					}, invoice);

					Campaign.getCampaignStages({
						id: invoice.campaign.id
					}).$promise.then(function (stages) {
						var collectionsStage = _.findWhere(stages, {
							name: 'Collections'
						});
						if (collectionsStage) {
							Campaign.moveLeadToCampaignStage({
								id: invoice.campaign.id
							}, {
								leadId: invoice.lead.id,
								newCampaignStageId: collectionsStage.id
							});
						}
					})
				}
			})
		};

		$scope.writeoff = function (invoice) {
			SweetAlert.swal({
				title: 'Write off balance',
				text: 'This will stop any payments\n\nDo you wish to continue?',
				type: 'warning',
				showCancelButton: true,
				confirmButtonText: "Yes",
				cancelButtonText: "No",
				closeOnConfirm: true,
				closeOnCancel: true
			}, function (isConfirm) {
				if (isConfirm) {
					Invoice.writeOff({
						id: invoice.id
					}, {
						amount: null
					}).$promise
					.then(updatedInvoice => {
						invoice.writtenOff = updatedInvoice.writtenOff
						invoice.writtenOffAmount = updatedInvoice.writtenOffAmount
						invoice.amountRemaining = updatedInvoice.amountRemaining
						invoice.sendInvoice = updatedInvoice.sendInvoice
					});
				}
			})
		};

		$scope.addPayment = function (invoice) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/collections/invoices/collections.invoices.payment.html',
				controller: 'InvoicePaymentCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					invoice: function () {
						return invoice;
					}
				}
			});

			modalInstance.result.then(function (payAmount) {
				invoice.amountRemaining = invoice.amountRemaining - payAmount;
				Invoice.update({
					id: invoice.id
				}, invoice);
			});
		};
	})