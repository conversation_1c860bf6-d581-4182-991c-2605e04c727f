'use strict';

angular.module('dialerFrontendApp')
    .controller('CampaignSuppressedEditCtrl', function ($scope, $rootScope, $http, $uibModalInstance, APP_SETTINGS, moment, suppressed, stages) {
        $scope.stages = stages;
        $scope.suppressed = angular.copy(suppressed);

        $scope.startDateOpened = false;
        $scope.endDateOpened = false;

        $scope.openStartDate = function () {
            $scope.startDateOpened = true;
        };

        $scope.openEndDate = function () {
            $scope.endDateOpened = true;
        };

        $scope.save = function () {
            if (!$scope.suppressed.endDate) $scope.suppressed.endDate = null;
            if (!$scope.suppressed.campaignstageId) $scope.suppressed.campaignstageId = null;

            if ($scope.suppressed.startDate) $scope.suppressed.startDate = moment($scope.suppressed.startDate).startOf('day').toISOString()
            if ($scope.suppressed.endDate) $scope.suppressed.endDate = moment($scope.suppressed.endDate).startOf('day').toISOString()

            if (suppressed.id) $http.put(APP_SETTINGS.BASE_API_URL + 'suppressed/' + suppressed.id, $scope.suppressed).then($uibModalInstance.close);
            else $http.post(APP_SETTINGS.BASE_API_URL + 'suppressed', $scope.suppressed).then($uibModalInstance.close);
        };

        $scope.cancel = $uibModalInstance.dismiss
        $scope.finish = $uibModalInstance.close;
    });
