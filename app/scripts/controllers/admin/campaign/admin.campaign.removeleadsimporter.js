'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignRemoveLeadsImporterCtrl', function($scope, $rootScope, $timeout, $window, campaignId, Campaign, System, FileUploader, SweetAlert, APP_SETTINGS) {
    $rootScope.pageTitle = 'Configure | Remove Leads Tool';
    $scope.stages = [];
    $scope.chosenStage;

    Campaign.getCampaignStages({
      id: campaignId
    }).$promise.then(function(stages) {
      $scope.stages = stages;
    })

    $scope.uploadItem = function(item) {
      var message = 'This deletes any call attempts or callresults for these leads and deletes the leads from KAOS entirely\r\n\r\nDo you wish to continue?';

      SweetAlert.swal({
          title: "Upload",
          text: message,
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes",
          cancelButtonText: "No",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function(isConfirm) {
          if (isConfirm) {
            item.url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaignId + '/batchremoveleads';
            item.upload();
          }
        });
    };

    $scope.uploader = new FileUploader({
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      },
      onCompleteItem: function(item, response) {
        (function updateProgress(taskId) {
          System.getThreadTaskProgress({
              taskId: taskId
            }).$promise
            .then(function(result) {
              item.progressPercent = result.percentComplete;
              if (item.progressPercent < 100 && !$scope.$$destroyed) {
                $timeout(function() {
                  updateProgress(taskId)
                }, 1000);
              }
            });
        })(response.taskId);
      }
    });
  });