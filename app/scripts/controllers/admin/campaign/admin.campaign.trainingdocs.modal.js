'use strict';

angular.module('dialerFrontendApp')
    .controller('TrainingDocsModalCtrl', function ($scope, $rootScope, $http, $uibModalInstance, APP_SETTINGS, editItem) {
        $scope.editItem = editItem;

        $scope.save = function () {
            if ($scope.editItem.id) {
                $http.put(APP_SETTINGS.BASE_API_URL + 'campaigntrainingdocs/' + $scope.editItem.id, $scope.editItem).then(function () {
                    $uibModalInstance.close();
                });
            } else {
                $scope.editItem.$save(function () {
                    $uibModalInstance.close();
                });
            }
        };

        $scope.cancel = $uibModalInstance.close;
    })