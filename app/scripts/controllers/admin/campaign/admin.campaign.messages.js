'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignMessageCtrl', function ($scope, $state, $rootScope, messages, CampaignMessage, Agent, agents) {
    $rootScope.pageTitle = 'Configure | Campaign | Messages';

    $scope.messages = messages.reverse();
    $scope.availableAgents = agents;
    // var agents = Agent.query().$promise.then(function (result) {
    //     $scope.availableAgents = result;
    // })

    $scope.addMessage = function () {
    	if ($scope.newMessage) {
    		var newMessage = new CampaignMessage();
    		newMessage.author = $rootScope.loggedInUser.name;
    		newMessage.content = $scope.newMessage;
    		newMessage.campaignId = $state.params.id;
    		newMessage.$save(function () {
                $rootScope.safeApply(function () {
                    $scope.messages.unshift(newMessage);
                });
    		});
    	}
    };

    $scope.deleteMessage = function (message) {
        message.$delete(function () {
            $scope.messages = $scope.messages.filter(function (m) {
              return m.id && m.id !== message.id;
            });
        });
    };
  });