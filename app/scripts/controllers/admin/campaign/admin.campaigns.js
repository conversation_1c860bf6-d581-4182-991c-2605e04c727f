'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminCampaignsCtrl', function ($scope, $rootScope, $uibModal, $interval, $timeout, APP_SETTINGS, $http, User, moment, Campaign, CampaignLeads, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Campaigns';

    User.get({
      id: $rootScope.loggedInUser.id
    }).$promise.then(function (user) {
      if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
        $timeout(function() {
            $rootScope.changePassword(false);
        });
      }
    })

    var campaigns = Campaign.query(function () {
      $scope.campaigns = campaigns;

      for (var i = 0; i < $scope.campaigns.length; i++) {
        $scope.campaigns[i].leads = CampaignLeads.count({
          id: $scope.campaigns[i].id
        });
      }
    });

    $scope.deleteCampaign = function (campaign) {
      SweetAlert.swal({
          title: "Delete Campaign",
          text: "Are you user you want to delete " + campaign.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var campaignId = campaign.id;
            campaign.$delete(function () {
              $scope.campaigns = $scope.campaigns.filter(function (c) {
                return c.id && c.id !== campaignId;
              });
            });
          }
        });
    };
  });