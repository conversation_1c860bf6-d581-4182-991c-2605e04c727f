'use strict';

angular.module('dialerFrontendApp')
    .controller('CampaignImportChangesHistoryCtrl', function ($scope, $rootScope, $http, DownloadUtils, campaign, stages, SweetAlert, APP_SETTINGS) {
        $rootScope.pageTitle = campaign.name + ' | Batch Change History';
        $scope.history = [];
        $scope.total = 0;
        $scope.page = 1;
        $scope.limit = 15;
        $scope.loading = false;
        $scope.type = '';

        $scope.getHistory = function () {
            $scope.loading = true;
            $scope.history.length = 0;
            var page = $scope.page - 1
            var queryStr = '?offset=' + (page * $scope.limit) + '&limit=' + $scope.limit
            if ($scope.type) queryStr += '&type=' + $scope.type;
            $http.get(APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaign.id + '/batchchangehistory' + queryStr).then(function (response) {
                $rootScope.safeApply(function () {
                    $scope.loading = false;
                    $scope.total = response.data.count;
                    $scope.history = response.data.rows;
                })
            })
        };

        $scope.getStage = function (id) {
            if (!id) return 'No Stage';
            if (id === -1) return 'Current Campaign Stage';
            var stage = _.findWhere(stages, {
                id: id
            })
            if (stage) return stage.name;
            return 'Unknown'
        };

        $scope.pageChanged = function (page) {
            $scope.page = page;
            $scope.getHistory();
        };

        $scope.download = function (history) {
            DownloadUtils.download(APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaign.id + '/batchchangehistory/' + history.id + '/download', history.filename);
        };

        $scope.getHistory();
    })