'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignTrainingDocImportCtrl', function ($scope, $window, $rootScope, FileUploader, editItem, APP_SETTINGS) {
    $rootScope.pageTitle = editItem.name + ' | Training Materials';

    var campaignId = editItem.id;

    $scope.uploader = new FileUploader({
    	url: APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaignId + '/uploadtrainingdocs',
    	headers: {
          'X-Access-Token': $window.sessionStorage.token,
          'X-Key': $window.sessionStorage.user
    	}
    });
  });