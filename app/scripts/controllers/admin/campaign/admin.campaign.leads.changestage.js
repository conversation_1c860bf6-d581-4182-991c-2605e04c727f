'use strict';

angular.module('dialerFrontendApp')
	.controller('CampaignLeadsChangeStageModalCtrl', function ($scope, $rootScope, $uibModalInstance, currentStageId, stages, editItem) {
		$scope.currentStageId = currentStageId;
		$scope.existingStage = currentStageId;
		$scope.stages = stages;
		$scope.editItem = editItem;

		$scope.ok = function () {
			$uibModalInstance.close($scope.currentStageId);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}
	})