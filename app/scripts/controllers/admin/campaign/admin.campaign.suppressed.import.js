'use strict';

angular.module('dialerFrontendApp')
    .controller('CampaignSuppressedImportCtrl', function ($scope, $rootScope, $timeout, $http, $uibModalInstance, APP_SETTINGS, System, moment, campaign, stages) {
        // $uibModalInstance.close($scope.filters);
        $scope.stages = [{ id: -1, name: 'Current Campaign Stage' }].concat(stages);
        $scope.campaignstageId = -1;
        $scope.startDate = new Date();
        $scope.endDate = undefined;
        $scope.updateOnly = false;

        $scope.startDateOpened = false;
        $scope.endDateOpened = false;

        $scope.openStartDate = function (ev) {
            $scope.startDateOpened = true;
        };

        $scope.openEndDate = function (ev) {
            $scope.endDateOpened = true;
        };

        $scope.importing = false;
        $scope.processing = false;
        $scope.finished = false;
        $scope.error = '';
        $scope.percentComplete = 0;
        $scope.progress = {};

        $scope.import = function () {
            var files = document.getElementById('suppressionFile').files;
            if (!files || !files.length) return
            var file = files[0];
            console.log(file);

            $scope.importing = true;
            $scope.error = false;

            var queryStr = '?campaignId=' + campaign.id
            if ($scope.campaignstageId) queryStr += '&campaignstageId=' + $scope.campaignstageId
            if ($scope.startDate) queryStr += '&startDate=' + moment($scope.startDate).startOf('day').toISOString()
            if ($scope.endDate) queryStr += '&endDate=' + moment($scope.endDate).startOf('day').toISOString()
            if ($scope.updateOnly) queryStr += '&updateOnly=true'

            var data = new FormData();
            data.append('file', file);

            $http.post(APP_SETTINGS.BASE_API_URL + 'suppressed/import' + queryStr, data, {
                transformRequest: angular.identity,
                headers: { 'Content-Type': undefined }
            }).success(function (response) {
                console.log(response)
                $scope.importing = false;
                if (response && response.taskId) {
                    $scope.processing = true;
                    (function updateProgress(taskId) {
                        System.getThreadTaskProgress({
                            taskId: taskId
                        }).$promise.then(function (result) {
                            $scope.percentComplete = result.percentComplete;
                            $scope.progress = result.data;
                            if ($scope.percentComplete < 100 && !$scope.$$destroyed) {
                                $timeout(function () {
                                    updateProgress(taskId)
                                }, 1000);
                            } else {
                                $scope.finished = true;
                            }
                        });
                    })(response.taskId);
                } else {
                    $scope.error = 'Something Went Wrong';
                }
            }).error(function (response) {
                console.log(response)
            })

        };

        $scope.cancel = $uibModalInstance.dismiss
        $scope.finish = $uibModalInstance.close;
    });
