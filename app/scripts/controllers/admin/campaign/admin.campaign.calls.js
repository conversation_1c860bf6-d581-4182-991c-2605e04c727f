'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignCallsCtrl', function ($scope, $rootScope, calls) {
    $rootScope.pageTitle = 'Configure | Campaign | Calls';

    $scope.calls = calls;

    $scope.stringifyDateTimeRule = function (callAttempt) {
    	return 'Between ' + callAttempt.startTime + ' and ' + callAttempt.endTime + ' on ' + getDaysOfWeekAsString(callAttempt);
    };

    var getDaysOfWeekAsString = function (dateTimeRuleSet) {
      var days = [];

      if (dateTimeRuleSet.monday) { days.push('Monday'); }
      if (dateTimeRuleSet.tuesday) { days.push('Tuesday'); }
      if (dateTimeRuleSet.wednesday) { days.push('Wednesday'); }
      if (dateTimeRuleSet.thursday) { days.push('Thursday'); }
      if (dateTimeRuleSet.friday) { days.push('Friday'); }
      if (dateTimeRuleSet.saturday) { days.push('Saturday'); }
      if (dateTimeRuleSet.sunday) { days.push('Sunday'); }

      if (days.length === 2 && days.indexOf('Saturday') >= 0 && days.indexOf('Sunday') >= 0) {
        return 'Weekends';
      }
      if (days.length === 5 && days.indexOf('Saturday') === -1 && days.indexOf('Sunday') === -1) {
        return 'Weekdays';
      }
      if (days.length === 7) {
        return 'any day';
      }

      return days.join(', ');
    };
  });