'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignImportChangesCtrl', function ($scope, $rootScope, $timeout, $window, campaign, Campaign, System, FileUploader, SweetAlert, APP_SETTINGS) {
    $rootScope.pageTitle = campaign.name + ' | Import Changes';
    $scope.stages = [];
    $scope.chosenStage;
    $scope.campaign = campaign;

    Campaign.getCampaignStages({
      id: campaign.id
    }).$promise.then(function (stages) {
      $scope.stages = stages;
    })

    $scope.uploadItem = function (item) {
      var message = '';
      switch ($scope.importType) {
        case 'batchpayments':
          message = 'Partial payments leave the leads in their current stages and full payments use the stage workflow to transition the leads\r\n\r\nDo you wish to continue?';
          break;
        // case 'batchremoveleads':
        //   message = 'This deletes any call attempts or callresults for these leads and deletes the leads from KAOS entirely\r\n\r\nDo you wish to continue?';
        //   break;
        case 'batchchangestage':
          message = 'This will transtion the leads the chosen stage, you can enter no stage to suppress these leads from calling\r\n\r\nDo you wish to continue?';
          break;
        default:
          message = 'Do you wish to continue?';
      }
      SweetAlert.swal({
        title: "Upload",
        text: message,
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "Yes",
        cancelButtonText: "No",
        closeOnConfirm: true,
        closeOnCancel: true
      },
        function (isConfirm) {
          if (isConfirm) {
            if ($scope.importType == 'batchchangestage') {
              item.url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaign.id + '/batchchangestage/' + ($scope.chosenStage || 0);
            } else {
              item.url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaign.id + '/' + $scope.importType;
            }

            item.upload();
          }
        });
    };

    $scope.uploader = new FileUploader({
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      },
      onCompleteItem: function (item, response) {
        (function updateProgress(taskId) {
          System.getThreadTaskProgress({
            taskId: taskId
          }).$promise
            .then(function (result) {
              item.progressPercent = result.percentComplete;
              if (item.progressPercent < 100 && !$scope.$$destroyed) {
                $timeout(function () {
                  updateProgress(taskId)
                }, 1000);
              }
            });
        })(response.taskId);
      }
    });
  });