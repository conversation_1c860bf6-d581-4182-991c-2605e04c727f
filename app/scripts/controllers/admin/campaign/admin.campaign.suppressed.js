'use strict';

angular.module('dialerFrontendApp')
    .controller('CampaignSuppressedCtrl', function ($scope, $rootScope, $http, $uibModal, APP_SETTINGS, campaign, stages, SweetAlert) {
        $rootScope.pageTitle = campaign.name + ' | Suppressed Leads';
        $scope.campaign = campaign;
        $scope.suppressions = [];
        $scope.total = 0;
        $scope.page = 1;
        $scope.limit = 15;
        $scope.filters = {
            finished: "",
            leadId: "",
            clientRef: "",
            campaignstageId: ""
        };
        $scope.loading = false;
        $scope.stages = stages;

        $scope.getSupressed = function () {
            $scope.loading = true;
            $scope.suppressions.length = 0;
            var page = $scope.page - 1
            var queryStr = '?campaignId=' + campaign.id + '&offset=' + (page * $scope.limit) + '&limit=' + $scope.limit
            for (var prop in $scope.filters) {
                if (prop === 'finished' && $scope.filters[prop] === 'skipped') {
                    queryStr += "&skipped=true"
                } else if ($scope.filters[prop]) queryStr += ('&' + prop + '=' + $scope.filters[prop])
            }
            $http.get(APP_SETTINGS.BASE_API_URL + 'suppressed' + queryStr).then(function (response) {
                $rootScope.safeApply(function () {
                    $scope.loading = false;
                    $scope.total = response.data.count;
                    $scope.suppressions = response.data.rows;
                })

            })
        };

        $scope.search = function () {
            $scope.page = 1;
            $scope.getSupressed();
        };

        $scope.pageChanged = function (page) {
            $scope.page = page;
            $scope.getSupressed();
        };

        $scope.import = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.suppressed.import.html',
                controller: 'CampaignSuppressedImportCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    campaign: function () { return campaign },
                    stages: function () { return stages }
                }
            });

            modalInstance.result.then(function () {
                $scope.search();
            })
        };

        $scope.finish = function (row) {
            var stage = row.campaignstage ? row.campaignstage.name : 'No Stage'
            var name = row.lead.first_name + ' ' + row.lead.last_name
            SweetAlert.swal({
                title: 'Finish Suppression Now',
                text: 'This will move ' + name + ' to ' + stage + '\n\nDo you wish to continue?',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'suppressed/' + row.id + '/finish').then($scope.getSupressed);
                }
            })
        };

        $scope.edit = function (row) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.suppressed.edit.html',
                controller: 'CampaignSuppressedEditCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    suppressed: function () { return row },
                    stages: function () { return stages }
                }
            });

            modalInstance.result.then(function () {
                $scope.getSupressed();
            })
        };

        $scope.cancel = function (row) {
            SweetAlert.swal({
                title: 'Cancel Suppression',
                text: 'Are you sure you want to cancel this Suppression?',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.delete(APP_SETTINGS.BASE_API_URL + 'suppressed/' + row.id).then($scope.getSupressed);
                }
            })
        };

        $scope.getSupressed();
    })