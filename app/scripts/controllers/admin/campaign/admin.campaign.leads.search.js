'use strict';

angular.module('dialerFrontendApp')
	.controller('CampaignLeadsSearchModalCtrl', function ($scope, $rootScope, $uibModalInstance, campaignId, filters) {
		if (filters['$or']) {
			filters.phone = filters['$or'][0].phone_home;
			delete filters['$or'];
		}
		$scope.filters = filters;

		$scope.search = function () {
			for (var prop in $scope.filters) {
				if (!$scope.filters[prop])
					delete $scope.filters[prop];
			}

			if ($scope.filters.phone) {
				var phone = $scope.filters.phone;
				$scope.filters['$or'] = [{
					phone_home: phone
				}, {
					phone_mobile: phone
				}, {
					phone_work: phone
				}, {
					phone_workmobile: phone
				}];
			} else {
				delete $scope.filters['$or'];
			}

			$uibModalInstance.close($scope.filters);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}
	})