'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignTrainingDocsCtrl', function ($scope, $rootScope, campaign, $uibModal, docs, Campaign, CampaignTrainingDoc) {
    $rootScope.pageTitle = `${campaign.name} | Training Documents`;

    $scope.campaign = campaign;
    $scope.docs = docs;

    $scope.getLink = function (doc) {
      if (doc.link) return doc.link
      return 'trainingdocs/' + campaign.id + '/' + doc.path.replace(/^.*[\\\/]/, '');
    };

    $scope.edit = function (doc) {
      var modalInstance = $uibModal.open({
        animation: true,
        keyboard: false,
        templateUrl: 'views/admin/campaign/admin.campaign.trainingdocs.modal.html',
        controller: 'TrainingDocsModalCtrl',
        resolve: {
          editItem: function () {
            return angular.copy(doc)
          }
        }
      });

      modalInstance.result.then(function (result) {
        Campaign.getDocs({
          id: campaign.id
        }).$promise.then(results => {
          $scope.docs = results
        })
      });
    };

    $scope.create = function () {
      var modalInstance = $uibModal.open({
        animation: true,
        keyboard: false,
        templateUrl: 'views/admin/campaign/admin.campaign.trainingdocs.modal.html',
        controller: 'TrainingDocsModalCtrl',
        resolve: {
          editItem: function () {
            var doc = new CampaignTrainingDoc()
            doc.campaignId = campaign.id
            return doc;
          }
        }
      });

      modalInstance.result.then(function (result) {
        Campaign.getDocs({
          id: campaign.id
        }).$promise.then(results => {
          $scope.docs = results
        })
      });
    };

    $scope.delete = function (doc) {
      var modalInstance = $uibModal.open({
        animation: true,
        keyboard: false,
        templateUrl: 'views/common/messageboxes/dialogModal.html',
        controller: 'DialogModalCtrl',
        resolve: {
          options: function () {
            return {
              yes: 'Yes',
              no: 'No',
              title: 'Delete ' + doc.name,
              content: 'Are you sure you want to delete this training document?'
            }
          }
        }
      });

      modalInstance.result.then(function (result) {
        if (result) {
          var docId = doc.id;
          CampaignTrainingDoc.delete({
            id: docId
          }).$promise.then(function () {
            $scope.docs = $scope.docs.filter(function (cs) {
              return cs.id && cs.id !== docId;
            });
          });
        }
      });
    };
  });