'use strict';

angular.module('dialerFrontendApp')
    .controller('CampaignLeadCtrl', function ($scope, $rootScope, $uibModal, $http, APP_SETTINGS, campaign, campaignStages, filters, Client, Lead, Campaign, CampaignLeads, Callback, CampaignStage, _, <PERSON><PERSON><PERSON>t, CallResult) {
        $rootScope.pageTitle = `${campaign.name} | Leads`;
        $scope.leads = [];
        $scope.totalLeads = 0;
        $scope.leadsPerPage = 30;
        $scope.sortType = "last_name";
        $scope.sortReverse = false;
        $scope.currentFilters = {};
        $scope.campaign = campaign;

        getResultsPage(1);

        $scope.pagination = {
            current: 1
        };

        $scope.displayedColumns = [{
            label: 'KAOS Id',
            field: 'id'
        }, {
            label: 'Client Ref',
            field: 'clientRef'
        }, {
            label: 'First Name',
            field: 'first_name'
        }, {
            label: 'Last Name',
            field: 'last_name'
        }, {
            label: 'Phone 1',
            field: 'phone_home'
        }, {
            label: 'Phone 2',
            field: 'phone_work'
        }, {
            label: 'Phone 3',
            field: 'phone_mobile'
        }, {
            label: 'Phone 4',
            field: 'phone_workmobile'
        }, {
            label: 'Contact Delay',
            field: 'dontContactUntil'
        }];

        if (campaign.campaigntype.name == 'Telefunding') {
            $scope.displayedColumns.push({
                label: 'Lead Type',
                field: 'tfSubSkill.name'
            })
        } else {
            $scope.displayedColumns.push({
                label: 'Lead Type',
                field: 'tmSubSkill.name'
            })
        }

        $scope.pageChanged = function (newPageNumber) {
            getResultsPage(newPageNumber);
        };

        $scope.filterChanged = function (field) {
            $scope.sortType = field;
            $scope.sortReverse = !$scope.sortReverse;
            $scope.pagination.current = 1;
            getResultsPage(1);
        };

        $scope.friendly = function (field) {
            var result = _.findWhere($scope.displayedColumns, {
                field: field
            });

            if (result) {
                return result.label;
            }

            switch (field) {
                default: return field;
            }
        };

        $scope.search = function () {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.leads.search.html',
                controller: 'CampaignLeadsSearchModalCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    filters: function () {
                        return $scope.currentFilters;
                    },
                    campaignId: function () {
                        return $scope.campaign.id
                    }
                }
            })

            modalInstance.result.then(function (filters) {
                $scope.filters = filters;
                $scope.pagination.current = 1;
                getResultsPage(1);
            })
        };

        $scope.editLead = function (lead) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.edit.html',
                controller: 'LeadModalEditCtrl',
                backdrop: 'static',
                size: 'lg',
                keyboard: false,
                resolve: {
                    editItem: function () {
                        return Lead.get({
                            id: lead.id
                        }).$promise;
                    },
                    existingItem: function () {
                        return lead;
                    },
                    agentId: function () {
                        return $rootScope.loggedInUser.agentId || 0;
                    }
                }
            });

            modalInstance.result.then(function (result) {
                var existing = _.findWhere($scope.leads, {
                    id: result.id
                })

                updateLeadInfo(existing, result);
            })
        };

        $scope.removeLead = function (lead) {
            SweetAlert.swal({
                title: "Remove Lead from Campaign",
                text: "Are you user you want to remove " + lead.first_name + "!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#DD6B55",
                confirmButtonText: "Yes, remove them!",
                cancelButtonText: "No, cancel",
                closeOnConfirm: true,
                closeOnCancel: true
            },
                function (isConfirm) {
                    if (isConfirm) {
                        CampaignLeads.delete({
                            id: $scope.campaign.id,
                            leadId: lead.id
                        }).$promise.then(function () {
                            $scope.leads = $scope.leads.filter(function (c) {
                                return c.id && c.id !== lead.id;
                            });
                        })
                    }
                });
        };

        $scope.changeStage = function (lead) {
            //throw up a modal asking for the destination
            //on return call Campaign.moveLeadToCampaignStage
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.leads.changestage.html',
                controller: 'CampaignLeadsChangeStageModalCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    currentStageId: function () {
                        return lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : null;
                    },
                    stages: function () {
                        return campaignStages;
                    },
                    editItem: function () {
                        return lead
                    }
                }
            });

            modalInstance.result.then(function (result) {
                Campaign.moveLeadToCampaignStage({
                    id: campaign.id
                }, {
                    leadId: lead.id,
                    newCampaignStageId: result || null
                }).$promise.then(function (res) {
                    if (result) {
                        if (lead.campaignleads.length) {
                            lead.campaignleads[0].currentCampaignStageId = result;
                        } else {
                            lead.campaignleads = [{
                                currentCampaignStageId: result
                            }];
                        }
                    } else {
                        lead.campaignleads = [];
                    }
                })
            })
        };

        $scope.createCallback = function (lead) {
            $uibModal.open({
                templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
                controller: 'SupervisorCallbackCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    callback: function () {
                        return new Callback();
                    },
                    callresult: function () {
                        return new CallResult();
                    },
                    lead: function () {
                        return lead;
                    },
                    campaign: function () {
                        return campaign;
                    },
                    agents: function () {
                        return Campaign.getAgents({
                            id: campaign.id
                        }).$promise;
                    },
                    client: function () {
                        return Client.get({
                            id: campaign.clientId
                        }).$promise;
                    },
                    stage: function () {
                        return CampaignStage.get({
                            id: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId || campaign.initialCampaignStageId : campaign.initialCampaignStageId
                        }).$promise;
                    }
                }
            });
        };

        $scope.getCampaignStage = function (lead) {
            if (lead.suppressions && lead.suppressions.length) {
                var isSuppressed = false
                for (var i = 0; i < lead.suppressions.length; i++) {
                    var sup = lead.suppressions[i];
                    if (sup.actualStartDate) return 'Suppressed'
                }
            }
            if (lead.campaignleads && lead.campaignleads.length) {
                var result = _.findWhere(campaignStages, {
                    id: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : null
                });

                if (result)
                    return result.name;
                else
                    return 'No Stage';
            } else {
                return 'No Stage';
            }
        };

        $scope.hasFutureSuppression = function (lead) {
            if (lead.suppressions && lead.suppressions.length) {
                for (var i = 0; i < lead.suppressions.length; i++) {
                    if (!lead.suppressions[i].actualStartDate) return true
                }
            }
            return false
        };

        $scope.suppress = function (row) {
            if ($scope.hasFutureSuppression(row)) return;
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/campaign/admin.campaign.suppressed.edit.html',
                controller: 'CampaignSuppressedEditCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    suppressed: function () { return { campaignId: campaign.id, leadId: row.id, startDate: new Date() } },
                    stages: function () { return campaignStages }
                }
            });

            modalInstance.result.then(function () {
                $scope.getSupressed();
            })
        };

        $scope.unsuppress = function (row) {
            var stage = row.suppressions[0].campaignstageId
            console.log(row)
            if (stage) {
                stage = _.findWhere(campaignStages, {
                    id: stage
                })
                stage = stage ? stage.name : 'No Stage'
            } else {
                stage = 'No Stage'
            }

            var name = row.first_name + ' ' + row.last_name
            SweetAlert.swal({
                title: 'Finish Suppression Now',
                text: 'This will move ' + name + ' to ' + stage + '\n\nDo you wish to continue?',
                type: 'warning',
                showCancelButton: true,
                confirmButtonText: "Yes",
                cancelButtonText: "No",
                closeOnConfirm: true,
                closeOnCancel: true
            }, function (isConfirm) {
                if (isConfirm) {
                    $http.put(APP_SETTINGS.BASE_API_URL + 'suppressed/' + row.suppressions[0].id + '/finish').then(function () {
                        getResultsPage($scope.pagination.current);
                    });
                }
            })
        };

        $scope.generateDisposition = function (lead) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/admin/lead/admin.lead.disposition.html',
                controller: 'LeadDispositionModalCtrl',
                backdrop: 'static',
                keyboard: false,
                resolve: {
                    lead: function () {
                        return lead;
                    },
                    stage: function () {
                        return Campaign.getStageDispositions({
                            id: campaign.id,
                            stageid: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : campaign.initialCampaignStageId
                        }).$promise;
                    },
                    callResult: function () {
                        return CallResult.save({
                            leadId: lead.id,
                            campaignstageId: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : campaign.initialCampaignStageId,
                            campaignId: campaign.id,
                            clientId: campaign.clientId,
                            callAttemptJson: JSON.stringify({
                                startTime: '00:00:00',
                                endTime: '23:59:59',
                                monday: 1,
                                tuesday: 1,
                                wednesday: 1,
                                thursday: 1,
                                friday: 1,
                                saturday: 1,
                                sunday: 1,
                                isCallback: true,
                                randomSelector: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaaa',
                                campaignstageId: lead.campaignleads && lead.campaignleads.length ? lead.campaignleads[0].currentCampaignStageId : campaign.initialCampaignStageId,
                                campaignId: campaign.id,
                                leadId: lead.id
                            })
                        }).$promise;
                    },
                    campaign: function () {
                        return campaign;
                    },
                    client: function () {
                        return Client.get({
                            id: campaign.clientId
                        }).$promise;
                    }
                }
            });

            modalInstance.result.then(function () {
                getResultsPage($scope.pagination.current);
            })
        }


        function updateLeadInfo(lead, info) {
            for (var prop in info) {
                if (lead.hasOwnProperty(prop)) {
                    lead[prop] = info[prop];
                }
            }
        }

        function getResultsPage(pageNumber) {
            CampaignLeads.query({
                id: $scope.campaign.id,
                page: pageNumber - 1,
                orderby: $scope.sortType,
                dir: $scope.sortReverse ? 'DESC' : 'ASC',
                filters: $scope.filters,
                prefilters: filters
            }).$promise.then(function (result) {
                $scope.leads = result.data.leads;
                $scope.totalLeads = result.data.count;
            })
        }
    });