'use strict';

angular.module('dialerFrontendApp')
  .controller('CampaignLeadImportCtrl', function ($scope, $rootScope, $timeout, $window, campaign, uploads, System, FileUploader, APP_SETTINGS) {
    $rootScope.pageTitle = campaign.name + ' | Lead Import';

    $scope.uploads = uploads.reverse();
	$scope.campaign = campaign;
	$scope.updateOnly = false;
    $scope.updateOnlyChange = function (ev) {
      var url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads'
      if (ev.target.checked) {
        url = APP_SETTINGS.BASE_API_URL + 'campaigns/' + $scope.campaign.id + '/uploadleads?updateOnly=true'
      }
      $scope.uploader.url = url
      for (var i = 0; i < $scope.uploader.queue.length; i++) {
        var element = $scope.uploader.queue[i];
        element.url = url;
      }
    };

    $scope.uploader = new FileUploader({
      url: APP_SETTINGS.BASE_API_URL + 'campaigns/' + campaign.id + '/uploadleads',
      headers: {
        'X-Access-Token': $window.sessionStorage.token,
        'X-Key': $window.sessionStorage.user
      },
      onCompleteItem: function (item, response) {
        (function updateProgress(taskId) {
          System.getThreadTaskProgress({
              taskId: taskId
            }).$promise
            .then(function (result) {
              item.progressPercent = result.percentComplete;
              if (item.progressPercent < 100 && !$scope.$$destroyed) {
                $timeout(function () {
                  updateProgress(taskId)
                }, 1000);
              }
            });
        })(response.taskId);
      }
    });

    function getStatus(upload) {
      System.getThreadTaskProgress({
          taskId: upload.taskId
        }).$promise
        .then(function (result) {
          upload.result = result;
        })
    }

    for (var i = 0; i < $scope.uploads.length; i++) {
      getStatus($scope.uploads[i]);
    }
  });