'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AgentStateEditCtrl', function ($scope, $location, $window, editItem) {
    $scope.update = !!editItem.id;
    $scope.editItem = editItem;

    $scope.save = function () {
      if ($scope.update) {
        $scope.editItem.$update(function () {
          $location.path('/admin/agentstates');
          $window.scrollTo(0,0);
        });
      }
      else {
        $scope.editItem.$save(function () {
          $location.path('/admin/agentstates');
          $window.scrollTo(0,0);
        });
      }
    };

    $scope.cancel = function () {
      $location.path('/admin/agentstates');
      $window.scrollTo(0,0);
    };
  });
