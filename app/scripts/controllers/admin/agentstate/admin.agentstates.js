'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('AdminAgentStatesCtrl', function ($scope, $rootScope, $uibModal, AgentState, SweetAlert) {
    $rootScope.pageTitle = 'Configure | Agent States';

    var agentStates = AgentState.query(function () {
      $scope.agentStates = agentStates;
    });

    $scope.deleteAgentState = function (state) {
      SweetAlert.swal({
          title: "Delete Agent State",
          text: "Are you user you want to delete " + state.name + "!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#DD6B55",
          confirmButtonText: "Yes, delete it!",
          cancelButtonText: "No, cancel",
          closeOnConfirm: true,
          closeOnCancel: true
        },
        function (isConfirm) {
          if (isConfirm) {
            var stateId = state.id;
            state.$delete(function () {
              $scope.agentStates = $scope.agentStates.filter(function (s) {
                return s.id && s.id !== stateId;
              });
            });
          }
        });
    };
  });