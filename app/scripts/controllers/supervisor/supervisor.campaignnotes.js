'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CampaignNotesCtrl', function ($scope, $rootScope, campaign, notes, CampaignNotes, _, SweetAlert) {
		$rootScope.pageTitle = campaign.name + ' | Campaign Notes';
		$scope.notes = notes || [];
		$scope.currentNote = '';
		$scope.noteHighligted;

		$scope.addNotes = function () {
			CampaignNotes.save({
				notes: $scope.currentNote,
				campaignId: campaign.id,
				clientId: campaign.client.id
			}).$promise.then(function (note) {
				$scope.notes.push(note);
				$scope.currentNote = '';
			})
		};

		$scope.edit = function (note) {
			$scope.noteHighligted = note.id;
			$scope.currentNote = note.notes;
		};

		$scope.save = function () {
			var note = _.findWhere($scope.notes, {
				id: $scope.noteHighligted
			});
			if (note) {
				note.notes = $scope.currentNote;
				CampaignNotes.update({
					id: note.id
				}, note).$promise.then(function () {
					$scope.noteHighligted = undefined;
					$scope.currentNote = '';
				});
			}
		};

		$scope.deleteNotes = function (note) {
			SweetAlert.swal({
					title: "Delete Note",
					text: "Are you user you want to delete this note?",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, delete it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function (isConfirm) {
					if (isConfirm) {
						CampaignNotes.remove({
							id: note.id
						}).$promise.then(function () {
							$scope.notes = _.filter($scope.notes, function (_note) {
								return _note.id != note.id
							});
						})
					}
				});
		};
	})