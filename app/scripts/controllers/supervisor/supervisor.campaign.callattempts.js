'use strict';

angular.module('dialerFrontendApp')
	.controller('SupervisorCallAttemptsCtrl', function ($scope, $rootScope, campaign, _, $uibModal, SweetAlert, moment, $http, APP_SETTINGS, CampaignStage, Agent, Campaign) {
		$rootScope.pageTitle = campaign.name + ' | Call Attempt Analysis';
		$scope.date = new Date();
		$scope.timeHour = parseInt(moment().format('H'));
		$scope.timeMinute = 0;
		$scope.campaign = campaign;

		$scope.update = function () {
			getData();
		};

		$scope.setStageAgents = function (stage) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/campaignstage/admin.campaignstage.agents.edit.html',
				controller: 'CampaignStageAgentsModalEditCtrl',
				backdrop: 'static',
				keyboard: false,
				size: 'lg',
				resolve: {
					editItem: function (CampaignStage) {
						return CampaignStage.get({
							id: stage.id
						}).$promise;
					},
					campaignStageAgents: function (CampaignStage) {
						return CampaignStage.getAgents({
							id: stage.id
						}).$promise;
					},
					agents: function (Agent) {
						return Agent.query().$promise;
					},
					skills: function (Campaign) {
						return Campaign.getLeadSubTypes({
							id: $scope.campaign.id
						}).$promise;
					}
				}
			});
		};

		function getData() {
			var body = {};
			if ($scope.date) {
				var dateObj = moment($scope.date).hour($scope.timeHour).minute($scope.timeMinute).second(0).utc();
				var timeOj = moment().hour($scope.timeHour).minute($scope.timeMinute).second(0);
				body = {
					date: dateObj.format('YYYY-MM-DD HH:mm:ss'),
					day: dateObj.format('dddd').toLowerCase(),
					starttime: timeOj.format('HH:mm:ss'),
					endtime: timeOj.subtract(1, 'second').format('HH:mm:ss')
				};
			}

			$http.post(APP_SETTINGS.BASE_API_URL + `campaigns/${campaign.id}/callattemptanalysis`, body).then(function (stages) {
				$scope.totalLeads = stages.data.totalLeads;
				$scope.totalCallAttempts = stages.data.totalCallAttempts;
				$scope.totalNoCallAttempts = stages.data.totalNoCallAttempts;
				$scope.totalCallback = stages.data.totalCallback;
				$scope.totalDontContactUntil = stages.data.totalDontContactUntil;
				$scope.totalViable = stages.data.totalViable;
				$scope.totalNoStage = stages.data.totalNoStage;
				$scope.totalBadNumbers = stages.data.totalBadNumbers;
				$scope.stages = stages.data.stages;
				$scope.suppressedNoStage = stages.data.suppressedNoStage;
				$scope.suppressedTotal = stages.data.suppressedTotal;
				console.log(stages);
			});
		}

		getData();
	})