/* jshint camelcase: false */
'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:DialerCtrl
 * @description
 * # DialerCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorBroadcastCtrl', function ($scope, $rootScope, $q, agents, messages, skills, campaigns, campaignStages, users, BroadcastMessage, _) {
		$rootScope.pageTitle = 'Supervisor | Broadcast';

		$scope.messages = [];
		messages.forEach(msg => {
			if (msg.createdUserId == $rootScope.loggedInUser.id && !_.find($scope.messages, function (m) {
					return m.uniqueId == msg.uniqueId
				})) {
				var user = _.find(users, function (u) {
					return u.id == msg.createdUserId;
				})
				if(user)
					msg.author = user.name;
				$scope.messages.push(msg);
			}
		})
		$scope.messages.reverse();
		$scope.message = {};

		$scope.agents = agents;
		$scope.friendlyAgents = _.map($scope.agents, function (item) {
			return item.name;
		});

		$scope.skills = skills;
		$scope.friendlySkills = _.map($scope.skills, function (item) {
			return item.name;
		});

		$scope.campaigns = campaigns;
		$scope.campaignStages = campaignStages;

		$scope.campaignsDisplayed = true;
		$scope.agentsDisplayed = false;
		$scope.skillsDisplayed = false;

		$scope.showAll = false;

		$scope.campaignsShow = function () {
			$scope.campaignsDisplayed = true;
			$scope.agentsDisplayed = false;
			$scope.skillsDisplayed = false;
		};

		$scope.agentsShown = function () {
			$scope.campaignsDisplayed = false;
			$scope.agentsDisplayed = true;
			$scope.skillsDisplayed = false;
		};

		$scope.skillsShown = function () {
			$scope.campaignsDisplayed = false;
			$scope.agentsDisplayed = false;
			$scope.skillsDisplayed = true;
		}

		$scope.addMessage = function () {
			var promises = [];

			if (!$scope.newMessage && !$scope.message.title) return;

			var uuid = createUUID();

			if ($scope.campaignsDisplayed) {
				if (!$scope.message.campaign) {
					for (var i = 0; i < $scope.campaigns.length; i++) {
						var newMessage = new BroadcastMessage();
						newMessage.createdUserId = $rootScope.loggedInUser.id;
						newMessage.uniqueId = uuid;
						newMessage.title = $scope.message.title;
						newMessage.content = $scope.newMessage;
						newMessage.campaignId = $scope.campaigns[i].id;
						promises.push(newMessage.$save());
					};
				} else {
					var newMessage = new BroadcastMessage();
					newMessage.createdUserId = $rootScope.loggedInUser.id;
					newMessage.content = $scope.newMessage;
					newMessage.title = $scope.message.title;
					newMessage.uniqueId = uuid;
					newMessage.campaignId = $scope.message.campaign;
					newMessage.campaignstageId = $scope.message.stage;
					promises.push(newMessage.$save());
				}
			} else if ($scope.agentsDisplayed) {
				if (!$scope.message.msgAgents) return;
				var agents = parseAgents($scope.message.msgAgents);
				for (var i = 0; i < agents.length; i++) {
					var newMessage = new BroadcastMessage();
					newMessage.createdUserId = $rootScope.loggedInUser.id;
					newMessage.uniqueId = uuid;
					newMessage.title = $scope.message.title;
					newMessage.content = $scope.newMessage;
					newMessage.agentId = agents[i];
					promises.push(newMessage.$save());
				};
			} else if ($scope.skillsDisplayed) {
				if (!$scope.message.msgSkills) return;
				var skills = parseSkills($scope.message.msgSkills);
				for (var i = 0; i < skills.length; i++) {
					var newMessage = new BroadcastMessage();
					newMessage.createdUserId = $rootScope.loggedInUser.id;
					newMessage.uniqueId = uuid;
					newMessage.title = $scope.message.title;
					newMessage.content = $scope.newMessage;
					newMessage.subskillId = skills[i];
					promises.push(newMessage.$save());
				};
			}

			$q.all(promises).then(function (messages) {
				$rootScope.safeApply(function () {
					messages[0].author = $rootScope.loggedInUser.name;
					$scope.messages.unshift(messages[0]);
					messages.unshift(messages[0]);
					$scope.newMessage = '';
					$scope.message.title = '';
				});
			}).catch(function (err) {
				console.log(err);
			})
		};

		$scope.toggleShow = function () {
			$scope.showAll = !$scope.showAll;
			if ($scope.showAll) {
				$scope.messages = [];
				messages.forEach(msg => {
					if (!_.find($scope.messages, function (m) {
							return m.uniqueId == msg.uniqueId
						})) {
						var user = _.find(users, function (u) {
					return u.id == msg.createdUserId;
				})
				if(user)
					msg.author = user.name;
						$scope.messages.push(msg);
					}
				})
				$scope.messages.reverse();
			} else {
				$scope.messages = [];
				messages.forEach(msg => {
					if (msg.createdUserId == $rootScope.loggedInUser.id && !_.find($scope.messages, function (m) {
							return m.uniqueId == msg.uniqueId
						})) {
						$scope.messages.push(msg);
					}
				})
				$scope.messages.reverse();
			}
		}

		$scope.deleteMessage = function (message) {
			message.$delete(function () {
				$scope.messages = $scope.messages.filter(function (m) {
					return m.id && m.id !== message.id;
				});
			});
		};

		var parseAgents = function (agents) {
			if (!agents || agents.length == 0) return [];
			var result = [];
			for (var i = 0; i < agents.length; i++) {
				result.push(_.findWhere($scope.agents, {
					name: agents[i]
				}).id);
			};
			return result;
		}

		var parseSkills = function (skills) {
			if (!skills || skills.length == 0) return [];
			var result = [];
			for (var i = 0; i < skills.length; i++) {
				result.push(_.findWhere($scope.skills, {
					name: skills[i]
				}).id);
			};
			return result;
		}

		var createUUID = function () {
			var d = new Date().getTime();
			if (window.performance && typeof window.performance.now === "function") {
				d += performance.now();; //use high-precision timer if available
			}
			var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
				var r = (d + Math.random() * 16) % 16 | 0;
				d = Math.floor(d / 16);
				return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
			});
			return uuid;
		};
	});