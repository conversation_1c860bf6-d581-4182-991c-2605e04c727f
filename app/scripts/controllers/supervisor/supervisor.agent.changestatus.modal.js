'use strict'

angular.module('dialerFrontendApp')
	.controller('SupervisorAgentChangeStatusCtrl', function ($scope, $uibModalInstance, _, agentStates, session) {		
		$scope.session = session;
		$scope.agentStates = agentStates;

		$scope.save = function () {
			$scope.session.agentStatus = _.findWhere(agentStates, {
				name: $scope.session.agentStatus.name
			})

			$scope.session.$save(function () {
				//audit(); moved to server side
				$uibModalInstance.close($scope.session);
			})
		}

		$scope.cancel = function () {
			$uibModalInstance.dismiss('cancel');
		}
	})
