'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorAgentDashboardCtrl', function ($scope, $rootScope, $state, $log, Campaign, Agent, CallResult, AgentSession, campaigns, agent, moment, _) {
		$rootScope.pageTitle = 'Supervisor | ' + agent.name;
		$scope.campaignId;
		$scope.agent = agent;
		$scope.campaignGoal = 0;
		var blankStats = {
			campaignGoal: 0,
			agentTotalEarned: 0,
			agentAvgEarned: 0,
			agentPercentEarned: 0,
			campaignTotalEarned: 0,
			campaignAvgEarned: 0,
			campaignPercentEarned: 0,
			todaysHours: 0,
			totalHours: 0,
			weeksHoursTarget: 0,
			weeksValueTarget: 0,
			weeksThresholdTarget: 0,
			actualWeeksHours: 0,
			actualWeeksValue: 0,
			creditCardCount: 0,
			saleOrGiftCount: 0,
			creditCardPercentage: 0,
			callCount: 0,
			avgCallDuration: 0,
			weeklyValues: [],
			monthlyValues: [],
			dailyValues: []
		};
		$scope.stats = blankStats;
		if(!(campaigns instanceof Array)) {
			campaigns = [campaigns];
		}
		$scope.campaigns = campaigns;
		$scope.start = '';
		$scope.end = ''
		for (var i = 0; i < campaigns.length; i++) {
			$scope.campaignGoal += campaigns[i].goal;
			var start = moment(campaigns[i].startDate);
			var end = moment(campaigns[i].endDate);
			if (!$scope.start || $scope.start > start) {
				$scope.start = start;
			}
			if (!$scope.end || $scope.end < end) {
				$scope.end = end;
			}
		}
		if (campaigns.length > 1) {
			$rootScope.pageTitle += ' on all campaigns';
		} else {
			$rootScope.pageTitle += ' on ' + campaigns[0].name;
		}

		var threshold = 100;
		if (agent.hourlyRate == 11) {
			threshold = 60;
		} else if (agent.hourlyRate == 13) {
			threshold = 80;
		}

		var weeklyValueChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};
		var monthlyValueChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};
		var dailyValueChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};

		var weeklyCountChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};
		var monthlyCountChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};
		var dailyCountChart = {
			labels: [],
			data: [
				[],
				[]
			]
		};

		$scope.valueChart = {
			series: ['Sales', 'Pledges'],
			options: {
				scaleShowLabels: false
			}
		};

		$scope.countChart = {
			series: ['Sales', 'Pledges'],
			options: {
				scaleShowLabels: false
			}
		}

		$scope.valueChart.data = monthlyValueChart.data;
		$scope.valueChart.labels = monthlyValueChart.labels;

		$scope.countChart.data = monthlyCountChart.data;
		$scope.countChart.labels = monthlyCountChart.labels;

		var totalWeeks = end.diff(start, 'weeks');
		var startDate = moment($scope.start);

		var months = [];
		var weeks = [];

		while (startDate <= $scope.end) {
			monthlyValueChart.labels.push(startDate.format('MMMM'));
			monthlyValueChart.data[0].push(0);
			monthlyValueChart.data[1].push(0);

			monthlyCountChart.labels.push(startDate.format('MMMM'));
			monthlyCountChart.data[0].push(0);
			monthlyCountChart.data[1].push(0);

			months.push(startDate.format('MMYY'));

			startDate.add(1, 'month');
		}

		startDate = moment($scope.start);
		while (startDate <= $scope.end) {
			weeklyValueChart.labels.push('Week: ' + startDate.week());
			weeklyValueChart.data[0].push(0);
			weeklyValueChart.data[1].push(0);

			weeklyCountChart.labels.push('Week: ' + startDate.week());
			weeklyCountChart.data[0].push(0);
			weeklyCountChart.data[1].push(0);

			weeks.push(startDate.format('DDMMYY'));

			startDate.add(1, 'week');
		}

		var blankSession = {
			currentCampaignStage: {
				name: 'Not Active',
				campaign: {
					name: 'Not Active',
					client: {
						name: 'Not Active'
					}
				}
			},
			currentLead: {
				first_name: 'Not',
				last_name: 'Active'
			},
			agentStatus: {
				name: 'Logged Out',
				color: '#ffffff'
			},
			inWrapUp: false,
			callState: 'Idle'
		};

		function getAgentSession() {
			AgentSession.get({
				id: $scope.agent.id
			}).$promise.then(function (session) {
				if (session && session.callState) {
					$scope.agent.session = session;
				} else {
					$scope.agent.session = blankSession;
				}
			})
		}

		function getCampaignStatsByAgent() {
			var now = moment();
			Agent.getDashboardStats({
				id: $scope.agent.id
			}).$promise.then(function (results) {
				$scope.stats = angular.copy(blankStats);
				var stats = results;
				var rtStats = $scope.stats;
				var campaignCount = 0;
				var agentCount = 0;
				for (var i = 0; i < $scope.campaigns.length; i++) {
					var campaign = $scope.campaigns[i];
					var currentTargets = _.find(campaign.campaignagenttargets, function (target) {
						return moment(target.start).startOf('day') <= now && moment(target.end).endOf('day') >= now;
					});

					if (currentTargets) {
						currentTargets.start = moment(currentTargets.start).startOf('day');
						currentTargets.end = moment(currentTargets.end).endOf('day');
					} else {
						currentTargets = {
							start: moment().startOf('day'),
							end: moment().endOf('day'),
							scheduledHours: 0,
							goal: 0,
							overrideGoal: 0
						}
					}

					rtStats.weeksHoursTarget += currentTargets.scheduledHours;
					rtStats.weeksValueTarget += currentTargets.overrideGoal || currentTargets.goal;
					rtStats.weeksThresholdTarget += (threshold * currentTargets.scheduledHours);

					var agentStats = _.where(stats.agentCampaignStatsByDate, {
						id: campaign.id
					});
					var campaignStatsByDate = _.where(stats.campaignStatsByDate, {
						id: campaign.id
					});
					var campaignHours = _.where(stats.dailyPayrolls, {
						campaignId: campaign.id
					});
					var campaignStats = _.findWhere(stats.campaignStats, {
						campaignId: campaign.id
					})

					if (agentStats) {
						for (var j = 0; j < agentStats.length; j++) {
							var value = agentStats[j];
							var valueDate = moment(value.date);
							agentCount += value.saleCount;
							agentCount += value.giftCount;
							rtStats.agentTotalEarned += value.saleAmount;
							rtStats.agentTotalEarned += value.giftAmount;
							rtStats.agentAvgEarned += value.avgSaleAmount;
							rtStats.agentAvgEarned += value.avgGiftAmount;
							rtStats.callCount += value.callCount;
							rtStats.avgCallDuration += value.avgCallDuration;
							rtStats.creditCardCount += value.creditCardCount;
							rtStats.saleOrGiftCount += value.saleOrGiftCount;
							if (valueDate >= currentTargets.start && valueDate <= currentTargets.end) {
								rtStats.actualWeeksValue += value.saleAmount;
								rtStats.actualWeeksValue += value.giftAmount;
							}
							rtStats.dailyValues.push(value);
							var valueMonth = moment(value.date).month();
							var valueWeek = moment(value.date).week();
							var month = _.findWhere(rtStats.monthlyValues, {
								month: valueMonth
							})
							var week = _.findWhere(rtStats.weeklyValues, {
								week: valueWeek
							})

							if (month) {
								month.saleAmount += value.saleAmount || 0;
								month.giftAmount += value.giftAmount || 0;
								month.saleCount += value.saleCount || 0;
								month.giftCount += value.giftCount || 0;
							} else {
								value.month = valueMonth;
								rtStats.monthlyValues.push(value);
							}

							if (week) {
								week.saleAmount += value.saleAmount || 0;
								week.giftAmount += value.giftAmount || 0;
								week.saleCount += value.saleCount || 0;
								week.giftCount += value.giftCount || 0;
							} else {
								value.week = valueWeek;
								rtStats.weeklyValues.push(value);
							}
						}
					}
					if (campaignStatsByDate) {
						for (var j = 0; j < campaignStatsByDate.length; j++) {
							var value = campaignStatsByDate[j];
							campaignCount += value.saleCount;
							campaignCount += value.giftCount;
							rtStats.campaignAvgEarned += ((value.saleAmount || 0) / (value.saleCount || 1));
							rtStats.campaignAvgEarned += ((value.giftAmount || 0) / (value.giftCount || 1));
							rtStats.campaignTotalEarned += value.saleAmount || 0;
							rtStats.campaignTotalEarned += value.giftAmount || 0;
						}
					}

					if (campaignHours && campaignHours.length) {
						for (var j = 0; j < campaignHours.length; j++) {
							var item = campaignHours[j];
							var itemDate = moment(item.date);
							if (itemDate >= currentTargets.start && itemDate <= currentTargets.end) {
								rtStats.actualWeeksHours += item.totalSeconds || 0;
							}
						}
					}

					if (campaignStats) {
						rtStats.todaysHours += campaignStats.agentHoursToday || 0;
						rtStats.totalHours += campaignStats.totalAgentHours || 0;
						rtStats.actualWeeksHours += campaignStats.agentHoursToday || 0;
					}
				}

				for (var x = 0; x < rtStats.monthlyValues.length; x++) {
					var entry = rtStats.monthlyValues[x];
					var index = months.indexOf(moment(entry.date).format('MMYY'));
					monthlyValueChart.data[0][index] = entry.saleAmount || 0;
					monthlyValueChart.data[1][index] = entry.giftAmount || 0;
					monthlyCountChart.data[0][index] = entry.saleCount || 0;
					monthlyCountChart.data[1][index] = entry.giftCount || 0;
				}

				for (var x = 0; x < rtStats.weeklyValues.length; x++) {
					var entry = rtStats.weeklyValues[x];
					var index = weeks.indexOf(moment(entry.date).format('DDMMYY'));
					weeklyValueChart.data[0][index] = entry.saleAmount || 0;
					weeklyValueChart.data[1][index] = entry.giftAmount || 0;
					weeklyCountChart.data[0][index] = entry.saleCount || 0;
					weeklyCountChart.data[1][index] = entry.giftCount || 0;
				}

				rtStats.actualWeeksHours = rtStats.actualWeeksHours / 60 / 60;
				rtStats.todaysHours = humanizeDuration(rtStats.todaysHours * 1000, {
					round: true
				});
				rtStats.totalHours = rtStats.totalHours / 60 / 60;


				rtStats.avgCallDuration = humanizeDuration((rtStats.avgCallDuration / rtStats.callCount) * 1000, {
					round: true
				});

				rtStats.creditCardPercentage = (100 / rtStats.saleOrGiftCount) * rtStats.creditCardCount;

				rtStats.agentPercentEarned = (100 / $scope.campaignGoal) * rtStats.agentTotalEarned;
				rtStats.campaignPercentEarned = (100 / rtStats.campaignTotalEarned) * rtStats.agentTotalEarned;

				rtStats.agentAvgEarned = rtStats.agentAvgEarned / agentCount;
				rtStats.campaignAvgEarned = rtStats.campaignAvgEarned / campaignCount;
			})
		}

		getAgentSession();
		getCampaignStatsByAgent();

		var sessionInterval = setInterval(getAgentSession, 3000);
		var statInterval = setInterval(getCampaignStatsByAgent, 10000);
		$scope.$on('$destroy', function () {
			window.onbeforeunload = undefined;
			clearInterval(sessionInterval);
			clearInterval(statInterval);
		});
	})