'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorCampaignDashboardCtrl', function ($scope, $rootScope, $state, $log, Campaign, CallResult, campaign, moment, _, $http) {
		$rootScope.pageTitle = 'Supervisor | ' + campaign.name + ' Dashboard';
		$scope.campaign = campaign;
		$scope.campaigntype = $scope.campaign.campaigntype.name;
		$scope.stats = {};
		$scope.showAddOnGifts = $scope.campaigntype == 'Telefunding';
		$scope.loading = false;

		campaign.projectedQuantity = 0;
		campaign.projectedAvgValue = 0;
		for (var i = 0; i < campaign.campaignprojections.length; i++) {
			var proj = campaign.campaignprojections[i];
			campaign.projectedQuantity += proj.projectedQuantity;
			campaign.projectedAvgValue += proj.projectedAvgValue;
		}
		if (campaign.campaignprojections.length) {
			campaign.projectedAvgValue = campaign.projectedAvgValue / campaign.campaignprojections.length;
		}

		if ($scope.campaigntype == 'Telefunding') {
			$scope.totalChart = {
				labels: [],
				series: ['Pledges'],
				data: [
					[]
				],
				options: {
					scaleShowLabels: false
				}
			};
		} else {
			$scope.totalChart = {
				labels: [],
				series: ['Sales', 'Gifts'],
				data: [
					[],
					[]
				],
				options: {
					scaleShowLabels: false
				}
			};
		}

		$scope.pie = {
			labels: [],
			data: []
		}

		campaign.campaigngoals.forEach(function (goal) {
			$scope.pie.labels.push(goal.reportingGroup);
			$scope.pie.data.push(0);
		})

		var end = moment(campaign.endDate);
		var start = moment(campaign.startDate);
		var totalWeeks = end.diff(start, 'weeks');
		var startDate = moment(campaign.startDate);

		while (startDate <= end) {
			$scope.totalChart.labels.push(startDate.format('MMMM') + " '" + startDate.format('YY'));
			$scope.totalChart.data[0].push(0);
			if ($scope.campaigntype != 'Telefunding') {
				$scope.totalChart.data[1].push(0);
			}
			startDate.add(1, 'month');
		}

		updateStats();
		var interval = setInterval(updateStats, 10000);
		$scope.$on('$destroy', function () {
			window.onbeforeunload = undefined;
			clearInterval(interval);
		});

		function updateStats() {
			if ($scope.loading) {
				return;
			}

			$scope.loading = true;
			Campaign.getStats({
				id: $scope.campaign.id,
				showAddOnGifts: $scope.showAddOnGifts
			}).$promise.then(function (results) {
				$scope.loading = false;
				
				$scope.stats = results.stats;
				var stats = $scope.stats;

				stats.weeksRemaining = end.diff(moment(), 'days') / 7;
				stats.weeksGone = totalWeeks - stats.weeksRemaining;
				stats.payrollHours = (stats.payroll / 60) / 60;
				if ($scope.campaigntype == 'Telefunding') {
					stats.leftToGoal = $scope.campaign.goal - stats.totalGiftAmount;
					stats.avgWeekly = stats.totalGiftAmount ? stats.totalGiftAmount / (stats.weeksGone || 1) : 0;
					stats.valuePerPayrollHour = stats.totalGiftAmount / (stats.payrollHours === "0" ? 1 : stats.payrollHours);
					stats.percentComplete = (100 / $scope.campaign.goal) * (stats.totalGiftAmount || 1);
					stats.countToHitProjection = $scope.campaign.projectedQuantity - stats.numberOfPledges;
					campaign.campaigngoals.forEach(function (goal, index) {
						$scope.pie.data[index] = stats[goal.reportingGroup + 'GiftAmount']
					})
				} else {
					stats.leftToGoal = $scope.campaign.goal - (stats.totalSaleAmount ? stats.totalSaleAmount : 0);
					stats.avgWeekly = stats.totalSaleAmount ? stats.totalSaleAmount / (stats.weeksGone || 1) : 0;
					stats.valuePerPayrollHour = stats.totalSaleAmount / (stats.payrollHours === "0" ? 1 : stats.payrollHours);
					stats.percentComplete = (100 / $scope.campaign.goal) * (stats.totalSaleAmount || 1);
					stats.countToHitProjection = $scope.campaign.projectedQuantity - stats.numberOfSales;
					campaign.campaigngoals.forEach(function (goal, index) {
						$scope.pie.data[index] = stats[goal.reportingGroup + 'SaleAmount']
					})
				}
				stats.avgWeeklyReq = stats.leftToGoal / stats.weeksRemaining;
				stats.weeksToComplete = stats.avgWeekly !== "0" ? stats.leftToGoal / stats.avgWeekly : 0;
				stats.avgSaleAmount = stats.totalSaleAmount ? stats.totalSaleAmount / stats.totalSaleCount : 0;
				stats.avgGiftAmount = stats.totalGiftAmount ? stats.totalGiftAmount / stats.totalGiftCount : 0;
				stats.projectedPercentComplete = $scope.campaign.projectedQuantity ? (100 / $scope.campaign.projectedQuantity) * stats.numberOfSalesOrPledges : 100;
				stats.dialsPerHour = stats.callCount / (stats.payrollHours === "0" ? 1 : stats.payrollHours);

				for (var i = 0; i < $scope.totalChart.labels.length; i++) {
					var label = $scope.totalChart.labels[i];
					for (var j = 0; j < results.callresults.length; j++) {
						var entry = results.callresults[j];
						if (label.indexOf(entry.month) == 0 && label.indexOf((entry.year + '').substring(2,4)) !== -1) {
							if ($scope.campaigntype != 'Telefunding') {
								$scope.totalChart.data[0][i] = entry.saleAmount;
								$scope.totalChart.data[1][i] = entry.giftAmount;
							} else {
								$scope.totalChart.data[0][i] = entry.giftAmount;
							}
						}
					}
				}
			});
		}
	})