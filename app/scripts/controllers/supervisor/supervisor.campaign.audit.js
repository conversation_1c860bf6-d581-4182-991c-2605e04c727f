'use strict';

angular.module('dialerFrontendApp')
    .controller('SupervisorCampaignAuditCtrl', function($scope, $rootScope, $timeout, $state, $log, campaign, audits, users, subskills, stages, moment, _) {
        $rootScope.pageTitle = 'Supervisor | ' + campaign.name + ' Audit';

        $scope.campaignChanges = [];
        $scope.stageChanges = {};
        $scope.dispositionChanges = {};
        $scope.agentChanges = {};
        $scope.projectionChanges = {};
        $scope.targetChanges = {};
        $scope.dtChanges = {};

        $scope.show = 'campaign';

        $scope.processField = function(field) {
            if (!field) return '';
            return field.replace(/([A-Z])/g, ' $1').replace(/^./, function(str) {
                return str.toUpperCase();
            });
        };

        function processValue(audit) {
            if (audit.field.toLowerCase().indexOf('userid') > -1) {
                var from = _.findWhere(users, {
                    id: parseInt(audit.from)
                })
                audit.from = from ? from.name : '';
                var to = _.findWhere(users, {
                    id: parseInt(audit.to)
                })
                audit.to = to ? to.name : '';
            }

            if (audit.field == 'agentskills') {
                audit.field = 'Lead Types';

                var from = [];
                if (audit.from) {
                    audit.from = JSON.parse(audit.from);
                    audit.from.forEach(s => {
                        var f = _.findWhere(subskills, {
                            id: parseInt(s)
                        })
                        if (f) {
                            from.push(f.name)
                        }
                    })
                }
                audit.from = from.join(', ')

                var to = [];
                if (audit.to) {
                    audit.to = JSON.parse(audit.to);
                    audit.to.forEach(s => {
                        var f = _.findWhere(subskills, {
                            id: parseInt(s)
                        })
                        if (f) {
                            to.push(f.name)
                        }
                    })
                }
                audit.to = to.join(', ')
            }

            if (audit.field.toLowerCase().indexOf('transitiontocampaignstageid') > -1) {
                var from = _.findWhere(stages, {
                    id: parseInt(audit.from)
                })
                audit.from = from ? from.name : '';
                var to = _.findWhere(stages, {
                    id: parseInt(audit.to)
                })
                audit.to = to ? to.name : '';
            }
        }

        (function process() {
            var stageChanges = [];
            var dispositionChanges = [];
            var agentChanges = [];
            var projectionChanges = [];
            var targetChanges = [];
            var dtChanges = [];

            audits.forEach(function(audit) {
                processValue(audit);

                if (!audit.detail) {
                    $scope.campaignChanges.push(audit);
                    return;
                }

                if (audit.detail.type) {
                    if (audit.detail.type == 'CampaignStage') stageChanges.push(audit);
                    if (audit.detail.type == 'CampaignStageDisposition') dispositionChanges.push(audit);
                    if (audit.detail.type == 'CampaignStageAgent') agentChanges.push(audit);
                    if (audit.detail.type == 'CampaignProjection') projectionChanges.push(audit);
                    if (audit.detail.type == 'CampaignAgentTarget') targetChanges.push(audit);
                    if (audit.detail.type == 'CampaignStageDateTimeRule') dtChanges.push(audit);
                }
            })

            stageChanges.forEach(a => {
                if ($scope.stageChanges[a.detail.name]) $scope.stageChanges[a.detail.name].push(a);
                else $scope.stageChanges[a.detail.name] = [a];
            })

            agentChanges.forEach(a => {
                if ($scope.agentChanges[a.detail.name]) $scope.agentChanges[a.detail.name].push(a);
                else $scope.agentChanges[a.detail.name] = [a];
            })

            dispositionChanges.forEach(a => {
                if ($scope.dispositionChanges[a.detail.name]) $scope.dispositionChanges[a.detail.name].push(a);
                else $scope.dispositionChanges[a.detail.name] = [a];
            })

            function lookupProjectionEnglish(input) {
                if (!input) return '';
                switch (input) {
                    case 'clientAvgValue':
                        return 'Projected Quantity';
                    case 'clientQuantity':
                        return 'Projected Avg Value';
                    case 'projectedRR':
                        return 'Projected Response Rate';
                    case 'projectedQuantity':
                        return 'Projected # Transactions';
                    case 'projectedAvgValue':
                        return 'Projected $/Transaction';
                    case 'defaultAskAmount':
                        return 'Min Ask Amount';
                    case 'askAmountIncrease':
                        return 'Ask Amount Increase %';
                    default:
                        return input;
                }
            }

            projectionChanges.forEach(a => {
                a.field = lookupProjectionEnglish(a.field);
                if ($scope.projectionChanges[a.detail.leadType]) $scope.projectionChanges[a.detail.leadType].push(a);
                else $scope.projectionChanges[a.detail.leadType] = [a];
            })

            targetChanges.forEach(a => {
                if ($scope.targetChanges[a.detail.agent]) $scope.targetChanges[a.detail.agent].push(a);
                else $scope.targetChanges[a.detail.agent] = [a];
            })



            dtChanges.forEach(a => {
                if ($scope.dtChanges[a.detail.stage]) $scope.dtChanges[a.detail.stage].push(a);
                else $scope.dtChanges[a.detail.stage] = [a];
            })
        })();
    })