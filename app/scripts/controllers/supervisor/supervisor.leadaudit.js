'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
  .controller('SupervisorLeadAuditCtrl', function ($scope, $rootScope, $state, $log, LeadAudit, Campaign, Lead, _) {
    $rootScope.pageTitle = 'Supervisor | Lead Audit';

    var campaigns = Campaign.query(function () {
      if (!$rootScope.loggedInUser.isAdmin && !$rootScope.loggedInUser.isSuperManager && !$rootScope.loggedInUser.isClientAdmin) {
        campaigns = _.where(campaigns, {
          owningUserId: $rootScope.loggedInUser.id
        });
      }
      $scope.campaigns = _.sortBy(campaigns, function (camp) {
        return camp.name
      });
      campaigns.forEach(function (cmp) {
        cmp.page = 1;
        cmp.changedLeads = [];
        cmp.allShown = false;
      })
      getLeads(1);
    });

    $scope.showAccepted = false;

    $scope.$watch("selectedCampaignId", function () {
      $scope.selectedCampaign = _.find($scope.campaigns, function (campaign) {
        return campaign.id == $scope.selectedCampaignId;
      });
      $scope.selectedCampaign.page = 1;
    })

    var getLeads = function () {
      for (var i = 0; i < $scope.campaigns.length; i++) {
        getChanges($scope.campaigns[i]);
      }
    }

    function getChanges(campaign) {
      Campaign.getCampaignLeadAudits({
        id: campaign.id,
        page: campaign.page - 1
      }).$promise.then(function (result) {
        if (!result.length) {
          campaign.allShown = true;
        }
        result.forEach(function (lead) {
          campaign.changedLeads.push(lead);
        })
      });
    }

    $scope.getMore = function () {
      $scope.selectedCampaign.page++;
      getChanges($scope.selectedCampaign);
    };

    $scope.clearAudit = function (audit) {
      audit.acceptedBy = $rootScope.loggedInUser.id
    };

    $scope.undoAudit = function (audit, parentLead) {
      var thisLead = parentLead;
      Lead.get({
        id: audit.leadId
      }, function (lead) {
        lead[audit.field] = audit.previousValue;
        lead.$update().then(function (result) {
          if (audit.field == "first_name") {
            thisLead.first_name == newAudit.newValue;
          } else if (audit.field == "last_name") {
            thisLead.first_name == newAudit.last_name;
          }
          if (result.audits && result.audits.length) {
            thisLead.leadaudits = thisLead.leadaudits.concat(result.audits);
          }
        })
      })

    }
  });