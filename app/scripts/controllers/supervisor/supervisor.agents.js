'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorAgentsCtrl', function ($scope, $rootScope, $q, $interval, $timeout, $state, $log, User, moment, AgentSession, _, Phone, $uibModal, AgentState) {
		$rootScope.pageTitle = 'Supervisor | Agents';
		$scope.sortType = 'name'
		$scope.sortReverse = false;
		$scope.eavesdropAgent = '';
		$scope.phone = Phone;
		$scope.agents = [];
		var blankSession = {
			currentCampaignStage: {
				name: 'Not Active',
				campaign: {
					name: 'Not Active'
				}
			},
			currentLead: {
				first_name: 'Not',
				last_name: 'Active'
			},
			agentStatus: {
				name: 'Logged Out',
				color: '#ffffff'
			},
			inWrapUp: false,
			callState: 'Idle'
		};

        $scope.editAgentStatus = function (session) {
            var modalInstance = $uibModal.open({
                templateUrl: 'views/supervisor/supervisor.agent.changestatus.modal.html',
                controller: 'SupervisorAgentChangeStatusCtrl',
                backdrop: 'static',
                size: 'md',
                keyboard: false,
                resolve: {
                    agentStates: function () {
						return AgentState.query().$promise
					},
					session: function () {
						return Promise.resolve(session)
					}
                }
            });
		}

		User.getAgents({
			id: $rootScope.loggedInUser.id
		}).$promise.then(function (agents) {
			agents = _.filter(agents, function (agent) {
				return agent.termDate < new Date();
			})
			$scope.agents = agents;
			refreshAgentSession();
			agents.forEach(function (agent) {
				agent.campaigns = [];
				agent.session = blankSession;
				agent.campaignstages && agent.campaignstages.forEach(function (stage) {
					if (!_.findWhere(agent.campaigns.filter(ac => !!ac), {
							id: stage && stage.campaign ? stage.campaign.id : null
						})) {
						agent.campaigns.push(stage.campaign)
					}
				})
			})
		});

		User.get({
			id: $rootScope.loggedInUser.id
		}).$promise.then(function (user) {
			if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
                $timeout(function() {
                    $rootScope.changePassword(false);
                });
			}
		})

		var refreshTask = $interval(refreshAgentSession, 3000);

		var eventDetachers = [];

		eventDetachers.push(
			$rootScope.$on('$stateChangeStart', function () {
				if (refreshTask) {
					$interval.cancel(refreshTask);
				}
			})
		);

		$scope.$on('$destroy', function () {
			_.each(eventDetachers, function (eventDetacher) {
				try {
					eventDetacher();
				} finally {}
			});
		});

		$scope.toggleListenToAgent = function (extNo) {
			Phone.hangup();
			if (extNo != $scope.eavesdropAgent) {
				Phone.makeCall('*88' + extNo);

				$scope.eavesdropAgent = extNo;
			} else {
				$scope.eavesdropAgent = '';
			}
		};

		$scope.hangup = function () {
			Phone.hangup();
		};

        $scope.changeAgentStatus = function (session, state) {
            if (!session.agentStatus || session.agentStatus.id !== state.id) {
                session.agentStatus = state;
                session.agentStatusSince = Date.now();

                session.$save(function (session) {

				});
            }
        };

		function refreshAgentSession() {
			AgentSession.query().$promise.then(function (sessions) {
				for (var i = 0; i < $scope.agents.length; i++) {
					var agent = $scope.agents[i];
					var session = _.findWhere(sessions, {
						agentId: agent.id
					});
					if (session) {
						agent.session = session;
					} else {
						agent.session = blankSession;
					}
				}
			})
		}

		//initialise the phone with the logged in user object
		if ($rootScope.loggedInUser.agent && $rootScope.loggedInUser.agent.device && Phone) {
			Phone.init({
				server: $rootScope.loggedInUser.agent.device.server,
				extension: $rootScope.loggedInUser.agent.device.extension,
				password: $rootScope.loggedInUser.agent.device.password,
				display: $rootScope.loggedInUser.agent.device.name
			});
		}
	})