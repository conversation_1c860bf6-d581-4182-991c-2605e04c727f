'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorCallbacksCtrl', function($scope, $rootScope, filters, title, Campaign, Callback, CallResult, CampaignStage, Client, SweetAlert, $uibModal) {
		$rootScope.pageTitle = title.name + ' | Callbacks';
		$scope.sortReverse = true;
		$scope.sortType = 'startDateTime';
		$scope.currentFilters = angular.copy(filters);
		$scope.pagination = {
			current: 1
		};
		$scope.friendlyFilters = [];
		$scope.total = 0;

		$scope.changeFilter = function(order) {
			$scope.sortType = order;
			$scope.sortReverse = !$scope.sortReverse;
			$scope.pagination.current = 1;

			getResultsPage(1);
		};

		$scope.search = function() {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/supervisor/supervisor.callbacks.search.html',
				controller: 'CallbacksSearchModalCtrl',
				backdrop: 'static',
				keyboard: false,
				resolve: {
					filters: function() {
						return $scope.currentFilters;
					}
				}
			})

			modalInstance.result.then(function(filters) {
				$scope.currentFilters = filters;

				$scope.friendlyFilters = [];
				calculaterFilters($scope.currentFilters);

				$scope.pagination.current = 1;
				getResultsPage(1);
			});
		};

		$scope.pageChanged = function(page) {
			getResultsPage(page);
		};

		$scope.removeFilter = function(filter, index) {
			$scope.friendlyFilters.splice(index, 1);
			delete $scope.currentFilters[filter.parent][filter.prop];
			getResultsPage(1);
		};

		$scope.removeAllFilters = function() {
			$scope.friendlyFilters = [];
			$scope.currentFilters = angular.copy(filters);
			getResultsPage(1);
		};

		$scope.edit = function(callback) {
			if (typeof callback.callAttemptJson == 'string') {
				callback.callAttemptJson = JSON.parse(callback.callAttemptJson);
			}
			var modalInstance = $uibModal.open({
				animation: true,
				keyboard: false,
				templateUrl: 'views/supervisor/editors/supervisor.editors.callback.html',
				controller: 'SupervisorCallbackCtrl',
				resolve: {
					callback: function() {
						return Callback.get({
							id: callback.id
						}).$promise;
					},
					callresult: function() {
						return CallResult.get({
							id: callback.callresultId
						}).$promise;
					},
					lead: function() {
						return callback.lead;
					},
					campaign: function() {
						return callback.campaign;
					},
					agents: function() {
						return Campaign.getAgents({
							id: callback.campaignId
						}).$promise;
					},
					client: function() {
						return Client.get({
							id: callback.campaign.clientId
						}).$promise;
					},
					stage: function() {
						return CampaignStage.get({
							id: callback.callAttemptJson.campaignstageId
						}).$promise;
					}
				}
			})

			modalInstance.result.then(function(result) {
				if (result.callresult) {
					callback.callresult = result.callresult;
				}
				if (result.id) {
					for (var prop in result) {
						if (result.hasOwnProperty(prop)) {
							callback[prop] = result[prop];
						}
					}
				}
			})
		};

		$scope.delete = function(callback) {
			SweetAlert.swal({
					title: "Delete Callback",
					text: "Are you user you want to delete this callback?",
					type: "warning",
					showCancelButton: true,
					confirmButtonColor: "#DD6B55",
					confirmButtonText: "Yes, delete it!",
					cancelButtonText: "No, cancel",
					closeOnConfirm: true,
					closeOnCancel: true
				},
				function(isConfirm) {
					if (isConfirm) {
						Callback.delete({
							id: callback.id
						}).$promise.then(function() {
							$scope.callbacks = $scope.callbacks.filter(function(a) {
								return a.id && a.id !== callback.id;
							});
						})
					}
				});
		};

		function getResultsPage(page) {
			Callback.search({
				page: page - 1,
				orderby: $scope.sortType,
				dir: $scope.sortReverse ? 'DESC' : 'ASC',
				filters: JSON.stringify($scope.currentFilters)
			}).$promise.then(function(results) {
				$scope.callbacks = results.rows;
				$scope.total = results.count;
			})
		}

		function calculaterFilters(obj, parent) {
			for (var prop in obj) {
				if (!Array.isArray(obj[prop]) && typeof(obj[prop]) === 'object') {
					calculaterFilters(obj[prop], prop);
				} else if (typeof(obj[prop]) === 'string') {
					var name = getName(prop, parent);
					if (name && obj[prop]) {
						$scope.friendlyFilters.push({
							name: name,
							prop: prop,
							parent: parent,
							value: obj[prop]
						});
					} else if (!obj[prop]) {
						delete obj[prop];
					}
				}
			}
		}

		function getName(prop, parent) {
			if (prop === 'campaignId') return null;
			switch (prop) {
				case 'id':
					if (parent === 'lead') return 'Kaos ID'
					else return prop;
					break;
				default:
					return prop;
			}
		}

		getResultsPage(1);
	})