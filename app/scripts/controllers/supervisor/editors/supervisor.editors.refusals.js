'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('EditRefusalModalCtrl', function ($scope, $rootScope, $uibModalInstance, callresult, campaign, reasons, CallResult, CampaignStage) {
		$scope.refusalReasons = [];
		var standardRefusals = [];
		var exceptionRefusals = [];

		if (campaign.campaigntype.name === 'Telefunding') {
			standardRefusals = _.where(reasons, {
				exception: false,
				telefunding: true
			});

			exceptionRefusals = _.where(reasons, {
				exception: true,
				telefunding: true
			});
		} else {
			standardRefusals = _.where(reasons, {
				exception: false,
				telemarketing: true
			});

			exceptionRefusals = _.where(reasons, {
				exception: true,
				telemarketing: true
			});
		}

		$scope.refusalReasons = standardRefusals.concat(exceptionRefusals);

		$scope.callresult = angular.copy(callresult);

		CampaignStage.get({
			id: callresult.campaignstageId
		}).$promise.then(function (stage) {
			if (stage.name !== '2nd Appeal') {
				if (campaign.campaigntype.name === 'Telefunding') {
					$scope.refusalReasons.push({
						name: 'Already gave this fiscal year'
					})
				} else {
					$scope.refusalReasons.push({
						name: 'Already subscribed this season'
					})
				}
			}
		})

		$scope.ok = function () {
			if (_.findWhere(standardRefusals, {
					name: $scope.callresult.refusalReason
				})) {
				//standard refusal
				$scope.callresult.wrapup = 'Stardard Refusal';
			} else {
				//exception refusal
				$scope.callresult.wrapup = 'Exception Refusal';
			}

			CallResult.update({
					id: $scope.callresult.id
				}, $scope.callresult).$promise
				.then(function () {
					$uibModalInstance.close($scope.callresult);
				});
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};
	})