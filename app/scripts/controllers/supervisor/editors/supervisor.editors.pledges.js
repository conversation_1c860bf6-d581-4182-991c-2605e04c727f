'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('EditPledgeModalCtrl', function ($scope, $rootScope, $uibModalInstance, callresult, CallResult, SweetAlert, Invoice) {
		$scope.callresult = angular.copy(callresult);

		$scope.popup1 = {
			opened: false
		};

		$scope.ok = function () {
			var errors = validate();

			if (errors.length) {
				var verificationMsg = errors.join('\n - ');
				SweetAlert.swal({
					title: "Invalid",
					text: 'Please correct the following error(s):\n\n - ' + verificationMsg
				});
			} else {
				if (!$scope.callresult.creditCardNumber)
					delete $scope.callresult.creditCardNumber;

				if (!$scope.callresult.creditCardExpDate)
					delete $scope.callresult.creditCardExpDate;

				if (!$scope.callresult.creditCardSecurityCode)
					delete $scope.callresult.creditCardSecurityCode;

				$scope.callresult.grandTotal = $scope.callresult.giftAmount;

				if ($scope.callresult.id) {
					CallResult.update({
							id: $scope.callresult.id
						}, $scope.callresult).$promise
						.then(function () {
							Invoice.getByCallResultId({
									callresultId: $scope.callresult.id
								}).$promise
								.then(function (invoice) {
									if (invoice) {
										invoice.grandTotal = $scope.callresult.grandTotal;
										if (invoice.amountRemaining > 0) {
											invoice.amountRemaining = $scope.callresult.grandTotal;
										}
										invoice.$update();
									}

									$uibModalInstance.close($scope.callresult);
								});
						});
				}
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}

		function validate() {
			var errors = [];

			if ($scope.callresult.creditCardNumber) {
				if (!$scope.validateCreditCardNumber($scope.callresult.creditCardNumber)) {
					errors.push('Invalid credit card number');
				}

				if ($scope.detectCardType($scope.callresult.creditCardNumber) != $scope.callresult.creditCardType) {
					errors.push('Credit card number does not match credit card type');
				}
			}

			if ($scope.callresult.creditCardExpDate) {
				if (!$scope.validateCreditCardDate($scope.callresult.creditCardExpDate)) {
					errors.push('Invalid credit card expiry date');
				}
			}

			if ($scope.callresult.creditCardSecurityCode) {
				if (!$scope.validateCreditCardPin($scope.callresult.creditCardSecurityCode)) {
					errors.push('Invalid credit card pin');
				}
			}

			return errors;
		}
	})