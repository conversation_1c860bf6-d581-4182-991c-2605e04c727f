'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('EditSaleModalCtrl', function ($scope, $rootScope, $uibModalInstance, $q, callresult, client, products, CallResult, Invoice, Sale, SweetAlert, _) {
		$scope.callresult = angular.copy(callresult);
		$scope.products = products;
		$scope.client = client;

		var itemsToDelete = [];

		$scope.addOnGiftEnabled = $scope.callresult.giftAmount > 0;

		var getProduct = function (order) {
			return _.findWhere($scope.products, {
				series: order.series,
				seats: order.seats,
				days: order.dayOfWeek
			});
		};

		$scope.checkvalues = function (order) {
			order.possibleDays = _.where($scope.products, {
				series: order.series,
				seats: order.seats
			});
		};

		$scope.callresult.sales.forEach(function (sale) {
			$scope.checkvalues(sale);
		})

		$scope.addSaleItem = function () {
			$scope.callresult.sales.push(new Sale());
		};

		$scope.removeSaleItem = function (index) {
			var item = $scope.callresult.sales[index];
			if (item.id) {
				itemsToDelete.push(item);
			}
			$scope.callresult.sales.splice(index, 1);
			$scope.updateGrandTotal();
		};

		$scope.updateCost = function (order) {
			var product = getProduct(order);
			if (product) {
				order.costEach = product.price;
				order.feePerTicket = product.feePerTicket;
			}
			$scope.updateTotal(order);
		};

		$scope.updateTotal = function (order) {
			var product = getProduct(order);
			order.productCode = product.productCode;
			order.tix_sub = product.tix_sub;
			order.salesTax = client.salesTax;
			if (product) {
				if (order.seatCount > 0) {
					order.subtotal = (order.costEach + order.feePerTicket) * order.seatCount;
				} else {
					order.subtotal = 0;
				}
			} else {
				order.subtotal = 0;
			}

			$scope.updateGrandTotal();
		};

		$scope.updateGrandTotal = function () {
			$scope.callresult.saleAmount = 0;

			_.each($scope.callresult.sales, function (order) {
				$scope.callresult.saleAmount += order.subtotal;
			})

			if ($scope.callresult.saleAmount === 0) {
				var subWithTax = 0;
			} else {
				var subWithTax = $scope.callresult.saleAmount + (($scope.client.salesTax / 100) * $scope.callresult.saleAmount);
			}

			if ($scope.addOnGiftEnabled) {
				if (!$scope.callresult.giftAmount && $scope.callresult.saleAmount) {
					$scope.callresult.grandTotal = subWithTax;
				} else if ($scope.callresult.giftAmount && !$scope.callresult.saleAmount) {
					$scope.callresult.grandTotal = subWithTax;
				} else {
					$scope.callresult.grandTotal = subWithTax + $scope.callresult.giftAmount;
				}
			} else {
				$scope.callresult.giftAmount = 0;
				if ($scope.callresult.saleAmount) {
					$scope.callresult.grandTotal = subWithTax;
				}
			}

			$scope.callresult.grandTotal += $scope.client.orderFee;
		};

		$scope.ok = function () {
			var errors = validate();

			if (errors.length) {
				var verificationMsg = errors.join('\n - ');
				SweetAlert.swal({
					title: "Invalid",
					text: 'Please correct the following error(s):\n\n - ' + verificationMsg
				});
			} else {
				if (!$scope.callresult.creditCardNumber)
					delete $scope.callresult.creditCardNumber

				if (!$scope.callresult.creditCardExpDate)
					delete $scope.callresult.creditCardExpDate

				if (!$scope.callresult.creditCardSecurityCode)
					delete $scope.callresult.creditCardSecurityCode

				var promises = [];

				if (itemsToDelete.length) {
					itemsToDelete.forEach(function (item) {
						promises.push(Sale.delete({
							id: item.id
						}));
					})
				}

				$scope.callresult.sales.forEach(function (sale) {
					if (sale.id) {
						promises.push(Sale.update({
							id: sale.id
						}, sale).$promise);
					} else {
						var newSale = new Sale();
						newSale.callresultId = $scope.callresult.id;
						newSale.campaignId = $scope.callresult.campaignId;
						newSale.agentId = $scope.callresult.agentId;
						newSale.campaignstageId = $scope.callresult.campaignstageId;
						newSale.leadId = $scope.callresult.leadId;
						newSale.clientId = $scope.callresult.clientId;

						newSale.productCode = sale.productCode;
						newSale.series = sale.series;
						newSale.seats = sale.seats;
						newSale.dayOfWeek = sale.dayOfWeek;
						newSale.tix_sub = sale.tix_sub;
						newSale.costEach = sale.costEach;
						newSale.feePerTicket = sale.feePerTicket;
						newSale.subtotal = sale.subtotal;

						newSale.salesTax = client.salesTax;

						promises.push(newSale.$save())
					}
				})

				promises.push(CallResult.update({
					id: $scope.callresult.id
				}, $scope.callresult).$promise);

				return $q.all(promises).then(function () {
					Invoice.getByCallResultId({
							callresultId: $scope.callresult.id
						}).$promise
						.then(function (invoice) {
							if (invoice) {
								invoice.grandTotal = $scope.callresult.grandTotal;								
								invoice.$update();
							}

							$uibModalInstance.close($scope.callresult);
						});
					$uibModalInstance.close($scope.callresult);
				});
			}
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		}

		function validate() {
			var errors = [];

			if ($scope.callresult.creditCardNumber) {
				if (!$scope.validateCreditCardNumber($scope.callresult.creditCardNumber)) {
					errors.push('Invalid credit card number');
				}

				if ($scope.detectCardType($scope.callresult.creditCardNumber) != $scope.callresult.creditCardType) {
					errors.push('Credit card number does not match credit card type');
				}
			}

			if ($scope.callresult.creditCardExpDate) {
				if (!$scope.validateCreditCardDate($scope.callresult.creditCardExpDate)) {
					errors.push('Invalid credit card expiry date');
				}
			}

			if ($scope.callresult.creditCardSecurityCode) {
				if (!$scope.validateCreditCardPin($scope.callresult.creditCardSecurityCode)) {
					errors.push('Invalid credit card pin');
				}
			}

			return errors;
		}
	})