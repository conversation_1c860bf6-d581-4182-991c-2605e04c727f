'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:AdminCtrl
 * @description
 * # AdminCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('SupervisorCampaignsCtrl', function ($scope, $rootScope, $timeout, $state, $log, campaigns, Campaign, User, moment, _) {
		$rootScope.pageTitle = 'Supervisor | Campaigns';
		$scope.sortType = 'name'
		$scope.sortReverse = false;
		$scope.campaigns = campaigns;
		if (!$rootScope.loggedInUser.isAdmin && !$rootScope.loggedInUser.isSuperManager && !$rootScope.loggedInUser.isClientAdmin) {
			$scope.campaigns = _.filter($scope.campaigns, function (campaign) {
				return campaign.owningUserId == $rootScope.loggedInUser.id;
			})
		}
		var now = moment();

		User.get({
			id: $rootScope.loggedInUser.id
		}).$promise.then(function (user) {
			if (user.firstLogin || moment(user.lastPasswordUpdate) < moment().add(-90, 'days')) {
                $timeout(function() {
                    $rootScope.changePassword(false);
                });
			}
		})

		var calcState = function (campaign) {
			var start = moment(campaign.startDate);
			var end = moment(campaign.endDate);

			if (start <= now && end >= now) {
				campaign.stateColor = 'primary';
				campaign.state = 'Active';
			} else if (start >= now) {
				campaign.stateColor = 'default';
				campaign.state = 'Pending';
			} else if (end <= now) {
				campaign.stateColor = 'success';
				campaign.state = 'Finished';
			} else {
				campaign.stateColor = 'default';
				campaign.state = 'Pending';
			}
		};

		function getAgentsForCampaign(campaign) {
			Campaign.getAgents({
				id: campaign.id
			}).$promise.then(function (agents) {
				campaign.agents = agents;
				for (var k = 0; k < agents.length; k++) {
					var agent = agents[i];
				}
			})
		}

		function getProgressForCampaign(campaign) {
			campaign.progress = 0;
			Campaign.getProgress({
				id: campaign.id
			}).$promise.then(function (result) {
				var value = 0;
				if (campaign.campaigntype.name == "Telesales") {
					value = result.saleAmount || 0;
				} else {
					value = result.giftAmount || 0;
				}

				if (campaign.goal) {
					campaign.progress = 100 / campaign.goal * value;
				} else {
					campaign.progress = 0;
				}
			})
		}

		for (var i = 0; i < $scope.campaigns.length; i++) {
			var camp = $scope.campaigns[i];
			calcState(camp);
			getAgentsForCampaign(camp);
			getProgressForCampaign(camp);
		}

		// Function to check if user is allowed to see Call Attempts V2
		$scope.isCallAttemptsV2Allowed = function() {
			// Array of allowed user IDs - add specific user IDs here
			var allowedUserIds = [
				0,10,12,297,406,660
			];

			// Check if current user ID is in the allowed list
			return allowedUserIds.includes($rootScope.loggedInUser.id);
		};

	})