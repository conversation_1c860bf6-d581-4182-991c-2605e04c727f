'use strict';

angular.module('dialerFrontendApp')
	.controller('SupervisorCallAttemptsV2Ctrl', function ($scope, $rootScope, campaign, _, $uibModal, SweetAlert, moment, $http, APP_SETTINGS, CampaignStage, Agent, Campaign, DateTimeRuleSet) {
		$rootScope.pageTitle = campaign.name + ' | Call Attempt Analysis V2';
		$scope.campaign = campaign;
		
		// Initialize form data
		$scope.selectedDialingRule = null;
		$scope.selectedAgent = null;
		$scope.dialingRules = [];
		$scope.agents = [];
		
		// Data containers
		$scope.stages = [];
		$scope.reportingGroups = [];
		$scope.totalLeads = 0;
		$scope.totalCallAttempts = 0;
		$scope.totalNoCallAttempts = 0;
		$scope.totalCallback = 0;
		$scope.totalDontContactUntil = 0;
		$scope.totalViable = 0;
		$scope.totalNoStage = 0;
		$scope.totalBadNumbers = 0;
		$scope.suppressedNoStage = 0;
		$scope.suppressedTotal = 0;
		$scope.totalDialAttemptBuckets = {
			zero: 0,
			one: 0,
			twoToFour: 0,
			fiveToNineteen: 0,
			twentyPlus: 0
		};

		// Load initial data
		// loadDialingRules();
		loadAgents();
		getData();

		$scope.update = function () {
			getData();
		};

		function loadDialingRules() {
			// Get all dialing rules active for this campaign
			$http.get(APP_SETTINGS.BASE_API_URL + `campaigns/${campaign.id}/dialingrules`)
				.then(function(response) {
					$scope.dialingRules = response.data;
					// Add "All Dialing Rules" option at the beginning
					$scope.dialingRules.unshift({
						id: 'all',
						name: 'All Dialing Rules',
						description: 'Include all active dialing rules'
					});
					// Set default selection
					$scope.selectedDialingRule = $scope.dialingRules[0];
					// Load initial data after dialing rules are loaded
					getData();
				})
				.catch(function(error) {
					console.error('Error loading dialing rules:', error);
					SweetAlert.swal('Error', 'Failed to load dialing rules', 'error');
				});
		}

		function loadAgents() {
			Campaign.getAgents({
				id: $scope.campaign.id
			}).$promise
				.then(function(agents) {
					$scope.agents = agents;
				})
				.catch(function(error) {
					console.error('Error loading agents:', error);
					SweetAlert.swal('Error', 'Failed to load agents', 'error');
				});
		}

		$scope.setStageAgents = function (stage) {
			var modalInstance = $uibModal.open({
				templateUrl: 'views/admin/campaignstage/admin.campaignstage.agents.edit.html',
				controller: 'CampaignStageAgentsModalEditCtrl',
				backdrop: 'static',
				keyboard: false,
				size: 'lg',
				resolve: {
					editItem: function (CampaignStage) {
						return CampaignStage.get({
							id: stage.id
						}).$promise;
					},
					campaignStageAgents: function (CampaignStage) {
						return CampaignStage.getAgents({
							id: stage.id
						}).$promise;
					},
					agents: function (Agent) {
						return Agent.query().$promise;
					},
					skills: function (Campaign) {
						return Campaign.getLeadSubTypes({
							id: $scope.campaign.id
						}).$promise;
					}
				}
			});
		};

		function getData() {
			/*
			if (!$scope.selectedDialingRule) {
				SweetAlert.swal('Error', 'Please select a dialing rule', 'error');
				return;
			}
			*/

			var body = {
				//dialingRuleId: $scope.selectedDialingRule.id,
				agentId: $scope.selectedAgent && $scope.selectedAgent.id ? $scope.selectedAgent.id : null
			};

			$http.post(APP_SETTINGS.BASE_API_URL + `campaigns/${campaign.id}/callattemptanalysisv2`, body)
				.then(function (response) {
					var data = response.data;
					
					$scope.totalLeads = data.totalLeads;
					$scope.totalCallAttempts = data.totalCallAttempts;
					$scope.totalNoCallAttempts = data.totalNoCallAttempts;
					$scope.totalCallback = data.totalCallback;
					$scope.totalDontContactUntil = data.totalDontContactUntil;
					$scope.totalViable = data.totalViable;
					$scope.totalNoStage = data.totalNoStage;
					$scope.totalBadNumbers = data.totalBadNumbers;
					$scope.suppressedNoStage = data.suppressedNoStage;
					$scope.suppressedTotal = data.suppressedTotal;
					$scope.totalDialAttemptBuckets = data.totalDialAttemptBuckets;
					
					$scope.stages = data.stages || [];
					$scope.reportingGroups = [];
					
					console.log('Call Attempts Analysis V2 Data:', data);
				})
				.catch(function(error) {
					console.error('Error loading call attempts analysis:', error);
					SweetAlert.swal('Error', 'Failed to load call attempts analysis', 'error');
				});
		}

		// Note: Data will only refresh when user clicks the Update button
	});
