'use strict';

/**
 * @ngdoc function
 * @name dialerFrontendApp.controller:LoginCtrl
 * @description
 * # LoginCtrl
 * Controller of the dialerFrontendApp
 */
angular.module('dialerFrontendApp')
	.controller('CallbacksSearchModalCtrl', function ($scope, $rootScope, filters, $uibModalInstance) {
		if (!filters.lead) {
			filters.lead = {};
		}

		$scope.filters = filters;

		$scope.ok = function () {
			$uibModalInstance.close($scope.filters);
		};

		$scope.cancel = function () {
			$uibModalInstance.dismiss();
		};
	})