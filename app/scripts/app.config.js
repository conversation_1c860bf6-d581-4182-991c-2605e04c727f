'use strict';

angular.module('dialerFrontendApp')
    .config(function ($stateProvider, $urlRouterProvider, $httpProvider, cfpLoadingBarProvider) {
        cfpLoadingBarProvider.includeSpinner = false;
        cfpLoadingBarProvider.latencyThreshold = 300;

        $httpProvider.interceptors.push('TokenInterceptor');

        $urlRouterProvider.otherwise('/login');

        $stateProvider
            .state('login', {
                url: '/login',
                controller: 'LoginCtrl',
                templateUrl: 'views/login.html'
            })
            .state('agent', {
                abstract: true,
                url: '/agent',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true
                }
            })
            .state('agent.dashboard', {
                url: '/dashboard',
                controller: 'AgentDashboardCtrl',
                templateUrl: 'views/agent/agent.dashboard.html',
                resolve: {
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    }
                }
            })
            .state('agent.dashboard_new', {
                url: '/dashboard_demo',
                controller: 'AgentDashboardCtrl',
                templateUrl: 'views/agent/agent.dashboard_new.html',
                resolve: {
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    }
                }
            })
            .state('agent.dashboardnew', {
                url: '/dashboardnew',
                controller: 'AgentDashboardCtrl',
                templateUrl: 'views/agent/agent.dashboard.new.html',
                resolve: {
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    }
                }
            })
            .state('agent.sales', {
                url: '/sales',
                controller: 'AgentSalesCtrl',
                templateUrl: 'views/agent/agent.sales.html',
                resolve: {
                    callresults: function ($rootScope, Agent) {
                        return Agent.getSales({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    },
                    title: function ($rootScope, Agent) {
                        return Agent.get({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    }
                }
            })
            .state('agent.pledges', {
                url: '/pledges',
                controller: 'AgentPledgesCtrl',
                templateUrl: 'views/agent/agent.pledges.html',
                resolve: {
                    callresults: function ($rootScope, Agent) {
                        return Agent.getPledges({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    },
                    title: function ($rootScope, Agent) {
                        return Agent.get({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    }
                }
            })
            .state('agent.refusals', {
                url: '/refusals',
                controller: 'AgentRefusalsCtrl',
                templateUrl: 'views/agent/agent.refusals.html',
                resolve: {
                    callresults: function ($rootScope, Agent) {
                        return Agent.getRefusals({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    },
                    title: function ($rootScope, Agent) {
                        return Agent.get({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    }
                }
            })
            .state('agent.callbacks', {
                url: '/callbacks',
                controller: 'AgentCallbacksCtrl',
                templateUrl: 'views/agent/agent.callbacks.html',
                resolve: {
                    callbacks: function ($rootScope, AgentCallbacks) {
                        return AgentCallbacks.query({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    },
                    title: function ($rootScope, Agent) {
                        return Agent.get({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    }
                }
            })
            .state('agent.callrecords', {
                url: '/callrecords',
                controller: 'AgentCallrecordsCtrl',
                templateUrl: 'views/agent/agent.callrecords.html',
                resolve: {
                    filters: function ($rootScope) {
                        const now = new Date();
                        const thirtyDaysAgo = new Date(now);
                        thirtyDaysAgo.setDate(now.getDate() - 30); 
                        return {
                            agentId: $rootScope.loggedInUser.agentId,
                            createdAt: { $gte: thirtyDaysAgo }
                        };
                    },
                    title: function ($rootScope, Agent) {
                        return Agent.get({
                            id: $rootScope.loggedInUser.agentId
                        }).$promise;
                    }
                }
            })
            .state('agent.leadanalysis', {
                url: '/leads/{id:int}/analysis',
                controller: 'AgentLeadAnalysisCtrl',
                templateUrl: 'views/agent/lead/agent.lead.analysis.html',
                resolve: {
                    lead: function ($stateParams, Lead) {
                        return Lead.getDetail({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor', {
                abstract: true,
                url: '/supervisor',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true
                }
            })
            .state('supervisor.campaigns', {
                url: '/campaigns',
                controller: 'SupervisorCampaignsCtrl',
                templateUrl: 'views/supervisor/supervisor.campaigns.html',
                resolve: {
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    }
                }
            })
            .state('supervisor.campaignsales', {
                url: '/campaigns/:id/sales',
                controller: 'AgentSalesCtrl',
                templateUrl: 'views/agent/agent.sales.html',
                resolve: {
                    callresults: function ($stateParams, Campaign) {
                        return Campaign.getSales({
                            id: $stateParams.id
                        }).$promise;
                    },
                    title: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigncallrecords', {
                url: '/campaigns/:id/callrecords',
                controller: 'SupervisorCallrecordsCtrl',
                templateUrl: 'views/supervisor/supervisor.callrecords.html',
                resolve: {
                    filters: function ($stateParams) {
                        return {
                            campaignId: $stateParams.id
                        };
                    },
                    title: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaignpledges', {
                url: '/campaigns/:id/pledges',
                controller: 'AgentPledgesCtrl',
                templateUrl: 'views/agent/agent.pledges.html',
                resolve: {
                    callresults: function ($stateParams, Campaign) {
                        return Campaign.getPledges({
                            id: $stateParams.id
                        }).$promise;
                    },
                    title: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaignnotes', {
                url: '/campaigns/:id/notes',
                controller: 'CampaignNotesCtrl',
                templateUrl: 'views/supervisor/supervisor.campaignnotes.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    notes: function ($stateParams, Campaign) {
                        return Campaign.getNotes({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaignrefusals', {
                url: '/campaigns/:id/refusals',
                controller: 'AgentRefusalsCtrl',
                templateUrl: 'views/agent/agent.refusals.html',
                resolve: {
                    callresults: function ($stateParams, Campaign) {
                        return Campaign.getRefusals({
                            id: $stateParams.id
                        }).$promise;
                    },
                    title: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigninvoices', {
                url: '/campaigns/{id:int}/invoices',
                controller: 'InvoicesCtrl',
                templateUrl: 'views/collections/invoices/collections.invoices.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigncallbacks', {
                url: '/campaigns/{id:int}/callbacks',
                controller: 'SupervisorCallbacksCtrl',
                templateUrl: 'views/supervisor/supervisor.callbacks.html',
                resolve: {
                    filters: function ($stateParams) {
                        return {
                            callback: {
                                campaignId: $stateParams.id
                            }
                        };
                    },
                    title: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigncallattempts', {
                url: '/campaigns/{id:int}/callattempts',
                controller: 'SupervisorCallAttemptsCtrl',
                templateUrl: 'views/supervisor/supervisor.campaign.callattempts.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigncallattemptsv2', {
                url: '/campaigns/{id:int}/callattemptsv2',
                controller: 'SupervisorCallAttemptsV2Ctrl',
                templateUrl: 'views/supervisor/supervisor.campaign.callattempts.v2.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaignaudit', {
                url: '/campaigns/{id:int}/audit',
                controller: 'SupervisorCampaignAuditCtrl',
                templateUrl: 'views/supervisor/supervisor.campaign.audit.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    audits: function ($stateParams, Campaign) {
                        return Campaign.getChanges({
                            id: $stateParams.id
                        }).$promise;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    },
                    subskills: function (SubSkill) {
                        return SubSkill.query().$promise;
                    },
                    stages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.agents', {
                url: '/agents',
                controller: 'SupervisorAgentsCtrl',
                templateUrl: 'views/supervisor/supervisor.agents.html',
                resolve: {
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    }
                }
            })
            .state('supervisor.agentcallrecords', {
                url: '/agents/:id/callrecords',
                controller: 'SupervisorCallrecordsCtrl',
                templateUrl: 'views/supervisor/supervisor.callrecords.html',
                resolve: {
                    filters: function ($stateParams) {
                        return {
                            agentId: $stateParams.id
                        };
                    },
                    title: function ($stateParams, Agent) {
                        return Agent.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.campaigndashboard', {
                url: '/campaigndashboard/{id:int}',
                controller: 'SupervisorCampaignDashboardCtrl',
                templateUrl: 'views/supervisor/dashboard/supervisor.dashboard.campaign.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('supervisor.agentdashboard', {
                url: '/agentdashboard/{id:int}/{campaignId:int}',
                controller: 'SupervisorAgentDashboardCtrl',
                templateUrl: 'views/supervisor/dashboard/supervisor.dashboard.agent.html',
                resolve: {
                    agent: function ($stateParams, Agent) {
                        return Agent.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaigns: function ($stateParams, Campaign, Agent) {
                        if ($stateParams.campaignId) {
                            return Agent.getCampaign({
                                id: $stateParams.id,
                                campaignId: $stateParams.campaignId
                            }).$promise;
                        } else {
                            return Agent.getCampaigns({
                                id: $stateParams.id
                            }).$promise;
                        }
                    }
                }
            })
            .state('supervisor.agentstatus', {
                url: '/agentstatus',
                controller: 'SupervisorAgentStatusCtrl',
                templateUrl: 'views/supervisor/supervisor.agentstatus.html',
                resolve: {
                    agentSessions: function (AgentSession) {
                        return AgentSession.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    }
                }
            })
            .state('supervisor.broadcast', {
                url: '/broadcast',
                controller: 'SupervisorBroadcastCtrl',
                templateUrl: 'views/supervisor/supervisor.broadcast.html',
                resolve: {
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    messages: function (BroadcastMessage) {
                        return BroadcastMessage.query().$promise;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    },
                    campaignStages: function (CampaignStage) {
                        return CampaignStage.query().$promise;
                    },
                    skills: function (SubSkill) {
                        return SubSkill.query().$promise;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    }
                }
            })
            .state('supervisor.leadaudit', {
                url: '/supervisor/leadaudit',
                controller: 'SupervisorLeadAuditCtrl',
                templateUrl: 'views/supervisor/supervisor.leadaudit.html'
            })
            .state('supervisor.campaignleads', {
                url: '/campaigns/{id:int}/leads',
                controller: 'CampaignLeadCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.leads.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaignStages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                    filters: function () {
                        return {};
                    }
                }
            })
            .state('supervisor.campaignstageleads', {
                url: '/campaigns/{id:int}/stage/{stageid:int}/leads',
                controller: 'CampaignLeadCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.leads.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaignStages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                    filters: function ($stateParams) {
                        return {
                            campaignstageId: $stateParams.stageid
                        };
                    }
                }
            })
            .state('supervisor.campaignstageskillleads', {
                url: '/campaigns/{id:int}/stage/{stageid:int}/skill/{skillid:int}/leads',
                controller: 'CampaignLeadCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.leads.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaignStages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                    filters: function ($stateParams) {
                        return {
                            campaignstageId: $stateParams.stageid,
                            subskillId: $stateParams.skillid
                        };
                    }
                }
            })
            .state('supervisor.campaigntrainingdocs', {
                url: '/campaigns/{id:int}/trainingdocs',
                controller: 'CampaignTrainingDocsCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.trainingdocs.html',
                resolve: {
                    docs: function ($stateParams, Campaign) {
                        return Campaign.getDocs({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin', {
                abstract: true,
                url: '/admin',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true,
                    requiredRole: 'admin'
                }
            })
            .state('report', {
                abstract: true,
                url: '/report',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true
                }
            })
            .state('admin.dispositions', {
                url: '/dispositions',
                controller: 'AdminDispositionsCtrl',
                templateUrl: 'views/admin/disposition/admin.dispositions.html'
            })
            .state('admin.dispositionadd', {
                url: '/dispositions/create',
                controller: 'DispositionEditCtrl',
                templateUrl: 'views/admin/disposition/admin.disposition.edit.html',
                resolve: {
                    editItem: function (Disposition) {
                        var customDisposition = new Disposition();
                        customDisposition.templateUrl = 'views/agent/dispositions/agent.disposition.custom.modal.html';
                        customDisposition.controller = 'CustomDispositionModalCtrl';
                        return customDisposition;
                    },
                    callResultFields: function (CallResultField) {
                        return CallResultField.query().$promise;
                    }
                }
            })
            .state('admin.dispositionedit', {
                url: '/dispositions/{id:int}',
                controller: 'DispositionEditCtrl',
                templateUrl: 'views/admin/disposition/admin.disposition.edit.html',
                resolve: {
                    editItem: function ($stateParams, Disposition) {
                        return Disposition.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    callResultFields: function (CallResultField) {
                        return CallResultField.query().$promise;
                    }
                }
            })
            .state('admin.datetimerulesets', {
                url: '/datetimerulesets',
                controller: 'AdminDateTimeRuleSetsCtrl',
                templateUrl: 'views/admin/datetimeruleset/admin.datetimerulesets.html'
            })
            .state('admin.datetimerulesetedit', {
                url: '/datetimerulesets/{id:int}',
                controller: 'DateTimeRuleSetEditCtrl',
                templateUrl: 'views/admin/datetimeruleset/admin.datetimeruleset.edit.html',
                resolve: {
                    editItem: function ($stateParams, DateTimeRuleSet) {
                        return DateTimeRuleSet.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.datetimerulesetadd', {
                url: '/datetimerulesets/create',
                controller: 'DateTimeRuleSetEditCtrl',
                templateUrl: 'views/admin/datetimeruleset/admin.datetimeruleset.edit.html',
                resolve: {
                    editItem: function (DateTimeRuleSet) {
                        var newItem = new DateTimeRuleSet();

                        newItem.startTime = new Date();
                        newItem.startTime.setHours(9);
                        newItem.startTime.setMinutes(0);
                        newItem.startTime.setSeconds(0);

                        newItem.endTime = new Date();
                        newItem.endTime.setHours(17);
                        newItem.endTime.setMinutes(0);
                        newItem.endTime.setSeconds(0);

                        return newItem;
                    }
                }
            })
            .state('admin.callresultfields', {
                url: '/callresultfields',
                controller: 'AdminCallResultFieldsCtrl',
                templateUrl: 'views/admin/callresultfield/admin.callresultfields.html'
            })
            .state('admin.callresultfieldedit', {
                url: '/callresultfields/{id:int}',
                controller: 'CallResultFieldEditCtrl',
                templateUrl: 'views/admin/callresultfield/admin.callresultfield.edit.html',
                resolve: {
                    editItem: function ($stateParams, CallResultField) {
                        return CallResultField.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    callResultFieldGroups: function (CallResultFieldGroup) {
                        return CallResultFieldGroup.query().$promise;
                    },
                    callResultFieldTypes: function (CallResultFieldType) {
                        return CallResultFieldType.query().$promise;
                    }
                }
            })
            .state('admin.callresultfieldadd', {
                url: '/callresultfields/create',
                controller: 'CallResultFieldEditCtrl',
                templateUrl: 'views/admin/callresultfield/admin.callresultfield.edit.html',
                resolve: {
                    editItem: function (CallResultField) {
                        return new CallResultField();
                    },
                    callResultFieldGroups: function (CallResultFieldGroup) {
                        return CallResultFieldGroup.query().$promise;
                    },
                    callResultFieldTypes: function (CallResultFieldType) {
                        return CallResultFieldType.query().$promise;
                    }
                }
            })
            .state('admin.clients', {
                url: '/clients',
                controller: 'AdminClientsCtrl',
                templateUrl: 'views/admin/client/admin.clients.html',
                resolve: {
                    clients: function (Client) {
                        return Client.query().$promise;
                    }
                }
            })
            .state('admin.clientedit', {
                url: '/clients/{id:int}',
                controller: 'ClientEditCtrl',
                templateUrl: 'views/admin/client/admin.client.edit.html',
                resolve: {
                    editItem: function ($stateParams, Client) {
                        return Client.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    costings: function ($stateParams, Client) {
                        return Client.getCostings({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaigns: function ($stateParams, Client) {
                        return Client.getCampaigns({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.clientadd', {
                url: '/clients/create',
                controller: 'ClientEditCtrl',
                templateUrl: 'views/admin/client/admin.client.edit.html',
                resolve: {
                    editItem: function (Client) {
                        return new Client();
                    },
                    costings: function () {
                        return [];
                    },
                    campaigns: function () {
                        return [];
                    }
                }
            })
            .state('admin.clientleads', {
                url: '/clients/{id:int}/leads',
                controller: 'ClientLeadCtrl',
                templateUrl: 'views/admin/client/admin.client.leads.html',
                resolve: {
                    leads: function ($stateParams, ClientLeads) {
                        return ClientLeads.query({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaigninvoices', {
                url: '/campaigns/{id:int}/invoices',
                controller: 'InvoicesCtrl',
                templateUrl: 'views/collections/invoices/collections.invoices.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignproducts', {
                url: '/campaigns/{id:int}/products',
                controller: 'CampaignProductsCtrl',
                templateUrl: 'views/admin/campaignproducts/admin.products.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignproductcreate', {
                url: '/campaigns/{id:int}/products/create',
                controller: 'CampaignProductEditCtrl',
                templateUrl: 'views/admin/campaignproducts/admin.product.edit.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    editItem: function (CampaignProduct) {
                        return new CampaignProduct();
                    }
                }
            })
            .state('admin.campaignproductedit', {
                url: '/campaigns/{campaignId:int}/products/{id:int}',
                controller: 'CampaignProductEditCtrl',
                templateUrl: 'views/admin/campaignproducts/admin.product.edit.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.campaignId
                        }).$promise;
                    },
                    editItem: function ($stateParams, CampaignProduct) {
                        return CampaignProduct.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignstages', {
                url: '/campaigns/{id:int}/stages',
                controller: 'CampaignStageCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.stages.html',
                resolve: {
                    stages: function ($stateParams, CampaignStage) {
                        return CampaignStage.query({
                            campaignId: $stateParams.id
                        }).$promise;
                    },
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignstageadd', {
                url: '/campaigns/{id:int}/stages/create',
                controller: 'CampaignStageEditCtrl',
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.edit.html',
                resolve: {
                    editItem: function ($stateParams, CampaignStage) {
                        var stage = new CampaignStage();
                        stage.campaignId = $stateParams.id;
                        return stage;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    dispositions: function (Disposition) {
                        return Disposition.query().$promise;
                    },
                    skills: function (Skill) {
                        return Skill.query().$promise;
                    }
                }
            })
            .state('admin.campaignstageleads', {
                url: '/campaignstages/{id:int}/leads',
                controller: 'CampaignStageLeadsCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.leads.html',
                resolve: {
                    stage: function (CampaignStage, $stateParams) {
                        return CampaignStage.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignstageedit', {
                url: '/campaignstages/{id:int}',
                controller: 'CampaignStageEditCtrl',
                templateUrl: 'views/admin/campaignstage/admin.campaignstage.edit.html',
                resolve: {
                    editItem: function ($stateParams, CampaignStage) {
                        return CampaignStage.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    dispositions: function (Disposition) {
                        return Disposition.query().$promise;
                    },
                    skills: function (Skill) {
                        return Skill.query().$promise;
                    }
                }
            })
            .state('admin.campaigns', {
                url: '/campaigns',
                controller: 'AdminCampaignsCtrl',
                templateUrl: 'views/admin/campaign/admin.campaigns.html'
            })
            .state('admin.campaignadd', {
                url: '/campaigns/create',
                controller: 'CampaignWizardCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.wizard.html',
                resolve: {
                    editItem: function (Campaign) {
                        var newCampaign = new Campaign();
                        newCampaign.leadContactIntervalHours = 24;
                        return newCampaign;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    },
                    campaignTypes: function (CampaignType) {
                        return CampaignType.query().$promise;
                    },
                    campaignStages: function () {
                        return [];
                    },
                    agentTargets: function () {
                        return [];
                    },
                    users: function (User) {
                        return User.query().$promise;
                    }
                }
            })
            .state('admin.campaignedit', {
                url: '/campaigns/{id:int}',
                controller: 'CampaignWizardCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.wizard.html',
                resolve: {
                    editItem: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    },
                    campaignTypes: function (CampaignType) {
                        return CampaignType.query().$promise;
                    },
                    campaignStages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                    dateTimeRules: function (DateTimeRuleSet) {
                        return DateTimeRuleSet.query().$promise;
                    },
                    agentTargets: function ($stateParams, Campaign) {
                        return Campaign.getAgentTargets({
                            id: $stateParams.id
                        }).$promise;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    }
                }
            })
            .state('admin.campaignleadimport', {
                url: '/campaigns/{id:int}/importleads',
                controller: 'CampaignLeadImportCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.importleads.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    uploads: function ($stateParams, Campaign) {
                        return Campaign.getUploads({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignimportchanges', {
                url: '/campaigns/{id:int}/importchanges',
                controller: 'CampaignImportChangesCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.importchanges.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignimportchangeshistory', {
                url: '/campaigns/{id:int}/importchanges/history',
                controller: 'CampaignImportChangesHistoryCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.importchanges.history.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    stages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignremoveleadsimporter', {
                url: '/campaigns/{id:int}/removeleads',
                controller: 'CampaignRemoveLeadsImporterCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.removeleadsimporter.html',
                resolve: {
                    skills: function (Skill) {
                        return Skill.query().$promise;
                    },
                    campaignId: function ($stateParams) {
                        return $stateParams.id;
                    },
                    uploads: function ($stateParams, Campaign) {
                        return Campaign.getUploads({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaigntrainingdocimport', {
                url: '/campaigns/{id:int}/importtrainingdocs',
                controller: 'CampaignTrainingDocImportCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.importtrainingdocs.html',
                resolve: {
                    editItem: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignleads', {
                url: '/campaigns/{id:int}/leads',
                controller: 'CampaignLeadCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.leads.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaignStages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                    filters: function () {
                        return {};
                    }
                }
            })
            .state('admin.campaigntrainingdocs', {
                url: '/campaigns/{id:int}/trainingdocs',
                controller: 'CampaignTrainingDocsCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.trainingdocs.html',
                resolve: {
                    docs: function ($stateParams, Campaign) {
                        return Campaign.getDocs({
                            id: $stateParams.id
                        }).$promise;
                    },
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaigncalls', {
                url: '/campaigns/{id:int}/calls',
                controller: 'CampaignCallsCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.calls.html',
                resolve: {
                    calls: function ($stateParams, CampaignCallAttempts) {
                        return CampaignCallAttempts.query({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.campaignsuppressed', {
                url: '/campaigns/{id:int}/suppressed',
                controller: 'CampaignSuppressedCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.suppressed.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    stages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                }
            })
            .state('admin.campaignsuppressionhistory', {
                url: '/campaigns/{id:int}/suppressed/history',
                controller: 'CampaignSuppressedHistoryCtrl',
                templateUrl: 'views/admin/campaign/admin.campaign.suppressed.history.html',
                resolve: {
                    campaign: function ($stateParams, Campaign) {
                        return Campaign.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    stages: function ($stateParams, Campaign) {
                        return Campaign.getCampaignStages({
                            id: $stateParams.id
                        }).$promise;
                    },
                }
            })
            .state('admin.agents', {
                url: '/agents',
                controller: 'AdminAgentsCtrl',
                templateUrl: 'views/admin/agent/admin.agents.html'
            })
            .state('admin.agentadd', {
                url: '/agents/create',
                controller: 'AgentEditCtrl',
                templateUrl: 'views/admin/agent/admin.agent.edit.html',
                resolve: {
                    editItem: function (Agent) {
                        return new Agent();
                    },
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    devices: function (Device) {
                        return Device.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    }
                }
            })
            .state('admin.agentedit', {
                url: '/agents/{id:int}',
                controller: 'AgentEditCtrl',
                templateUrl: 'views/admin/agent/admin.agent.edit.html',
                resolve: {
                    editItem: function ($stateParams, Agent) {
                        return Agent.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    agentStates: function (AgentState) {
                        return AgentState.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    devices: function (Device) {
                        return Device.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    }
                }
            })
            .state('admin.agentstates', {
                url: '/agentstates',
                controller: 'AdminAgentStatesCtrl',
                templateUrl: 'views/admin/agentstate/admin.agentstates.html'
            })
            .state('admin.agentstateadd', {
                url: '/agentstates/create',
                controller: 'AgentStateEditCtrl',
                templateUrl: 'views/admin/agentstate/admin.agentstate.edit.html',
                resolve: {
                    editItem: function (AgentState) {
                        return new AgentState();
                    }
                }
            })
            .state('admin.agentstateedit', {
                url: '/agentstates/{id:int}',
                controller: 'AgentStateEditCtrl',
                templateUrl: 'views/admin/agentstate/admin.agentstate.edit.html',
                resolve: {
                    editItem: function ($stateParams, AgentState) {
                        return AgentState.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.skills', {
                url: '/skills',
                controller: 'AdminSkillsCtrl',
                templateUrl: 'views/admin/skill/admin.skills.html'
            })
            .state('admin.skilladd', {
                url: '/skills/create',
                controller: 'SkillEditCtrl',
                templateUrl: 'views/admin/skill/admin.skill.edit.html',
                resolve: {
                    editItem: function (SubSkill) {
                        return new SubSkill();
                    }
                }
            })
            .state('admin.skilledit', {
                url: '/skills/{id:int}',
                controller: 'SkillEditCtrl',
                templateUrl: 'views/admin/skill/admin.skill.edit.html',
                resolve: {
                    editItem: function ($stateParams, SubSkill) {
                        return SubSkill.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.refusalreasons', {
                url: '/refusalreasons',
                controller: 'AdminRefusalReasonsCtrl',
                templateUrl: 'views/admin/refusalreason/admin.refusalreasons.html',
                resolve: {
                    reasons: function (RefusalReason) {
                        return RefusalReason.query().$promise;
                    }
                }
            })
            .state('admin.users', {
                url: '/users',
                controller: 'AdminUsersCtrl',
                templateUrl: 'views/admin/user/admin.users.html'
            })
            .state('admin.usersadd', {
                url: '/users/create',
                controller: 'UserEditCtrl',
                templateUrl: 'views/admin/user/admin.users.edit.html',
                resolve: {
                    editItem: function (User) {
                        return new User();
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    }
                }
            })
            .state('admin.usersedit', {
                url: '/users/{id:int}',
                controller: 'UserEditCtrl',
                templateUrl: 'views/admin/user/admin.users.edit.html',
                resolve: {
                    editItem: function ($stateParams, User) {
                        return User.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    }
                }
            })
            .state('admin.campaigntypes', {
                url: '/campaigntypes',
                controller: 'AdminCampaignTypesCtrl',
                templateUrl: 'views/admin/campaigntype/admin.campaigntypes.html'
            })
            .state('admin.campaigntypeadd', {
                url: '/campaigntypes/create',
                controller: 'CampaignTypeEditCtrl',
                templateUrl: 'views/admin/campaigntype/admin.campaigntype.edit.html',
                resolve: {
                    editItem: function (CampaignType) {
                        return new CampaignType();
                    },
                    skills: function (Skill) {
                        return Skill.query().$promise;
                    }
                }
            })
            .state('admin.campaigntypeedit', {
                url: '/campaigntypes/{id:int}',
                controller: 'CampaignTypeEditCtrl',
                templateUrl: 'views/admin/campaigntype/admin.campaigntype.edit.html',
                resolve: {
                    editItem: function ($stateParams, CampaignType) {
                        return CampaignType.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    skills: function (Skill) {
                        return Skill.query().$promise;
                    }
                }
            })
            .state('admin.leadcallhistory', {
                url: '/leads/{id:int}/callhistory',
                controller: 'LeadCallHistoryCtrl',
                templateUrl: 'views/admin/lead/admin.lead.callhistory.html',
                resolve: {
                    lead: function ($stateParams, Lead) {
                        return Lead.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    history: function ($stateParams, Lead) {
                        return Lead.getCallHistory({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.leadaudithistory', {
                url: '/leads/{id:int}/audithistory',
                controller: 'LeadAuditHistoryCtrl',
                templateUrl: 'views/admin/lead/admin.lead.audithistory.html',
                resolve: {
                    lead: function ($stateParams, Lead) {
                        return Lead.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    history: function ($stateParams, Lead) {
                        return Lead.getAuditHistory({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('admin.leadanalysis', {
                url: '/leads/{id:int}/analysis',
                controller: 'LeadAnalysisCtrl',
                templateUrl: 'views/admin/lead/admin.lead.analysis.html',
                resolve: {
                    lead: function ($stateParams, Lead) {
                        return Lead.getDetail({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('report.agenttimeline', {
                url: '/reports/agenttimeline',
                controller: 'ReportAgentTimelineCtrl',
                templateUrl: 'views/report/report.agenttimeline.html',
                resolve: {
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    }
                }
            })
            .state('report.all', {
                url: '/all',
                controller: 'ReportCustomCtrl',
                templateUrl: 'views/report/reports.html',
                resolve: {
                    reportFolder: function () {
                        return '';
                    },
                    reports: function (Report) {
                        return Report.query().$promise;
                    }
                }
            })
            .state('report.clientlogin', {
                url: '/clientreports',
                controller: 'ReportClientLoginCtrl',
                templateUrl: 'views/report/report.client.html'
            })
            .state('report.clientloginhistory', {
                url: '/{id:int}/clienthistory',
                controller: 'ReportClientHistoryCtrl',
                templateUrl: 'views/report/report.client.history.html',
                resolve: {
                    reportId: function ($stateParams) {
                        return $stateParams.id;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    }
                }
            })
            .state('report.campaign', {
                url: '/campaign',
                controller: 'ReportCustomCtrl',
                templateUrl: 'views/report/reports.html',
                resolve: {
                    reportFolder: function () {
                        return 'Campaign Reports';
                    },
                    reports: function (Report) {
                        return Report.query().$promise;
                    }
                }
            })
            .state('report.agent', {
                url: '/agent',
                controller: 'ReportCustomCtrl',
                templateUrl: 'views/report/reports.html',
                resolve: {
                    reportFolder: function () {
                        return 'Agent Reports';
                    },
                    reports: function (Report) {
                        return Report.query().$promise;
                    }
                }
            })
            .state('report.custom', {
                url: '/custom',
                controller: 'ReportCustomCtrl',
                templateUrl: 'views/report/reports.html',
                resolve: {
                    reportFolder: function () {
                        return 'Custom Reports';
                    },
                    reports: function (Report) {
                        return Report.query().$promise;
                    }
                }
            })
            .state('report.history', {
                url: '/{id:int}/history',
                controller: 'ReportHistoryCtrl',
                templateUrl: 'views/report/report.history.html',
                resolve: {
                    report: function ($stateParams, Report) {
                        return Report.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    }
                }
            })
            .state('report.create', {
                url: '/create',
                controller: 'ReportBuilderCtrl',
                templateUrl: 'views/report/report.builder.html',
                resolve: {
                    reportObj: function () {
                        return {};
                    },
                    modules: function (ReportModules) {
                        return ReportModules.query().$promise;
                    }
                }
            })
            .state('report.edit', {
                url: '/{id:int}/edit',
                controller: 'ReportBuilderCtrl',
                templateUrl: 'views/report/report.builder.html',
                resolve: {
                    reportObj: function ($stateParams, Report) {
                        return Report.get({
                            id: $stateParams.id
                        }).$promise;
                    },
                    modules: function (ReportModules) {
                        return ReportModules.query().$promise;
                    }
                }
            })
            .state('report.schedules', {
                url: '/{id:int}/schedules',
                controller: 'ReportSchedulesCtrl',
                templateUrl: 'views/report/schedule/report.schedules.html',
                resolve: {
                    schedules: function ($stateParams, Report) {
                        return Report.getSchedules({
                            id: $stateParams.id
                        }).$promise;
                    },
                    reportId: function ($stateParams) {
                        return $stateParams.id;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    },
                    clients: function (Client) {
                        return Client.query().$promise;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    }
                }
            })
            .state('report.views', {
                url: '/views',
                controller: 'ReportViewsCtrl',
                templateUrl: 'views/report/views/reports.views.html'
            })
            .state('report.viewsweeklygoals', {
                url: '/weeklygoals',
                controller: 'ReportWeeklyGoalsCtrl',
                templateUrl: 'views/report/views/reports.views.weeklygoals.html'
            })
            .state('report.viewsexpiredcallbacks', {
                url: '/expiredcallbacks',
                controller: 'ReportViewsExpiredCallbacksCtrl',
                templateUrl: 'views/report/views/reports.views.expiredcallbacks.html'
            })
            .state('report.viewspledges', {
                url: '/pledges',
                controller: 'ReportViewsPledgesCtrl',
                templateUrl: 'views/report/views/reports.views.pledges.html'
            })
            .state('report.viewssales', {
                url: '/sales',
                controller: 'ReportViewsSalesCtrl',
                templateUrl: 'views/report/views/reports.views.sales.html'
            })
            .state('report.viewscollections', {
                url: '/collections',
                controller: 'ReportViewsCollectionsCtrl',
                templateUrl: 'views/report/views/reports.views.collections.html'
            })
            .state('system', {
                abstract: true,
                url: '/system',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true,
                    requiredRole: 'admin'
                }
            })
            .state('system.email', {
                url: '/email',
                controller: 'EmailCtrl',
                templateUrl: 'views/system/system.email.html',
                resolve: {
                    email: function (System) {
                        return System.get({
                            key: 'email'
                        }).$promise;
                    }
                }
            })
            .state('system.errors', {
                url: '/errors',
                controller: 'ErrorsCtrl',
                templateUrl: 'views/system/system.errors.html',
                resolve: {
                    errors: function (Panic) {
                        return Panic.query().$promise;
                    }
                }
            })
            .state('system.devices', {
                url: '/devices',
                controller: 'DevicesCtrl',
                templateUrl: 'views/system/system.devices.html',
                resolve: {
                    devices: function (Device) {
                        return Device.query().$promise;
                    }
                }
            })
            .state('system.devicesedit', {
                url: '/devices/{id:int}',
                controller: 'EditDevicesCtrl',
                templateUrl: 'views/system/devices/system.devices.edit.html',
                resolve: {
                    editItem: function ($stateParams, Device) {
                        return Device.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('system.devicescreate', {
                url: '/devices/create',
                controller: 'EditDevicesCtrl',
                templateUrl: 'views/system/devices/system.devices.edit.html',
                resolve: {
                    editItem: function (Device) {
                        return new Device();
                    }
                }
            })
            .state('system.sessions', {
                url: '/session',
                controller: 'SessionsCtrl',
                templateUrl: 'views/system/sessions/system.sessions.html',
                resolve: {
                    users: function (User) {
                        return User.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    }
                }
            })
            .state('system.emailhistory', {
                url: '/emailhistory',
                controller: 'EmailHistoryCtrl',
                templateUrl: 'views/system/system.emailhistory.html',
                resolve: {
                    history: function (EmailHistory) {
                        return EmailHistory.query().$promise;
                    }
                }
            })
            .state('system.merchants', {
                url: '/merchants',
                controller: 'MerchantsCtrl',
                templateUrl: 'views/system/merchants/system.merchants.html',
                resolve: {
                    users: function (User) {
                        return User.query().$promise;
                    },
                    agents: function (Agent) {
                        return Agent.query().$promise;
                    }
                }
            })
            .state('collections', {
                abstract: true,
                url: '/collections',
                templateUrl: 'views/common/master.layout.html',
                data: {
                    requiredLogin: true,
                    requiredRole: 'collections'
                }
            })
            .state('collections.invoices', {
                url: '/invoices',
                controller: 'InvoicesCtrl',
                templateUrl: 'views/collections/invoices/collections.invoices.html',
                resolve: {
                    campaign: function () {
                        return null;
                    }
                }
            })
            .state('collections.invoicehistory', {
                url: '/invoices/{id:int}/history',
                controller: 'InvoiceHistoryCtrl',
                templateUrl: 'views/collections/invoices/collections.invoice.history.html',
                resolve: {
                    invoice: function ($stateParams, Invoice) {
                        return Invoice.get({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('collections.payments', {
                url: '/payments',
                controller: 'AdminPaymentsCtrl',
                templateUrl: 'views/collections/payment/admin.payments.html',
                resolve: {
                    clients: function (Client) {
                        return Client.query().$promise;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    },
                    users: function (User) {
                        return User.query().$promise;
                    }
                }
            })
            .state('collections.invoicesexports', {
                url: '/invoices/{id:int}/exports',
                controller: 'InvoiceExportsCtrl',
                templateUrl: 'views/collections/invoices/collections.invoices.exports.html',
                resolve: {
                    invoicesHistory: function ($stateParams, Invoice) {
                        return Invoice.getHistory({
                            id: $stateParams.id
                        }).$promise;
                    }
                }
            })
            .state('collections.invoicespayments', {
                url: '/invoices/{id:int}/payments',
                controller: 'InvoicePaymentsCtrl',
                templateUrl: 'views/collections/invoices/collections.invoices.payments.html',
                resolve: {
                    payments: function ($stateParams, APP_SETTINGS, $http) {
                        return $http.get(APP_SETTINGS.BASE_API_URL + 'invoices/' + $stateParams.id + '/payments');
                    }
                }
            })
            .state('collections.recurring', {
                url: '/recurring',
                controller: 'AdminRecurringCtrl',
                templateUrl: 'views/collections/recurring/admin.recurring.html',
                resolve: {
                    recurring: function (RecurringPayment) {
                        return RecurringPayment.query().$promise;
                    },
                    campaigns: function (Campaign) {
                        return Campaign.query().$promise;
                    }
                }
            })
            .state('paymenttest', {
                url: '/testpayment',
                controller: 'TestPaymentCtrl',
                templateUrl: 'views/test/test.payment.html'
            })
            .state('pledgetest', {
                url: '/test',
                controller: 'TestPledgeV2Ctrl',
                templateUrl: 'views/test/test.pledge.v2.html'
            });
    });