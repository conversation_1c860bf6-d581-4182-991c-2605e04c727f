/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
	.service('SipML5WebRTCPhone', function (eventEmitter, $http) {
		// Setup event emitter methods (on, once, emit) for this service
		eventEmitter.inject(this);



		//** Private Properties **//

		var sipStack,
			_credentials,
			callSession,
			registerSession,
			self = this;



		//** Private Methods **//

		function getRecordingLocation() {
			$http({
				method: 'GET',
				url: `https://${self.credentials.server}:3000/api/v1/getrecordinglocation?apitoken=0bfcc65c-e362-4017-8435-61a5e53cd4ae&ext=${self.credentials.extension}`
			}).then(function successCallback(response) {
				if (response && response.data && !response.data.error) {
					self.recordingLocation = response.data.recordingLocation;
					self.callUuid = response.data.uuid;
				}
			}, function errorCallback(response) {
				console.log('getRecordingLocation() error')
				console.log(response)
			})
		}

		function startRingTone() {
			try { ringtone.play(); }
			catch (e) { }
		}

		function stopRingTone() {
			try { ringtone.pause(); }
			catch (e) { }
		}

		function startRingbackTone() {
			try { ringbacktone.play(); }
			catch (e) { }
		}

		function stopRingbackTone() {
			try { ringbacktone.pause(); }
			catch (e) { }
		}

		function createSipStack(credentials) {
			self.credentials = credentials;
			sipStack = new SIPml.Stack({
				realm: credentials.server,
				impi: credentials.extension,
				impu: 'sip:' + credentials.extension + '@' + credentials.server,
				password: credentials.password,
				display_name: credentials.display,
				// ice_servers: [{
				// 	url: 'stun:stun2.innovovoip.com'
				// }],
				ice_servers: [{"url":"stun:stun.l.google.com:19302"},{"url":"stun:stun.counterpath.net:3478"},{"url":"stun:numb.viagenie.ca:3478"}],
				websocket_proxy_url: 'wss://' + credentials.server + ':7443',
				outbound_proxy_url: 'udp://' + credentials.server + ':5060',
				enable_rtcweb_breaker: false,
				events_listener: { events: '*', listener: eventsListener },
				sip_headers: [
					{ name: 'User-Agent', value: 'DualtoneClient-v1.0.0.0' },
					{ name: 'Organization', value: 'Dualtone Communications Ltd' }
				]
			});

			sipStack.start();

			return sipStack;
		}

		// function registerSIPAccount() {
		// 	registerSession = sipStack.newSession('register', {
		// 		events_listener: { events: '*', listener: eventsListener }
		// 	});

		// 	registerSession.register();
		// }

		function eventsListener(e) {
			console.info('session event = ' + e.type);

			switch (e.type) {
				case 'starting': break;
				case 'started':
					{
						// try {
						// 	registerSIPAccount();
						// }
						// catch (e) { }
						break;
					}
				case 'stopping': case 'stopped': case 'failed_to_start': case 'failed_to_stop':
					{
						stopRingTone();
						stopRingbackTone();
						self.emit('stopped');
						self.init(_credentials)
						break;
					}
				case 'i_new_call':
					{
						self.emit('newinboundcall');
						break;
					}
				case 'm_permission_requested':
					{
						break;
					}
				case 'm_permission_accepted':
				case 'm_permission_refused':
					{
						break;
					}
				case 'connecting': case 'connected':
					{
						var bConnected = (e.type === 'connected');
						if (registerSession && e.session === registerSession) {
							self.emit('registered', e);
						}
						else if (e.session === callSession) {
							if (bConnected) {
								stopRingbackTone();
								stopRingTone();
								getRecordingLocation();
								self.emit('callconnected', e);
							}

							self.emit('callstatuschange', { label: e.description });
						}
						break;
					}
				case 'terminating': case 'terminated':
					{
						callSession = null;
						self.onCall = false;

						self.emit('callstatuschange', { label: 'Idle' });

						if (e.session === registerSession) {
							// SIP session ended
							registerSession = null;
							self.emit('unregistered', e);
						}
						else {
							// Call session ended
							stopRingbackTone();
							stopRingTone();

							self.emit('callended', e);
						}
						break;
					}
				case 'i_ect_new_call':
					{
						// transferCall = e.session;
						break;
					}
				case 'i_ao_request':
					{
						if (e.session === callSession) {
							var iSipResponseCode = e.getSipResponseCode();
							if (iSipResponseCode === 180 || iSipResponseCode === 183) {
								try {
									var header = e.o_event.o_message.ao_headers.find(h => h.s_name === 'User-Agent')
									if (header) {
										self.emit('uuid', header.s_value);
									}
								} catch (e) {

								}
								self.emit('callringing');
								self.emit('callstatuschange', { label: 'Ringing...' });
							}
						}
						break;
					}

				case 'm_early_media':
					{
						if (e.session === callSession) {
							stopRingbackTone();
							stopRingTone();

							self.emit('callstatuschange', { label: 'Ringing...' });
						}
						break;
					}
				case 'm_local_hold_ok':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'Call placed on hold' });
						}
						break;
					}
				case 'm_local_hold_nok':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'Failed to place remote party on hold' });
						}
						break;
					}
				case 'm_local_resume_ok':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'In Call' });
						}
						break;
					}
				case 'm_local_resume_nok':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'Failed to unhold call' });
						}
						break;
					}
				case 'm_remote_hold':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'Placed on hold by remote party' });
						}
						break;
					}
				case 'm_remote_resume':
					{
						if (e.session === callSession) {
							self.emit('callstatuschange', { label: 'Taken off hold by remote party' });
						}
						break;
					}
				default: break;
			}
		}



		//** Public Properties **//

		this.held = false;
		this.muted = false;
		this.onCall = false;
		this.callStatus = 'Idle';
		this.PHONE_TYPE = 'WebRTC';
		this.recordingPaused = false;


		//** Public Methods **//

		this.makeCall = function (number, callerId, dontRecord) {
			self.recordingLocation = null;
			self.callUuid = null;
			self.recordingPaused = false;

			// Testing
			if (callerId === '1111111111') {
				number = '*9196';
				// number = '03301229999';
			}

			if (sipStack) {
				var headers = [{ name: 'X-Dialer-CallerId', value: callerId }]
				if (dontRecord) {
					headers.push({ name: 'X-Dialer-DontRecord', value: '1' })
				}

				callSession = sipStack.newSession('call-audio', {
					audio_remote: document.getElementById('audio_remote'),
					events_listener: { events: '*', listener: eventsListener },
					sip_headers: headers
				});

				// startRingbackTone();

				self.emit('callstarted', number);
				self.onCall = true;

				return callSession.call(number) === 0;
			}
			else return false;
		};

		this.hangup = function () {
			if (callSession) {
				return callSession.hangup() === 0;
			}
			else return false;
		};

		this.sendDtmf = function (digit) {
			if (callSession) {
				console.info('Sending DTMF digit: ', digit);
				return callSession.dtmf(digit) === 0;
			}
			else return false;
		};

		this.transfer = function (to) {
			if (callSession) {
				return callSession.transfer(to) === 0;
			}
			else return false;
		};

		this.toggleHold = function () {
			if (callSession) {
				callSession.held = !callSession.held;
				var success = (callSession.held ? callSession.hold() : callSession.resume()) === 0;
				if (!success) {
					callSession.held = !callSession.held;
				}
				self.held = callSession.held;
				return success;
			}
			else {
				self.held = false;
				return false;
			}
		};

		this.toggleMute = function () {
			if (callSession) {
				callSession.muted = !callSession.muted;
				var success = (callSession.mute('audio', callSession.muted)) === 0;
				if (!success) {
					callSession.muted = !callSession.muted;
				}
				self.muted = callSession.muted;
				return success;
			}
			else {
				self.muted = false;
				return false;
			}
		};

		this.pauseRecording = function () {
			console.log('Pausing recording');
			if (self.callUuid && self.recordingLocation) {
				$http.post(`https://${self.credentials.server}:3000/api/v1/pause`, {
					apitoken: '0bfcc65c-e362-4017-8435-61a5e53cd4ae',
					uuid: self.callUuid,
					recordingLocation: self.recordingLocation
				}).then(function successCallback(response) {
					if (response && response.data && response.data.success) {
						self.recordingPaused = true;
					}
				}, function errorCallback(response) {
					console.log('pauseRecording() error')
					console.log(response)
				})
			}
			else {
				console.log('Pause recording failed: Unable to find recording in progress');
			}
		};

		this.resumeRecording = function () {
			console.log('Resuming recording');
			if (self.callUuid && self.recordingLocation) {
				$http.post(`https://${self.credentials.server}:3000/api/v1/resume`, {
					apitoken: '0bfcc65c-e362-4017-8435-61a5e53cd4ae',
					uuid: self.callUuid,
					recordingLocation: self.recordingLocation
				}).then(function successCallback(response) {
					if (response && response.data && response.data.success) {
						self.recordingPaused = false;
					}
				}, function errorCallback(response) {
					console.log('resumeRecording() error')
					console.log(response)
				})
			}
			else {
				console.log('Resume recording failed: Unable to find recording in progress');
			}
		};

		this.init = function (credentials) {
			_credentials = credentials;
			if (!SIPml.isInitialized()) {
				var readyCallback = function (e) {
					createSipStack(credentials);
				};

				var errorCallback = function (e) {
					console.error('Failed to initialize the engine: ' + e.message);
				};

				SIPml.init(readyCallback, errorCallback);
			}
			else {
				createSipStack(credentials);
			}
		};

		this.deinit = function () {
			if (sipStack) {
				try {
					sipStack.stop();
				}
				finally { }
			}
		};
	});