/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
	.service('VertoPhone', function (eventEmitter) {
		// Setup event emitter methods (on, once, emit) for this service
		eventEmitter.inject(this);


		//** Private Properties **//
		
		var deviceCredentials,
			vertoHandle,
			vertoCallbacks,
			callSession,
			registerSession,
			selectedSpeaker,
			selectedMic,
			self = this;


		//** Private Methods **//
	    
	    function createVertoHandle () {
			// if (jQuery.verto.audioOutDevices && jQuery.verto.audioOutDevices.length) {
			// 	selectedSpeaker = jQuery.verto.audioOutDevices[0];
			// }
			// if (jQuery.verto.audioInDevices && jQuery.verto.audioInDevices.length) {
			// 	selectedMic = jQuery.verto.audioInDevices[0];
			// }

	    	var vertoCallbacks = {
				onWSLogin: onWSLogin,
				onWSClose: onWSClose,
				onDialogState: onDialogState,
				onEvent: onEvent
			};

	        vertoHandle = new jQuery.verto({
				login: deviceCredentials.extension + '@' + deviceCredentials.server,
				tag: "audio_remote",
				passwd: deviceCredentials.password,
				socketUrl: 'wss://' + deviceCredentials.server + ':8082',
				ringFile: '/sounds/ringbacktone.wav',
				iceServers: true
			},
			vertoCallbacks);

			vertoHandle.deviceParams({
				useSpeak: selectedSpeaker||'any',
				useAudio: selectedMic||'any',
				useMic: selectedMic||'any'
            });
	    }

	    function onWSLogin (verto, success) {
			console.log('onWSLogin', success);
			self.emit('registered', success);
		}

		function onWSClose (verto, success) {
			console.log('onWSClose', success);
			self.emit('stopped');
        	callSession = null;
        	self.onCall = false;
			self.emit('callstatuschange', { label: 'Idle' });
			self.emit('unregistered');
		}

		function onDialogState (d) {
			if (!callSession) {
				callSession = d;
			}

			console.debug('onDialogState:', d);
			switch (d.state.name) {
				case "ringing":
					// incomingCall(d.params.caller_id_number);
					break;
				case "trying":
					console.debug('Calling: ', d.cidString());
                    self.emit('callringing');
                    self.emit('callstatuschange', { label: 'Ringing...' });
					break;
				case "early":
					console.debug('Early media: ', d.cidString());
					break;
				case "active":
					console.debug('Talking to:', d.cidString());
                    self.emit('callconnected', d.params);
                    self.emit('callstatuschange', { label: 'In Call' });
					break;
				case "hangup":
					console.debug('Call ended with cause: ' + d.cause);
                	callSession = null;
                	self.onCall = false;
                	self.emit('callended', d);
    				self.emit('callstatuschange', { label: 'Idle' });
					break;
				case "destroy":
					console.debug('Destroying: ' + d.cause);
                	callSession = null;
                	self.onCall = false;
                	self.emit('callended', d);
    				self.emit('callstatuschange', { label: 'Idle' });
					break;
				default:
					console.warn('Got a not implemented state:', d);
					break;
			}
		}

		function onEvent (v, e) {
			console.debug('onEvent:', e);
		}


		//** Public Properties **//

		this.held = false;
		this.muted = false;
		this.onCall = false;
		this.callStatus = 'Idle';
		this.PHONE_TYPE = 'Verto';
		this.recordingPaused = false;


		//** Public Methods **//

		this.makeCall = function (number, callerId) {
			// Testing
			if (callerId === '1111111111') {
				// number = '*9196';
				number = '03301229999';
			}

			if (vertoHandle) {
				callSession = vertoHandle.newCall({
					destination_number: number,
					caller_id_name: callerId,
					caller_id_number: callerId,
					outgoingBandwidth: "default",
					incomingBandwidth: "default",
					useStereo: true,
					useVideo: false,
					useMic: selectedMic||'any',
					useAudio: selectedMic||'any',
					useSpeak: selectedSpeaker||'any',
					dedEnc: false,
					userVariables: {} // TODO: could be useful
				});

		        self.emit('callstarted', number);
		        self.onCall = true;

		        return true;
		    }
	        else return false;
		};

		this.hangup = function () {
	        if (callSession) {
	            callSession.hangup();
	            return true;
	        }
	        else return false;
		};

		this.sendDtmf = function (digit) {
			if (callSession) {
				callSession.dtmf(digit);
				return true;
			}
			else return false;
		};

		this.transfer = function (to) {
	        return false;
		};

		this.toggleHold = function () {
	        if (callSession) {
	            callSession.held = !callSession.held;
	            if (callSession.held) {
	            	callSession.hold();
	            }
	            else {
	            	callSession.unhold();
	            }

            	self.held = callSession.held;
            	return true;
	        }
	        else {
	        	self.held = false;
	        	return false;
	        }
		};

		this.toggleMute = function () {
	        if (callSession) {
	            callSession.muted = !callSession.muted;
            	callSession.mute(callSession.muted ? 'off':'on');
            	self.muted = callSession.muted;
            	return true;
	        }
	        else {
	        	self.muted = false;
	        	return false;
	        }
		};

		this.pauseRecording = function () {
			console.log('pausing recording');
			this.recordingPaused = true;
		};

		this.resumeRecording = function () {
			console.log('resuming recording');
			this.recordingPaused = false;
		};

		this.init = function (credentials) {
			deviceCredentials = credentials;

			$.FSRTC.checkPerms(function () {
				jQuery.verto.init({}, createVertoHandle);
			}, true, false);
		};

		this.deinit = function () {
			if (vertoHandle) {
				try {

				}
				finally { }
			}
		};
	});