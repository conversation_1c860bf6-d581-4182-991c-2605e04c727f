/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
	.service('SipJSWebRTCPhone', function (eventEmitter) {
		// Setup event emitter methods (on, once, emit) for this service
		eventEmitter.inject(this);


		//** Private Properties **//
		
		var ua = null,
			callSession = null,
			sipCredentials = null,
			self = this;


		//** Private Methods **//
	    
	    function createSipStack(credentials) {
	    	sipCredentials = credentials;
	        var config = {
				uri: credentials.extension+'@'+credentials.server,
				ws_servers: 'ws://' + credentials.server + ':5066',
				authorizationUser: credentials.extension,
				password: credentials.password,
				register: true
			};

			ua = new SIP.UA(config);
			ua.start();
	    }





		//** Public Properties **//

		this.onCall = false;
		this.callStatus = 'Idle';
		this.PHONE_TYPE = 'WebRTC';



		//** Public Methods **//

		this.makeCall = function (number, callerId) {
			// Testing
			// number = '0800500005';

			var options = {
				media: {
					constraints: {
						audio: true,
						video: false
					},
					render: {
						remote: document.getElementById('audio_remote')
					}
				}
			};

			callSession	= ua.invite('sip:' + number + '@' + sipCredentials.server, options);

			self.onCall = true;
		};

		this.sendDtmf = function (digit) {
			
		};

		this.hangup = function () {
	        if (callSession) {
	            callSession.bye();
	            self.onCall = false;
	            return true;
	        }
	        else return false;
		};

		this.transfer = function (to) {
	        // if (callSession) {
	        //     return callSession.transfer(to) === 0;
	        // }
	        // else return false;
		};

		this.toggleHold = function () {
	        // if (callSession) {
	        //     callSession.held = !callSession.held;
	        //     return (callSession.held ? callSession.hold() : callSession.resume()) === 0;
	        // }
	        // else return false;
		};

		this.toggleMute = function () {
	        // if (callSession) {
	        //     callSession.muted = !callSession.muted;
	        //     return callSession.mute('audio', callSession.muted) === 0;
	        // }
	        // else return false;
		};

		this.init = function (credentials) {
	        createSipStack(credentials);
		};

		this.deinit = function () {

		};
	});