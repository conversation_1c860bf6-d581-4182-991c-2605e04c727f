angular.module('dialerFrontendApp').service('DownloadUtils', function ($http, $window) {
    this.download = function (url, filename, type) {
        var token = $window.sessionStorage.token;
        var key = $window.sessionStorage.user;
        if (url.indexOf('?') > -1) {
            url += '&access_token=' + token + '&x_key=' + key
        } else {
            url += '?access_token=' + token + '&x_key=' + key
        }
        $http({
            url: url,
            method: "GET",
            responseType: "arraybuffer" // important
        }).then(response => {
            var data = response.data
            var blobData = [data]
            var blob = new Blob(blobData, { type: type || 'application/octet-stream' });
            if (typeof window.navigator.msSaveBlob !== 'undefined') {
                // IE workaround for "HTML7007: One or more blob URLs were 
                // revoked by closing the blob for which they were created. 
                // These URLs will no longer resolve as the data backing 
                // the URL has been freed."
                window.navigator.msSaveBlob(blob, filename);
            }
            else {
                var blobURL = window.URL.createObjectURL(blob);
                var tempLink = document.createElement('a');
                tempLink.style.display = 'none';
                tempLink.href = blobURL;
                tempLink.setAttribute('download', filename);

                // Safari thinks _blank anchor are pop ups. We only want to set _blank
                // target if the browser does not support the HTML5 download attribute.
                // This allows you to download files in desktop safari if pop up blocking 
                // is enabled.
                if (typeof tempLink.download === 'undefined') {
                    tempLink.setAttribute('target', '_blank');
                }

                document.body.appendChild(tempLink);
                tempLink.click();
                document.body.removeChild(tempLink);
                window.URL.revokeObjectURL(blobURL);
            }
        });
    }
})

