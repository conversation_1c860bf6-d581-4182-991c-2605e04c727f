/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
	.service('MockPhone', function (eventEmitter, $timeout) {
		// Setup event emitter methods (on, once, emit) for this service
		eventEmitter.inject(this);


		//** Private Properties **//
		
		var self = this,
			mockEventTimer = null;


		//** Private Methods **//

        function startRingTone() {
            try { ringtone.play(); }
            catch (e) {  }
        }

        function stopRingTone() {
            try { ringtone.pause(); }
            catch (e) {  }
        }

        function startRingbackTone() {
            try { ringbacktone.play(); }
            catch (e) {  }
        }

        function stopRingbackTone() {
            try { ringbacktone.pause(); }
            catch (e) {  }
        }


		//** Public Properties **//

		this.onCall = false;
		this.callStatus = 'Idle';
		this.PHONE_TYPE = 'MockPhone';
		this.muted = false;
		this.held = false;


		//** Public Methods **//

		this.makeCall = function (number, callerId) {
			self.onCall = true;

			mockEventTimer = $timeout(function () {
            	self.emit('callconnected', {});
				self.emit('callstatuschange', { label: 'In Call' });
				stopRingTone();
			}, 5000);

			self.emit('callstarted', {});
            self.emit('callstatuschange', { label: 'Ringing...' });
            startRingTone();
		};

		this.hangup = function () {
            self.onCall = false;
			stopRingTone();

            if (mockEventTimer) {
            	try {
	            	$timeout.cancel(mockEventTimer);
	            	mockEventTimer = null;
	            }
	            catch (e) {}
            }

            self.emit('callended', {});
		};

		this.pauseRecording = function () {}

		this.resumeRecording = function () {}

		this.transfer = function (to) {

		};

		this.toggleHold = function () {
			this.held = !this.held;
		};

		this.toggleMute = function () {
			this.muted = !this.muted;
		};

		this.init = function (credentials) {

		};

		this.deinit = function () {

		};
	});