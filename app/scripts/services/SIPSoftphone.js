/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
	.service('SIPSoftphone', function (eventEmitter, APP_SETTINGS, dtvSocket) {
		// Setup event emitter methods (on, once, emit) for this service
		eventEmitter.inject(this);



		//** Private Properties **//
		
		var self = this;



		//** Public Properties **//

		this.onCall = false;
		this.callStatus = 'Idle';
		this.PHONE_TYPE = 'Softphone';
		this.extension = '';



		//** Public Methods **//

		this.makeCall = function (number, callerId) {
			dtvSocket.emit('request:originate', { extension: extension, number: number, callerId: callerId }, function (success) {
				if (success) {
			        self.emit('callstarted', number);
			        self.onCall = true;
				}
				else {
					// TODO: Handle call failure error
				}
			});
		};

		this.hangup = function () {
			dtvSocket.emit('request:hangup', { extension: extension }, function (success) {
				if (success) {
			        self.onCall = false;
				}
				else {
					// TODO: Handle call failure error
				}
			});
		};

		this.sendDtmf = function (digit) {
			
		};

		this.transfer = function (to) {

		};

		this.toggleHold = function () {

		};

		this.toggleMute = function () {

		};

		this.init = function (credentials) {
			self.extension = credentials.extension;
			
			dtvSocket.connect();

			dtvSocket.on('event:callconnected', function (e) {
				if (e.direction === 'outbound') {
					self.emit('callconnected')
				}
			});

			dtvSocket.on('event:callhungup', function (e) {
				if (e.direction === 'outbound') {
			        self.emit('callended');
				}
			});

			dtvSocket.on('event:callringing', function (e) {
				if (e.direction === 'outbound') {
					self.emit('callringing');
				}
			});
		};

		this.deinit = function () {

		};
	});