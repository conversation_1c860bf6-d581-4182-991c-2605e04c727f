'use strict';

var underscore = angular.module('underscore', []);
  underscore.factory('_', function() {
    return window._; // assumes underscore has already been loaded on the page
});


var papaParse = angular.module('papaParse', []);
  papaParse.factory('Papa', function() {
    return window.Papa; // assumes PapaParse has already been loaded on the page
});


var momentModule = angular.module('momentModule', []);
  momentModule.factory('moment', function() {
    return window.moment; // assumes PapaParse has already been loaded on the page
});