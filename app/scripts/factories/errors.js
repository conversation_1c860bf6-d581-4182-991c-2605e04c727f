'use strict';

angular.module('dialerFrontendApp')
    .factory('$exceptionHandler', function($injector) {
        return function(exception, cause) {
            console.error(exception);
            var rScope = $injector.get('$rootScope');
            if (rScope) {
                if (!rScope.errors || rScope.errors.length) {
                    rScope.errors = [];
                }
                rScope.errors.push({
                    exception: {
                        message: exception.toString(),
                        stack: exception.stack
                    },
                    cause: cause
                });
                if (rScope.errors.length > 20) {
                    rScope.errors.shift();
                }
            }
        };
    });