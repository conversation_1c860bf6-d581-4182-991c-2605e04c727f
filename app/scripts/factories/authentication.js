'use strict';

angular.module('dialerFrontendApp')
  .factory('AuthenticationFactory', function ($window) {
    var auth = {
      isLogged: false,
      check: function () {
        if ($window.sessionStorage.token && $window.sessionStorage.user) {
          this.isLogged = true;
        } else {
          this.isLogged = false;
          delete this.user;
        }
      }
    };

    return auth;
  })
  .factory('UserAuthFactory', function ($window, $location, $http, AuthenticationFactory, APP_SETTINGS) {
    return {
      login: function (username, password) {
        return $http.post(APP_SETTINGS.BASE_API_URL + 'login', {
          username: username,
          password: password
        });
      },
      logout: function () {
        if (AuthenticationFactory.isLogged) {

          AuthenticationFactory.isLogged = false;
          delete AuthenticationFactory.user;
          delete AuthenticationFactory.userRole;

          delete $window.sessionStorage.token;
          delete $window.sessionStorage.user;
          delete $window.sessionStorage.userRole;

          $location.path('/login');
        }
      }
    };
  })
  .factory('TokenInterceptor', function ($q, $window) {
    return {
      request: function (config) {
        config.headers = config.headers || {};
        if ($window.sessionStorage.token) {
          config.headers['X-Access-Token'] = $window.sessionStorage.token;
          config.headers['X-Key'] = $window.sessionStorage.user;
          if (!config.headers.hasOwnProperty('Content-Type')) config.headers['Content-Type'] = 'application/json';
        }
        return config || $q.when(config);
      },

      response: function (response) {
        return response || $q.when(response);
      },

      responseError: function (rejection) {
        return rejection || $q.when(rejection);
      }
    };
  });