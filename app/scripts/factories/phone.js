'use strict';

angular.module('dialerFrontendApp')
	.factory('Phone', function ($rootScope, SIPSoftphone, SipJSWebRTCPhone, SipML5WebRTCPhone, MockPhone, VertoPhone) {
		if($rootScope.loggedInUser && $rootScope.loggedInUser.agent && $rootScope.loggedInUser.agent.device) {
			switch ($rootScope.loggedInUser.agent.device.type) {
				case 'MockPhone':
				 	return MockPhone;
				case 'WebRTC':
					return SipML5WebRTCPhone;
				case 'Verto':
					return VertoPhone;
				case 'Softphone':
					return SIPSoftphone;
			}
		} else {
			return null;
		}
	});

  
angular.module('dialerFrontendApp')
	.factory('dtvSocket', function ($rootScope, APP_SETTINGS) {
		if (APP_SETTINGS.DTV_API_URL) {
			var socket;
			return {
				connect: function () {
					if (!socket) {
	 					socket = window.io.connect(APP_SETTINGS.DTV_API_URL);
	 				}
				},
				on: function (eventName, callback) {
					if (socket) {
						socket.on(eventName, function () {  
							var args = arguments;
							$rootScope.safeApply(function () {
								callback.apply(socket, args);
							});
						});
					}
				},
				emit: function (eventName, data, callback) {
					if (socket) {
						socket.emit(eventName, data, function () {
							var args = arguments;
							$rootScope.safeApply(function () {
								if (callback) {
									callback.apply(socket, args);
								}
							});
						});
					}
				}
			};
		}
		else {
			return null;
		}
	});