/* jshint ignore:start */
'use strict';

angular.module('dialerFrontendApp')
    .directive('tagInput', function () {
        return {
            restrict: 'E',
            scope: {
                inputTags: '=taglist',
                autocomplete: '=autocomplete'
            },
            link: function ($scope, element, attrs) {
                $scope.defaultWidth = 250;
                $scope.tagText = '';
                $scope.placeholder = attrs.placeholder;
                $scope.found = false;
                if ($scope.autocomplete) {
                    $scope.autocompleteFocus = function (event, ui) {
                        $(element).find('input').val(ui.item.value);
                        return false;
                    };
                    $scope.autocompleteSelect = function (event, ui) {
                        $scope.$apply('tagText=\'' + ui.item.value + '\'');
                        $scope.$apply('addTag()');
                        return false;
                    };
                    $(element).find('input').autocomplete({
                        minLength: 0,
                        source: function (request, response) {
                            var item;
                            return response(function () {
                                var tagArray = $scope.tagArray();
                                var i, len, ref, results;
                                ref = $scope.autocomplete;
                                results = [];
                                for (i = 0, len = ref.length; i < len; i++) {
                                    item = ref[i];
                                    if (item.toLowerCase().indexOf(request.term.toLowerCase()) !== -1) {
                                        if (tagArray.indexOf(item) === -1) {
                                            results.push(item);
                                        }
                                    }
                                }
                                return results;
                            }());
                        },
                        focus: function (_this) {
                            return function (event, ui) {
                                return $scope.autocompleteFocus(event, ui);
                            };
                        }(this),
                        select: function (_this) {
                            return function (event, ui) {
                                return $scope.autocompleteSelect(event, ui);
                            };
                        }(this)
                    });
                }
                $scope.tagArray = function () {
                    if ($scope.inputTags === undefined) {
                        return [];
                    }
                    return $scope.inputTags;
                };
                $scope.addTag = function () {
                    var tagArray;
                    if ($scope.tagText.length === 0) {
                        return;
                    }
                    tagArray = $scope.tagArray();
                    if (tagArray.indexOf($scope.tagText) === -1) {
                        if ($scope.autocomplete) {
                            if(!tagArray) tagArray = [];
                            if ($scope.autocomplete.indexOf($scope.tagText) > -1) {                                
                                tagArray.push($scope.tagText);
                            }
                        } else {
                            tagArray.push($scope.tagText);
                        }
                    }
                    $scope.inputTags = tagArray;
                    return $scope.tagText = '';
                };
                $scope.deleteTag = function (key) {
                    var tagArray;
                    tagArray = $scope.tagArray();
                    if (tagArray.length > 0 && $scope.tagText.length === 0 && key === undefined) {
                        tagArray.pop();
                    } else {
                        if (key !== undefined) {
                            tagArray.splice(key, 1);
                        }
                    }
                    if ($scope.autocomplete && tagArray) {
                        return $scope.inputTags = tagArray.join(',');
                    } else if ($scope.autocomplete) {
                        return $scope.inputTags = [];
                    } else {
                        return '';
                    }
                };
                $scope.$watch('tagText', function (newVal, oldVal) {
                    var tempEl;
                    if (!(newVal === oldVal && newVal === undefined)) {
                        tempEl = $('<span>' + newVal + '</span>').appendTo('body');
                        $scope.inputWidth = tempEl.width() + 5;
                        if ($scope.inputWidth < $scope.defaultWidth) {
                            $scope.inputWidth = $scope.defaultWidth;
                        }
                        return tempEl.remove();
                    }
                });
                element.bind('keydown', function (e) {
                    var key;
                    key = e.which;
                    if (key === 9 || key === 13) {
                        e.preventDefault();
                    }
                    if (key === 8) {
                        return $scope.$apply('deleteTag()');
                    }
                });
                return element.bind('keyup', function (e) {
                    var key;
                    key = e.which;
                    if (key === 9 || key === 13 || key === 188) {
                        e.preventDefault();
                        return $scope.$apply('addTag()');
                    }
                });
            },
            template: '<div class=\'tag-input-ctn\'><div class=\'input-tag\' data-ng-repeat="tag in tagArray()">{{tag}}<div class=\'delete-tag\' data-ng-click=\'deleteTag($index)\'>&times;</div></div><input type=\'text\' data-ng-style=\'{width: inputWidth}\' data-ng-model=\'tagText\' placeholder=\'{{placeholder}}\'/></div>'
        };
    });