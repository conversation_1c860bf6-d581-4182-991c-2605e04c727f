'use strict';

angular.module('dialerFrontendApp')
	.directive('eavForm', function () {
		return {
			scope: true,
			compile: function (elem, attrs) {
				var renderTemplate = '<div ng-repeat="field in ' + attrs.eavFormFields + '"><eav-field eav-field-template="field.callresultfieldtype.templateUrl" /></div>';
				elem.append(renderTemplate);
			}
		};
	})
	.directive('eavField', function () {
		return {
			scope: true,
			compile: function (elem, attrs) {
				var renderTemplate = '<div ng-include="' + attrs.eavFieldTemplate + '"></div>';
				elem.append(renderTemplate);
			}
		};
	});