/*global $:false */
'use strict';

angular.module('dialerFrontendApp')
  .directive('sideNavigation', function sideNavigation() {
    return {
      restrict: 'A',
      link: function (scope, element) {
        // Call the metsiMenu plugin and plug it to sidebar navigation
        element.metisMenu();
      }
    };
  })
  .directive('minimalizaSidebar', function minimalizaSidebar($timeout) {
    return {
      restrict: 'A',
      template: '<a class="navbar-minimalize minimalize-styl-2 btn btn-primary " href="" ng-click="minimalize()"><i class="fa fa-bars"></i></a>',
      controller: function ($scope) {
        $scope.minimalize = function () {
          $('body').toggleClass('mini-navbar');
          if (!$('body').hasClass('mini-navbar') || $('body').hasClass('body-small')) {
            // Hide menu in order to smoothly turn on when maximize menu
            $('#side-menu').hide();
            $('.kaosLogo').show();
            // For smoothly turn on menu
            $timeout(function () {
              $('#side-menu').fadeIn(500);
            }, 100);
          } else {
            // Remove all inline style from jquery fadeIn function to reset menu state
            $('#side-menu').removeAttr('style');
            $('.kaosLogo').hide();
          }
        };
      }
    };
  })
  .directive('iboxTools', function iboxTools($timeout) {
    return {
      restrict: 'A',
      scope: true,
      templateUrl: 'views/common/ibox_tools.html',
      controller: function ($scope, $element) {
        // Function for collapse ibox
        $scope.showhide = function () {
          var ibox = $element.closest('div.ibox');
          var icon = $element.find('i:first');
          var content = ibox.find('div.ibox-content');
          content.slideToggle(200);
          // Toggle icon from up to down
          icon.toggleClass('fa-chevron-up').toggleClass('fa-chevron-down');
          ibox.toggleClass('').toggleClass('border-bottom');
          $timeout(function () {
            ibox.resize();
            ibox.find('[id^=map-]').resize();
          }, 50);
        };

        // Function for close ibox
        $scope.closebox = function () {
          var ibox = $element.closest('div.ibox');
          ibox.remove();
        };
      }
    };
  })
  .directive('pageTitle', function pageTitle($rootScope, $timeout) {
    return {
      link: function (scope, element) {
        var listener = function (event, toState) {
          var title = 'Dialer';
          if (toState.data && toState.data.pageTitle) {
            title = 'Dialer | ' + toState.data.pageTitle;
          }
          $timeout(function () {
            element.text(title);
          });
        };
        $rootScope.$on('$stateChangeStart', listener);
      }
    };
  })
  .directive('fitHeight', function fitHeight() {
    return {
      restrict: 'A',
      link: function (scope, element) {
        // element.css('height', $(window).height() + 'px');
        element.css('min-height', $(window).height() + 'px');

        $(window).bind('resize', function () {
          element.css('min-height', $(window).height() + 'px');
        });
      }
    };
  })
  .directive('fileModel', ['$parse', function ($parse) {
    return {
      restrict: 'A',
      link: function (scope, element, attrs) {
        var model = $parse(attrs.fileModel);
        var modelSetter = model.assign;

        element.bind('change', function () {
          scope.$apply(function () {
            modelSetter(scope, element[0].files[0]);
          });
        });
      }
    };
  }])
  .directive('onlyDigits', function () {
    return {
      require: 'ngModel',
      restrict: 'A',
      link: function (scope, element, attr, ctrl) {
        function inputValue(val) {
          if (val) {
            var digits = val.replace(/[^0-9]/g, '');

            if (digits !== val) {
              ctrl.$setViewValue(digits);
              ctrl.$render();
            }
            return parseInt(digits, 10);
          }
          return undefined;
        }
        ctrl.$parsers.push(inputValue);
      }
    };
  });