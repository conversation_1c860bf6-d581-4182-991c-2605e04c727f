'use strict';

angular.module('dialerFrontendApp')
    .directive('leadcallhistory', function () {
        return {
            restrict: 'A',
            scope: {
                leadid: '=leadid',
            },
            template: `<div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Call History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-show="(!history || history.length === 0) && !loading">
                    <p>No Call History</p>
                </div>
                <div class="feed-activity-list">
                    <div dir-paginate="result in history | orderBy: 'createdAt': true | itemsPerPage: 5" class="feed-element" pagination-id="callHistoryPaging">
                        <div class="media-body">
                            <strong>{{result.agent ? result.agent.name : 'DataImporter'}}</strong> wrapped up with
                            <strong>{{result.wrapup}}</strong> on campaign
                            <strong>{{result.campaign.name}} ({{result.campaignstage.name}})</strong>
                            <br>
                            <small class="text-muted">{{ formatDate(result.createdAt) }}</small>
                            <div class="well" style="white-space: pre-wrap" ng-show="result.notes">{{result.notes}}</div>
                            <br>
                            <button ng-if="result.callrecords && result.callrecords.length" class="btn btn-xs btn-success" ng-click="result.showcalls = !result.showcalls" type="button">{{ result.showcalls ? 'Hide Dials' : 'Show Dials'}}</button>
                            <div class="well well-sm" ng-if="result.showcalls" ng-repeat="call in result.callrecords">
                                <small>CLI: {{ call.callerId }}</small>
                                <br>
                                <small>Phone: {{ call.callTo }}</small>
                                <br>
                                <small>Start Time: {{ formatTime(call.startDateTime) }}</small>
                                <br>
                                <small>Duration: {{ humanizeTimespan(call.totalDurationSecs) }}</small>
                                <br>
                                <a ng-if="call.recordingLocation && call.recordingServer" ng-hide="call.audio" ng-click="playRecording(call)" href=""><i class="fa fa-play"></i></a>
                                <a ng-if="call.audio" ng-click="audio.paused ? audio.play() : audio.pause()" href=""><i class="fa" ng-class="{'fa-play': audio.paused, 'fa-pause': !audio.paused}"></i></a>
                                <a ng-if="call.audio" ng-click="call.audio.stop(); call.audio = null;" href=""><i class="fa fa-stop"></i></a>
                                <div ng-if="call.audio" class="input-group">
                                    <input class="form-control" type="range" min="0" max="1" step="0.01" ng-model="call.audio.progress">
                                </div>
                            </div>
                        </div>
                    </div>
                    <dir-pagination-controls pagination-id="callHistoryPaging"></dir-pagination-controls>
                </div>
            </div>
        </div>`,
            controller: function ($scope, Lead, moment) {
                $scope.history = [];
                $scope.loading = true;
                Lead.getCallHistory({
                    id: $scope.leadid
                }).$promise.then(function (results) {
                    $scope.history = results.map(function (result) {
                        var notes = result.notes;
                        switch (result.wrapup) {
                            case 'Standard Refusal':
                            case 'Exception Refusal':
                                result.notes = 'Refusal Reason: ' + result.refusalReason + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                                break;
                            case 'Pledge V2':
                            case 'Pledge V3':
                            case 'Pledge Credit Card':
                            case 'Pledge Invoice':
                                result.notes = 'Pledge Amount: $' + result.giftAmount + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                                if (result.installmentHistory) {
                                    // show the installments
                                    result.notes += '\nInstallments: ' + result.installmentHistory;
                                    if (result.installmentError) {
                                        result.notes += '\nInstallment Error: ' + result.installmentError;
                                    }
                                }
                                break;
                            case 'Sale':
                                result.notes = 'Sale Amount: $' + result.grandTotal + '\nDecision Maker: ' + result.decisionMaker + '\nNotes: ' + (result.notes || 'none');
                                break;
                            case 'Recurring Payment':
                            case 'Recurring Gift':
                                result.notes = '';
                                if (result.recurringpayments && result.recurringpayments.length > 0) {
                                    result.notes = 'Recurring Amount: $:' + result.recurringpayments[0].amount + '\nInstallment Every: ' + result.recurringpayments[0].unit + '\nFirst Payment: ' + moment(result.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                                }
                                result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');

                                break;
                            case 'Recurring Maintenance':
                                result.notes = '';
                                var recurring = $scope.history.slice().reverse().find(r => r.recurringpayments && r.recurringpayments.length);
                                if (recurring) {
                                    result.notes = 'Recurring Amount: $:' + recurring.recurringpayments[0].amount + '\nInstallment Every: ' + recurring.recurringpayments[0].unit + '\nFirst Payment: ' + moment(recurring.recurringpayments[0].firstPayment).format('MMM-DD-YYYY') + '\n';
                                }

                                result.notes += 'Decision Maker: ' + result.decisionMaker + '\nNotes: ' + (notes || 'none');
                                break;
                        }
                        return result;
                    });
                    $scope.loading = false;
                });
            }
        };
    });