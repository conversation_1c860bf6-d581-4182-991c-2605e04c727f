'use strict';

/**
 * @ngdoc overview
 * @name dialerFrontendApp
 * @description
 * # dialerFrontendApp
 *
 * Main module of the application.
 */
angular
  .module('dialerFrontendApp', [
    'ngAnimate',
    'ngResource',
    'ui.bootstrap',
    'ui.bootstrap.datetimepicker',
    'ui.bootstrap.tabs',
    'underscore',
    'ui.router',
    'angular-loading-bar',
    'angularFileUpload',
    'timer',
    'NgSwitchery',
    'papaParse',
    'colorpicker.module',
    'momentModule',
    'rt.eventemitter',
    'mgo-angular-wizard',
    'ui.select',
    'uuid',
    'chart.js',
    'angularUtils.directives.dirPagination',
    'ui.utils.masks',
    'ngAudio'
  ]);