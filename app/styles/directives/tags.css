.tag-input-ctn {
  border: 1px solid #e5e6e7;
  padding: 6px 12px;
  display: inline-block;
  background-color: #FFFFFF;
  width: 100%;
  border-radius: 1px;
  box-shadow: inset 0px 1px 1px rgba(0, 0, 0, 0.075);
}
.tag-input-ctn input {
  display: inline-block;
  /*float: left;*/
  height: 18px;
  padding: 0px;
  font-size: 13px;
  line-height: 18px;
  color: black;
  border: 0px;
  margin: 2px;
}
.tag-input-ctn input:focus {
  outline: 0;
  box-shadow: 0px;
  border-color: #1ab394;
}
.tag-input-ctn .input-tag {
  padding: 2px 4px;
  line-height: 12px;
  font-size: 11px;
  background-color: #e3eaf6;
  display: inline-block;
  float: left;
  border-radius: 2px;
  margin: 2px 5px 2px 0px;
  border: 1px solid #a9b6d2;
}
.tag-input-ctn .input-tag .delete-tag {
  display: inline-block;
  font-size: 12px;
  cursor: pointer;
  padding: 0px 2px;
}
.tag-input-ctn .input-tag .delete-tag:hover {
  background-color: #96b4d2;
}
