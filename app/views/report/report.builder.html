<div class="modal-header">
	<h3 class="modal-title">{{ update ? 'Edit - ' + oldName : 'New Report' }}</h3>
</div>
<div class="modal-body">
	<form role="form" class="form-horizontal">
		<wizard on-finish="wizardCompleted()" edit-mode="update">
			<wz-step title="Details">
				<div class="form-group">
					<label class="col-sm-2 control-label">Name</label>
					<div class="col-sm-8">
						<input type="text" class="form-control" placeholder="Name" ng-model="report.name" required />
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Folder</label>
					<div class="col-sm-8">
						<select class="form-control" ng-options="folder.name as folder.name for folder in folders | orderBy:'name'" ng-model="report.folder" required>
							<option value="">--Please Select--</option>
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Primary Module</label>
					<div class="col-sm-8">
						<select class="form-control" ng-options="module.name as module.name for module in modules | orderBy:'name'" ng-change="updatePrimary()" ng-model="report.primaryModule">
							<option value="">--Please Select--</option>
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Related Modules</label>
					<div class="col-sm-8">
						<ui-select multiple="true" ng-model="report.relatedModules" ng-disabled="disabled" theme="bootstrap" stlye="width: 300px;">
							<ui-select-match placeholder="Select related modules...">{{$item}}</ui-select-match>
							<ui-select-choices repeat="model in friendlyRelatedModules | filter:$select.search">
								{{ model }}
							</ui-select-choices>
						</ui-select>
					</div>
				</div>
				<div class="wz-next-container col-sm-8 col-sm-offset-3">
					<button class="btn btn-primary" wz-next="setAvailableColumns()" ng-disabled="!report.name || !report.primaryModule || !report.folder">Save &amp; Continue</button>
				</div>
				<div style="clear:both;"></div>
			</wz-step>
			<wz-step title="Columns">
				<div class="form-group">
					<label class="col-sm-2 control-label">Select Columns</label>
					<div class="col-sm-8">
						<ui-select multiple="true" ng-model="report.columns" ng-disabled="disabled" theme="bootstrap" style="width: 300px;">
							<ui-select-match placeholder="Select columns...">{{$item}}</ui-select-match>
							<ui-select-choices repeat="column in friendlyColumns | filter:$select.search">
								{{ column }}
							</ui-select-choices>
						</ui-select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Group By</label>
					<div class="col-sm-6">
						<select class="form-control" ng-options="column as column for column in friendlyColumns" ng-model="report.groupby[0].field">
							<option value="">--None--</option>
						</select>
					</div>
					<div class="col-sm-2">
						<select class="form-control" ng-model="report.groupby[0].dir">
							<option value="ASC">Ascending</option>
							<option value="DESC">Descending</option>
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Group By</label>
					<div class="col-sm-6">
						<select class="form-control" ng-options="column as column for column in friendlyColumns" ng-model="report.groupby[1].field">
							<option value="">--None--</option>
						</select>
					</div>
					<div class="col-sm-2">
						<select class="form-control" ng-model="report.groupby[1].dir">
							<option value="ASC">Ascending</option>
							<option value="DESC">Descending</option>
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-2 control-label">Calculations</label>
					<div class="col-sm-4" ng-hide="report.calculatedColumns.length > 0">No numeric fields found</div>
					<div class="col-sm-8" ng-show="report.calculatedColumns.length">
						<table class="table">
							<thead>
								<th>Columns</th>
								<th style="width:100px">Sum</th>
								<th style="width:100px">Average</th>
								<th style="width:100px">Lowest</th>
								<th style="width:100px">Highest</th>
							</thead>
							<tbody>
								<tr ng-repeat="column in report.calculatedColumns">
									<td>{{ column.modal }}.{{ column.field }}</td>
									<td><input type="checkbox" ng-model="column.sum" /></td>
									<td><input type="checkbox" ng-model="column.avg" /></td>
									<td><input type="checkbox" ng-model="column.low" /></td>
									<td><input type="checkbox" ng-model="column.high" /></td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="wz-next-container col-sm-8 col-sm-offset-3">
					<button class="btn btn-primary" wz-next="saveColumns()">Save &amp; Continue</button>
				</div>
				<div style="clear:both;"></div>
			</wz-step>
			<wz-step title="Filters">
				<div class="form-group">
					<label class="form-label col-sm-offset-2">All Conditions <small>(all conditions must be met)</small></label>
				</div>
				<div class="form-group" ng-repeat="filter in report.filters.all">
					<label class="control-label col-sm-2">Condition</label>
					<div class="col-sm-3">
						<select class="form-control" ng-options="column as column for column in friendlyColumns" ng-model="filter.field" ng-change="fieldChanged(filter)">
							<option value="">Select Field</option>
						</select>
					</div>
					<div class="col-sm-2">
						<select class="form-control" ng-options="operator.value as operator.label for operator in filter.operators" ng-model="filter.operator"></select>
					</div>
					<div class="col-sm-2">
						<input ng-if="filter.type !== 'date'" type="text" ng-model="filter.value" class="form-control" />
						<input ng-if="filter.type === 'date'" type="date" ng-model="filter.value" class="form-control" />
					</div>
					<button class="btn btn-success" ng-click="addFilter(true)">New</button>
					<button class="btn btn-danger" ng-click="removeFilter(true, $index)" ng-show="$index">Remove</button>
				</div>
				<br>
				<div class="form-group">
					<label class="form-label col-sm-offset-2">Any Condition <small>(at least one of the conditions must be met)</small></label>
				</div>
				<div class="form-group" ng-repeat="filter in report.filters.any">
					<label class="control-label col-sm-2">Condition</label>
					<div class="col-sm-3">
						<select class="form-control" ng-options="column as column for column in friendlyColumns" ng-model="filter.field" ng-change="fieldChanged(filter)">
							<option value="">Select Field</option>
						</select>
					</div>
					<div class="col-sm-2">
						<select class="form-control" ng-options="operator.value as operator.label for operator in filter.operators" ng-model="filter.operator">
							<option value="">equals</option>
						</select>
					</div>
					<div class="col-sm-2">
						<input type="text" ng-model="filter.value" class="form-control" />
					</div>
					<button class="btn btn-success" ng-click="addFilter(false)">New</button>
					<button class="btn btn-danger" ng-click="removeFilter(false, $index)" ng-show="$index">Remove</button>
				</div>
				<div class="wz-next-container col-sm-8 col-sm-offset-3">
					<button class="btn btn-primary" wz-next="saveReport()">Save &amp; Close</button>
				</div>
				<div style="clear:both;"></div>
			</wz-step>
		</wizard>
	</form>
</div>