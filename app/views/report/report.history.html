<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th></th>
					<th ng-if="!loggedInUser.isClient">Client Ready</th>
					<th>Name</th>
					<th>Run Date</th>
					<th ng-if="!loggedInUser.isClient">User</th>
					<th ng-if="!loggedInUser.isClient" ng-repeat="filter in filters">{{ filter }}</th>
					<th></th>					
				</thead>
				<tbody>
					<tr dir-paginate="history in report.reporthistories | orderBy:'createdAt':true | itemsPerPage:20">
						<td>
							<span ng-if="history.new" class="label label-primary">New</span>
						</td>
						<td ng-if="!loggedInUser.isClient">
                			<input type="checkbox" class="js-switch" ng-change="updateClientHistory(history)" ui-switch ng-model="history.isClientReady" />
						</td>
						<td>{{ history.name }}</td>
						<td>{{ formatDate(history.createdAt) }}</td>
						<td ng-if="!loggedInUser.isClient">{{ history.userId ? getUser(history.userId) : 'unknown' }}</td>
						<td ng-if="!loggedInUser.isClient" ng-repeat="filter in filters">{{ findFilter(history, filter) }}</td>
						<td>
							<button class="btn btn-sm btn-success" type="button" ng-if="!loggedInUser.isClient && history.isClientReady" ng-click="email(history)">Email</button>
							<a class="btn btn-sm btn-primary" href="" ng-click="getLink(history)">Download</a>
							<button class="btn btn-sm btn-danger" type="button" ng-if="!loggedInUser.isClient" ng-click="delete(history)">Delete</button>
						</td>
					</tr>
					<tr ng-hide="report.reporthistories.length > 0">
						<td colspan="99">No history</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>