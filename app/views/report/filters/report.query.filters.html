<form role="form" class="form-horizontal" ng-submit="run()">
	<div class="modal-header">
		<h3 class="modal-title">{{ report.name }} Filters</h3>
	</div>
	<div class="modal-body">
		<div class="form-group" ng-if="clientAvailable">
			<label class="col-sm-2 control-label">Client</label>
			<div class="col-sm-9">
				<select class="form-control" ng-model="filters.client" ng-change="filterCampaigns()" ng-options="client.id as client.name for client in clients">
					<option value="">--All--</option>
				</select>
			</div>
		</div>
		<div class="form-group row" ng-if="campaignAvailable">
			<div ng-if="!campaignAvailable.multiple">
				<label class="col-sm-2 control-label">Campaign</label>
				<div class="col-sm-9">
					<select class="form-control" ng-model="filters.campaign" ng-options="campaign.id as campaign.name for campaign in campaigns " ng-change="loadStages()">
						<option value="">--All--</option>
					</select>
				</div>
			</div>
			<label ng-if="!campaignAvailable.multiple" ng-show="report.definition.allowSisterCampaign" class="col-sm-11 control-label"><input type="checkbox" ng-model="addSisterCampaign"> Add a connected campaign</label>
			<div ng-if="!campaignAvailable.multiple" ng-show="addSisterCampaign">
				<label class="col-sm-2 control-label">Connected Campaign</label>
				<div class="col-sm-9">
					<select class="form-control" ng-model="filters.sisterCampaign" ng-options="campaign.id as campaign.name for campaign in campaigns ">
						<option value="">--All--</option>
					</select>
				</div>
			</div>
			<div ng-if="campaignAvailable.multiple">
				<label class="col-sm-2 control-label">Campaigns</label>
				<div class="col-sm-9">
					<ui-select multiple="true" ng-model="filters.campaigns" theme="bootstrap">
						<ui-select-match placeholder="Select campaigns...">{{$item.name}}</ui-select-match>
						<ui-select-choices repeat="campaign.id as campaign in campaigns | filter:$select.search">
							{{ campaign.name }}
						</ui-select-choices>
					</ui-select>
				</div>
			</div>
		</div>
		<div class="row form-group" ng-if="stagesAvailable">
			<label class="col-sm-2 control-label">Campaign Stage</label>
			<div class="col-sm-9">
				<select class="form-control" ng-model="filters.stage" ng-disabled="!campaignstages.length" ng-options="stage.id as stage.name for stage in campaignstages">
					<option value="">{{campaignstages.length ? '--All--' : '--Select Campaign To Select Stage--'}}</option>
				</select>
			</div>
		</div>
		<div class="form-group" ng-if="agentAvailable">
			<div class="row" ng-if="!agentAvailable.multiple">
				<label class="col-sm-2 control-label">Agent</label>
				<div class="col-sm-9">
					<select class="form-control" ng-model="filters.agent" ng-options="agent.id as agent.name for agent in agents">
						<option value="">--All--</option>
					</select>
				</div>
			</div>
			<div class="row" ng-if="agentAvailable.multiple">
				<label class="col-sm-2 control-label">Agents</label>
				<div class="col-sm-9">
					<ui-select multiple="true" ng-model="filters.agents" theme="bootstrap">
						<ui-select-match placeholder="Select campaigns...">{{$item.name}}</ui-select-match>
						<ui-select-choices repeat="agent.id as agent in agents | filter:$select.search">
							{{ agent.name }}
						</ui-select-choices>
					</ui-select>
				</div>
			</div>
		</div>
		<div class="form-group" ng-if="dateFilters">
			<label class="col-sm-2 control-label">Start Date</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="filters.startDate" is-open="dates.start" ng-required="true" close-text="Close" required />
					<span class="input-group-btn">
						<button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
					</span>
				</div>
			</div>
		</div>
		<div class="form-group" ng-if="dateFilters">
			<label class="col-sm-2 control-label">End Date</label>
			<div class="col-sm-9">
				<div class="input-group">
					<input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="filters.endDate" is-open="dates.end" ng-required="true" close-text="Close" required />
					<span class="input-group-btn">
						<button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
					</span>
				</div>
			</div>
		</div>
		<div class="clearfix"></div>
	</div>
	<div class="modal-footer">
		<div class="col-sm-11">
			<button class="btn btn-success" type="submit">Run</button>
			<button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
		</div>
	</div>
</form>