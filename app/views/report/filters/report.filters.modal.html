<div class="modal-header">
	<h3 class="modal-title">{{ report.name }} Filters</h3>
</div>
<div class="modal-body">
	<form role="form" class="form-horizontal">
		<div class="panel blank-panel ui-tab">
			<div class="panel-body">
				<tabset>
					<tab heading="Basic">
						<div class="form-group" ng-if="models.indexOf('CallResult') > -1">
							<label class="col-sm-2 control-label">Start date</label>
							<div class="col-sm-9">
								<div class="input-group">
									<input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="query.startDate" is-open="startDateOpened" ng-required="true" close-text="Close" required />
									<span class="input-group-btn">
										<button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
									</span>
								</div>
							</div>
						</div>
						<div class="form-group" ng-if="models.indexOf('CallResult') > -1">
							<label class="col-sm-2 control-label">End Date</label>
							<div class="col-sm-9">
								<div class="input-group">
									<input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="query.endDate" is-open="endDateOpened" ng-required="true" close-text="Close" required />
									<span class="input-group-btn">
										<button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
									</span>
								</div>
							</div>
						</div>
						<div class="form-group" ng-if="models.indexOf('Client') > -1">
							<label class="col-sm-2 control-label">Client</label>
							<div class="col-sm-9">
								<select class="form-control" ng-model="query.client" ng-options="client.id as client.name for client in clients">
									<option value="">--Any--</option>
								</select>
							</div>
						</div>
						<div class="form-group" ng-if="models.indexOf('Campaign') > -1">
							<label class="col-sm-2 control-label">Campaign</label>
							<div class="col-sm-9">
								<select class="form-control" ng-model="query.campaign" ng-options="campaign.id as campaign.name for campaign in campaigns | filter: { clientId: query.client }">
									<option value="">--Any--</option>
								</select>
							</div>
						</div>
						<div class="form-group" ng-if="models.indexOf('CampaignStage') > -1">
							<label class="col-sm-2 control-label">Stage</label>
							<div class="col-sm-9">
								<select class="form-control" ng-model="query.stage" ng-options="stage.id as stage.name for stage in campaignstages | filter: { clientId: query.client }">
									<option value="">--Any--</option>
								</select>
							</div>
						</div>
						<div class="form-group" ng-if="models.indexOf('Agent') > -1">
							<label class="col-sm-2 control-label">Agent</label>
							<div class="col-sm-9">
								<select class="form-control" ng-model="query.agent" ng-options="agent.id as agent.name for agent in agents">
									<option value="">--Any--</option>
								</select>
							</div>
						</div>
					</tab>
					<tab heading="Advanced">
						<h3>And</h3>
						<div class="form-group" ng-repeat="filter in filters.all">
							<label class="control-label col-sm-2">Condition</label>
							<div class="col-sm-3">
								<select class="form-control" ng-options="field for field in fields" ng-model="filter.field" ng-change="fieldChanged(filter)">
									<option value="">Select Field</option>
								</select>
							</div>
							<div class="col-sm-2">
								<select class="form-control" ng-options="operator.value as operator.label for operator in operators" ng-model="filter.operator"></select>
							</div>
							<div class="col-sm-3">
								<input type="text" ng-model="filter.value" class="form-control" />
							</div>
							<button class="btn btn-success" ng-click="addFilter(true)">New</button>
							<button class="btn btn-danger" ng-click="removeFilter(true, $index)" ng-show="$index">Remove</button>
						</div>
						<h3>Or</h3>
						<div class="form-group" ng-repeat="filter in filters.any">
							<label class="control-label col-sm-2">Condition</label>
							<div class="col-sm-3">
								<select class="form-control" ng-options="field for field in fields" ng-model="filter.field" ng-change="fieldChanged(filter)">
									<option value="">Select Field</option>
								</select>
							</div>
							<div class="col-sm-2">
								<select class="form-control" ng-options="operator.value as operator.label for operator in operators" ng-model="filter.operator"></select>
							</div>
							<div class="col-sm-3">
								<input type="text" ng-model="filter.value" class="form-control" />
							</div>
							<button class="btn btn-success" ng-click="addFilter(false)">New</button>
							<button class="btn btn-danger" ng-click="removeFilter(false, $index)" ng-show="$index">Remove</button>
						</div>
					</tab>
				</tabset>

			</div>

		</div>
		<div class="clearfix"></div>
	</form>
</div>
<div class="modal-footer">
	<div class="col-sm-11">
		<button class="btn btn-success" ng-click="run()">Run</button>
		<button class="btn btn-danger" ng-click="cancel()">Cancel</button>
	</div>
</div>