<div class="ibox">
	<div class="ibox-content">
		<label class="control-label col-sm-1" style="padding-top: 10px;">Start Date</label>
		<div class="col-sm-3">
			<p class="input-group">
	            <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="startDate" is-open="startDateOpened" close-text="Close" />
	            <span class="input-group-btn">
	            	<button type="button" class="btn btn-default" ng-click="startDateOpened = !startDateOpened; endDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
	            </span>
	        </p>
        </div>
		<label class="control-label col-sm-1" style="padding-top: 10px;">End Date</label>
		<div class="col-sm-3">
			<p class="input-group">
                <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="endDate" is-open="endDateOpened" close-text="Close" />
                <span class="input-group-btn">
                	<button type="button" class="btn btn-default" ng-click="endDateOpened = !endDateOpened; startDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
                </span>
            </p>
        </div>
		<button type="button" class="btn btn-success" ng-click="pageChanged(1)">Search</button>
	</div>

	<div class="ibox-content" style="overflow-x: scroll">
		<uib-tabset>
			<uib-tab heading="Payments">
				<table class="table">
					<thead>
						<th>
							Has Db Changes
						</th>
						<th ng-repeat="col in columns.payments" ng-if="!col.hide">
							<span class="clickable" ng-click='orderChanged("payments", col.name, !sortReverse.payments)'>
								{{ col.label }}
								<span ng-show='orderby.payments == col.name && !sortReverse.payments' class="fa fa-caret-down"></span>
								<span ng-show='orderby.payments == col.name && sortReverse.payments' class="fa fa-caret-up"></span>
							</span>
						</th>
					</thead>
					<tbody>
						<tr dir-paginate="row in payments.rows | itemsPerPage: limit" total-items="payments.count" current-page="pagination.payments.current" pagination-id="payments">
							<td>
								<a ng-if="row.hasDbChanges" class="btn btn-xs btn-primary" ng-click="showChanges(row)">Yes</a>
								{{ row.hasDbChanges ? '' : 'No' }}
							</td>
							<td ng-repeat="col in columns.payments" ng-if="!col.hide">
								{{ col.datetime ? formatDate(getObjectPropertyByString(row, col.name)) : col.bool ? getObjectPropertyByString(row, col.name) == true ? 'Yes' : 'No' : getObjectPropertyByString(row, col.name) }}
							</td>
						</tr>
						<tr ng-show="!payments.rows.length">
							<td colspan="99">No payments found</td>
						</tr>
					</tbody>
				</table>
				<dir-pagination-controls on-page-change="pageChanged(newPageNumber)" pagination-id="payments"></dir-pagination-controls>
			</uib-tab>
			<uib-tab heading="Exception Refusals">
				<table class="table">
					<thead>
						<th>
							Has Db Changes
						</th>
						<th ng-repeat="col in columns.refusals">
							<span class="clickable" ng-click='orderChanged("exceptions", col.name, !sortReverse.exceptions)'>
								{{ col.label }}
								<span ng-show='orderby.exceptions == col.name && !sortReverse.exceptions' class="fa fa-caret-down"></span>
								<span ng-show='orderby.exceptions == col.name && sortReverse.exceptions' class="fa fa-caret-up"></span>
							</span>
						</th>
					</thead>
					<tbody>
						<tr dir-paginate="row in exceptions.rows | itemsPerPage: limit" total-items="exceptions.count" current-page="pagination.exceptions.current" pagination-id="exceptions">
							<td>
								<a ng-if="row.hasDbChanges" class="btn btn-xs btn-primary" ng-click="showChanges(row)">Yes</a>
								{{ row.hasDbChanges ? '' : 'No' }}
							</td>
							<td ng-repeat="col in columns.refusals">{{ col.datetime ? formatDate(getObjectPropertyByString(row, col.name)) : col.bool ? getObjectPropertyByString(row, col.name) == true ? 'Yes' : 'No' : getObjectPropertyByString(row, col.name) }}</td>
						</tr>
						<tr ng-show="!exceptions.rows.length">
							<td colspan="99">No exception refusals found</td>
						</tr>
					</tbody>
				</table>
				<dir-pagination-controls on-page-change="pageChanged(newPageNumber)" pagination-id="exceptions"></dir-pagination-controls>
			</uib-tab>
			<uib-tab heading="Standard Refusals">
				<table class="table">
					<thead>
						<th>
							Has Db Changes
						</th>
						<th ng-repeat="col in columns.refusals">
							<span class="clickable" ng-click='orderChanged("refusals", col.name, !sortReverse.refusals)'>
								{{ col.label }}
								<span ng-show='orderby.refusals == col.name && !sortReverse.refusals' class="fa fa-caret-down"></span>
								<span ng-show='orderby.refusals == col.name && sortReverse.refusals' class="fa fa-caret-up"></span>
							</span>
						</th>
					</thead>
					<tbody>
						<tr dir-paginate="row in refusals.rows | itemsPerPage: limit" total-items="refusals.count" current-page="pagination.refusals.current" pagination-id="refusals">
							<td>
								<a ng-if="row.hasDbChanges" class="btn btn-xs btn-primary" ng-click="showChanges(row)">Yes</a>
								{{ row.hasDbChanges ? '' : 'No' }}
							</td>
							<td ng-repeat="col in columns.refusals">{{ col.datetime ? formatDate(getObjectPropertyByString(row, col.name)) : col.bool ? getObjectPropertyByString(row, col.name) == true ? 'Yes' : 'No' : getObjectPropertyByString(row, col.name) }}</td>
						</tr>
						<tr ng-show="!refusals.rows.length">
							<td colspan="99">No standard refusals found</td>
						</tr>
					</tbody>
				</table>
				<dir-pagination-controls on-page-change="pageChanged(newPageNumber)" pagination-id="refusals"></dir-pagination-controls>
			</uib-tab>
		</uib-tabset>
	</div>
</div>