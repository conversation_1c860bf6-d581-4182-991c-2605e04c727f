<div class="ibox">
	<div class="ibox-content" style="overflow-x: scroll">
		<form class="form-horizontal" style="margin-bottom: 35px;">
			<div class="form-group">
				<label class="col-sm-1 control-label">Week</label>
				<div class="col-sm-2">
					<select ng-model="selectedWeek" class="form-control" ng-options="week as week.name for week in availableWeeks" ng-change="getResults()"></select>
				</div>
			</div>
		</form>
		<div class="tab-pane" ng-repeat="user in users" ng-if="user.clients.length">
			<h3>{{user.name}}'s Team</h3>
			<div style="margin-left: 15px;">
				<table class="table" style="margin-left: 15px; margin-bottom: 40px;">
					<thead>
						<th></th>
						<th></th>
						<th>Sch Hrs</th>
						<th>Act Hrs</th>
						<th>Dials / Hr</th>
						<th>Goal</th>
						<th>Actual Amt</th>
						<th>Goal %</th>
						<th>Goal/hr</th>
						<th>Actual/hr</th>
						<th>Acq %</th>
						<th>Ren %</th>
						<th>Lap %</th>
						<th>Mon</th>
						<th>Tues</th>
						<th>Wed</th>
						<th>Thur</th>
						<th>Fri</th>
						<th>Sat</th>
						<th>Sun</th>
						<th>CC Rate #</th>
						<th>CC Rate $</th>
						<th>Renew Inc</th>
						<th>Add-on</th>
						<th>Total #</th>
						<th>New $</th>
						<th>New $ CC</th>
						<th>New $ CC Rate</th>
						<th>CC $</th>
						<th>Unpaid Invoice $</th>
						<th>Paid Invoice $</th>
					</thead>
					<tbody ng-repeat="client in user.clients">
						<tr>
							<td colspan="99">
								<h4>{{client.name}}</h4>
							</td>
						</tr>
						<tr ng-repeat="agent in client.agents">
							<td></td>
							<td>{{agent['Agent Name']}}</td>
							<td>{{agent['Sch Hrs'] || 0 | number:0 }}</td>
							<td>{{agent['Act Hours'] || 0| number:0 }}</td>
							<td>{{agent['Calls / Hr'] || 0 | number:0 }}</td>
							<td>{{agent['Goal'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Actual Amt'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Goal %'] || 0 | number:0}}%</td>
							<td>{{agent['Goal/hr'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Actual/hr'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Acq %'] || 0 | number:0 }}%</td>
							<td>{{agent['Ren %'] || 0 | number:0 }}%</td>
							<td>{{agent['Lap %'] || 0 | number:0 }}%</td>
							<td>{{agent['Monday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Tuesday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Wednesday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Thursday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Friday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Saturday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Sunday Total'] || 0 | currency:'$':0}}</td>
							<td>{{agent['CC Rate #'] || 0 | number:0}}%</td>
							<td>{{agent['CC Rate $'] || 0 | number:0}}%</td>
							<td>{{agent['Renew Inc'] || 0 | number:0}}%</td>
							<td>{{agent['Add-on'] || 0 | number:0}}%</td>
							<td>{{agent['Total #'] || 0 | number:0}}</td>
							<td>{{agent['New $'] || 0 | currency:'$':0}}</td>
							<td>{{agent['New $ CC'] || 0 | currency:'$':0}}</td>
							<td>{{agent['New $ CC Rate'] || 0 | number:0}}%</td>
							<td>{{agent['CC $'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Unpaid Invoice $'] || 0 | currency:'$':0}}</td>
							<td>{{agent['Paid Invoice $'] || 0 | currency:'$':0}}</td>
						</tr>
						<tr>
							<td></td>
							<td><strong>Client Totals</strong></td>
							<td>{{client.totals['Sch Hrs'] || 0 | number:0 }}</td>
							<td>{{client.totals['Act Hours'] || 0| number:0 }}</td>
							<td>{{client.totals['Calls / Hr'] || 0 | number:0 }}</td>
							<td>{{client.totals['Goal'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Actual Amt'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Goal %'] || 0 | number:0}}%</td>
							<td>{{client.totals['Goal/hr'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Actual/hr'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Acq %'] || 0 | number:0 }}%</td>
							<td>{{client.totals['Ren %'] || 0 | number:0 }}%</td>
							<td>{{client.totals['Lap %'] || 0 | number:0 }}%</td>
							<td>{{client.totals['Monday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Tuesday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Wednesday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Thursday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Friday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Saturday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Sunday Total'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['CC Rate #'] || 0 | number:0}}%</td>
							<td>{{client.totals['CC Rate %'] || 0 | number:0}}%</td>
							<td>{{client.totals['Renew Inc'] || 0 | number:0}}%</td>
							<td>{{client.totals['Add-on'] || 0 | number:0}}%</td>
							<td>{{client.totals['Total #'] || 0 | number:0}}</td>
							<td>{{client.totals['New $'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['New $ CC'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['New $ CC Rate'] || 0 | number:0}}%</td>
							<td>{{client.totals['CC $'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Unpaid Invoice $'] || 0 | currency:'$':0}}</td>
							<td>{{client.totals['Paid Invoice $'] || 0 | currency:'$':0}}</td>
						</tr>
					</tbody>
					<tr style="font-weight: bold;">
						<td><strong>Team Lead Totals</strong></td>
						<td></td>
						<td>{{user.totals['Sch Hrs'] || 0 | number:0 }}</td>
						<td>{{user.totals['Act Hours'] || 0| number:0 }}</td>
						<td>{{user.totals['Calls / Hr'] || 0 | number:0 }}</td>
						<td>{{user.totals['Goal'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Actual Amt'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Goal %'] || 0 | number:0}}%</td>
						<td>{{user.totals['Goal/hr'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Actual/hr'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Acq %'] || 0 | number:0 }}%</td>
						<td>{{user.totals['Ren %'] || 0 | number:0 }}%</td>
						<td>{{user.totals['Lap %'] || 0 | number:0 }}%</td>
						<td>{{user.totals['Monday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Tuesday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Wednesday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Thursday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Friday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Saturday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Sunday Total'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['CC Rate #'] || 0 | number:0}}%</td>
						<td>{{user.totals['CC Rate $'] || 0 | number:0}}%</td>
						<td>{{user.totals['Renew Inc'] || 0 | number:0}}%</td>
						<td>{{user.totals['Add-on'] || 0 | number:0}}%</td>
						<td>{{user.totals['Total #'] || 0 | number:0}}</td>
						<td>{{user.totals['New $'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['New $ CC'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['New $ CC Rate'] || 0 | number:0}}%</td>
						<td>{{user.totals['CC $'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Unpaid Invoice $'] || 0 | currency:'$':0}}</td>
						<td>{{user.totals['Paid Invoice $'] || 0 | currency:'$':0}}</td>
					</tr>
					<tr style="font-weight: bold; margin-top: 30px;" ng-if="grandTotals.user == user.name">
						<td><strong>Grand Total</strong></td>
						<td></td>
						<td>{{grandTotals['Sch Hrs'] || 0 | number:0 }}</td>
						<td>{{grandTotals['Act Hours'] || 0| number:0 }}</td>
						<td>{{grandTotals['Calls / Hr'] || 0 | number:0 }}</td>
						<td>{{grandTotals['Goal'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Actual Amt'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Goal %'] || 0 | number:0}}%</td>
						<td>{{grandTotals['Goal/hr'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Actual/hr'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Acq %'] || 0 | number:0 }}%</td>
						<td>{{grandTotals['Ren %'] || 0 | number:0 }}%</td>
						<td>{{grandTotals['Lap %'] || 0 | number:0 }}%</td>
						<td>{{grandTotals['Monday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Tuesday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Wednesday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Thursday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Friday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Saturday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Sunday Total'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['CC Rate #'] || 0 | number:0}}%</td>
						<td>{{grandTotals['CC Rate $'] || 0 | number:0}}%</td>
						<td>{{grandTotals['Renew Inc'] || 0 | number:0}}%</td>
						<td>{{grandTotals['Add-on'] || 0 | number:0}}%</td>
						<td>{{grandTotals['Total #'] || 0 | number:0}}</td>
						<td>{{grandTotals['New $'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['New $ CC'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['New $ CC Rate'] || 0 | number:0}}%</td>
						<td>{{grandTotals['CC $'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Unpaid Invoice $'] || 0 | currency:'$':0}}</td>
						<td>{{grandTotals['Paid Invoice $'] || 0 | currency:'$':0}}</td>
					</tr>
				</table>
			</div>
		</div>
	</div>
</div>