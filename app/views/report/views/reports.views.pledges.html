<div class="ibox">
	<div class="ibox-content">
		<label class="control-label col-sm-1" style="padding-top: 10px;">Start Date</label>
		<div class="col-sm-3">
			<p class="input-group">
	            <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="startDate" is-open="startDateOpened" close-text="Close" />
	            <span class="input-group-btn">
	            	<button type="button" class="btn btn-default" ng-click="startDateOpened = !startDateOpened; endDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
	            </span>
	        </p>
        </div>
		<label class="control-label col-sm-1" style="padding-top: 10px;">End Date</label>
		<div class="col-sm-3">
			<p class="input-group">
                <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="endDate" is-open="endDateOpened" close-text="Close" />
                <span class="input-group-btn">
                	<button type="button" class="btn btn-default" ng-click="endDateOpened = !endDateOpened; startDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
                </span>
            </p>
        </div>
		<button type="button" class="btn btn-success" ng-click="pageChanged(1)">Search</button>
	</div>
	
	<div class="ibox-content" style="overflow-x: scroll">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th>
						Has Db Changes
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "skill"; sortReverse = !sortReverse'>
							Reporting Group
							<span ng-show='orderby == "skill" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "skill" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "subSkill"; sortReverse = !sortReverse'>
							Lead Type
							<span ng-show='orderby == "subSkill" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "subSkill" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "createdAt"; sortReverse = !sortReverse'>
							Pledge Date
							<span ng-show='orderby == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "payDate"; sortReverse = !sortReverse'>
							Pay Date
							<span ng-show='orderby == "payDate" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "payDate" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "giftAmount"; sortReverse = !sortReverse'>
							Gift Amount
							<span ng-show='orderby == "giftAmount" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "giftAmount" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.id"; sortReverse = !sortReverse'>
							KAOS Id
							<span ng-show='orderby == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.clientRef"; sortReverse = !sortReverse'>
							Lead Client Ref
							<span ng-show='orderby == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.clientSourceCode"; sortReverse = !sortReverse'>
							Lead Source Code
							<span ng-show='orderby == "lead.clientSourceCode" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.clientSourceCode" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.division"; sortReverse = !sortReverse'>
							Lead Division
							<span ng-show='orderby == "lead.division" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.division" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.salutaion"; sortReverse = !sortReverse'>
							Lead Salutation
							<span ng-show='orderby == "lead.salutaion" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.salutaion" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.first_name"; sortReverse = !sortReverse'>
							Lead First Name
							<span ng-show='orderby == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.last_name"; sortReverse = !sortReverse'>
							Lead Last Name
							<span ng-show='orderby == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "decisionMaker"; sortReverse = !sortReverse'>
							Decision Maker
							<span ng-show='orderby == "decisionMaker" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "decisionMaker" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.lyAmount"; sortReverse = !sortReverse'>
							Lead LY Amount
							<span ng-show='orderby == "lead.lyAmount" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.lyAmount" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "numberOfInstallments"; sortReverse = !sortReverse'>
							No Of Installments
							<span ng-show='orderby == "numberOfInstallments" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "numberOfInstallments" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "installmentNotes"; sortReverse = !sortReverse'>
							Installment Notes
							<span ng-show='orderby == "installmentNotes" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "installmentNotes" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "useExistingCreditCard"; sortReverse = !sortReverse'>
							Use Existing Credit Card
							<span ng-show='orderby == "useExistingCreditCard" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "useExistingCreditCard" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "freeTickets"; sortReverse = !sortReverse'>
							Free Tickets
							<span ng-show='orderby == "freeTickets" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "freeTickets" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "declineBenefits"; sortReverse = !sortReverse'>
							Decline Benefits
							<span ng-show='orderby == "declineBenefits" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "declineBenefits" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "newMembershipCard"; sortReverse = !sortReverse'>
							New Membership Card
							<span ng-show='orderby == "newMembershipCard" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "newMembershipCard" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "giftMatchingCompany"; sortReverse = !sortReverse'>
							Gift Matching Company
							<span ng-show='orderby == "giftMatchingCompany" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "giftMatchingCompany" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "notes"; sortReverse = !sortReverse'>
							Notes
							<span ng-show='orderby == "notes" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "notes" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "requiresFollowUp"; sortReverse = !sortReverse'>
							Requires Follow Up
							<span ng-show='orderby == "requiresFollowUp" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "requiresFollowUp" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.address1"; sortReverse = !sortReverse'>
							Lead Address1
							<span ng-show='orderby == "lead.address1" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.address1" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.address2"; sortReverse = !sortReverse'>
							Lead Address2
							<span ng-show='orderby == "lead.address2" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.address2" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.city"; sortReverse = !sortReverse'>
							Lead City
							<span ng-show='orderby == "lead.city" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.city" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.state"; sortReverse = !sortReverse'>
							Lead State
							<span ng-show='orderby == "lead.state" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.state" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.zip"; sortReverse = !sortReverse'>
							Lead Zip
							<span ng-show='orderby == "lead.zip" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.zip" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_home"; sortReverse = !sortReverse'>
							Lead Phone1
							<span ng-show='orderby == "lead.phone_home" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_home" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_mobile"; sortReverse = !sortReverse'>
							Lead Phone2
							<span ng-show='orderby == "lead.phone_mobile" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_mobile" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_work"; sortReverse = !sortReverse'>
							Lead Phone3
							<span ng-show='orderby == "lead.phone_work" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_work" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_workmobile"; sortReverse = !sortReverse'>
							Lead Phone4
							<span ng-show='orderby == "lead.phone_workmobile" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_workmobile" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.email"; sortReverse = !sortReverse'>
							Lead Email
							<span ng-show='orderby == "lead.email" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.email" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "campaign.name"; sortReverse = !sortReverse'>
							Campaign Name
							<span ng-show='orderby == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "campaignstage.name"; sortReverse = !sortReverse'>
							Campaign Stage
							<span ng-show='orderby == "campaignstage.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "campaignstage.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "client.name"; sortReverse = !sortReverse'>
							Client Name
							<span ng-show='orderby == "client.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "client.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
				</thead>
				<tbody>
					<tr dir-paginate="pledge in pledges | itemsPerPage: limit" total-items="count" current-page="pagination.current">
						<td>
							<a ng-if="pledge.hasDbChanges" class="btn btn-xs btn-primary" ng-click="showChanges(pledge)">Yes</a>
							{{ pledge.hasDbChanges ? '' : 'No' }}
						</td>
						<td>{{ pledge.skill }}</td>
						<td>{{ pledge.subSkill }}</td>
						<td>{{ formatDate(pledge.createdAt) }}</td>
						<td>{{ formatDate(pledge.payDate, true) }}</td>
						<td>{{ pledge.giftAmount | currency:'$':0 }}</td>
						<td>{{ pledge.lead.id }}</td>
						<td>{{ pledge.lead.clientRef }}</td>
						<td>{{ pledge.lead.clientSourceCode }}</td>
						<td>{{ pledge.lead.division }}</td>
						<td>{{ pledge.lead.salutation }}</td>
						<td>{{ pledge.lead.first_name }}</td>
						<td>{{ pledge.lead.last_name }}</td>
						<td>{{ pledge.decisionMaker }}</td>
						<td>{{ pledge.lead.lyAmount | currency:'$':0 }}</td>
						<td>{{ pledge.numberOfInstallments || 0 | number:0 }}</td>
						<td>{{ pledge.installmentNotes }}</td>
						<td>{{ pledge.useExistingCreditCard ? 'Yes' : 'No' }}</td>
						<td>{{ pledge.freeTickets || 0 | number:0 }}</td>
						<td>{{ pledge.declineBenefits ? 'Yes' : 'No' }}</td>
						<td>{{ pledge.newMembershipCard ? 'Yes' : 'No' }}</td>
						<td>{{ pledge.giftMatchingCompany }}</td>
						<td>{{ pledge.notes }}</td>
						<td>{{ pledge.requiresFollowUp ? 'Yes' : 'No' }}</td>
						<td>{{ pledge.lead.address1 }}</td>
						<td>{{ pledge.lead.address2 }}</td>
						<td>{{ pledge.lead.city }}</td>
						<td>{{ pledge.lead.state }}</td>
						<td>{{ pledge.lead.zip }}</td>
						<td>{{ pledge.lead.phone_home }}</td>
						<td>{{ pledge.lead.phone_mobile }}</td>
						<td>{{ pledge.lead.phone_work }}</td>
						<td>{{ pledge.lead.phone_workmobile }}</td>
						<td>{{ pledge.lead.email }}</td>
						<td>{{ pledge.campaign.name }}</td>
						<td>{{ pledge.campaignstage.name }}</td>
						<td>{{ pledge.client.name }}</td>
					</tr>
					<tr ng-show="!pledges.length">
						<td colspan="99">No pledges found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
</div>

