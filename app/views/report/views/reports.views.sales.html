<div class="ibox">
	<div class="ibox-content">
		<label class="control-label col-sm-1" style="padding-top: 10px;">Start Date</label>
		<div class="col-sm-3">
			<p class="input-group">
	            <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="startDate" is-open="startDateOpened" close-text="Close" />
	            <span class="input-group-btn">
	            	<button type="button" class="btn btn-default" ng-click="startDateOpened = !startDateOpened; endDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
	            </span>
	        </p>
        </div>
		<label class="control-label col-sm-1" style="padding-top: 10px;">End Date</label>
		<div class="col-sm-3">
			<p class="input-group">
                <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="endDate" is-open="endDateOpened" close-text="Close" />
                <span class="input-group-btn">
                	<button type="button" class="btn btn-default" ng-click="endDateOpened = !endDateOpened; startDateOpened = false"><i class="glyphicon glyphicon-calendar"></i></button>
                </span>
            </p>
        </div>
		<button type="button" class="btn btn-success" ng-click="pageChanged(1)">Search</button>
	</div>
	
	<div class="ibox-content" style="overflow-x: scroll">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th>
						Has Db Changes
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.skill"; sortReverse = !sortReverse'>
							Reporting Group
							<span ng-show='orderby == "callresult.skill" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.skill" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.subSkill"; sortReverse = !sortReverse'>
							Lead Type
							<span ng-show='orderby == "callresult.subSkill" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.subSkill" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "createdAt"; sortReverse = !sortReverse'>
							Sale Date/Time
							<span ng-show='orderby == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.id"; sortReverse = !sortReverse'>
							KAOS Id
							<span ng-show='orderby == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.clientRef"; sortReverse = !sortReverse'>
							Lead Client Id
							<span ng-show='orderby == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.clientSourceCode"; sortReverse = !sortReverse'>
							Lead Source Code
							<span ng-show='orderby == "lead.clientSourceCode" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.clientSourceCode" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.division"; sortReverse = !sortReverse'>
							Lead Division
							<span ng-show='orderby == "lead.division" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.division" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.salutaion"; sortReverse = !sortReverse'>
							Lead Salutation
							<span ng-show='orderby == "lead.salutaion" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.salutaion" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.first_name"; sortReverse = !sortReverse'>
							Lead First Name
							<span ng-show='orderby == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.last_name"; sortReverse = !sortReverse'>
							Lead Last Name
							<span ng-show='orderby == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.decisionMaker"; sortReverse = !sortReverse'>
							Decision Maker
							<span ng-show='orderby == "callresult.decisionMaker" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.decisionMaker" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "series"; sortReverse = !sortReverse'>
							Series
							<span ng-show='orderby == "series" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "series" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "seats"; sortReverse = !sortReverse'>
							Seats
							<span ng-show='orderby == "seats" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "seats" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "dayOfWeek"; sortReverse = !sortReverse'>
							Day Of Week
							<span ng-show='orderby == "dayOfWeek" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "dayOfWeek" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "seatCount"; sortReverse = !sortReverse'>
							Seat Count
							<span ng-show='orderby == "seatCount" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "seatCount" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "costEach"; sortReverse = !sortReverse'>
							costEach
							<span ng-show='orderby == "costEach" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "costEach" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "feePerTicket"; sortReverse = !sortReverse'>
							Fee Per Ticket
							<span ng-show='orderby == "feePerTicket" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "feePerTicket" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "salesTax"; sortReverse = !sortReverse'>
							Sales Tax
							<span ng-show='orderby == "salesTax" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "salesTax" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "subtotal"; sortReverse = !sortReverse'>
							Subtotal
							<span ng-show='orderby == "subtotal" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "subtotal" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "client.orderFee"; sortReverse = !sortReverse'>
							Client Order Fee
							<span ng-show='orderby == "client.orderFee" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "client.orderFee" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.giftAmount"; sortReverse = !sortReverse'>
							Gift Amount
							<span ng-show='orderby == "callresult.giftAmount" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.giftAmount" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.grandTotal"; sortReverse = !sortReverse'>
							Grand Total
							<span ng-show='orderby == "callresult.grandTotal" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.grandTotal" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.useExistingCreditCard"; sortReverse = !sortReverse'>
							Use Existing Credit Card
							<span ng-show='orderby == "callresult.useExistingCreditCard" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.useExistingCreditCard" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.creditCardType"; sortReverse = !sortReverse'>
							Credit Card Type
							<span ng-show='orderby == "callresult.creditCardType" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.creditCardType" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">
						<span class="clickable" ng-click='orderby = "callresult.creditCardNumber"; sortReverse = !sortReverse'>
							Credit Card Number
							<span ng-show='orderby == "callresult.creditCardNumber" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.creditCardNumber" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">
						<span class="clickable" ng-click='orderby = "callresult.creditCardExpDate"; sortReverse = !sortReverse'>
							Credit Card Exp Date
							<span ng-show='orderby == "callresult.creditCardExpDate" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.creditCardExpDate" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">
						<span class="clickable" ng-click='orderby = "callresult.creditCardSecurityCode"; sortReverse = !sortReverse'>
							Credit Card Security Code
							<span ng-show='orderby == "callresult.creditCardSecurityCode" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.creditCardSecurityCode" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.notes"; sortReverse = !sortReverse'>
							Notes
							<span ng-show='orderby == "callresult.notes" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.notes" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.newMembershipCard"; sortReverse = !sortReverse'>
							New Membership Card
							<span ng-show='orderby == "callresult.newMembershipCard" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.newMembershipCard" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "callresult.freeTickets"; sortReverse = !sortReverse'>
							Free Tickets
							<span ng-show='orderby == "callresult.freeTickets" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "callresult.freeTickets" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.address1"; sortReverse = !sortReverse'>
							Lead Address1
							<span ng-show='orderby == "lead.address1" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.address1" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.address2"; sortReverse = !sortReverse'>
							Lead Address2
							<span ng-show='orderby == "lead.address2" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.address2" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.city"; sortReverse = !sortReverse'>
							Lead City
							<span ng-show='orderby == "lead.city" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.city" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.state"; sortReverse = !sortReverse'>
							Lead State
							<span ng-show='orderby == "lead.state" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.state" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.zip"; sortReverse = !sortReverse'>
							Lead Zip
							<span ng-show='orderby == "lead.zip" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.zip" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_home"; sortReverse = !sortReverse'>
							Lead Phone1
							<span ng-show='orderby == "lead.phone_home" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_home" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_mobile"; sortReverse = !sortReverse'>
							Lead Phone2
							<span ng-show='orderby == "lead.phone_mobile" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_mobile" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_work"; sortReverse = !sortReverse'>
							Lead Phone3
							<span ng-show='orderby == "lead.phone_work" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_work" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.phone_workmobile"; sortReverse = !sortReverse'>
							Lead Phone4
							<span ng-show='orderby == "lead.phone_workmobile" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.phone_workmobile" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "lead.email"; sortReverse = !sortReverse'>
							Lead Email
							<span ng-show='orderby == "lead.email" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "lead.email" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "campaign.name"; sortReverse = !sortReverse'>
							Campaign Name
							<span ng-show='orderby == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "campaignstage.name"; sortReverse = !sortReverse'>
							Campaign Stage
							<span ng-show='orderby == "campaignstage.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "campaignstage.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='orderby = "client.name"; sortReverse = !sortReverse'>
							Client Name
							<span ng-show='orderby == "client.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='orderby == "client.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
				</thead>
				<tbody>
					<tr dir-paginate="sale in sales | itemsPerPage: limit" total-items="count" current-page="pagination.current">
						<td>
							<a ng-if="sale.hasDbChanges" class="btn btn-xs btn-primary" ng-click="showChanges(sale)">Yes</a>
							{{ sale.hasDbChanges ? '' : 'No' }}
						</td>
						<td>{{ sale.callresult.skill }}</td>
						<td>{{ sale.callresult.subSkill }}</td>
						<td>{{ formatDate(sale.createdAt) }}</td>
						<td>{{ sale.lead.id }}</td>
						<td>{{ sale.lead.clientRef }}</td>
						<td>{{ sale.lead.clientSourceCode }}</td>
						<td>{{ sale.lead.division }}</td>
						<td>{{ sale.lead.salutation }}</td>
						<td>{{ sale.lead.first_name }}</td>
						<td>{{ sale.lead.last_name }}</td>
						<td>{{ sale.callresult.decisionMaker }}</td>
						<td>{{ sale.series }}</td>
						<td>{{ sale.seats }}</td>
						<td>{{ sale.dayOfWeek }}</td>
						<td>{{ sale.seatCount }}</td>
						<td>{{ sale.costEach }}</td>
						<td>{{ sale.feePerTicket }}</td>
						<td>{{ sale.salesTax }}</td>
						<td>{{ sale.subtotal }}</td>
						<td>{{ sale.client.orderFee }}</td>
						<td>{{ sale.callresult.giftAmount }}</td>
						<td>{{ sale.callresult.grandTotal }}</td>
						<td>{{ sale.callresult.useExistingCreditCard ? 'Yes' : 'No' }}</td>
						<td>{{ sale.callresult.creditCardType }}</td>
						<td ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">{{ sale.callresult.creditCardNumber }}</td>
						<td ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">{{ sale.callresult.creditCardExpDate }}</td>
						<td ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClient">{{ sale.callresult.creditCardSecurityCode }}</td>
						<td>{{ sale.callresult.notes }}</td>
						<td>{{ sale.callresult.newMembershipCard ? 'Yes' : 'No' }}</td>
						<td>{{ sale.callresult.freeTickets }}</td>
						<td>{{ sale.lead.address1 }}</td>
						<td>{{ sale.lead.address2 }}</td>
						<td>{{ sale.lead.city }}</td>
						<td>{{ sale.lead.state }}</td>
						<td>{{ sale.lead.zip }}</td>
						<td>{{ sale.lead.phone_home }}</td>
						<td>{{ sale.lead.phone_mobile }}</td>
						<td>{{ sale.lead.phone_work }}</td>
						<td>{{ sale.lead.phone_workmobile }}</td>
						<td>{{ sale.lead.email }}</td>
						<td>{{ sale.campaign.name }}</td>
						<td>{{ sale.campaignstage.name }}</td>
						<td>{{ sale.client.name }}</td>
					</tr>
					<tr ng-show="!sales.length">
						<td colspan="99">No sales found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
	<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
</div>

