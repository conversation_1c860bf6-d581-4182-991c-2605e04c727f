<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th>
						<span class="clickable" ng-click='setDirection("callback.startDateTime")'>
							Start Date Time
							<span ng-show='sortType == "callback.startDateTime" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "callback.startDateTime" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='setDirection("agent.name")'>
							Agent
							<span ng-show='sortType == "agent.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "agent.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='setDirection("lead.id")'>
							KAOS Id
							<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='setDirection("lead.last_name")'>
							Lead
							<span ng-show='sortType == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='setDirection("campaign.name")'>
							Campaign
							<span ng-show='sortType == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						Current Campaign Stage
					</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="callback in callbacks | itemsPerPage: 30" total-items="total" ng-class="{ calledSinceExpiry: callback.calledSinceExpiry }">
						<td>{{ formatDate(callback.startDateTime) }}</td>
						<td>{{ callback.agent.name }}</td>
						<td>{{ callback.lead.id }}</td>
						<td>{{ callback.lead.first_name }} {{callback.lead.last_name}}</td>
						<td>{{ callback.campaign.name }}</td>
						<td>{{ getCurrentStage(callback) }}</td>
						<td><button class="btn btn-xs btn-success" ng-click="reassign(callback)">Reassign</button></td>
						<td><button class="btn btn-xs btn-danger" ng-click="delete(callback)">Delete</button></td>
					</tr>
					<tr ng-if="!callbacks.length && !initialLoad">
						<td colspan="99" style="text-align: center">No Expired Callbacks</td>
					</tr>
					<tr ng-if="initialLoad">
						<td colspan="99" style="text-align: center">Loading</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
		</div>
	</div>
</div>