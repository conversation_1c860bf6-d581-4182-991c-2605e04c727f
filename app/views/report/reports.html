<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-if="!loggedInUser.isClient" ng-href="/#/report/create">New Report</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Type</th>
					<th>Last Run</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="report in reports | orderBy: 'name' | filter: { folder: reportFolder }">
						<td>{{ report.name }}</td>
						<td>{{ report.folder }}</td>
						<td>{{ report.reporthistories.length ? formatDate(report.reporthistories[report.reporthistories.length - 1].createdAt) : 'Not Run' }}</td>
						<td>
							<uib-dropdown class="btn-group">
								<button type="button" class="btn btn-primary" data-toggle="dropdown">
									Options <span class="caret"></span>
								</button>
								<ul class="dropdown-menu" role="menu">
									<li ng-if="!loggedInUser.isClient && loggedInUser.id">
										<a href="" ng-click="run(report)">Run</a>
									</li>
									<li ng-if="!loggedInUser.isClient">
										<a ng-href="#/report/{{ report.id }}/schedules">Schedules</a>
									</li>
									<li>
										<a ng-href="#/report/{{ report.id }}/history">History</a>
									</li>
									<li ng-if="!loggedInUser.isClient">
										<a ng-href="/#/report/{{report.id}}/edit">Edit</a>
									</li>
									<li ng-if="!report.isSystem && !loggedInUser.isClient">
										<a href="" ng-click="delete(report)">Delete</a>
									</li>
								</ul>
							</uib-dropdown>
						</td>
					</tr>
					<tr ng-hide="anyReports">
						<td colspan="99">No reports found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>