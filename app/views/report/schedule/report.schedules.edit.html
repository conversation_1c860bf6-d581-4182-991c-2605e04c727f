<form role="form" ng-submit="save()">
  <div class="modal-header">
    <h3 class="modal-title">Report Schedule</h3>
  </div>
  <div class="modal-body">
    <div class="tabs-container">
      <uib-tabset>
        <uib-tab heading="Schedule">
          <div class="panel-body">
            <!-- <div class="form-group">
          <label>Repeat Frequency</label>
          <input class="form-control" type="number" min="0" ng-model="schedule.frequency" />
        </div> -->
            <div class="form-group">
              <label>Repeat Every</label>
              <select class="form-control" ng-model="schedule.runEvery">
                <option value="">No Repetition</option>
                <option value="minute">Minute</option>
                <option value="hour">Hour</option>
                <option value="day">Day</option>
                <option value="week">Week</option>
                <option value="month">Month</option>
              </select>
            </div>
            <div class="form-group">
              <label>Start Date</label>
              <div class="input-group">
                <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="schedule.startDate" is-open="endDateOpened" ng-required="true" close-text="Close" required />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                </span>
              </div>
            </div>
            <div class="form-group">
              <label>Start Time</label>
              <div class="input-group">
                <div class="col-sm-2">
                  <uib-timepicker ng-model="schedule.startTime" show-meridian="false"></uib-timepicker>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label>Recipients</label>
              <ui-select multiple="true" ng-model="schedule.recipients" theme="bootstrap" stlye="width: 300px;">
                <ui-select-match placeholder="Select users to receive...">{{$item.name}}</ui-select-match>
                <ui-select-choices repeat="user in users | filter:$select.search">
                  {{ user.name }}
                </ui-select-choices>
              </ui-select>
            </div>
            <div class="form-group">
              <label><input type="checkbox" ng-model="schedule.isClientReady" /> Client Ready?</label>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="Filters">
          <div class="panel-body">
            <div class="form-group">
              <label>Date Range</label>
              <select class="form-control" ng-model="schedule.filters.dateRange">
                <option value="">All Time</option>
                <option value="yesterday">Yesterday</option>
                <option value="today">Today</option>
                <option value="last3Days">Last 3 Days</option>
                <option value="lastWeek">Last Week</option>
                <option value="last7Days">Last 7 Days</option>
                <option value="thisWeek">This Week</option>
                <option value="lastFortnight">Last Fortnight</option>
                <option value="lastMonth">Last Month</option>
                <option value="last4weeks">Last 4 Weeks</option>
                <option value="thisMonth">This Month</option>
              </select>
            </div>
            <div class="form-group" ng-if="availableFilters.indexOf('Client') > -1">
              <label class="control-label">Client</label>
              <select class="form-control" ng-change="filterCampaigns()" ng-model="schedule.filters.Client" ng-options="client.id as client.name for client in clients">
                <option value="">--Any--</option>
              </select>
            </div>
            <div class="form-group" ng-if="availableFilters.indexOf('Campaign') > -1">
              <label class="control-label">Campaign</label>
              <select class="form-control" ng-model="schedule.filters.Campaign" ng-options="campaign.id as campaign.name for campaign in campaigns" ng-change="loadStages()">
                <option value="">--Any--</option>
              </select>
            </div>
            <div class="form-group" ng-if="availableFilters.indexOf('CampaignStage') > -1">
              <label class="control-label">Stage</label>
              <select class="form-control" ng-model="schedule.filters.CampaignStage" ng-options="stage.id as stage.name for stage in campaignstages" ng-disabled="!campaignstages.length">
                <option value="">--Any--</option>
              </select>
            </div>
            <div class="form-group" ng-if="availableFilters.indexOf('Agent') > -1">
              <label class="control-label">Agent</label>
              <select class="form-control" ng-model="schedule.filters.Agent" ng-options="agent.id as agent.name for agent in agents">
                <option value="">--Any--</option>
              </select>
            </div>
          </div>
        </uib-tab>
      </uib-tabset>
    </div>

  </div>
  <div class="modal-footer">
    <button class="btn btn-success" type="submit">Save</button>
    <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>