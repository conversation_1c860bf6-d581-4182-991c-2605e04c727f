<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<button type="button" class="btn btn-success btn-add-new" ng-click="new()">New Schedule</button>
			<table class="table">
				<thead>
					<th>Frequency</th>
					<th ng-repeat="filter in filters">{{splitOnCamelcase(filter)}}</th>
					<th>Run Time</th>
					<th>Recipients</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="schedule in schedules">
						<td>{{ schedule.frequency }} {{ schedule.runEvery }}</td>
						<td ng-repeat="filter in filters">{{resolveFilter(filter, schedule.filters[filter])}}</td>
						<td>{{ formatTime(schedule.startTime, false) }}</td>
						<td>{{ schedule.recipients }}</td>
						<td>
							<button type="button" class="btn btn-primary" ng-click="edit(schedule)">Edit</button>
							<button type="button" class="btn btn-danger" ng-click="delete(schedule)">Delete</button>						
						</td>
					</tr>
					<tr ng-hide="schedules.length">
						<td colspan="99">No schedules found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>