<form ng-submit="setpassword()">
	<div class="modal-header">
		<h3 class="modal-title">{{client ? 'Change Report Password' : 'Change Password'}}</h3>
	</div>
	<div class="modal-body">
		<div class="form-group" ng-if="!ignoreCurrent">
			<label>Current Password</label>
			<input type="password" class="form-control" placeholder="Current Password" ng-model="user.currentPassword" required focus />
			<div ng-show="passwordError">
				<br>
				<span><mark>{{ passwordError }}</mark></span>
			</div>
		</div>
		<div class="form-group">
			<label>New Password</label>
			<input type="password" class="form-control" placeholder="New Password" ng-model="user.newPassword" required tooltip-placement="bottom" tooltip="{{ passwordFormatText }}" />
			<div ng-show="newPasswordError">
				<br>
				<span><mark>{{ newPasswordError }}</mark></span>
			</div>
		</div>
		<div class="form-group">
			<label>Repeat Password</label>
			<input type="password" class="form-control" placeholder="Repeat Password" ng-model="user.repeatPassword" required />
			<div ng-show="repeatPassword">
				<br>
				<span><mark>{{ repeatPassword }}</mark></span>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button class="btn btn-primary" type="submit">Save</button>
		<button ng-if="withCancel" class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
	</div>
</form>