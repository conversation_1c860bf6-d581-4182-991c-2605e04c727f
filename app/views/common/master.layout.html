<!-- Navigation -->
<style>
	body {
		background-color: #2f4050;
	}
</style>
<div ng-include="'views/common/navigation.html'"></div>

<!-- Page wraper -->
<!-- ng-class with current state name give you the ability to extended customization your view -->
<div id="page-wrapper" class="gray-bg {{$state.current.name}}" fit-height>

    <!-- Page wrapper -->
    <div ng-include="'views/common/topnavbar.html'"></div>

    <!-- Main view  -->
    <div ui-view class="ui-view-padding"></div>

    <!-- Footer -->
    <div ng-include="'views/common/footer.html'"></div>

</div>
<!-- End page wrapper-->