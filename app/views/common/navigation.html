<nav class="navbar-default navbar-static-side" role="navigation">
    <div class="sidebar-collapse">
        <!-- <img src="images/kaos-side.jpg" style="left: 0; bottom: 0; position: fixed; width: 220px;" alt=""> -->
        <ul side-navigation class="nav" id="side-menu">
            <li>
                <img src="images/kaos.jpg" class="kaosLogo" style="width: 220px;" alt="">
            </li>
            <li class="nav-header">
                <div class="dropdown profile-element">
                    <span>
                        <img alt="image" class="img-circle" src="{{ loggedInUser.avatar ? 'avatars/' + loggedInUser.avatar : 'images/icon-user-default.png' }}" style="width: 48px; height: 48px;">
                    </span>
                    <a class="dropdown-toggle" data-toggle="dropdown" href>
                        <span class="clear">
                            <span class="block m-t-xs">
                                <strong class="font-bold">{{loggedInUser.name}}</strong>
                            </span>
                            <span class="text-muted text-xs block">{{loggedInUser.isAdmin ? 'Administrator' : loggedInUser.isSupervisor ? 'Campaign Manager' : loggedInUser.isClient ? 'Client' : 'Agent' }} <b class="caret"></b></span>
                        </span>
                    </a>
                    <ul class="dropdown-menu animated fadeInRight m-t-xs">
                        <li ng-if="loggedInUser.name != 'superadmin'"><a ng-click="changePassword(true)" href>Change Password</a></li>
                        <li ng-if="loggedInUser.name != 'superadmin'" class="divider"></li>
                        <li><a ng-click="logout()" href>Logout</a></li>
                    </ul>
                </div>
                <div class="logo-element">
                    Menu
                </div>
            </li>
            <li ng-class="{active: $state.includes('agent')}" ng-show="loggedInUser.isAgent || loggedInUser.isClientAgent">
                <a ng-href><i class="fa fa-th-large"></i> <span class="nav-label">Agent</span> <span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('agent')}">
                    <li><a ui-sref-active="active" ui-sref="agent.dashboard">Dashboard</a></li>
                    <li><a ui-sref-active="active" ui-sref="agent.sales">Sales</a></li>
                    <li><a ui-sref-active="active" ui-sref="agent.pledges">Pledges</a></li>
                    <li><a ui-sref-active="active" ui-sref="agent.refusals">Refusals</a></li>
                    <li><a ui-sref-active="active" ui-sref="agent.callbacks">Callbacks</a></li>
                    <li><a ui-sref-active="active" ui-sref="agent.callrecords">Call Records</a></li>

                </ul>
            </li>
            <li ng-class="{active: $state.includes('supervisor')}" ng-show="loggedInUser.isSupervisor || loggedInUser.isClientAdmin">
                <a ng-href><i class="fa fa-th-large"></i> <span class="nav-label">Supervisor</span> <span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('supervisor')}">
                    <li><a ui-sref-active="active" ui-sref="supervisor.campaigns">Campaigns</a></li>
                    <li><a ui-sref-active="active" ui-sref="supervisor.agents">Agents</a></li>
                    <li><a ui-sref-active="active" ui-sref="supervisor.broadcast">Broadcast</a></li>
                    <li><a ui-sref-active="active" ui-sref="supervisor.leadaudit">Lead Audit</a></li>
                </ul>
            </li>
            <li ng-class="{active: $state.includes('admin')}" ng-show="loggedInUser.isAdmin || loggedInUser.isSuperManager">
                <a ng-href><i class="fa fa-wrench"></i> <span class="nav-label">Configure</span><span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('admin')}">
                    <li><a ui-sref-active="active" ui-sref="admin.clients">Clients</a></li>
                    <li><a ui-sref-active="active" ui-sref="admin.campaigns">Campaigns</a></li>
                    <li ng-if="loggedInUser.isAdmin"><a ui-sref-active="active" ui-sref="admin.agents">Agents</a></li>
                    <li ng-if="loggedInUser.isAdmin"><a ui-sref-active="active" ui-sref="admin.agentstates">Agent States</a></li>
                    <li><a ui-sref-active="active" ui-sref="admin.skills">Lead Types</a></li>
                    <li ng-if="loggedInUser.username == 'dualtone_admin'"><a ui-sref-active="active" ui-sref="admin.campaigntypes">Campaign Types</a></li>
                    <li><a ui-sref-active="active" ui-sref="admin.datetimerulesets">Date/Time Rules</a></li>
                    <li><a ui-sref-active="active" ui-sref="admin.refusalreasons">Refusal Reasons</a></li>
                    <li ng-if="loggedInUser.username == 'dualtone_admin'"><a ui-sref-active="active" ui-sref="admin.dispositions">Dispositions</a></li>
                    <li ng-if="loggedInUser.username == 'dualtone_admin'"><a ui-sref-active="active" ui-sref="admin.callresultfields">Call Result Fields</a></li>
                    <li><a ui-sref-active="active" ui-sref="admin.users">Users</a></li>
                </ul>
            </li>
            <li ng-class="{active: $state.includes('report')}">
                <a ng-href><i class="fa fa-bar-chart-o"></i> <span class="nav-label">Reports</span><span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('report')}">
                    <li><a ui-sref-active="active" ng-show="loggedInUser.isClient  || loggedInUser.isClientAdmin" ui-sref="report.clientlogin">Exports</a></li>
                    <li><a ui-sref-active="active" ng-hide="!loggedInUser.isClientAdmin && !loggedInUser.isClientAgent && loggedInUser.client && loggedInUser.client.reportPasswordRequired" ui-sref="report.views">Views</a></li>
                    <li><a ui-sref-active="active" ng-show="loggedInUser.isAdmin || loggedInUser.isSuperManager" ui-sref="report.all">All Reports</a></li>
                    <li><a ui-sref-active="active" ng-show="loggedInUser.isAdmin || loggedInUser.isSupervisor" ui-sref="report.campaign">Campaign Reports</a></li>
                    <li><a ui-sref-active="active" ng-show="loggedInUser.isAdmin || loggedInUser.isSupervisor" ui-sref="report.agent">Agent Reports</a></li>
                    <li><a ui-sref-active="active" ng-show="loggedInUser.isAdmin || loggedInUser.isSuperManager" ui-sref="report.custom">Custom Reports</a></li>
                </ul>
            </li>
            <li ng-class="{active: $state.includes('collections')}" ng-show="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin">
                <a ng-href><i class="fa fa-money"></i> <span class="nav-label">Collections</span><span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('collections')}">
                    <li><a ui-sref-active="active" ui-sref="collections.invoices">Invoices</a></li>
                    <li><a ui-sref-active="active" ui-sref="collections.payments">Payments</a></li>
					<li><a ui-sref-active="active" ui-sref="collections.recurring">Recurring</a></li>
                </ul>
            </li>
            <li ng-class="{active: $state.includes('system')}" ng-show="loggedInUser.name == 'superadmin'">
                <a ng-href><i class="fa fa-cog"></i> <span class="nav-label">System</span><span class="fa arrow"></span></a>
                <ul class="nav nav-second-level" ng-class="{in: $state.includes('system')}">
                    <li><a ui-sref-active="active" ui-sref="system.email">Email</a></li>
                    <li><a ui-sref-active="active" ui-sref="system.emailhistory">Email History</a></li>
                    <li><a ui-sref-active="active" ui-sref="system.devices">Devices</a></li>
                    <li><a ui-sref-active="active" ui-sref="system.sessions">Sessions</a></li>
                    <li><a ui-sref-active="active" ui-sref="system.errors">Errors</a></li>
                    <li><a ui-sref-active="active" ui-sref="system.merchants">Merchants</a></li>
                </ul>
            </li>
        </ul>
    </div>
</nav>