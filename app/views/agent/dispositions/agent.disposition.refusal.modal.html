<div class="modal-header">
  <span><strong>KAOS Id:</strong> {{lead.id}}</span>
  <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
  <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
</div>
<div class="modal-body" modaldraggable>
  <form novalidate class="form">
    <div class="form-group">
      <label>Decision Maker</label>
      <input type="text" class="form-control" placeholder="Decision maker" ng-model="formData.decisionMaker" />
    </div>
    <div class="form-data">
      <label>Refusal Reason</label>
      <select class="form-control" ng-model="refusalReason" ng-options="reason as (reason.name || reason) for reason in refusalReasons">
        <option value="">--Please Select a Reason--</option>
      </select>
    </div>
    <div class="form-data" ng-if="refusalReason == 'Other'">
      <label>Other reason</label>
      <input class="form-control" type="text" ng-model="refusalReasonCustom" />
    </div>
    <br>
    <div class="form-group">
      <button class="btn btn-primary" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Lead Details</button>
    </div>
    <div class="form-group">
      <label>Call Notes</label>
      <textarea class="form-control" rows="3" ng-model="notes"></textarea>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  <button class="btn btn-success" ng-click="ok()">Save & Wrap-up</button>
</div>