<div class="modal-header" style="padding: 15px 15px 25px 15px;">
    <span><strong>KAOS Id:</strong> {{lead.id}}</span>
    <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
    <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
    <div style="float:right">
        <button class="btn btn-primary" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Donor Details</button>
    </div>
</div>
<div class="modal-body" modaldraggable style="overflow-x: hidden;overflow-y: scroll;max-height:80vh;">
    <form novalidate class="form">
        <h3>Recurring Payments</h3>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Pay</label>
                    <input type="number" min="0" step="0.01" class="form-control" ng-model="recurring.amount" />
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>Every</label>
                    <select class="form-control" ng-model="recurring.unit">
                        <option value="day">Daily</option>
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                        <option value="quarter">Quarter</option>
                    </select>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group">
                    <label>First Payment On</label>
                    <div class="input-group">
                        <input type="text" class="form-control" min-date="initialOptions.minDate" max-date="initialOptions.maxDate" uib-datepicker-popup="{{format}}" ng-model="recurring.firstPayment" is-open="dates.initial" close-text="Close" required />
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" ng-click="openDate('initial', $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="hr-line-dashed" style="clear:both;"></div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>Decision Maker</label>
                    <input type="text" class="form-control" placeholder="Decision Maker" ng-model="formData.decisionMaker" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Gift Matching Company</label>
                    <input type="text" class="form-control" placeholder="Gift Matching Company" ng-model="formData.giftMatchingCompany" />
                </div>
            </div>

            <div class="col-md-4">
                <label> <input type="checkbox" ng-model="formData.declineBenefits"> Decline Benefits? </label>
            </div>
            <div class="col-md-4">
                <label style="margin-right:10px;">Free Tix Count</label>
                <label style="margin-right:10px;">
                    <input type="radio" ng-model="formData.freeTickets" value="0"> 0
                </label>
                <label style="margin-right:10px;">
                    <input type="radio" ng-model="formData.freeTickets" value="2"> 2
                </label>
                <label style="margin-right:10px;">
                    <input type="radio" ng-model="formData.freeTickets" value="4"> 4
                </label>
            </div>
            <div class="col-md-4" ng-if="client.membershipCard">
                <label> <input type="checkbox" ng-model="formData.newMembershipCard"> New Membership Card Required? </label>
            </div>
        </div>

        <div class="hr-line-dashed" style="clear:both;"></div>


        <!-- TSYS -->
        <div class="row">
            <div class="col-md-3">
                <div class="form-group tsys" id="tsep-cardNumDiv">
                    <label>Credit Card Number</label>
                    <input ng-if="validtoken" disabled ng-model="cardToken.cardNumber" />
                    <p ng-if="carderrors['tsep-cardNum']" class="text-danger">{{carderrors['tsep-cardNum']}}</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group tsys" id="tsep-datepickerDiv">
                    <label>Expiry Date</label>
                    <input ng-if="validtoken" disabled ng-model="cardToken.expirationDate" />
                    <p ng-if="carderrors['tsep-datepicker']" class="text-danger">{{carderrors['tsep-datepicker']}}</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group tsys" id="tsep-cvv2Div">
                    <label>CVV2</label>
                    <p ng-if="carderrors['tsep-cvv2']" class="text-danger">{{carderrors['tsep-cvv2']}}</p>
                </div>
            </div>
            <div class="col-md-3" style="padding-top: 23px;">
                <div ng-show="testingTsys" style="display: inline-block; vertical-align: middle;">
                    <div class="sk-spinner sk-spinner-wave">
                        <div class="sk-rect1"></div>
                        <div class="sk-rect2"></div>
                        <div class="sk-rect3"></div>
                        <div class="sk-rect4"></div>
                        <div class="sk-rect5"></div>
                    </div>
                </div>
                <button ng-show="!testingTsys" ng-if="!validtoken" ng-disabled="cardToken.tsepToken || saving" class="btn btn-success">Check Card</button>
                <button ng-show="!testingTsys" ng-if="validtoken" class="btn btn-grey" ng-click="clearCard()">Clear Card</button>
            </div>
            <div class="col-md-12">
                <span class="text-danger">{{carderrors.master}}</span> <span class="text-danger">{{tokenerror}}</span>
            </div>
        </div>

        <div class="hr-line-dashed" style="clear:both"></div>

        <div class="row">
            <div class="col-md-6">
                <h3 style="margin-right:20px; padding-top: 4px;">Verification</h3>
                <div>
                    <label><input ng-model="formData.requiresFollowUp" type="checkbox" value=""> Requires immediate follow-up</label>
                </div>
                <div>
                    <label><input ng-model="verify.giftAmount" type="checkbox" value=""> Did you verify the dollar amount of the gift with the donor?</label>
                </div>
                <div>
                    <label><input ng-model="verify.emailAddress" type="checkbox" value=""> Did you ask donor for an e-mail address?</label>
                </div>
                <div>
                    <label><input ng-model="verify.invoiceDelivery" type="checkbox" value=""> Did you tell the donor to expect their invoice via email (from {{ client.email }})?</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Call Notes</label>
                    <textarea class="form-control" rows="3" ng-model="notes"></textarea>
                </div>
            </div>
        </div>

    </form>
</div>
<div class="modal-footer" style="clear:both;">
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
    <button class="btn btn-success" ng-disabled="disableOkButton" ng-click="ok()">Save & Wrap-up</button>
</div>