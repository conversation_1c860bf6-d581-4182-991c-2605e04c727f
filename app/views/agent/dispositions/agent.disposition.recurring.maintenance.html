<div class="modal-header">
    <span><strong>KAOS Id:</strong> {{lead.id}}</span>
    <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
    <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
    <div style="margin-top: 10px"><strong>Status: <span ng-class="{'text-navy': !recurring.isCancelled, 'text-danger': recurring.isCancelled}">{{recurring.isCancelled ? 'Cancelled' : 'Active'}}</span> </strong></div>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>Pay</label>
                <input type="number" min="0" step="0.01" class="form-control" ng-model="recurring.amount" />
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Every</label>
                <select class="form-control" ng-model="recurring.unit">
					<option value="day">Daily</option>
                    <option value="week">Week</option>
                    <option value="month">Month</option>
                    <option value="quarter">Quarter</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Next Payment</label>
                <div class="input-group">
                    <input type="text" class="form-control" min-date="minDate" uib-datepicker-popup="{{format}}" ng-model="nextPayment.paymentDate" is-open="dates.initial" close-text="Close" required />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-click="openDate('initial', $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <span ng-show="recurring.isCancelled && nextPayment.paymentDate" style="margin-right: 10px">Saving will make this Recurring Plan Active</span>
    <button class="btn btn-success" ng-click="save()" ng-disabled="!nextPayment.paymentDate" type="button">Save</button>
    <button class="btn" ng-click="close()" type="button">Cancel</button>
</div>