<style type="text/css">
	.hr-line-dashed {
		margin: 10px 0;
	}

	.form-group {
		margin-bottom: 5px;
	}

	.modal-body {
		padding: 10px 30px 10px 30px;
	}
</style>
<div class="modal-header" style="padding: 15px 15px 25px 15px;">
	<span><strong>KAOS Id:</strong> {{lead.id}}</span>
	<span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
	<span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
	<div style="float:right">
		<button class="btn btn-primary btn-sm" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Lead Details</button>
	</div>
</div>
<div class="modal-body" modaldraggable>
	<form novalidate class="form">
		<div class="form-group">
			<div>
				<button class="btn btn-primary btn-sm" ng-click="addNewOrder()">Add additional product line</button>
			</div>
			<table class="table">
				<thead>
					<th style="width:140px;">Series</th>
					<th style="width:140px;">Seats</th>
					<th style="width:140px;">Days</th>
					<th style="width:140px;">Venue</th>
					<th>Count</th>
					<th>Cost Per Tix</th>
					<th>Fee Per Tix</th>
					<th>Subtotal</th>
					<th style="width:50px;"></th>
				</thead>
				<tbody>
					<tr ng-repeat="order in orderItems">
						<td>
							<select class="form-control" ng-model="order.series" ng-options="product.series as product.series for product in products | unique: 'series'" ng-change="updateCost(order)">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>
							<select class="form-control" ng-disabled="!order.series" ng-model="order.seats" ng-options="product.seats as product.seats for product in products | filter: { series: order.series } | unique: 'seats'" ng-change="checkvalues(order)">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>
							<select class="form-control" ng-disabled="!order.seats" ng-model="order.dayOfWeek" ng-options="product.days as product.days for product in order.possibleDays" ng-change="updateCost(order)">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>{{getVenue(order)}}</td>
						<td>
							<input ng-disabled="!order.dayOfWeek" type="number" class="form-control" ng-model="order.seatCount" min="0" ng-change="updateCost(order)" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.costEach" readonly="readonly" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.feePerTicket" readonly="readonly" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.subtotal" readonly="readonly" />
						</td>
						<td><button ng-hide="!$index" class="btn" ng-click="removeOrder(order)">X</button></td>
					</tr>
				</tbody>
			</table>
		</div>
	</form>
	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<label> <input type="checkbox" ng-model="addOnGiftEnabled" ng-change="updateGrandTotal()"> Add-on Gift? </label>
			<div class="row">
				<div class="col-md-4">
					<div class="form-group">
						<label>Gift Amount</label>
						<input type="number" ng-disabled="!addOnGiftEnabled" min="0" step="0.01" class="form-control" ng-model="giftAmount" ng-change="updateGrandTotal()" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Verify Gift Amount</label>
						<input type="number" ng-disabled="!addOnGiftEnabled" min="0" step="0.01" class="form-control" ng-model="verifyGiftAmount" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Gift Matching Company</label>
						<input type="text" class="form-control" ng-model="formData.giftMatchingCompany" placeholder="Matching Company" />
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-4">
					<label> <input type="checkbox" ng-model="declineBenfits"> Decline Benefits? </label>
				</div>
				<div class="col-md-4">
					<label style="margin-right:10px;">Free Tix Count</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="0"> 0
					</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="2"> 2
					</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="4"> 4
					</label>
				</div>
			</div>
		</div>
	</div>

	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<div class="row">
				<div class="col-md-4">
					<p>Sales Tax: {{ client.salesTax || 0 }}% - Order Fee: ${{ client.orderFee || 0 }}</p>
				</div>
				<div class="col-lg-4">
					<div ng-if="client.membershipCard">
						<label> <input type="checkbox" ng-model="formData.newMembershipCard"> New Membership Card Required? </label>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-4">
					<div class="form-group">
						<label>Subtotal (products only)</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="subtotal" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Add-on Gift</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="giftAmountTotal" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Grand Total</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="grandTotal" />
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<div class="row" ng-show="client.allowExistingCC && lead.existingCCDigits">
				<div class="col-md-4">
					<label><input ng-model="formData.useExistingCC" type="checkbox"> Use Existing Credit Card</label>
				</div>
				<div class="col-sm-4">
					<label>Existing credit card type</label>
					<p>{{ lead.existingCCType }}</p>
				</div>
				<div class="col-sm-4">
					<label>Existing credit card type</label>
					<p>{{ lead.existingCCDigits && lead.existingCCDigits.length > 4 ? lead.existingCCDigits.substring(4) : lead.existingCCDigits }}</p>
				</div>
			</div>

			<div class="row">
				<div class="col-md-3" ng-hide="formData.useExistingCC">
					<div class="form-group">
						<label>Credit Card Type</label>
						<select class="form-control" ng-model="formData.creditCardType">
							<option value="">--Not Selected--</option>
							<option ng-repeat="card in cards" value="{{ card }}">{{card}}</option>
						</select>
					</div>
				</div>
				<div class="col-md-3" ng-hide="formData.useExistingCC">
					<div class="form-group">
						<label>Last 4 CC Digits</label>
						<input class="form-control" type="text" ng-model="formData.creditCardNumber" autocomplete="new-password" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group">
						<label>Expiry Date</label>
						<input class="form-control" type="text" ng-model="formData.creditCardDate" placeholder="MMYY" autocomplete="new-password" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-4">
			<div class="form-group">
				<label># of installments</label>
				<select class="form-control" ng-model="formData.numberOfInstallments">
					<option value="">--NA--</option>
					<option value="2">2</option>
					<option value="3">3</option>
					<option value="4">4</option>
					<option value="5">5</option>
					<option value="6">6</option>
					<option value="7">7</option>
					<option value="8">8</option>
					<option value="9">9</option>
					<option value="10">10</option>
					<option value="11">11</option>
					<option value="12">12</option>
				</select>
			</div>
		</div>
		<div class="col-md-4">
			<div class="form-group">
				<label>Installment Notes</label>
				<input class="form-control" type="text" ng-model="formData.installmentNotes" />
			</div>
		</div>
		<div class="col-md-4" style="padding-top:30px;"">
            <span>Must be paid in full prior to first performance</span>
          </div>
        </div>
	<div class=" hr-line-dashed"></div>
		<div class="row">
			<div class="col-lg-12">
				<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<label>Decision Maker</label>
							<input class="form-control" ng-model="formData.decisionMaker" placeholder="Decision Maker" type="text" />
						</div>
						<div>
							<label> <input type="checkbox" ng-model="acceptAddon"> Did you ask the Patron for an add-on gift? </label>
						</div>
						<div>
							<label> <input type="checkbox" ng-model="acceptSaleAmount"> Did you confirm the grand total sale amount? </label>
						</div>
						<div>
							<label> <input type="checkbox" ng-model="acceptEmailAddress"> Did you ask the Patron for their e-mail address? </label>
						</div>
					</div>
					<div class="col-md-6">
						<div class="form-group">
							<label>Notes</label>
							<textarea class="form-control" ng-model="notes" style="height:84px"></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="modal-footer">
		<button class="btn btn-danger" ng-click="cancel()">Cancel</button>
		<button class="btn btn-success" ng-click="ok()">Save & Wrap-up</button>
	</div>