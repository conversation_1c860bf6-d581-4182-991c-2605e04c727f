<div class="modal-header" style="padding: 15px 15px 25px 15px;">
  <span><strong>KAOS Id:</strong> {{lead.id}}</span>
  <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
  <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
  <div style="float:right">
    <button type="button" class="btn btn-primary" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Donor Details</button>
  </div>
</div>
<div class="modal-body" modaldraggable>
  <form novalidate class="form">
    <div class="col-md-3">
      <div class="form-group">
        <label>Gift Amount</label>
        <input type="number" min="0" step="0.01" class="form-control" ng-model="formData.giftAmount" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>Verify Gift Amount</label>
        <input type="number" min="0" step="0.01" class="form-control" ng-model="formData.verifyGiftAmount" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>Decision Maker</label>
        <input type="text" class="form-control" placeholder="Decision Maker" ng-model="formData.decisionMaker" />
      </div>
    </div>
    <div class="col-md-3">
      <div class="form-group">
        <label>Gift Matching Company</label>
        <input type="text" class="form-control" placeholder="Gift Matching Company" ng-model="formData.giftMatchingCompany" />
      </div>
    </div>
    <div class="col-md-4">
      <label> <input type="checkbox" ng-model="formData.declineBenefits"> Decline Benefits? </label>
    </div>
    <div class="col-md-4">
      <label style="margin-right:10px;">Free Tix Count</label>
      <label style="margin-right:10px;">
        <input type="radio" ng-model="formData.freeTickets" value="0"> 0
      </label>
      <label style="margin-right:10px;">
        <input type="radio" ng-model="formData.freeTickets" value="2"> 2
      </label>
      <label style="margin-right:10px;">
        <input type="radio" ng-model="formData.freeTickets" value="4"> 4
      </label>
    </div>
    <div class="col-md-4" ng-if="client.membershipCard">
      <label> <input type="checkbox" ng-model="formData.newMembershipCard"> New Membership Card Required? </label>
    </div>
    <div class="hr-line-dashed" style="clear:both;"></div>
    <div class="col-lg-12">
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label>Payment Type</label>
            <select class="form-control" ng-model="formData.paymentType" ng-change="changePaymentType()">
              <option value="Credit Card">Credit Card</option>
              <option ng-if="admin || client.paperInvoices || client.emailInvoices" value="Invoice">Invoice</option>
            </select>
          </div>
        </div>
      </div>
      <div class="row" ng-show="formData.paymentType === 'Credit Card' && client.allowExistingCC && lead.existingCCDigits">
        <div class="col-md-4" style="margin-top:10px;">
          <label><input ng-model="formData.useExistingCC" type="checkbox"> Use Existing Credit Card</label>
        </div>
        <div class="col-sm-4">
          <label>Existing credit card type</label>
          <p>{{ lead.existingCCType }}</p>
        </div>
        <div class="col-sm-4">
          <label>Existing credit card number</label>
          <p>{{ lead.existingCCDigits.length > 4 ? lead.existingCCDigits.substring(4) : lead.existingCCDigits }}</p>
        </div>
      </div>
      <div class="row" ng-if="formData.paymentType === 'Credit Card'">
        <div class="col-md-3" ng-if="!formData.useExistingCC">
          <div class="form-group">
            <label>Credit Card Type</label>
            <select class="form-control" ng-model="formData.creditCardType">
              <option value="">--Not Selected--</option>
              <option ng-repeat="card in cards" value="{{ card }}">{{card}}</option>
            </select>
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Last 4 CC Digits</label>
            <input class="form-control" ng-disabled="formData.useExistingCC" type="text" ng-model="formData.creditCardNumber" autocomplete="new-password" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
          </div>
        </div>
        <div class="col-md-3">
          <div class="form-group">
            <label>Expiry Date</label>
            <input class="form-control" type="text" ng-model="formData.creditCardDate" placeholder="MMYY" autocomplete="new-password" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group" ng-if="formData.paymentType == 'Credit Card'">
            <label># of installments</label>
            <select class="form-control" ng-model="formData.numberOfInstallments">
              <option value="">--NA--</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
              <option value="6">6</option>
              <option value="7">7</option>
              <option value="8">8</option>
              <option value="9">9</option>
              <option value="10">10</option>
              <option value="11">11</option>
              <option value="12">12</option>
            </select>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group" ng-if="formData.paymentType == 'Credit Card'">
            <label>Installment Notes</label>
            <input class="form-control" type="text" ng-model="formData.installmentNotes" />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group" ng-if="!formData.numberOfInstallments || formData.paymentType == 'Invoice'">
            <label>Expected Due Date</label>
            <p class="input-group">
              <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="formData.payDate" is-open="popup1.opened" min-date="minDate" max-date="formData.paymentType == 'Invoice' ? invoiceMaxDate : maxDate" close-text="Close" disabled />
              <span class="input-group-btn">
                <button type="button" class="btn btn-default" ng-click="openDatePicker()"><i class="glyphicon glyphicon-calendar"></i></button>
              </span>
            </p>
          </div>
        </div>
        <div class="col-md-6" style="margin-top:20px;">
          <div class="checkbox">
            <label><input ng-model="formData.requiresFollowUp" type="checkbox" value=""> Requires immediate follow-up</label>
          </div>
        </div>
      </div>
    </div>
    <div class="hr-line-dashed" style="clear:both" ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && (admin || client.paperInvoices)"></div>
    <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && (admin || client.paperInvoices)">
      <div class="col-lg-12">
        <h3>Delivery Details</h3>
      </div>
      <div class="col-lg-12">
        <label><input ng-model="verify.sendByMail" type="checkbox" value=""> Send invoice via paper instead of e-mail</label>
      </div>
    </div>
    <div class="hr-line-dashed" style="clear:both"></div>
    <div>
      <div class="col-lg-12">
        <div class="row">
          <div class="col-md-6">
            <h3 style="margin-right:20px; padding-top: 4px;">Verification</h3>
            <div>
              <label><input ng-model="verify.giftAmount" type="checkbox" value=""> Did you verify the dollar amount of the gift with the donor?</label>
            </div>
            <div>
              <label><input ng-model="verify.emailAddress" type="checkbox" value=""> Did you ask donor for an e-mail address?</label>
            </div>
            <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && lead.email && !verify.sendByMail">
              <label><input ng-model="verify.invoiceDelivery" type="checkbox" value=""> Did you tell the donor to expect their invoice via email (from {{ client.returnEmail }})?</label>
            </div>
            <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType !== 'email'">
              <label><input ng-model="verify.invoiceDelivery" type="checkbox" value=""> Did you tell the donor to expect their invoice via US mail?</label>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label>Call Notes</label>
              <textarea class="form-control" rows="3" ng-model="notes"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
<div class="modal-footer" style="clear:both;">
  <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
  <button class="btn btn-success" type="button" ng-click="ok()">Save & Wrap-up</button>
</div>