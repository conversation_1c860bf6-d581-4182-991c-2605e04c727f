<style type="text/css">
	.hr-line-dashed {
		margin: 10px 0;
	}

	.form-group {
		margin-bottom: 5px;
	}

	.modal-body {
		padding: 10px 30px 10px 30px;
	}
</style>
<div class="modal-header" style="padding: 15px 15px 25px 15px;">
	<span><strong>KAOS Id:</strong> {{lead.id}}</span>
	<span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
	<span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
	<div style="float:right">
		<button class="btn btn-primary btn-sm" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Lead Details</button>
	</div>
</div>
<div class="modal-body" modaldraggable>
	<form novalidate class="form">
		<div class="form-group">
			<div>
				<button class="btn btn-primary btn-sm" ng-click="addNewOrder()">Add additional product line</button>
			</div>
			<table class="table">
				<thead>
					<th style="width:140px;">Series</th>
					<th style="width:140px;">Seats</th>
					<th style="width:140px;">Days</th>
					<th>Count</th>
					<th>Cost Per Tix</th>
					<th>Fee Per Tix</th>
					<th>Subtotal</th>
					<th style="width:50px;"></th>
				</thead>
				<tbody>
					<tr ng-repeat="order in orderItems">
						<td>
							<select class="form-control" ng-model="order.series" ng-options="product.series as product.series for product in products | unique: 'series'">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>
							<select class="form-control" ng-disabled="!order.series" ng-model="order.seats" ng-options="product.seats as product.seats for product in products | filter : { series: order.series } : true | unique: 'seats'" ng-change="checkvalues(order)">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>
							<select class="form-control" ng-disabled="!order.seats" ng-model="order.dayOfWeek" ng-options="product.days as product.days for product in order.possibleDays" ng-change="updateCost(order)">
								<option value="">-- Select --</option>
							</select>
						</td>
						<td>
							<input ng-disabled="!order.dayOfWeek" type="number" class="form-control" ng-model="order.seatCount" min="0" ng-change="updateTotal(order)" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.costEach" readonly="readonly" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.feePerTicket" readonly="readonly" />
						</td>
						<td>
							<input type="text" class="form-control" ng-model="order.subtotal" readonly="readonly" />
						</td>
						<td><button ng-hide="!$index" class="btn" ng-click="removeOrder(order)">X</button></td>
					</tr>
				</tbody>
			</table>
		</div>
	</form>
	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<label> <input type="checkbox" ng-model="addOnGiftEnabled" ng-change="updateGrandTotal()"> Add-on Gift? </label>
			<div class="row">
				<div class="col-md-4">
					<div class="form-group">
						<label>Gift Amount</label>
						<input type="number" ng-disabled="!addOnGiftEnabled" min="0" step="0.01" class="form-control" ng-model="giftAmount" ng-change="updateGrandTotal()" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Verify Gift Amount</label>
						<input type="number" ng-disabled="!addOnGiftEnabled" min="0" step="0.01" class="form-control" ng-model="verifyGiftAmount" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Gift Matching Company</label>
						<input type="text" class="form-control" ng-model="formData.giftMatchingCompany" placeholder="Matching Company" />
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-4">
					<label> <input type="checkbox" ng-model="declineBenfits"> Decline Benefits? </label>
				</div>
				<div class="col-md-4">
					<label style="margin-right:10px;">Free Tix Count</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="0"> 0
					</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="2"> 2
					</label>
					<label style="margin-right:10px;">
						<input type="radio" ng-model="freeTix" value="4"> 4
					</label>
				</div>
			</div>
		</div>
	</div>

	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<div class="row">
				<div class="col-md-4">
					<p>Sales Tax: {{ client.salesTax || 0 }}% - Order Fee: ${{ client.orderFee || 0 }}</p>
				</div>
				<div class="col-lg-4">
					<div ng-if="client.membershipCard">
						<label> <input type="checkbox" ng-model="formData.newMembershipCard"> New Membership Card Required? </label>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-4">
					<div class="form-group">
						<label>Subtotal (products only)</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="subtotal" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Add-on Gift</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="giftAmountTotal" />
					</div>
				</div>
				<div class="col-md-4">
					<div class="form-group">
						<label>Grand Total</label>
						<input class="form-control" readonly="readonly" type="text" ng-model="grandTotal" />
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="hr-line-dashed"></div>

	<div class="row" ng-if="existingCard.id">
		<div class="col-sm-12">
			<p>Existing Card: {{parseCardType(existingCard.cardType)}} Ending: ####-####-####-{{existingCard.maskedCardNumber}} Expiration: {{existingCard.expirationDate}}</p>
		</div>
	</div>
	<div class="row">
		<div class="col-md-3">
			<div class="form-group tsys" id="tsep-cardNumDiv">
				<label>Credit Card Number</label>
				<input ng-if="validtoken" disabled ng-model="cardToken.cardNumber" />
				<p ng-if="carderrors['tsep-cardNum']" class="text-danger">{{carderrors['tsep-cardNum']}}</p>
				<!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardNumber" autocomplete="off"
					 ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
			</div>
		</div>
		<div class="col-md-3">
			<div class="form-group tsys" id="tsep-datepickerDiv">
				<label>Expiry Date</label>
				<input ng-if="validtoken" disabled ng-model="cardToken.expirationDate" />
				<p ng-if="carderrors['tsep-datepicker']" class="text-danger">{{carderrors['tsep-datepicker']}}</p>
				<!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.expirationDate" placeholder="MMYY"
					 autocomplete="off" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
			</div>
		</div>
		<div class="col-md-3">
			<div class="form-group tsys" id="tsep-cvv2Div">
				<label>CVV2</label>
				<input ng-if="validtoken" disabled />
				<p ng-if="carderrors['tsep-cvv2']" class="text-danger">{{carderrors['tsep-cvv2']}}</p>
				<!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardHolderName" autocomplete="off"
					 ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
			</div>
		</div>
		<div class="col-md-3" style="padding-top: 23px;">
			<button type="button" ng-if="!validtoken" ng-disabled="cardToken.tsepToken" class="btn btn-success">Check Card</button>
			<!-- <button type="button" ng-if="!validtoken" class="btn btn-primary" ng-click="saveCard()" ng-disabled="!cardToken || !cardToken.tsepToken">Save Card</button> -->
			<button type="button" ng-if="validtoken" class="btn btn-grey" ng-click="clearCard()">Clear Card</button>
		</div>
		<div class="col-md-12">
			<span class="text-danger">{{carderrors.master}}</span> <span class="text-danger">{{tokenerror}}</span>
		</div>
	</div>
	<div class="row">
		<div class="col-md-4">
			<label> <input type="checkbox" ng-model="formData.splitPayments" ng-change="updatePaymentCount()">
				Split Payments?</label>
		</div>
	</div>
	<div class="hr-line-dashed" style="clear:both" ng-if="formData.splitPayments"></div>
	<div class="row" ng-if="formData.splitPayments">
		<div class="col-md-3">
			<div class="form-group">
				<label>Initial Payment Date</label>
				<div class="input-group">
					<input type="text" class="form-control" min-date="formData.initialPaymentDate" max-date="maxDate" uib-datepicker-popup="{{format}}" ng-model="formData.initialPaymentDate" is-open="dates.initial" ng-change="updatePaymentCount()" close-text="Close" required />
					<span class="input-group-btn">
						<button type="button" class="btn btn-default" ng-click="openDate('initial', $event)"><i class="glyphicon glyphicon-calendar"></i></button>
					</span>
				</div>
			</div>
		</div>
		<div class="col-md-3">
			<div class="form-group">
				<label>No of Monthly Payments</label>
				<input type="number" class="form-control" ng-model="payments.count" ng-change="updatePaymentCount()" min="1" max="3" />
			</div>
		</div>
	</div>
	<div ng-if="formData.splitPayments && payments.count">
		<div class="row">
			<div class="col-md-1"></div>
			<div class="col-md-5">
				<strong>Amount</strong>
			</div>
			<div class="col-md-6">
				<strong>Payment Date</strong>
			</div>
		</div>
		<div class="row" ng-repeat="payment in payments.values track by $index">
			<div class="col-md-1"><strong>{{$index + 1}}</strong></div>
			<div class="col-md-5">
				<div class="form-group">
					<input type="number" step="0.01" class="form-control" ng-model="payment.amount" min="0" ng-change="updatePayment()" />
				</div>
			</div>
			<div ng-class="{'col-md-6': !paymentsBroken, 'col-md-5': paymentsBroken}" class="col-md-6">
				<div class="form-group">
					<div class="input-group">
						<input type="text" class="form-control" uib-datepicker-popup="{{format}}" min-date="formData.initialPaymentDate" max-date="maxDate" ng-model="payment.date" ng-disabled="!$index" is-open="dates[$index + '']" close-text="Close" required />
						<span class="input-group-btn">
							<button type="button" class="btn btn-default" ng-disabled="!$index" ng-click="openDate($index, $event)"><i class="glyphicon glyphicon-calendar"></i></button>
						</span>
					</div>
				</div>
			</div>
			<div ng-if="paymentsBroken">
				<button class="btn" ng-click="fixPayments($index)">Fix</button>
			</div>
		</div>
		<div class="row m-b-sm">
			<div class="col-md-2"><strong>Total</strong></div>
			<div class="col-md-4">
				{{payments.total | currency:'$'}}
			</div>
			<div class="col-md-2"><strong>Difference</strong></div>
			<div class="col-md-4">
				<span ng-class="{'text-danger': paymentsBroken, 'text-heavy': paymentsBroken}">{{payments.difference}}</span>
			</div>
		</div>
	</div>
	<div class="hr-line-dashed"></div>
	<div class="row">
		<div class="col-lg-12">
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label>Decision Maker</label>
						<input class="form-control" ng-model="formData.decisionMaker" placeholder="Decision Maker" type="text" />
					</div>
					<div>
						<label> <input type="checkbox" ng-model="acceptAddon"> Did you ask the Patron for an add-on gift? </label>
					</div>
					<div>
						<label> <input type="checkbox" ng-model="acceptSaleAmount"> Did you confirm the grand total sale amount? </label>
					</div>
					<div>
						<label> <input type="checkbox" ng-model="acceptEmailAddress"> Did you ask the Patron for their e-mail address?
						</label>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>Notes</label>
						<textarea class="form-control" ng-model="notes" style="height:84px"></textarea>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button class="btn btn-danger" ng-click="cancel()">Cancel</button>
	<button class="btn btn-success" ng-click="ok()">Save & Wrap-up</button>
</div>