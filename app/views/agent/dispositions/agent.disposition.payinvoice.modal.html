<div class="modal-header">
  <span><strong>KAOS Id:</strong> {{lead.id}}</span>
  <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
  <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
</div>
<div class="modal-body" modaldraggable>
	<form novalidate class="form">
		<h2>Invoice</h2>
		<div class="row">
			<div class="col-sm-12">
				<div class="form-group">
					<label>Original Amount</label>
					<input type="number" ng-model="formData.originalAmount" ng-change="updateAmount()" class="form-control" min="0" step="0.01" />
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-sm-4">
				<div class="form-group">
					<label>Invoice Amount</label>
					<div>
						<span>{{ invoice.amountRemaining | currency:"$":2 }}</span>
					</div>
				</div>
			</div>
			<div class="col-sm-4">
				<div class="form-group">
					<label>Pay Amount</label>
					<input class="form-control" type="number" ng-model="formData.payAmount" min="0" step="0.01" />
				</div>
			</div>
			<div class="col-sm-4">
				<div class="form-group">
					<label>Amount Remaining</label>
					<div>
						<span>{{ invoice.amountRemaining - formData.payAmount | currency:"$":2 }}</span>
					</div>
				</div>
			</div>
		</div>
		
		<div class="form-group">
			<label>Decision Maker</label>
			<input type="text" class="form-control" placeholder="Decision Maker" ng-model="formData.decisionMaker" />
		</div>
		<div class="hr-line-dashed"></div>
		<div class="row">
			<div class="col-lg-12">
				<h3>Payment</h3>
				<div class="row">
					<div class="col-md-6">
						<div class="form-group">
							<label>Credit Card Type</label>
							<select class="form-control" ng-model="formData.creditCardType">
								<option value="">--Not Selected--</option>
								<option ng-repeat="card in cards" value="{{ card }}">{{card}}</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-4">
						<div class="form-group">
							<label>Last 4 CC Digits</label>
							<input class="form-control" type="text" ng-model="formData.creditCardNumber" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
						</div>
					</div>
					<div class="col-md-4">
						<div class="form-group">
							<label>Expiry Date</label>
							<input class="form-control" type="text" ng-model="formData.creditCardDate" placeholder="MMYY" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" />
						</div>
					</div>
					
				</div>
			</div>
		</div>
		<div class="hr-line-dashed"></div>
		<div class="row">
			<div class="col-lg-12">
				<h3 style="float:left; margin-right:20px; padding-top: 4px;">Verification</h3>
				<button class="btn btn-primary" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Donor Details</button>
				<div>
					<label><input ng-model="verify.leftToPay" type="checkbox" value=""> Did you confirm the amount to pay?</label>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-lg-12">
				<div class="form-group">
					<label>Call Notes</label>
					<textarea class="form-control" rows="3" ng-model="notes"></textarea>
				</div>
			</div>
		</div>
	</form>
</div>
<div class="modal-footer">
	<button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
	<button class="btn btn-success" type="button" ng-click="ok()">Save & Wrap-up</button>
</div>