<div class="ibox">
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click='filterChanged("createdAt")'>
						Start Date/Time
						<span ng-show='sortType == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("totalDurationSecs")'>
						Duration
						<span ng-show='sortType == "totalDurationSecs" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "totalDurationSecs" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.id")'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.clientRef")'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.first_name")'>
						Lead
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callerId")'>
						Caller Id
						<span ng-show='sortType == "callerId" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callerId" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callTo")'>
						Dialed Number
						<span ng-show='sortType == "callTo" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callTo" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callresult.wrapup")'>
						Wrapup
						<span ng-show='sortType == "callresult.wrapup" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callresult.wrapup" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("campaign.name")'>
						Campaign
						<span ng-show='sortType == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent"></th>
			</thead>
			<tbody>
				<tr dir-paginate="cr in callRecords | itemsPerPage: perPage" total-items="totalCalls" current-page="pagination.current">
					<td>{{ formatDate(cr.createdAt) }}</td>
					<td>{{ humanizeSeconds(cr.totalDurationSecs) }}</td>
					<td>{{ cr.lead.id }}</td>
					<td>{{ cr.lead.clientRef }}</td>
					<td>{{ cr.lead.first_name }} {{ cr.lead.last_name }}</td>
					<td>{{ cr.callerId }}</td>
					<td>{{ cr.callTo }}</td>
					<td>{{ cr.callresult.wrapup }}</td>
					<td>{{ cr.campaign.name }}</td>
					<td ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent">
						<uib-dropdown class="btn-group dropdown">
							<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-href="/#/agent/leads/{{cr.lead.id}}/analysis">Analyze</a></li>
							</ul>
						</uib-dropdown>
					</td>
				</tr>
				<tr ng-if="!callRecords.length">
					<td colspan="99">No Call Records Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
	</div>
</div>