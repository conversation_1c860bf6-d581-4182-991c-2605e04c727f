<div class="row">
    <div class="col-lg-4 col-md-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Lead Details</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <tbody>
                        <tr>
                            <td><strong>KAOS ID</strong></td>
                            <td>{{lead.id}}</td>
                            <td><strong>Name</strong></td>
                            <td>{{lead.first_name}} {{lead.last_name}}</td>
                        </tr>
                        <tr>
                            <td><strong>Client</strong></td>
                            <td>{{lead.client.name}}</td>
                            <td><strong>Client Id</strong></td>
                            <td>{{lead.clientRef}}</td>
                        </tr>
                        <tr>
                            <td><strong>TF Reporting Group</strong></td>
                            <td>{{lead.tfSkill.name}}</td>
                            <td><strong>TF Lead Type</strong></td>
                            <td>{{lead.tfSubSkill.name}}</td>
                        </tr>
                        <tr>
                            <td><strong>TM Reporting Group</strong></td>
                            <td>{{lead.tmSkill.name}}</td>
                            <td><strong>TM Lead Type</strong></td>
                            <td>{{lead.tmSubSkill.name}}</td>
                        </tr>
                        <tr>
                            <td><strong>Salutation</strong></td>
                            <td>{{lead.salutation}}</td>
                            <td><strong>Company Name</strong></td>
                            <td>{{lead.company_name}}</td>
                        </tr>
                        <tr>
                            <td><strong>Address 1</strong></td>
                            <td>{{lead.address1}}</td>
                            <td><strong>Address 2</strong></td>
                            <td>{{lead.address2}}</td>
                        </tr>
                        <tr ng-if="lead.address3">
                            <td><strong>Address 3</strong></td>
                            <td colspan="3">{{lead.address3}}</td>
                        </tr>
                        <tr>
                            <td><strong>City</strong></td>
                            <td>{{lead.city}}</td>
                            <td><strong>State</strong></td>
                            <td>{{lead.state}}</td>
                        </tr>
                        <tr>
                            <td><strong>Zip</strong></td>
                            <td>{{lead.zip}}</td>
                            <td><strong>Email</strong></td>
                            <td>{{lead.email}}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone 1</strong></td>
                            <td>{{lead.phone_home}}</td>
                            <td><strong>Phone 2</strong></td>
                            <td>{{lead.phone_mobile}}</td>
                        </tr>
                        <tr>
                            <td><strong>Phone 3</strong></td>
                            <td>{{lead.phone_work}}</td>
                            <td><strong>Phone 4</strong></td>
                            <td>{{lead.phone_workmobile}}</td>
                        </tr>
                        <tr>
                            <td><strong>Member Level</strong></td>
                            <td>{{lead.memberLevel}}</td>
                            <td><strong>Client Worker</strong></td>
                            <td>{{lead.clientWorker}}</td>
                        </tr>
                        <tr>
                            <td><strong>Spouse</strong></td>
                            <td>{{lead.spouse_name}}</td>
                            <td><strong>Lead Delay</strong></td>
                            <td>{{formatDate(lead.dontContactUntil) || 'None'}}</td>
                        </tr>
                        <tr>
                            <td><strong>Custom Field 1</strong></td>
                            <td>{{lead.custom1}}</td>
                            <td><strong>Custom Field 2</strong></td>
                            <td>{{lead.custom2}}</td>
                        </tr>
                        <tr>
                            <td><strong>Agent Tag</strong></td>
                            <td>{{lead.agentPortfolioTag || 'None'}}</td>
                            <td><strong></strong></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td><strong>Notes</strong>
                            <td colspan="3">{{lead.notes}}</td>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Callbacks</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <table class="table" ng-if="callbacks && callbacks.length">
                    <thead>
                        <th>Date Time</th>
                        <th>Campaign</th>
                        <th>Phone Number</th>
                        <th>Agent</th>
                        <th>Status</th>
                    </thead>
                    <tbody>
                        <tr dir-paginate="cb in callbacks | orderBy: 'startDateTime': true | itemsPerPage: 5" class="feed-element" pagination-id="callbacksPaging">
                            <td>{{formatDate(cb.startDateTime)}}</td>
                            <td>{{cb.campaign.name}}</td>
                            <td>{{cb.phone ? lead[cb.phone] : 'unknown'}}</td>
                            <td>{{cb.agent.name}}</td>
                            <td>{{cb.expired ? 'Expired' : cb.deleted ? 'Complete' : 'Scheduled'}}</td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls ng-if="callbacks && callbacks.length" pagination-id="callbacksPaging"></dir-pagination-controls>
                <span ng-if="!callbacks || !callbacks.length">No Callbacks Created</span>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Remaining Call Attempts</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div class="row" ng-repeat="campaign in campaigns | orderBy: 'name'">
                    <div class="col-sm-3"><strong>{{campaign.name}}</strong></div>
                    <div class="col-sm-3"><strong>{{campaign.stages.length ? campaign.stages[0].name : 'No Stage'}}</strong></div>
                    <div class="col-sm-6">
                        <div class="row" style="margin-bottom: 5px;" ng-if="campaign.stages.length && campaign.stages[0].rules && campaign.stages[0].rules.length" ng-repeat="rule in campaign.stages[0].rules | orderBy: 'name'">
                            <div class="col-sm-6">{{rule.name}}</div>
                            <div class="col-sm-6">count: {{rule.count}}</div>
                        </div>
                        <div ng-if="!campaign.stages.length || !campaign.stages[0].rules || !campaign.stages[0].rules.length">
                            <div class="col-sm-6"></div>
                            <div class="col-sm-6">count: 0</div>
                        </div>
                    </div>
                    <div ng-if="campaign.stages.length && campaign.stages[0].agents && campaign.stages[0].agents.length">
                        <div class="col-sm-3" style="margin-top: 15px;"></div>
                        <div class="col-sm-9" style="margin-top: 15px;">
                            <span style="margin-right: 7px;"><strong>Available Agents: </strong></span>
                            <span style="margin-right: 7px;" ng-if="campaign.stages[0].validAgents" ng-repeat="agent in campaign.stages[0].agents">
                                {{ agent.name }}
                            </span>
                            <span ng-if="!campaign.stages[0].validAgents">
                                No agents with correct skills
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Giving History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!hasGiftHistory()">
                    <p>No Giving History</p>
                </div>
                <div ng-if="hasGiftHistory()">
                    <div>
                        <span style="border: 1px grey solid; padding: 4px; float:right; margin-bottom: 5px;">
                            <strong>Lifetime $:</strong> {{lead.lifttimeGiving}} {{lead.lifetimeGivingDate }}
                        </span>
                    </div>
                    <table class="table table-hover no-margins">
                        <thead>
                            <th></th>
                            <th>Amount</th>
                            <th>Match $</th>
                        </thead>
                        <tbody>
                            <tr ng-if="tyAmount">
                                <td>This Year</td>
                                <td>{{tyAmount}}</td>
                                <td>{{lead.tyMatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="lead.lyAmount">
                                <td>Last Year</td>
                                <td>{{lead.lyAmount}}</td>
                                <td>{{lead.lyMatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="lead.lap1Amount">
                                <td>1yr Lap</td>
                                <td>{{lead.lap1Amount}}</td>
                                <td>{{lead.lap1MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="lead.lap2Amount">
                                <td>2yr Lap</td>
                                <td>{{lead.lap2Amount}}</td>
                                <td>{{lead.lap2MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="lead.lap3Amount">
                                <td>3yr Lap</td>
                                <td>{{lead.lap3Amount}}</td>
                                <td>{{lead.lap3MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="lead.lap4Amount">
                                <td>Lap 4+</td>
                                <td>{{lead.lap4Amount}}</td>
                                <td>{{lead.lap4MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td colspan="6"></td>
                            </tr>
                        </tbody>
                        <thead>
                            <th>Last 5 Gifts</th>
                        </thead>
                        <thead>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Source</th>
                            <th>Ref</th>
                            <th colspan="2">Benefits</th>
                        </thead>
                        <tbody>
                            <tr ng-if="lead.gift1Amount || lead.lastGiftAmount">
                                <td>{{lead.gift1Date || lead.lastGiftDate}}</td>
                                <td>{{lead.gift1Amount || lead.lastGiftAmount}}</td>
                                <td>{{lead.gift1Source || lead.lastGiftSource}}</td>
                                <td>{{lead.gift1Ref || lead.lastGiftReference}}</td>
                                <td colspan="2">{{lead.gift1Benefits}}</td>
                            </tr>
                            <tr ng-if="lead.gift2Amount">
                                <td>{{lead.gift2Date}}</td>
                                <td>{{lead.gift2Amount}}</td>
                                <td>{{lead.gift2Source}}</td>
                                <td>{{lead.gift2Ref}}</td>
                                <td colspan="2">{{lead.gift2Benefits}}</td>
                            </tr>
                            <tr ng-if="lead.gift3Amount">
                                <td>{{lead.gift3Date}}</td>
                                <td>{{lead.gift3Amount}}</td>
                                <td>{{lead.gift3Source}}</td>
                                <td>{{lead.gift3Ref}}</td>
                                <td colspan="2">{{lead.gift3Benefits}}</td>
                            </tr>
                            <tr ng-if="lead.gift4Amount">
                                <td>{{lead.gift4Date}}</td>
                                <td>{{lead.gift4Amount}}</td>
                                <td>{{lead.gift4Source}}</td>
                                <td>{{lead.gift4Ref}}</td>
                                <td colspan="2">{{lead.gift4Benefits}}</td>
                            </tr>
                            <tr ng-if="lead.gift5Amount">
                                <td>{{lead.gift5Date}}</td>
                                <td>{{lead.gift5Amount}}</td>
                                <td>{{lead.gift5Source}}</td>
                                <td>{{lead.gift5Ref}}</td>
                                <td colspan="2">{{lead.gift5Benefits}}</td>
                            </tr>
                        </tbody>
                        <thead ng-if="lead.monthlyGivingAmount">
                            <th colspan="6">Monthly giving donor</th>
                        </thead>
                        <tbody ng-if="lead.monthlyGivingAmount">
                            <tr>
                                <td colspan="6">{{lead.monthlyGivingAmount}}</td>
                            </tr>
                            <tr>
                                <td colspan="6">{{lead.monthlyGivingStart}}</td>
                            </tr>
                            <tr>
                                <td colspan="6">{{lead.monthlyGivingEnd}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Buying History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!hasBuyingHistory()">
                    <p>No Giving History</p>
                </div>
                <div ng-if="hasBuyingHistory()">
                    <div>
                        <span style="border: 1px grey solid; padding: 4px; float:right; margin-bottom: 5px;">
                            <strong>Lifetime $: </strong>{{lead.lifetimeBuying}} {{lead.lifetimeBuyingDate}}
                        </span>
                    </div>
                    <table class="table table-hover no-margins">
                        <thead>
                            <th>Type</th>
                            <th>Yr</th>
                            <th>Event</th>
                            <th>Date</th>
                            <th>$</th>
                            <th>Loc</th>
                            <th>Seats</th>
                            <th>Addl</th>
                        </thead>
                        <tbody>
                            <tr ng-if="lead.tix1">
                                <td colspan="8"><strong>Tix 1</strong> {{lead.tix1}}</td>
                            </tr>
                            <tr ng-if="lead.tix2">
                                <td colspan="8"><strong>Tix 2</strong>{{lead.tix2}}</td>
                            </tr>
                            <tr ng-if="lead.tix3">
                                <td colspan="8"><strong>Tix 3</strong>{{lead.tix3}}</td>
                            </tr>
                            <tr ng-if="lead.tix4">
                                <td colspan="8"><strong>Tix 4</strong>{{lead.tix4}}</td>
                            </tr>
                            <tr ng-if="lead.tix5">
                                <td colspan="8"><strong>Tix 5</strong>{{lead.tix5}}</td>
                            </tr>
                            <tr ng-if="lead.tix6">
                                <td colspan="8"><strong>Tix 6</strong>{{lead.tix6}}</td>
                            </tr>
                            <tr ng-if="lead.tix7">
                                <td colspan="8"><strong>Tix 7</strong>{{lead.tix7}}</td>
                            </tr>
                            <tr ng-if="lead.tix8">
                                <td colspan="8"><strong>Tix 8</strong>{{lead.tix8}}</td>
                            </tr>
                            <tr ng-if="lead.tix9">
                                <td colspan="8"><strong>Tix 9</strong>{{lead.tix9}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 1)">
                                <td>{{lead.tix1Type}}</td>
                                <td>{{lead.tix1Yr}}</td>
                                <td>{{lead.tix1Event}}</td>
                                <td>{{lead.tix1Date}}</td>
                                <td>{{lead.tix1Cost}}</td>
                                <td>{{lead.tix1Loc}}</td>
                                <td>{{lead.tix1Seats}}</td>
                                <td>{{lead.tix1Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 2)">
                                <td>{{lead.tix2Type}}</td>
                                <td>{{lead.tix2Yr}}</td>
                                <td>{{lead.tix2Event}}</td>
                                <td>{{lead.tix2Date}}</td>
                                <td>{{lead.tix2Cost}}</td>
                                <td>{{lead.tix2Loc}}</td>
                                <td>{{lead.tix2Seats}}</td>
                                <td>{{lead.tix2Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 3)">
                                <td>{{lead.tix3Type}}</td>
                                <td>{{lead.tix3Yr}}</td>
                                <td>{{lead.tix3Event}}</td>
                                <td>{{lead.tix3Date}}</td>
                                <td>{{lead.tix3Cost}}</td>
                                <td>{{lead.tix3Loc}}</td>
                                <td>{{lead.tix3Seats}}</td>
                                <td>{{lead.tix3Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 4)">
                                <td>{{lead.tix4Type}}</td>
                                <td>{{lead.tix4Yr}}</td>
                                <td>{{lead.tix4Event}}</td>
                                <td>{{lead.tix4Date}}</td>
                                <td>{{lead.tix4Cost}}</td>
                                <td>{{lead.tix4Loc}}</td>
                                <td>{{lead.tix4Seats}}</td>
                                <td>{{lead.tix4Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 5)">
                                <td>{{lead.tix5Type}}</td>
                                <td>{{lead.tix5Yr}}</td>
                                <td>{{lead.tix5Event}}</td>
                                <td>{{lead.tix5Date}}</td>
                                <td>{{lead.tix5Cost}}</td>
                                <td>{{lead.tix5Loc}}</td>
                                <td>{{lead.tix5Seats}}</td>
                                <td>{{lead.tix5Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 6)">
                                <td>{{lead.tix6Type}}</td>
                                <td>{{lead.tix6Yr}}</td>
                                <td>{{lead.tix6Event}}</td>
                                <td>{{lead.tix6Date}}</td>
                                <td>{{lead.tix6Cost}}</td>
                                <td>{{lead.tix6Loc}}</td>
                                <td>{{lead.tix6Seats}}</td>
                                <td>{{lead.tix6Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 7)">
                                <td>{{lead.tix7Type}}</td>
                                <td>{{lead.tix7Yr}}</td>
                                <td>{{lead.tix7Event}}</td>
                                <td>{{lead.tix7Date}}</td>
                                <td>{{lead.tix7Cost}}</td>
                                <td>{{lead.tix7Loc}}</td>
                                <td>{{lead.tix7Seats}}</td>
                                <td>{{lead.tix7Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 8)">
                                <td>{{lead.tix8Type}}</td>
                                <td>{{lead.tix8Yr}}</td>
                                <td>{{lead.tix8Event}}</td>
                                <td>{{lead.tix8Date}}</td>
                                <td>{{lead.tix8Cost}}</td>
                                <td>{{lead.tix8Loc}}</td>
                                <td>{{lead.tix8Seats}}</td>
                                <td>{{lead.tix8Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(lead, 9)">
                                <td>{{lead.tix9Type}}</td>
                                <td>{{lead.tix9Yr}}</td>
                                <td>{{lead.tix9Event}}</td>
                                <td>{{lead.tix9Date}}</td>
                                <td>{{lead.tix9Cost}}</td>
                                <td>{{lead.tix9Loc}}</td>
                                <td>{{lead.tix9Seats}}</td>
                                <td>{{lead.tix9Addl}}</td>
                            </tr>
                            <tr ng-repeat="field in customFields">
                                <td>{{field.TYPE}}</td>
                                <td>{{field.YEAR}}</td>
                                <td>{{field.EVENT}}</td>
                                <td>{{field.DATE}}</td>
                                <td>{{field.COST}}</td>
                                <td>{{field.LOC}}</td>
                                <td>{{field.SEATS}}</td>
                                <td>{{field.ADDL}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Interaction History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!hasInteractionHistory()">
                    <p>No interaction History</p>
                </div>
                <div ng-if="hasInteractionHistory()">
                    <table class="table table-hover no-margins">
                        <thead>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Detail</th>
                        </thead>
                        <tbody>
                            <tr ng-if="lead.interaction1Type">
                                <td>{{lead.interaction1Date}}</td>
                                <td>{{lead.interaction1Type}}</td>
                                <td>{{lead.interaction1Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction2Type">
                                <td>{{lead.interaction2Date}}</td>
                                <td>{{lead.interaction2Type}}</td>
                                <td>{{lead.interaction2Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction3Type">
                                <td>{{lead.interaction3Date}}</td>
                                <td>{{lead.interaction3Type}}</td>
                                <td>{{lead.interaction3Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction4Type">
                                <td>{{lead.interaction4Date}}</td>
                                <td>{{lead.interaction4Type}}</td>
                                <td>{{lead.interaction4Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction5Type">
                                <td>{{lead.interaction5Date}}</td>
                                <td>{{lead.interaction5Type}}</td>
                                <td>{{lead.interaction5Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction6Type">
                                <td>{{lead.interaction6Date}}</td>
                                <td>{{lead.interaction6Type}}</td>
                                <td>{{lead.interaction6Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction7Type">
                                <td>{{lead.interaction7Date}}</td>
                                <td>{{lead.interaction7Type}}</td>
                                <td>{{lead.interaction7Detail}}</td>
                            </tr>
                            <tr ng-if="lead.interaction8Type">
                                <td>{{lead.interaction8Date}}</td>
                                <td>{{lead.interaction8Type}}</td>
                                <td>{{lead.interaction8Detail}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6">
        <!--<div leadcallhistory leadid="lead.id"></div> -->
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Call History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-show="!history || history.length === 0">
                    <p>No Call History</p>
                </div>
                <div class="feed-activity-list">
                    <div dir-paginate="result in history | orderBy: 'createdAt': true | itemsPerPage: 5" class="feed-element" pagination-id="callHistoryPaging">
                        <div class="media-body">
                            <strong>{{result.agent ? result.agent.name : 'DataImporter'}}</strong> wrapped up with
                            <strong>{{result.wrapup}}</strong> on campaign
                            <strong>{{result.campaign.name}} ({{result.campaignstage.name}})</strong>
                            <br>
                            <small class="text-muted">{{ formatDate(result.createdAt) }}</small>
                            <div class="well" style="white-space: pre-wrap" ng-show="result.notes">{{result.notes}}</div>
                            <br>
                            <button ng-if="result.callrecords && result.callrecords.length" class="btn btn-xs btn-success" ng-click="result.showcalls = !result.showcalls" type="button">{{ result.showcalls ? 'Hide Dials' : 'Show Dials'}}</button>
                            <div class="well well-sm" ng-if="result.showcalls" ng-repeat="call in result.callrecords">
                                <small>CLI: {{ call.callerId }}</small>
                                <br>
                                <small>Phone: {{ call.callTo }}</small>
                                <br>
                                <small>Start Time: {{ formatTime(call.startDateTime) }}</small>
                                <br>
                                <small>Duration: {{ humanizeTimespan(call.totalDurationSecs) }}</small>
                                <br>
                                <a ng-if="call.recordingLocation && call.recordingServer" ng-hide="call.audio" ng-click="playRecording(call)" href=""><i class="fa fa-play"></i></a>
                                <a ng-if="call.audio" ng-click="audio.paused ? audio.play() : audio.pause()" href=""><i class="fa" ng-class="{'fa-play': audio.paused, 'fa-pause': !audio.paused}"></i></a>
                                <a ng-if="call.audio" ng-click="call.audio.stop(); call.audio = null;" href=""><i class="fa fa-stop"></i></a>
                                <div ng-if="call.audio" class="input-group">
                                    <input class="form-control" type="range" min="0" max="1" step="0.01" ng-model="call.audio.progress">
                                </div>
                            </div>
                        </div>
                    </div>
                    <dir-pagination-controls pagination-id="callHistoryPaging"></dir-pagination-controls>
                </div>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Audit History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!audits || !audits.length">
                    <p>No Changes made to Lead</p>
                </div>
                <table ng-if="audits && audits.length" class="table">
                    <thead>
                        <th>Field</th>
                        <th>Orig Value</th>
                        <th>New Value</th>
                        <th>Date</th>
                        <th>User</th>
                    </thead>
                    <tbody>
                        <tr dir-paginate="audit in audits | orderBy: 'createdAt': true | itemsPerPage: 5" pagination-id="auditPaging">
                            <td>{{parseField(audit.field)}}</td>
                            <td>{{audit.previousValue || '--BLANK--'}}</td>
                            <td>{{audit.newValue || '--BLANK--'}}</td>
                            <td>{{formatDate(audit.createdAt, true)}}</td>
                            <td>{{audit.user ? audit.user.name : 'Unknown'}}</td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls ng-if="audits && audits.length" pagination-id="auditPaging"></dir-pagination-controls>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Campaign History</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!campaignLeadUpdates || !campaignLeadUpdates.length">
                    <p>No Campaign Changes</p>
                </div>
                <table ng-if="campaignLeadUpdates && campaignLeadUpdates.length" class="table">
                    <thead>
                        <th>Campaign</th>
                        <th>Orig Stage</th>
                        <th>Dest Stage</th>
                        <th>Date</th>
                    </thead>
                    <tbody>
                        <tr dir-paginate="audit in campaignLeadUpdates | filter:{data:'currentCampaignStageId'} | orderBy: 'createdAt': true | itemsPerPage: 5" pagination-id="campaignLeadPaging">
                            <td>{{audit.campaign.name}}</td>
                            <td>{{getStage(audit, audit.from)}}</td>
                            <td>{{getStage(audit, audit.to)}}</td>
                            <td>{{formatDate(audit.createdAt, true)}}</td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls ng-if="campaignLeadUpdates && campaignLeadUpdates.length" pagination-id="campaignLeadPaging"></dir-pagination-controls>
            </div>
        </div>

        <!-- Suppressions -->
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Suppressions</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!suppressions || !suppressions.length">
                    <p>No Suppressions</p>
                </div>
                <table ng-if="suppressions && suppressions.length" class="table">
                    <thead>
                        <th>Campaign</th>
                        <th>Stage</th>
                        <th>Start</th>
                        <th>End</th>
                        <th>Status</th>
                    </thead>
                    <tbody>
                        <tr dir-paginate="row in suppressions | orderBy: 'createdAt': true | itemsPerPage: 5" pagination-id="suppressionsPaging">
                            <td>{{row.campaign.name}}</td>
                            <td>{{getStage(row, row.campaignstageId)}}</td>
                            <td>{{formatDate(row.actualStartDate || row.startDate)}}</td>
                            <td>{{formatDate(row.actualEndDate || row.endDate)}}</td>
                            <td>{{row.skipped ? 'Skipped' : row.finished ? 'Finished' : row.actualStartDate ? 'Suppressed' : 'Pending'}}</td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls ng-if="campaignLeadUpdates && campaignLeadUpdates.length" pagination-id="suppressionsPaging"></dir-pagination-controls>
            </div>
        </div>

        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Invoices</h5>
                <div ibox-tools></div>
            </div>
            <div class="ibox-content">
                <div ng-if="!invoices || !invoices.length">
                    <p>No Invoices for Lead</p>
                </div>
                <table ng-if="invoices && invoices.length" class="table">
                    <thead>
                        <th>Date</th>
                        <th>Total</th>
                        <th>Left to pay</th>
                        <th>Type</th>
                        <th>Due</th>
                        <th>Request Count</th>
                    </thead>
                    <tbody>
                        <tr dir-paginate="invoice in invoices | orderBy: 'createdAt': true | itemsPerPage: 5" pagination-id="invoicePaging">
                            <td>{{formatDate(invoice.createdAt, true)}}</td>
                            <td>{{invoice.grandTotal | currency:'$':2}}</td>
                            <td>{{invoice.amountRemaining | currency:'$':2}}</td>
                            <td>{{invoice.deliveryMethod}}</td>
                            <td>{{formatDate(invoice.dueDate, true)}}</td>
                            <td>{{invoice.requestCount}}</td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls ng-if="invoices && invoices.length" pagination-id="invoicePaging"></dir-pagination-controls>
                <!-- List of all invoices and their amount to pay and due date and request count and type (paper, email) -->
            </div>
        </div>
    </div>
</div>