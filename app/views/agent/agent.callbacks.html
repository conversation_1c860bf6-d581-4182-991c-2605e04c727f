<div class="ibox">
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click='sortType = "lead.id"; sortReverse = !sortReverse'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.clientRef"; sortReverse = !sortReverse'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "startDateTime"; sortReverse = !sortReverse'>
						Call From
						<span ng-show='sortType == "startDateTime" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "startDateTime" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "endDateTime"; sortReverse = !sortReverse'>
						Call To
						<span ng-show='sortType == "endDateTime" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "endDateTime" && sortReverse' class="fa fa-caret-up"></span>
					</span>				
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "campaign.name"; sortReverse = !sortReverse'>
						Campaign
						<span ng-show='sortType == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
					</span>				
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.first_name"; sortReverse = !sortReverse'>
						Lead First Name
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.last_name"; sortReverse = !sortReverse'>
						Lead Last Name
						<span ng-show='sortType == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead[phone]"; sortReverse = !sortReverse'>
						Phone Number
						<span ng-show='sortType == "lead[phone]" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead[phone]" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "callresult.notes"; sortReverse = !sortReverse'>
						Notes
						<span ng-show='sortType == "callresult.notes" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callresult.notes" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent"></th>
			</thead>
			<tbody>
				<tr dir-paginate="callback in callbacks | orderBy:sortType:sortReverse | itemsPerPage: 30">
					<td>{{ callback.lead.id }}</td>
					<td>{{ callback.lead.clientRef }}</td>
					<td>{{ formatDate(callback.startDateTime) }}</td>
					<td>{{ formatDate(callback.endDateTime) }}</td>
					<td>{{ callback.campaign.name }}</td>
					<td>{{ callback.lead.first_name }}</td>
					<td>{{ callback.lead.last_name }}</td>
					<td>{{ callback.lead[callback.phone] }}</td>
					<td>{{ callback.callresult.notes }}</td>
					<td ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent">
						<uib-dropdown class="btn-group dropdown">
							<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-click="edit(callback)">Edit</a></li>
								<li><a ng-href="/#/agent/leads/{{callback.lead.id}}/analysis">Analyze</a></li>
							</ul>
						</uib-dropdown>
					</td>
				</tr>
				<tr ng-if="!callbacks.length">
					<td colspan="99">No Callbacks Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls></dir-pagination-controls>
	</div>
</div>