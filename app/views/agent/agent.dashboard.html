<div class="row" ng-hide="!loggedInUser.showStats">
    <div class="col-lg-3 col-sm-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <span class="label label-success pull-right">Today</span>
                <h5>Call Counts</h5>
            </div>
            <div class="ibox-content">
                <h1 class="no-margins">{{ callCountToday | number:0 || 0 }}</h1>
                <small>Total Calls Made Today</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-sm-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <span class="label label-success pull-right">Today</span>
                <h5>Total Transaction Value</h5>
            </div>
            <div class="ibox-content">
                <h1 class="no-margins">{{ totalGiftAmountToday | currency:"$":2 }}</h1>
                <small>Total Transactions Received Today</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-sm-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <span class="label label-success pull-right">Today</span>
                <h5>Avg Transaction Value</h5>
            </div>
            <div class="ibox-content">
                <h1 class="no-margins">{{ avgGiftPerCallToday | currency:"$":2 }}</h1>
                <small>Avg Transaction Per Call Today</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-sm-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <span class="label label-success pull-right">Today</span>
                <h5>Wrap Up Duration</h5>
            </div>
            <div class="ibox-content">
                <h1 class="no-margins">{{ avgWrapUpDurationToday || '00:00' }}</h1>
                <small>Average Wrap Up Duration Today</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-4 col-md-6">

        <div class="ibox float-e-margins" ng-if="agentSession.currentLead">
            <div class="ibox-title">
                <h5>Giving History</h5>
            </div>
            <div class="ibox-content">
                <div ng-if="!hasGiftHistory(agentSession.currentLead)">
                    <p>No Giving History</p>
                </div>
                <div ng-if="hasGiftHistory(agentSession.currentLead)">
                    <div>
                        <span style="border: 1px grey solid; padding: 4px; float:right; margin-bottom: 5px;">
                            <strong>Lifetime $:</strong> {{agentSession.currentLead.lifttimeGiving}} {{agentSession.currentLead.lifetimeGivingDate }}
                        </span>
                    </div>
                    <table class="table table-hover no-margins">
                        <thead>
                            <th></th>
                            <th>Amount</th>
                            <th>Match $</th>
                        </thead>
                        <tbody>
                            <tr ng-if="agentSession.currentLead.tyAmount">
                                <td>This Year</td>
                                <td>{{agentSession.currentLead.tyAmount}}</td>
                                <td>{{agentSession.currentLead.tyMatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.lyAmount">
                                <td>Last Year</td>
                                <td>{{agentSession.currentLead.lyAmount}}</td>
                                <td>{{agentSession.currentLead.lyMatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.lap1Amount">
                                <td>1yr Lap</td>
                                <td>{{agentSession.currentLead.lap1Amount}}</td>
                                <td>{{agentSession.currentLead.lap1MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.lap2Amount">
                                <td>2yr Lap</td>
                                <td>{{agentSession.currentLead.lap2Amount}}</td>
                                <td>{{agentSession.currentLead.lap2MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.lap3Amount">
                                <td>3yr Lap</td>
                                <td>{{agentSession.currentLead.lap3Amount}}</td>
                                <td>{{agentSession.currentLead.lap3MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.lap4Amount">
                                <td>Lap 4+</td>
                                <td>{{agentSession.currentLead.lap4Amount}}</td>
                                <td>{{agentSession.currentLead.lap4MatchAmount}}</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td colspan="6"></td>
                            </tr>
                        </tbody>
                        <thead>
                            <th>Last 5 Gifts</th>
                        </thead>
                        <thead>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Source</th>
                            <th>Ref</th>
                            <th colspan="2">Benefits</th>
                        </thead>
                        <tbody>
                            <tr ng-if="agentSession.currentLead.gift1Amount || agentSession.currentLead.lastGiftAmount">
                                <td>{{agentSession.currentLead.gift1Date || agentSession.currentLead.lastGiftDate}}</td>
                                <td>{{agentSession.currentLead.gift1Amount || agentSession.currentLead.lastGiftAmount}}</td>
                                <td>{{agentSession.currentLead.gift1Source || agentSession.currentLead.lastGiftSource}}</td>
                                <td>{{agentSession.currentLead.gift1Ref || agentSession.currentLead.lastGiftReference}}</td>
                                <td colspan="2">{{agentSession.currentLead.gift1Benefits}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.gift2Amount">
                                <td>{{agentSession.currentLead.gift2Date}}</td>
                                <td>{{agentSession.currentLead.gift2Amount}}</td>
                                <td>{{agentSession.currentLead.gift2Source}}</td>
                                <td>{{agentSession.currentLead.gift2Ref}}</td>
                                <td colspan="2">{{agentSession.currentLead.gift2Benefits}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.gift3Amount">
                                <td>{{agentSession.currentLead.gift3Date}}</td>
                                <td>{{agentSession.currentLead.gift3Amount}}</td>
                                <td>{{agentSession.currentLead.gift3Source}}</td>
                                <td>{{agentSession.currentLead.gift3Ref}}</td>
                                <td colspan="2">{{agentSession.currentLead.gift3Benefits}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.gift4Amount">
                                <td>{{agentSession.currentLead.gift4Date}}</td>
                                <td>{{agentSession.currentLead.gift4Amount}}</td>
                                <td>{{agentSession.currentLead.gift4Source}}</td>
                                <td>{{agentSession.currentLead.gift4Ref}}</td>
                                <td colspan="2">{{agentSession.currentLead.gift4Benefits}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.gift5Amount">
                                <td>{{agentSession.currentLead.gift5Date}}</td>
                                <td>{{agentSession.currentLead.gift5Amount}}</td>
                                <td>{{agentSession.currentLead.gift5Source}}</td>
                                <td>{{agentSession.currentLead.gift5Ref}}</td>
                                <td colspan="2">{{agentSession.currentLead.gift5Benefits}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="ibox float-e-margins" ng-if="agentSession.currentLead">
            <div class="ibox-title">
                <h5>Buying History</h5>
            </div>
            <div class="ibox-content" style="overflow-x: scroll;">
                <div ng-if="!hasBuyingHistory(agentSession.currentLead)">
                    <p>No Giving History</p>
                </div>
                <div ng-if="hasBuyingHistory(agentSession.currentLead)">
                    <div>
                        <span style="border: 1px grey solid; padding: 4px; float:right; margin-bottom: 5px;">
                            <strong>Lifetime $: </strong>{{agentSession.currentLead.lifetimeBuying}} {{agentSession.currentLead.lifetimeBuyingDate}}
                        </span>
                    </div>
                    <table class="table table-hover no-margins">
                        <thead>
                            <th>Type</th>
                            <th>Yr</th>
                            <th>Event</th>
                            <th>Date</th>
                            <th>$</th>
                            <th>Loc</th>
                            <th>Seats</th>
                            <th>Addl</th>
                        </thead>
                        <tbody>
                            <tr ng-if="agentSession.currentLead.tix1">
                                <td colspan="8"><strong>Tix 1</strong> {{agentSession.currentLead.tix1}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix2">
                                <td colspan="8"><strong>Tix 2</strong>{{agentSession.currentLead.tix2}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix3">
                                <td colspan="8"><strong>Tix 3</strong>{{agentSession.currentLead.tix3}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix4">
                                <td colspan="8"><strong>Tix 4</strong>{{agentSession.currentLead.tix4}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix5">
                                <td colspan="8"><strong>Tix 5</strong>{{agentSession.currentLead.tix5}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix6">
                                <td colspan="8"><strong>Tix 6</strong>{{agentSession.currentLead.tix6}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix7">
                                <td colspan="8"><strong>Tix 7</strong>{{agentSession.currentLead.tix7}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix8">
                                <td colspan="8"><strong>Tix 8</strong>{{agentSession.currentLead.tix8}}</td>
                            </tr>
                            <tr ng-if="agentSession.currentLead.tix9">
                                <td colspan="8"><strong>Tix 9</strong>{{agentSession.currentLead.tix9}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 1)">
                                <td>{{agentSession.currentLead.tix1Type}}</td>
                                <td>{{agentSession.currentLead.tix1Yr}}</td>
                                <td>{{agentSession.currentLead.tix1Event}}</td>
                                <td>{{agentSession.currentLead.tix1Date}}</td>
                                <td>{{agentSession.currentLead.tix1Cost}}</td>
                                <td>{{agentSession.currentLead.tix1Loc}}</td>
                                <td>{{agentSession.currentLead.tix1Seats}}</td>
                                <td>{{agentSession.currentLead.tix1Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 2)">
                                <td>{{agentSession.currentLead.tix2Type}}</td>
                                <td>{{agentSession.currentLead.tix2Yr}}</td>
                                <td>{{agentSession.currentLead.tix2Event}}</td>
                                <td>{{agentSession.currentLead.tix2Date}}</td>
                                <td>{{agentSession.currentLead.tix2Cost}}</td>
                                <td>{{agentSession.currentLead.tix2Loc}}</td>
                                <td>{{agentSession.currentLead.tix2Seats}}</td>
                                <td>{{agentSession.currentLead.tix2Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 3)">
                                <td>{{agentSession.currentLead.tix3Type}}</td>
                                <td>{{agentSession.currentLead.tix3Yr}}</td>
                                <td>{{agentSession.currentLead.tix3Event}}</td>
                                <td>{{agentSession.currentLead.tix3Date}}</td>
                                <td>{{agentSession.currentLead.tix3Cost}}</td>
                                <td>{{agentSession.currentLead.tix3Loc}}</td>
                                <td>{{agentSession.currentLead.tix3Seats}}</td>
                                <td>{{agentSession.currentLead.tix3Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 4)">
                                <td>{{agentSession.currentLead.tix4Type}}</td>
                                <td>{{agentSession.currentLead.tix4Yr}}</td>
                                <td>{{agentSession.currentLead.tix4Event}}</td>
                                <td>{{agentSession.currentLead.tix4Date}}</td>
                                <td>{{agentSession.currentLead.tix4Cost}}</td>
                                <td>{{agentSession.currentLead.tix4Loc}}</td>
                                <td>{{agentSession.currentLead.tix4Seats}}</td>
                                <td>{{agentSession.currentLead.tix4Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 5)">
                                <td>{{agentSession.currentLead.tix5Type}}</td>
                                <td>{{agentSession.currentLead.tix5Yr}}</td>
                                <td>{{agentSession.currentLead.tix5Event}}</td>
                                <td>{{agentSession.currentLead.tix5Date}}</td>
                                <td>{{agentSession.currentLead.tix5Cost}}</td>
                                <td>{{agentSession.currentLead.tix5Loc}}</td>
                                <td>{{agentSession.currentLead.tix5Seats}}</td>
                                <td>{{agentSession.currentLead.tix5Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 6)">
                                <td>{{agentSession.currentLead.tix6Type}}</td>
                                <td>{{agentSession.currentLead.tix6Yr}}</td>
                                <td>{{agentSession.currentLead.tix6Event}}</td>
                                <td>{{agentSession.currentLead.tix6Date}}</td>
                                <td>{{agentSession.currentLead.tix6Cost}}</td>
                                <td>{{agentSession.currentLead.tix6Loc}}</td>
                                <td>{{agentSession.currentLead.tix6Seats}}</td>
                                <td>{{agentSession.currentLead.tix6Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 7)">
                                <td>{{agentSession.currentLead.tix7Type}}</td>
                                <td>{{agentSession.currentLead.tix7Yr}}</td>
                                <td>{{agentSession.currentLead.tix7Event}}</td>
                                <td>{{agentSession.currentLead.tix7Date}}</td>
                                <td>{{agentSession.currentLead.tix7Cost}}</td>
                                <td>{{agentSession.currentLead.tix7Loc}}</td>
                                <td>{{agentSession.currentLead.tix7Seats}}</td>
                                <td>{{agentSession.currentLead.tix7Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 8)">
                                <td>{{agentSession.currentLead.tix8Type}}</td>
                                <td>{{agentSession.currentLead.tix8Yr}}</td>
                                <td>{{agentSession.currentLead.tix8Event}}</td>
                                <td>{{agentSession.currentLead.tix8Date}}</td>
                                <td>{{agentSession.currentLead.tix8Cost}}</td>
                                <td>{{agentSession.currentLead.tix8Loc}}</td>
                                <td>{{agentSession.currentLead.tix8Seats}}</td>
                                <td>{{agentSession.currentLead.tix8Addl}}</td>
                            </tr>
                            <tr ng-if="showTix(agentSession.currentLead, 9)">
                                <td>{{agentSession.currentLead.tix9Type}}</td>
                                <td>{{agentSession.currentLead.tix9Yr}}</td>
                                <td>{{agentSession.currentLead.tix9Event}}</td>
                                <td>{{agentSession.currentLead.tix9Date}}</td>
                                <td>{{agentSession.currentLead.tix9Cost}}</td>
                                <td>{{agentSession.currentLead.tix9Loc}}</td>
                                <td>{{agentSession.currentLead.tix9Seats}}</td>
                                <td>{{agentSession.currentLead.tix9Addl}}</td>
                            </tr>
                            <tr ng-repeat="field in customFields">
                                <td>{{field.TYPE}}</td>
                                <td>{{field.YEAR}}</td>
                                <td>{{field.EVENT}}</td>
                                <td>{{field.DATE}}</td>
                                <td>{{field.COST}}</td>
                                <td>{{field.LOC}}</td>
                                <td>{{field.SEATS}}</td>
                                <td>{{field.ADDL}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="ibox float-e-margins" ng-if="agentSession.currentLead.paymentlogs && agentSession.currentLead.paymentlogs.length">
            <div class="ibox-title">
                <h5>Payment History</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Campaign</th>
                            <th>Amount</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="row in agentSession.currentLead.paymentlogs | reverse">
                            <td>{{row.actualPaymentDate || row.paymentDate | date:'longDate'}}</td>
                            <td>{{row.campaign.name}}</td>
                            <td>{{row.amount | currency:'$'}}</td>
                            <td>{{row.isPaid ? 'Paid' : row.disabled ? 'Disabled' : row.deleted ? 'Deleted' : row.error ? 'Failed (' + row.error + ')' : 'Pending'}} <strong>{{row.recurringpaymentId ? '(Recurring - ' + row.recurringpayment.unit + ')' : ''}}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
    <div class="col-lg-4 col-md-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Messages</h5>
            </div>
            <div class="ibox-content">
                <div class="ui-tab">
                    <uib-tabset>
                        <uib-tab ng-click="newMessages.campaign = false">
                            <tab-heading>
                                <i class="fa fa-bars" title="Campaign"></i>
                                <i class="fa fa-exclamation" style="color: red;" ng-if="newMessages.campaign" title="Campaign"></i>
                            </tab-heading>
                            <div class="feed-activity-list">
                                <p ng-show="!agentSession.currentCampaignStage || !messages.campaignMessages || !messages.campaignMessages.length">No campaign messages.</p>
                                <div class="feed-element" style="padding-bottom: 0px;" dir-paginate="message in messages.campaignMessages | itemsPerPage: 1" pagination-id="campaignMessages" current-page="pagination.campaign.current">
                                    <div>
                                        <small class="pull-right text-navy">{{ formatDate(message.updatedAt) }}</small>
                                        <strong>{{ message.title }}</strong>
                                        <div>{{ message.content }}</div>
                                        <small class="text-navy">Author: {{ getUser(message.createdUserId) }}</small>
                                    </div>
                                </div>
                            </div>
                            <dir-pagination-controls pagination-id="campaignMessages"></dir-pagination-controls>
                        </uib-tab>
                        <uib-tab ng-click="newMessages.skill = false">
                            <tab-heading>
                                <i class="fa fa-bolt" title="Skills"></i>
                                <i class="fa fa-exclamation" style="color: red;" ng-if="newMessages.skill" title="Campaign"></i>
                            </tab-heading>
                            <div class="feed-activity-list">
                                <p ng-show="!agentSession.currentCampaignStage || !messages.skillMessages || !messages.skillMessages.length">No lead type messages.</p>
                                <div class="feed-element" style="padding-bottom: 0px;" dir-paginate="message in messages.skillMessages | itemsPerPage: 1" pagination-id="skillMessages" current-page="pagination.skill.current">
                                    <div>
                                        <small class="pull-right text-navy">{{ formatDate(message.updatedAt) }}</small>
                                        <strong>{{ message.title }}</strong>
                                        <div>{{ message.content }}</div>
                                        <small class="text-navy">Author: {{ getUser(message.createdUserId) }}</small>
                                    </div>
                                </div>
                            </div>
                            <dir-pagination-controls pagination-id="skillMessages"></dir-pagination-controls>
                        </uib-tab>
                        <uib-tab ng-click="newMessages.agent = false">
                            <tab-heading>
                                <i class="fa fa-user" title="Agent"></i>
                                <i class="fa fa-exclamation" style="color: red;" ng-if="newMessages.agent" title="Campaign"></i>
                            </tab-heading>
                            <div class="feed-activity-list">
                                <p ng-show="!agentSession.currentCampaignStage || !messages.agentMessages || !messages.agentMessages.length">No agent messages.</p>
                                <div class="feed-element" style="padding-bottom: 0px;" dir-paginate="message in messages.agentMessages | itemsPerPage: 1" pagination-id="agentMessages" current-page="pagination.agent.current">
                                    <div>
                                        <small class="pull-right text-navy">{{ formatDate(message.updatedAt) }}</small>
                                        <strong>{{ message.title }}</strong>
                                        <div>{{ message.content }}</div>
                                        <small class="text-navy">Author: {{ getUser(message.createdUserId) }}</small>
                                    </div>
                                </div>
                            </div>
                            <dir-pagination-controls pagination-id="agentMessages"></dir-pagination-controls>
                        </uib-tab>
                    </uib-tabset>
                </div>
            </div>
        </div>

        <div class="ibox float-e-margins {{ newLead ? 'animated pulse' : 'animated' }}">
            <div class="ibox-title">
                <h5>Donor / Patron <button ng-show="agentSession.currentLead" type="button" class="btn btn-xs" ng-click="editLead()">edit</button></h5>
            </div>
            <div class="ibox-content">
                <div ng-show="loadingNextCall" class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"></div>
                    <div class="sk-rect2"></div>
                    <div class="sk-rect3"></div>
                    <div class="sk-rect4"></div>
                    <div class="sk-rect5"></div>
                </div>
                <div ng-show="!loadingNextCall && !agentSession.currentLead">
                    <p>No current lead selected.</p>
                </div>
                <table ng-show="agentSession.currentLead && !loadingNextCall" class="table table-hover no-margins">
                    <tbody>
                        <tr>
                            <td>Client</td>
                            <td>{{ agentSession.currentCampaignStage.campaign.client.name }} (Local Time: {{ clientDateTime() }})</td>
                        </tr>
                        <tr>
                            <td>Campaign</td>
                            <td>{{ agentSession.currentCampaignStage.campaign.name }}</td>
                        </tr>
                        <tr>
                            <td>Stage</td>
                            <td>{{ agentSession.currentCampaignStage.name }}</td>
                        </tr>
                        <tr ng-if="badCreditCard">
                            <td><span class="text-danger">Warning</span></td>
                            <td>Bad Credit Card</td>
                        </tr>
                        <tr ng-if="agentSession.currentCampaignStage.campaign.campaigntype.name == 'Telefunding' && getAskAmount(agentSession.currentLead)" style="border-style:solid; border-color:#ED5565;">
                            <td>Ask Amount</td>
                            <td class="animated infinite pulse"><span class="label label-danger pulse">{{ getAskAmount(agentSession.currentLead) | currency:"$":0 }}</span></td>
                        </tr>
                        <tr>
                            <td>Lead Type</td>
                            <td>
                                {{
                                agentSession.currentCampaignStage.campaign.campaigntype.name == "Telefunding"
                                ?
                                agentSession.currentLead.tfSkill.name + ' - ' + agentSession.currentLead.tfSubSkill.name
                                :
                                agentSession.currentLead.tmSkill.name + ' - ' + agentSession.currentLead.tmSubSkill.name
                                }}
                            </td>
                        </tr>
                        <tr>
                            <td>KAOS Id</td>
                            <td>{{ agentSession.currentLead.id }}</td>
                        </tr>
                        <tr>
                            <td>Client Ref</td>
                            <td>{{ agentSession.currentLead.clientRef }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.company_name">
                            <td>Company Name</td>
                            <td>{{ agentSession.currentLead.company_name }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.salutation">
                            <td>Salutation</td>
                            <td>{{ agentSession.currentLead.salutation }}</td>
                        </tr>
                        <tr>
                            <td>Name</td>
                            <td>{{ agentSession.currentLead.first_name + (agentSession.currentLead.last_name ? ' ' + agentSession.currentLead.last_name : '') }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.memberLevel">
                            <td>Member Level</td>
                            <td>{{ agentSession.currentLead.memberLevel }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.phone_home" ng-class="{ highlightedNumber: (activeDiallingNumber && activeDiallingNumber.type === 'phone_home') }">
                            <td>Phone 1</td>
                            <td>{{ agentSession.currentLead.phone_home }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.phone_mobile" ng-class="{ highlightedNumber: (activeDiallingNumber && activeDiallingNumber.type === 'phone_mobile') }">
                            <td>Phone 2</td>
                            <td>{{ agentSession.currentLead.phone_mobile }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.phone_work" ng-class="{ highlightedNumber: (activeDiallingNumber && activeDiallingNumber.type === 'phone_work') }">
                            <td>Phone 3</td>
                            <td>{{ agentSession.currentLead.phone_work }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.phone_workmobile" ng-class="{ highlightedNumber: (activeDiallingNumber && activeDiallingNumber.type === 'phone_workmobile') }">
                            <td>Phone 4</td>
                            <td>{{ agentSession.currentLead.phone_workmobile }}</td>
                        </tr>
                        <tr>
                            <td>Email</td>
                            <td>{{ agentSession.currentLead.email }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.spouse_name">
                            <td>Spouse Name</td>
                            <td>{{ agentSession.currentLead.spouse_name }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.clientWorker">
                            <td>Client Worker</td>
                            <td>{{ agentSession.currentLead.clientWorker }}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.custom1 || agentSession.currentLead.custom2">
                            <td ng-if="agentSession.currentLead.custom1">{{ agentSession.currentLead.custom1 }}</td>
                            <td ng-if="agentSession.currentLead.custom2">{{ agentSession.currentLead.custom2 }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Training Materials</h5>
            </div>
            <div class="ibox-content">
                <p ng-show="!agentSession.currentCampaignStage || !campaignTrainingDocs || campaignTrainingDocs.length === 0">No documents related to this campaign.</p>
                <table class="table table-hover no-margins" ng-show="agentSession.currentCampaignStage.campaign && campaignTrainingDocs && campaignTrainingDocs.length > 0">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Description</th>
                            <th>Link</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="doc in campaignTrainingDocs">
                            <td>{{ doc.name }}</td>
                            <td>{{ doc.description }}</td>
                            <td>
                                <a ng-show="!doc.link" class="pdf-link" href="{{getTrainingDocUrl(doc)}}" target="_blank"><i class="fa fa-file-pdf-o"></i></a>
                                <a ng-show="doc.link" class="pdf-link" href="{{getTrainingDocUrl(doc)}}" target="_blank"><i class="fa fa-link"></i></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
    <div class="col-lg-4 col-md-6">
        <div class="ibox float-e-margins">
            <div class="ibox-title agentstatus" style="background-color:{{ agentSession.agentStatus.color }};color:{{ (agentSession.agentStatus.color === '#ffffff') ? '#676a6c' : '#fff' }};">
                <uib-dropdown class="agent-status-menu ibox-tools dropdown" style="color:#676a6c;">
                    <a class="dropdown-toggle" uib-dropdown-toggle>
                        <i class="fa fa-wrench"></i>
                    </a>
                    <ul class="dropdown-menu dropdown-user" uib-dropdown-menu>
                        <li ng-repeat="state in agentStates">
                            <a ng-click="changeAgentStatus(state)">{{ state.name }}</a>
                        </li>
                    </ul>
                </uib-dropdown>
                <h5 style="color:#000000">Status: {{ agentSession.agentStatus.name }}</h5>
                <button style="float: right" class="btn btn-xs btn-danger" ng-click="panicButton()">Report Problem</button>
                <button style="float: right; margin-right: 5px;" class="btn btn-xs btn-primary" ng-if="loggedInUser.username === 'dt_test' || loggedInUser.username === 'gkabler' || loggedInUser.username === 'plarson'" ng-click="forceLead()">Override</button>
            </div>
            <div class="ibox-content">
                <audio id="audio_remote" autoplay="autoplay" />
                <audio id="dtmfTone" src="/sounds/dtmf.wav" />
                <audio id="ringtone" loop src="/sounds/ringbacktone.wav" />
                <audio id="ringbacktone" loop src="/sounds/ringbacktone.wav" />
                <div ng-show="showCallPrepTimer" class="wrapup-progress-container">
                    <uib-progressbar value="callPrepTimePercentLeft" type="warning" style="margin-bottom: 8px;">
                        <span>{{ callPrepSecondsLeft || 0 }} secs</span>
                    </uib-progressbar>
                    <div style="float:right;">
                        <a ng-click="onCallPrepCompleted(true)" ng-show="attemptedCallToCurrentLead"><span class="label label-default">Redial Last Number</span></a>
                        <a ng-click="flagPreviousNumberAsBad()" ng-show="agentSession.currentLead && attemptedCallToCurrentLead" ng-disabled="badNumberButtonDisabled"><span class="label label-default">Flag previous number as bad</span></a>
                        <a ng-click="onCallPrepCompleted(false)" ng-show="activeDiallingNumber"><span class="label label-primary">Dial Now</span></a>
                    </div>
                </div>
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Duration</th>
                            <th>Status</th>
                            <th>Caller Id</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <timer autostart="false" language="en" interval="1000">{{mminutes}}:{{sseconds}}</timer>
                            </td>
                            <td>{{ agentSession.callState || 'Idle' }}</td>
                            <td>{{ agentSession.currentCampaignStage.callerId || agentSession.currentCampaignStage.campaign.defaultCallerId }}</td>
                            <td>
                                <span ng-if="callInProgress" title="{{ phone.muted ? 'Unmute' : 'Mute' }}" class="btn btn-success btn-xs" ng-click="phone.toggleMute()"><i class="fa {{ phone.muted ? 'fa-microphone' : 'fa-microphone-slash'}}"></i></span>
                                <!-- <span ng-if="callInProgress" title="{{ phone.held ? 'Retrieve' : 'Hold' }}" class="btn btn-success btn-xs" ng-click="phone.toggleHold()"><i class="fa {{ phone.held ? 'fa-play' : 'fa-pause'}}"></i></span> -->
                                <uib-dropdown ng-if="callInProgress" class="btn-group" style="margin-right: 50px">
                                    <span type="button" class="btn btn-success btn-xs" data-toggle="dropdown"><i class="fa fa-th"></i></span>
                                    <div class="dropdown-menu" role="menu" ng-click="$event.stopPropagation()" style="box-shadow:none;border:none;background:transparent;">
                                        <div class="row">
                                            <button ng-click="phone.sendDtmf('1')" type="button" class="btn btn-default">1</button>
                                            <button ng-click="phone.sendDtmf('2')" type="button" class="btn btn-default">2</button>
                                            <button ng-click="phone.sendDtmf('3')" type="button" class="btn btn-default">3</button>
                                        </div>
                                        <div class="row">
                                            <button ng-click="phone.sendDtmf('4')" type="button" class="btn btn-default">4</button>
                                            <button ng-click="phone.sendDtmf('5')" type="button" class="btn btn-default">5</button>
                                            <button ng-click="phone.sendDtmf('6')" type="button" class="btn btn-default">6</button>
                                        </div>
                                        <div class="row">
                                            <button ng-click="phone.sendDtmf('7')" type="button" class="btn btn-default">7</button>
                                            <button ng-click="phone.sendDtmf('8')" type="button" class="btn btn-default">8</button>
                                            <button ng-click="phone.sendDtmf('9')" type="button" class="btn btn-default">9</button>
                                        </div>
                                        <div class="row">
                                            <button ng-click="phone.sendDtmf('*')" type="button" class="btn btn-default">*</button>
                                            <button ng-click="phone.sendDtmf('0')" type="button" class="btn btn-default">0</button>
                                            <button ng-click="phone.sendDtmf('#')" type="button" class="btn btn-default">#</button>
                                        </div>
                                    </div>
                                </uib-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <uib-dropdown class="btn-group" ng-hide="autoDialEnabled">
                        <button type="button" ng-disabled="!agentSession.currentLead || phone.onCall" class="btn btn-primary" data-toggle="dropdown">
                            Dial <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <li>
                                <a ng-show="agentSession.currentLead.phone_home && agentSession.currentLead.phone_home.length > 5" ng-click="makeCall(agentSession.currentLead.phone_home, agentSession.currentCampaignStage.callerId || agentSession.currentCampaignStage.campaign.defaultCallerId, agentSession.currentCampaignStage.campaign.dontRecord, 'phone_home')">Home: {{ agentSession.currentLead.phone_home }}</a>
                            </li>
                            <li>
                                <a ng-show="agentSession.currentLead.phone_mobile && agentSession.currentLead.phone_mobile.length > 5" ng-click="makeCall(agentSession.currentLead.phone_mobile, agentSession.currentCampaignStage.callerId || agentSession.currentCampaignStage.campaign.defaultCallerId, agentSession.currentCampaignStage.campaign.dontRecord, 'phone_mobile')">Mobile: {{ agentSession.currentLead.phone_mobile }}</a>
                            </li>
                            <li>
                                <a ng-show="agentSession.currentLead.phone_work && agentSession.currentLead.phone_work.length > 5" ng-click="makeCall(agentSession.currentLead.phone_work, agentSession.currentCampaignStage.callerId || agentSession.currentCampaignStage.campaign.defaultCallerId, agentSession.currentCampaignStage.campaign.dontRecord, 'phone_work')">Work: {{ agentSession.currentLead.phone_work }}</a>
                            </li>
                            <li>
                                <a ng-show="agentSession.currentLead.phone_workmobile && agentSession.currentLead.phone_workmobile.length > 5" ng-click="makeCall(agentSession.currentLead.phone_workmobile, agentSession.currentCampaignStage.callerId || agentSession.currentCampaignStage.campaign.defaultCallerId, agentSession.currentCampaignStage.campaign.dontRecord, 'phone_workmobile')">Work Mobile: {{ agentSession.currentLead.phone_workmobile }}</a>
                            </li>
                        </ul>
                    </uib-dropdown>
                    <button ng-hide="!phone.onCall" ng-click="phone.hangup()" class="btn btn-danger {{ pulseHangup ? 'animated pulse' : 'animated' }}" type="button">
                        <i class="fa fa-close"></i>&nbsp;Hangup
                    </button>
                    <button ng-hide="!agentSession.currentCallResult || !agentSession.currentLead || phone.onCall || attemptedCallToCurrentLead" ng-click="rejectAndIdle()" class="btn btn-danger" type="button">
                        <i class="fa fa-close"></i>&nbsp;Reject & Idle
                    </button>
                    <uib-dropdown class="btn-group">
                        <button type="button" ng-hide="!agentSession.currentCallResult || !attemptedCallToCurrentLead || wrapUpComplete" class="btn btn-warning" data-toggle="dropdown">
                            <i class="fa fa-bars"></i>&nbsp;Wrap-up <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <li ng-repeat="disposition in dispositions">
                                <a ng-click="wrapUpCall(disposition)">{{ disposition.name }}</a>
                            </li>
                        </ul>
                    </uib-dropdown>
                </div>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Lead Notes</h5>
            </div>
            <div class="ibox-content">
                <div ng-show="!agentSession.currentLead">
                    <p>No current lead selected.</p>
                </div>
                <div ng-show="agentSession.currentLead" style="padding-bottom:15px;">
                    <textarea class="form-control" style="margin-top:10px;" rows="3" ng-change="notesChanges = true;" ng-model="agentSession.currentLead.notes" maxlength="250"></textarea>
                </div>
                <button ng-show="agentSession.currentLead" class="btn btn-success" ng-click="saveLeadNotes()" type="button">Save</button>
                <span ng-if="notesChanges"> you have unsaved lead notes</span>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Call History</h5>
            </div>
            <div class="ibox-content" style="max-height: 400px; overflow-x: auto;">
                <div ng-show="!leadCallHistory || leadCallHistory.length === 0">
                    <p>No previous call history found for lead.</p>
                </div>
                <div class="feed-activity-list">
                    <div ng-repeat="result in leadCallHistory" class="feed-element">
                        <div class="media-body">
                            <strong>{{result.agent ? result.agent.name : 'DataImporter'}}</strong>
                            wrapped up with
                            <strong>{{result.wrapup}}</strong>
                            on campaign
                            <strong>{{result.campaign.name}} ({{result.campaignstage.name}})</strong>
                            <br>
                            <small class="text-muted">{{ formatDate(result.createdAt) }}</small>
                            <div class="well" style="white-space: pre-wrap" ng-show="result.notes">{{result.notes}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Interaction History</h5>
            </div>
            <div class="ibox-content">
                <div ng-show="!agentSession.currentLead">
                    <p>No current lead selected.</p>
                </div>
                <table ng-if="agentSession.currentLead" class="table table-hover no-margins">
                    <thead>
                        <th>Date</th>
                        <th>Type</th>
                        <th>Detail</th>
                    </thead>
                    <tbody>
                        <tr ng-if="agentSession.currentLead.interaction1Type">
                            <td>{{agentSession.currentLead.interaction1Date}}</td>
                            <td>{{agentSession.currentLead.interaction1Type}}</td>
                            <td>{{agentSession.currentLead.interaction1Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction2Type">
                            <td>{{agentSession.currentLead.interaction2Date}}</td>
                            <td>{{agentSession.currentLead.interaction2Type}}</td>
                            <td>{{agentSession.currentLead.interaction2Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction3Type">
                            <td>{{agentSession.currentLead.interaction3Date}}</td>
                            <td>{{agentSession.currentLead.interaction3Type}}</td>
                            <td>{{agentSession.currentLead.interaction3Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction4Type">
                            <td>{{agentSession.currentLead.interaction4Date}}</td>
                            <td>{{agentSession.currentLead.interaction4Type}}</td>
                            <td>{{agentSession.currentLead.interaction4Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction5Type">
                            <td>{{agentSession.currentLead.interaction5Date}}</td>
                            <td>{{agentSession.currentLead.interaction5Type}}</td
                            <td>{{agentSession.currentLead.interaction5Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction6Type">
                            <td>{{agentSession.currentLead.interaction6Date}}</td>
                            <td>{{agentSession.currentLead.interaction6Type}}</td>
                            <td>{{agentSession.currentLead.interaction6Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction7Type">
                            <td>{{agentSession.currentLead.interaction7Date}}</td>
                            <td>{{agentSession.currentLead.interaction7Type}}</td>
                            <td>{{agentSession.currentLead.interaction7Detail}}</td>
                        </tr>
                        <tr ng-if="agentSession.currentLead.interaction8Type">
                            <td>{{agentSession.currentLead.interaction8Date}}</td>
                            <td>{{agentSession.currentLead.interaction8Type}}</td>
                            <td>{{agentSession.currentLead.interaction8Detail}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>