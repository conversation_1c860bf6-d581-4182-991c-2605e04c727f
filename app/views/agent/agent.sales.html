<div class="ibox">
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th style="width: 10px;"></th>
				<th>
					<span class="clickable" ng-click='sortType = "createdAt"; sortReverse = !sortReverse'>
						Date/Time
						<span ng-show='sortType == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.id"; sortReverse = !sortReverse'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.clientRef"; sortReverse = !sortReverse'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "subSkill"; sortReverse = !sortReverse'>
						Lead Type
						<span ng-show='sortType == "subSkill" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "subSkill" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.first_name"; sortReverse = !sortReverse'>
						Lead First Name
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.last_name"; sortReverse = !sortReverse'>
						Lead Last Name
						<span ng-show='sortType == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.company"; sortReverse = !sortReverse'>
						Lead Company
						<span ng-show='sortType == "lead.company" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.company" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "decisionMaker"; sortReverse = !sortReverse'>
						Decision Maker
						<span ng-show='sortType == "decisionMaker" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "decisionMaker" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "saleAmount"; sortReverse = !sortReverse'>
						Sale Amount
						<span ng-show='sortType == "saleAmount" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "saleAmount" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "giftAmount"; sortReverse = !sortReverse'>
						Add on gift
						<span ng-show='sortType == "giftAmount" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "giftAmount" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "paymentType"; sortReverse = !sortReverse'>
						Payment Type
						<span ng-show='sortType == "paymentType" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "paymentType" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent || loggedInUser.isAdmin || loggedInUser.isSupervisor"></th>
			</thead>
			<tbody dir-paginate="result in callresults | orderBy:sortType:sortReverse | itemsPerPage: resultsPerPage">
				<tr>
					<td><a href="" ng-click="result.showsales = !result.showsales"><i class="fa {{ result.showsales ? 'fa-minus' : 'fa-plus'}}"></i></td>
					<td>{{ formatDate(result.createdAt) }}</td>
					<td>{{ result.lead.id }}</td>
					<td>{{ result.lead.clientRef }}</td>
					<td>{{ result.subSkill }}</td>
					<td>{{ result.lead.first_name }}</td>
					<td>{{ result.lead.last_name }}</td>
					<td>{{ result.lead.company }}</td>
					<td>{{ result.decisionMaker }}</td>
					<td>{{ result.saleAmount | currency:"$":0 }}</td>
					<td>{{ result.giftAmount || 0 | currency:"$":0 }}</td>
					<td>{{ result.paymentType }}</td>
					<td ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent || loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin">
						<uib-dropdown class="btn-group dropdown">
							<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent" ng-href="/#/agent/leads/{{result.lead.id}}/analysis">Analyze</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin" ng-href="/#/admin/leads/{{result.lead.id}}/analysis">Analyze</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin" href="" ng-click="edit(result)"></i>Edit</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isClientAdmin" href="" ng-click="delete(result)">Delete</a></li>
							</ul>
						</uib-dropdown>
					</td>
				</tr>
				<tr class="table-subrow" ng-show="result.showsales" ng-repeat="sale in result.sales">
					<td></td>
					<td colspan="99"><strong>{{ sale.seatCount }}x Series:</strong> {{ sale.series }} <strong>Seats:</strong> {{ sale.seats }} <strong>Day:</strong> {{ sale.dayOfWeek }} <strong>Cost:</strong> {{ sale.subtotal | currency:"$":0 }}</td>
				</tr>
			</tbody>
			<tbody ng-if="!callresults.length">
				<tr>
					<td colspan="99">No Sales Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls></dir-pagination-controls>
	</div>
</div>