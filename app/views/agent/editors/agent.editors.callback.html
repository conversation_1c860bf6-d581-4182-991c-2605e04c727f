<form class="form" ng-submit="save()">
  <div class="modal-body">
    <div class="row">    
      <label>Start Date</label>
      <p class="input-group">
        <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="callbackDate" max-date="dateOptions.maxDate" min-date="dateOptions.minDate" is-open="datePickerOpened" ng-required="true" close-text="Close" readonly="true" ng-change="calcCallback()" />
        <span class="input-group-btn">
          <button type="button" class="btn btn-default" ng-click="datePickerOpened = !datePickerOpened"><i class="glyphicon glyphicon-calendar"></i></button>
        </span>
      </p>
      <div class="row" style="padding-top:20px;padding-bottom:20px;">
        <div class="col-sm-4">
          <label>Client Callback Time</label>
          <br>
          <select style="width: 75px; margin-right: 5px; float: left" ng-model="startHour" ng-change="calcCallback()" class="form-control" ng-options="hour for hour in hours"></select>
          <span style="float: left; padding-top: 7px;">:</span>
          <select style="width: 75px; margin-left: 5px; float: left" ng-model="startMinute" ng-change="calcCallback()" class="form-control" ng-options="minute for minute in minutes"></select>
        </div>
        <div class="col-sm-8" style="padding-top: 30px;">
          until <strong>{{ callbackEndDateTime }}</strong> ({{ client.timezone }})
        </div>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <span><strong>Your Time:</strong></span><br>
          <p>between <strong>{{ localCallbackStartDateTime }}</strong> and <strong>{{ localCallbackEndDateTime }}</strong></p>
        </div>
      </div>
      <br>
      <div class="row">
        <div class="col-sm-4">
          <label>Phone Number</label>
          <select class="form-control" ng-model="callback.phone">
            <option value="phone_home" ng-if="lead.phone_home">{{ lead.phone_home }}</option>
            <option value="phone_mobile" ng-if="lead.phone_mobile">{{ lead.phone_mobile }}</option>
            <option value="phone_work" ng-if="lead.phone_work">{{ lead.phone_work }}</option>
            <option value="phone_workmobile" ng-if="lead.phone_workmobile">{{ lead.phone_workmobile }}</option>
          </select>
        </div>
      </div>
      <br>
      <div class="row">
        <div class="col-sm-12">
          <label>Call Notes</label>
          <textarea class="form-control" style="margin-top:10px;" rows="3" ng-model="callresult.notes"></textarea>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
    <button class="btn btn-success" type="submit">Save</button>
  </div>
</form>