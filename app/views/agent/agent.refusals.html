<div class="ibox">
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click='sortType = "createdAt"; sortReverse = !sortReverse'>
						Date/Time
						<span ng-show='sortType == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.id"; sortReverse = !sortReverse'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.clientRef"; sortReverse = !sortReverse'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "subSkill"; sortReverse = !sortReverse'>
						Lead Type
						<span ng-show='sortType == "subSkill" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "subSkill" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.first_name"; sortReverse = !sortReverse'>
						Lead First Name
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.last_name"; sortReverse = !sortReverse'>
						Lead Last Name
						<span ng-show='sortType == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "lead.company"; sortReverse = !sortReverse'>
						Lead Company
						<span ng-show='sortType == "lead.company" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.company" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "decisionMaker"; sortReverse = !sortReverse'>
						Decision Maker
						<span ng-show='sortType == "decisionMaker" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "decisionMaker" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "refusalReason"; sortReverse = !sortReverse'>
						Refusal Reason
						<span ng-show='sortType == "refusalReason" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "refusalReason" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='sortType = "wrapup"; sortReverse = !sortReverse'>
						Exception
						<span ng-show='sortType == "wrapup" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "wrapup" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent || loggedInUser.isAdmin || loggedInUser.isSupervisor"></th>
			</thead>
			<tbody>
				<tr dir-paginate="refusal in refusals | orderBy:sortType:sortReverse | itemsPerPage: resultsPerPage">
					<td>{{ formatDate(refusal.createdAt) }}</td>
					<td>{{ refusal.lead.id }}</td>
					<td>{{ refusal.lead.clientRef }}</td>
					<td>{{ refusal.subSkill }}</td>
					<td>{{ refusal.lead.first_name }}</td>
					<td>{{ refusal.lead.last_name }}</td>
					<td>{{ refusal.lead.company }}</td>
					<td>{{ refusal.decisionMaker }}</td>
					<td>{{ refusal.refusalReason }}</td>
					<td>{{ refusal.isException }}</td>
					<td ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent || loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin">
						<uib-dropdown class="btn-group dropdown">
							<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-if="loggedInUser.isAgent || loggedInUser.isClientAgent" ng-href="/#/agent/leads/{{refusal.lead.id}}/analysis">Analyze</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin" ng-href="/#/admin/leads/{{refusal.lead.id}}/analysis">Analyze</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin" href="" ng-click="edit(refusal)">Edit</a></li>
							</ul>
						</uib-dropdown>
					</td>
				</tr>
				<tr ng-if="!refusals.length">
					<td colspan="99">No Refusals Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls></dir-pagination-controls>
	</div>
</div>