<form role="form" ng-submit="ok()">
  <div class="modal-header">
      <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Device</h3>
  </div>
  <div class="modal-body">
    <div class="form-group">
      <label>Phone Type</label>
      <select class="form-control" ng-model="editItem.type" ng-options="phone.type as phone.name for phone in phones">
        <option value="">--Select a Phone--</option>
      </select>
    </div>
    <div class="form-group">
      <label>Extension</label>
      <input type="text" class="form-control" placeholder="Extension" ng-model="editItem.extension" required />
    </div>
    <div class="form-group">
      <label>Name</label>
      <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
    </div>
    <div class="form-group">
      <label>Server</label>
      <input type="text" class="form-control" placeholder="Server" ng-model="editItem.server" />
    </div>
    <div class="form-group">
      <label>Password</label>
      <input type="password" class="form-control" placeholder="Password" ng-model="editItem.password" />
    </div>
    <div class="form-group">
      <label><input type="checkbox" ng-model="editItem.recordCalls"> Record Calls</label>
    </div>
  </div>
  <div class="modal-footer">
      <button class="btn btn-success" type="submit">Save</button>
      <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>