<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<table class="table">
				<thead>
					<th>User Id</th>
					<th>Date/Time</th>
					<th>Message</th>
					<th>Errors</th>
					<th>JSON Data</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="error in errors | orderBy:'createdAt':true | itemsPerPage:20">
						<td>{{ error.userId }}</td>
						<td>{{ formatDate(error.createdAt) }}</td>
						<td>{{ error.message }}</td>
						<td>
							<div ng-repeat="err in error.errors">
								<button type="button" class="btn btn-xs" ng-click="err.shown = !err.shown">{{err.shown ? 'Hide' : 'Show'}}</button>
								<span ng-show="err.shown">
									<p><strong>Message: </strong>{{err.exception.message}}</p>
									<p><strong>Stack: </strong>{{err.exception.stack}}</p>
								</span>
							</div>
						</td>
						<td>
							<button type="button" class="btn btn-xs" ng-click="error.showJson = !error.showJson">{{err.shown ? 'Hide' : 'Show'}}</button>
							<div ng-show="error.showJson">
								<p ng-repeat="(key, value) in error.jsonData"><strong>{{key}}: </strong>{{value}}</p>
							</div>
						</td>
						<td>
							<button type="button" class="btn btn-xs btn-danger" ng-click="deleteError(error)">Delete</button>
						</td>
					</tr>
					<tr ng-hide="errors.length > 0">
						<td colspan="99">No Errors Raised</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>