<form role="form" ng-submit="save()">
  <div class="modal-header">
      <h3 class="modal-title">Email Settings</h3>
  </div>
  <div class="modal-body">
    <div class="form-group">
      <select ng-model="email.service" class="form-control">
        <option value="">SMTP</option>
        <option value="1und1">1und1</option>
        <option value="AOL">AOL</option>
        <option value="DebugMail.io">DebugMail.io</option>
        <option value="DynectEmail">DynectEmail</option>
        <option value="FastMail">FastMail</option>
        <option value="GandiMail">GandiMail</option>
        <option value="Gmail">Gmail</option>
        <option value="Godaddy">Godaddy</option>
        <option value="GodaddyAsia">GodaddyAsia</option>
        <option value="GodaddyEurope">GodaddyEurope</option>
        <option value="hot.ee">hot.ee</option>
        <option value="Hotmail">Hotmail</option>
        <option value="iCloud">iCloud</option>
        <option value="mail.ee">mail.ee</option>
        <option value="Mail.ru">Mail.ru</option>
        <option value="Mailgun">Mailgun</option>
        <option value="Mailjet">Mailjet</option>
        <option value="Mandrill">Mandrill</option>
        <option value="Naver">Naver</option>
        <option value="Postmark">Postmark</option>
        <option value="QQ">QQ</option>
        <option value="QQex">QQex</option>
        <option value="SendCloud">SendCloud</option>
        <option value="SendGrid">SendGrid</option>
        <option value="SES">SES</option>
        <option value="Sparkpost">Sparkpost</option>
        <option value="Yahoo">Yahoo</option>
        <option value="Yandex">Yandex</option>
        <option value="Zoho">Zoho</option>
      </select>
    </div>
    <div class="form-group" ng-if="!email.service">
      <label>Host</label>
      <input type="text" class="form-control" placeholder="Host" ng-model="email.host" required />
    </div>
    <div class="form-group" ng-if="!email.service">
      <label>Port</label>
      <input type="number" class="form-control" placeholder="Port" ng-model="email.port" min="0" />
    </div>
    <div class="form-group">
      <label>From Address</label>
      <input type="text" class="form-control" placeholder="From Address" ng-model="email.from" required />
    </div>
    <div class="form-group" ng-if="!email.service">
      <label>Require SSL <input type="checkbox" ng-model="email.secure"></label>
    </div>
    <div class="form-group" ng-if="email.service || email.secure">
      <label>Username</label>
      <input type="text" class="form-control" placeholder="Username" ng-model="email.auth.user" />
    </div>
    <div class="form-group" ng-if="email.service || email.secure">
      <label>Password</label>
      <input type="password" class="form-control" placeholder="Password" ng-model="email.auth.pass" />
    </div>
  </div>
  <div class="modal-footer">
      <button class="btn btn-success" type="submit">Save</button>
      <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>