<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<h3>User Sessions</h3>
			<table class="table">
				<thead>
					<th>Id</th>
					<th>User</th>
					<th>Last Tick</th>
					<th><PERSON>rowser</th>
					<th>Address</th>
					<th>Page</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="session in userSessions | orderBy: 'id' | itemsPerPage: 10" pagination-id="userTable">
						<td>{{ session.id }}</td>
						<td>{{ findUser(session.id) }}</td>
						<td>{{ epochTime(session.lastKeepAlivePing) }}</td>
						<td>{{ session.userAgent }}</td>
						<td>{{ session.host }}</td>
						<td>{{ session.state }}</td>
						<td>
							<button type="button" class="btn btn-danger" ng-click="endUserSession(session)">End</button>
						</td>
					</tr>
					<tr ng-hide="userSessions.length > 0">
						<td colspan="99">No Users Logged In</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls pagination-id="userTable"></dir-pagination-controls>
		</div>
	</div>
</div>

<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<h3>Agent Sessions</h3>
			<table class="table">
				<thead>
					<th>Id</th>
					<th>Agent</th>
					<th>User</th>
					<th>Device</th>
					<th>Last Tick</th>
					<th>Call State</th>
					<th>Agent Status</th>
					<th>Campaign</th>
					<th>Stage</th>
					<th>Lead</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="session in agentSessions | orderBy: 'id' | itemsPerPage: 10" pagination-id="agentTable">
						<td>{{ session.id }}</td>
						<td>{{ findAgent(session.id) }}</td>
						<td>{{ findAgentUser(session.id) }}</td>
						<td>{{ findAgentDevice(session.id) }}</td>
						<td>{{ epochTime(session.lastKeepAlivePing) }}</td>
						<td>{{ session.callState || 'none' }}</td>
						<td>{{ session.agentStatus ? session.agentStatus.name : 'none' }}</td>
						<td>{{ session.currentCampaignStage ? session.currentCampaignStage.campaign.name : 'none' }}</td>
						<td>{{ session.currentCampaignStage ? session.currentCampaignStage.name : 'none' }}</td>
						<td>{{ session.currentLead ? session.currentLead.first_name + ' ' + session.currentLead.last_name : 'none' }}</td>
						<td>
							<button type="button" class="btn btn-danger" ng-click="endAgentSession(session)">End</button>
						</td>
					</tr>
					<tr ng-hide="agentSessions.length > 0">
						<td colspan="99">No Agents Logged In</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls pagination-id="agentTable"></dir-pagination-controls>
		</div>
	</div>
</div>