<form role="form" ng-submit="save()" name="form">
    <div class="modal-header">
        <h3 class="modal-title">{{ merchant.id ? 'Edit' : 'Create' }} Merchant</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-sm-12">
                <div class="form-group">
                    <label>deviceID</label>
                    <input type="text" class="form-control" placeholder="merchantID" ng-model="merchant.deviceID" required />
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label>merchantID</label>
                    <input type="text" class="form-control" placeholder="merchantID" ng-model="merchant.merchantID" required />
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label>developerID</label>
                    <input type="text" class="form-control" placeholder="developerID" ng-model="merchant.developerID" required />
                </div>
            </div>
            <div class="col-sm-12">
                <div class="form-group">
                    <label>Client</label>
                    <select class="form-control" ng-model="merchant.clientId" ng-options="client.id as client.name for client in clients" required>
                        <option value="">--Please Select--</option>
                    </select>
                </div>
            </div>
        </div>

        <div style="clear:both;"></div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-success" type="submit">Save</button>
        <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
    </div>
</form>