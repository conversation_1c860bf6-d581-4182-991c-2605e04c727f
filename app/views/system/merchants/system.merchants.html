<div class="ibox">
    <div class="ibox-content">
        <div class="tab-pane">
            <h3>Merchants <button ng-click="edit()" class="btn btn-primary btn-xs">Create</button></h3>
            <table class="table">
                <thead>
                    <th>Client</th>
                    <th>Device ID</th>
                    <th>Merchant ID</th>
                    <th>Developer ID</th>
                    <th>Ready</th>
                    <th></th>
                </thead>
                <tbody>
                    <tr dir-paginate="merchant in merchants | itemsPerPage: 10" pagination-id="merchantTable">
                        <td>{{ merchant.client.name }}</td>
                        <td>{{ merchant.deviceID }}</td>
                        <td>{{ merchant.merchantID }}</td>
                        <td>{{ merchant.developerID }}</td>
                        <td>{{ merchant.transactionKey ? 'Yes' : 'No' }}</td>
                        <td>
                            <uib-dropdown class="btn-group">
                                <button type="button" class="btn btn-xs btn-primary" data-toggle="dropdown">
                                    Actions <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <li ng-if="!merchant.transactionKey"><a ng-click="generate(merchant)">Generate</a></li>
                                    <li ng-if="merchant.transactionKey"><a ng-click="test(merchant)">Test</a></li>
                                    <li class="divider"></li>
                                    <li><a ng-click="edit(merchant)">Edit</a></li>
                                    <li><a ng-click="delete(merchant)">Delete</a></li>
                                </ul>
                            </uib-dropdown>
                        </td>
                    </tr>
                    <tr ng-hide="merchants.length > 0">
                        <td colspan="99">No Merchants Found</td>
                    </tr>
                </tbody>
            </table>
            <dir-pagination-controls pagination-id="merchantTable"></dir-pagination-controls>
        </div>
    </div>
</div>