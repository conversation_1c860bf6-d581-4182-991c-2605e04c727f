<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<h3>Failures</h3>
			<table class="table">
				<thead>
					<th>Date/Time</th>
					<th>Error</th>
					<th>From</th>
					<th>To</th>
					<th>Subject</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="item in history | filter:{success:false} | orderBy:'createdAt':true | itemsPerPage: 10" pagination-id="failures">
						<td>{{ formatDate(item.createdAt) }}</td>
						<td>{{ item.error }}</td>
						<td>{{ item.mailOptions.from }}</td>
						<td>{{ item.mailOptions.to }}</td>
						<td>{{ item.mailOptions.subject }}</td>
						<td>
							<a href="" class="btn btn-xs btn-success" ng-click="item.$resend()">Resend</a>
						</td>
					</tr>
					<tr ng-hide="history.length > 0">
						<td colspan="99">No Email History</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls pagination-id="failures"></dir-pagination-controls>
		</div>
	</div>
</div>

<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<h3>Successes</h3>
			<table class="table">
				<thead>
					<th>Date/Time</th>
					<th>From</th>
					<th>To</th>
					<th>Subject</th>
				</thead>
				<tbody>
					<tr dir-paginate="item in history | filter:{success:true} | orderBy:'createdAt':true | itemsPerPage: 10" pagination-id="successes">
						<td>{{ formatDate(item.createdAt) }}</td>
						<td>{{ item.mailOptions.from }}</td>
						<td>{{ item.mailOptions.to }}</td>
						<td>{{ item.mailOptions.subject }}</td>
					</tr>
					<tr ng-hide="history.length > 0">
						<td colspan="99">No Email History</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls pagination-id="successes"></dir-pagination-controls>
		</div>
	</div>
</div>
