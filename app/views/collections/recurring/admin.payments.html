<div class="modal-header">
    <span><strong>KAOS Id:</strong> {{lead.id}}</span>
    <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
    <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
</div>
<div class="modal-body">
    <table class="table">
        <thead>
            <tr>
                <th>Date</th>
                <th>Amount</th>
                <th>Status</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="row in payments">
                <td>{{row.actualPaymentDate||row.paymentDate | date:'longDate'}}</td>
                <td>{{row.amount | currency:'$'}}</td>
                <td>{{ payment.status == 'error' ? payment.error : payment.disabled ? 'Disabled' : payment.isPaid ? 'Paid' : 'Pending' }}</td>
                <th>
                    <uib-dropdown class="btn-group dropdown" ng-show="payment.status === 'pending">
                        <button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
                            Actions <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            <li><a>Cancel</a></li>
                        </ul>
                    </uib-dropdown>
                </th>
            </tr>
        </tbody>
    </table>
</div>
<div class="modal-footer">
    <button class="btn btn-success" ng-click="close()" type="button">Close</button>
</div>