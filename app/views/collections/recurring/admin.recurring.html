<div class="ibox">
    <div class="ibox-content">
        <div class="tab-pane">
            <div class="row">
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>KAOS Id</label>
                        <input type="text" class="form-control" ng-model="filters.leadId" />
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Client Ref</label>
                        <input type="text" class="form-control" ng-model="filters.clientRef" />
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Campaign</label>
                        <select class="form-control" ng-model="filters.campaign" ng-options="campaign.id as campaign.name for campaign in campaigns">
                            <option value="">All Campaigns</option>
                        </select>
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control" ng-model="filters.status">
                            <option value="">All</option>
                            <option value="active">Active</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="ibox">
    <div class="ibox-content">
        <div class="tab-pane">
            <table class="table">
                <thead>
                    <th>KAOS Id</th>
                    <th>Client Ref</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Campaign</th>
                    <th>Schedule</th>
                    <th>First Payment</th>
                    <th>Next Payment</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th></th>
                </thead>
                <tbody>
                    <tr ng-repeat="row in filtered()">
                        <td>{{ row.leadId }}</td>
                        <td>{{ row.lead.clientRef }}</td>
                        <td>{{ row.lead.first_name }}</td>
                        <td>{{ row.lead.last_name }}</td>
                        <td>{{ row.campaign.name }}</td>
                        <td>{{ unit(row) }}</td>
                        <td>{{ row.paymentlogs[0].paymentDate | date:'longDate' }}</td>
                        <td>{{ row.isCancelled || row.isDeleted ? 'Never' : row.nextPayment || 'Pending' | date:'longDate' }}</td>
                        <td>{{ total(row) | currency:'$'}} from {{row.count}} payment(s)</td>
                        <td>{{ row.isCancelled ? 'Cancelled' : row.isFinished ? 'Finished' : 'Active' }}</td>
                        <td>
                            <uib-dropdown class="btn-group dropdown">
                                <button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
                                    Actions <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <li><a ng-click="edit(row)">Edit</a></li>
                                    <li><a ng-click="payments(row)">Payments</a></li>
                                    <li ng-show="!row.isCancelled"><a ng-click="cancel(row)">Cancel</a></li>
                                </ul>
                            </uib-dropdown>
                        </td>
                    </tr>
                    <tr ng-hide="filtered().length">
                        <td colspan="99">No items found</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>