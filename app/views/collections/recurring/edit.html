<div class="modal-header">
    <div class="row">
        <div class="col-sm-6">
            <span><strong>KAOS Id:</strong> {{lead.id}}</span>
            <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
            <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
        </div>
        <div class="col-sm-6" style="text-align: end;">
            <strong>Status: <span ng-class="{'text-navy': !recurring.isCancelled, 'text-danger': recurring.isCancelled}">{{recurring.isCancelled ? 'Cancelled' : 'Active'}} {{lastError ? ' - ' + lastError : ''}}</span> </strong>
        </div>
    </div>

</div>
<div class="modal-body" modaldraggable>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group">
                <label>Pay</label>
                <input type="number" min="0" step="0.01" class="form-control" ng-model="recurring.amount" />
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Every</label>
                <select class="form-control" ng-model="recurring.unit">
                    <option value="day">Daily</option>
                    <option value="week">Week</option>
                    <option value="month">Month</option>
                    <option value="quarter">Quarter</option>
                </select>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>Next Payment</label>
                <div class="input-group">
                    <input type="text" class="form-control" min-date="minDate" uib-datepicker-popup="{{format}}" ng-model="nextPayment.paymentDate" is-open="dates.initial" close-text="Close" required />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-click="openDate('initial', $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    <div class="row" ng-if="existingCard.id">
        <div class="col-sm-12">
            <p>Existing Card: {{parseCardType(existingCard.cardType)}} Ending: ####-####-####-{{existingCard.maskedCardNumber}} Expiration: {{existingCard.expirationDate}}</p>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-cardNumDiv">
                <label>Credit Card Number</label>
                <input ng-if="validtoken" disabled ng-model="cardToken.cardNumber" />
                <p ng-if="carderrors['tsep-cardNum']" class="text-danger">{{carderrors['tsep-cardNum']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardNumber" autocomplete="off"
                         ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-datepickerDiv">
                <label>Expiry Date</label>
                <input ng-if="validtoken" disabled ng-model="cardToken.expirationDate" />
                <p ng-if="carderrors['tsep-datepicker']" class="text-danger">{{carderrors['tsep-datepicker']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.expirationDate" placeholder="MMYY"
                         autocomplete="off" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-cvv2Div">
                <label>CVV2</label>
                <!-- <input ng-if="validtoken" disabled /> -->
                <p ng-if="carderrors['tsep-cvv2']" class="text-danger">{{carderrors['tsep-cvv2']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardHolderName" autocomplete="off"
                         ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3" style="padding-top: 23px;">
            <div ng-show="testingTsys" style="display: inline-block; vertical-align: middle;">
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"></div>
                    <div class="sk-rect2"></div>
                    <div class="sk-rect3"></div>
                    <div class="sk-rect4"></div>
                    <div class="sk-rect5"></div>
                </div>
            </div>
            <button ng-show="!testingTsys" ng-if="!validtoken" ng-disabled="cardToken.tsepToken || saving" class="btn btn-success">Check Card</button>
            <!-- <button ng-if="!validtoken" class="btn btn-primary" ng-click="saveCard()" ng-disabled="!cardToken || !cardToken.tsepToken">Save Card</button> -->
            <button ng-show="!testingTsys" ng-if="validtoken" class="btn btn-grey" ng-click="clearCard()">Clear Card</button>
        </div>
        <div class="col-md-12">
            <span class="text-danger">{{carderrors.master}}</span> <span class="text-danger">{{tokenerror}}</span>
        </div>
    </div>
    <div class="hr-line-dashed"></div>
    <div class="row">
        <div class="col-md-12">
            <div class="form-group">
                <label for="">Notes</label>
                <textarea class="form-control" rows="3" ng-model="notes"></textarea>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <span ng-show="recurring.isCancelled && nextPayment.paymentDate && validtoken" style="margin-right: 10px">Saving will make this Recurring Plan Active</span>
    <button class="btn btn-success" ng-click="save()" ng-disabled="!nextPayment.paymentDate || !validtoken" type="button">Save</button>
    <button class="btn" ng-click="close()" type="button">Cancel</button>
</div>