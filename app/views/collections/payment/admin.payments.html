<div class="ibox">
    <div class="ibox-content">
        <div class="tab-pane">
            <div class="row">
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Start Date</label>
                        <div class="input-group">
                            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="filters.startDate" is-open="dates.start" close-text="Close" required />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>End Date</label>
                        <div class="input-group">
                            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="filters.endDate" is-open="dates.end" close-text="Close" required />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Lead Id</label>
                        <input type="text" class="form-control" ng-model="filters.leadId" />
                    </div>
                </div>
                <div class="col-xs-3">
                    <div class="form-group">
                        <label>Client Ref</label>
                        <input type="text" class="form-control" ng-model="filters.clientRef" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-4" ng-hide="loggedInUser.isClientAdmin">
                    <div class="form-group">
                        <label>Client</label>
                        <select class="form-control" ng-model="filters.client" ng-options="client as client.name for client in clients track by client.id">
                            <option value="">All Clients</option>
                        </select>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label>Campaign</label>
                        <select class="form-control" ng-model="filters.campaign" ng-options="campaign as campaign.name for campaign in campaigns | filter:{clientId:filters.client.id} track by campaign.id">
                            <option value="">All Campaigns</option>
                        </select>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="form-group">
                        <label>User</label>
                        <select class="form-control" ng-model="filters.user" ng-options="user as user.name for user in users track by user.id">
                            <option value="">All Users</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label><input type="checkbox" ng-model="filters.filterPaid"> Show Only Unpaid</label>
                        <label style="margin-left: 10px;"><input type="checkbox" ng-model="filters.filterErrors"> Show Only Errors</label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <button class="btn btn-success" ng-click="search(true)">Search</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="ibox">
    <div class="ibox-content">
        <div class="tab-pane">
            <table class="table">
                <thead>
                    <th>Client</th>
                    <th>Campaign</th>
                    <th>Lead Id</th>
                    <th>Client Ref</th>
                    <th>Amount</th>
                    <th>Due Date</th>
                    <th>Payment Date</th>
                    <th>Status</th>
                    <th></th>
                </thead>
                <tbody>
                    <tr dir-paginate="payment in payments | itemsPerPage:10" total-items="pagination.total" current-page="pagination.current">
                        <td>{{ payment.client.name }}</td>
                        <td>{{ payment.campaign.name }}</td>
                        <td>{{ payment.leadId }}</td>
                        <td>{{ payment.lead.clientRef }}</td>
                        <td>{{ payment.amount | currency:'$' }}</td>
                        <td>{{ payment.paymentDate | date:'mediumDate' }}</td>
                        <td>{{ payment.actualPaymentDate | date:'mediumDate' }}</td>
                        <td>{{ payment.status == 'error' ? payment.error : payment.disabled ? 'Disabled' : payment.isPaid ? 'Paid' : 'Pending' }}{{payment.source ==='recurring' ? ' (Recurring)' : ''}}</td>
                        <td>
                            <uib-dropdown class="btn-group">
                                <button type="button" class="btn btn-xs btn-primary" data-toggle="dropdown">
                                    Actions <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
									<li ng-show="payment.source === 'recurring'"><a ng-click="edit(payment)">Edit Recurring</a></li>
                                    <li><a ng-click="disable($index)">Cancel</a></li>
                                </ul>
                            </uib-dropdown>
                        </td>
                    </tr>
                    <tr ng-hide="payments.length > 0">
                        <td colspan="99" style="text-align: center">No payments found</td>
                    </tr>
                </tbody>
            </table>
            <dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
        </div>
    </div>
</div>