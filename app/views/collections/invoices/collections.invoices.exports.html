<div class="ibox">
    <div class="ibox-content">
        <table class="table">
            <thead>
                <th>Run Date</th>
                <th>Amount Requested</th>
                <th></th>
            </thead>
            <tbody>
                <tr dir-paginate="item in invoicesHistory | itemsPerPage: 20">
                    <td>{{ formatDate(item.createdAt) }}</td>
                    <td>{{ item.requestAmount | currency:"$":0 }}</td>
                    <td>
                        <button type="button" ng-click="downloadPDF(item)" class="btn btn-success">View PDF</button>
                        <button type="button" ng-click="email(item)" class="btn btn-success">Email PDF</button>
                    </td>
                </tr>
                <tr ng-if="!invoicesHistory.length">
                    <td colspan="99">No Invoices Created</td>
                </tr>
            </tbody>
        </table>
        <dir-pagination-controls></dir-pagination-controls>
    </div>
</div>