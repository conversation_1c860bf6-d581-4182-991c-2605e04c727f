<form ng-submit="search()">
    <div class="modal-body" >
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>KAOS Id</label>
                    <input class="form-control" type="text" ng-model="filters.id">
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Client Ref</label>
                    <input class="form-control" type="text" ng-model="filters.clientRef">
                </div>
            </div>
        </div>
        <div class="row" ng-if="!campaignId">
            <div class="col-sm-6" ng-hide="loggedInUser.isClientAdmin">
                <div class="form-group">
                    <label>Client</label>
                    <select ng-options="client.name as client.name for client in clients" ng-model="filters.clientName" class="form-control">
                        <option value="">--none--</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Campaign</label>
                    <select ng-options="campaign.name as campaign.name for campaign in campaigns" ng-model="filters.campaignName" class="form-control">
                        <option value="">--none--</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Lead First Name</label>
                    <input class="form-control" type="text" ng-model="filters.leadFirstName">
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Lead Last Name</label>
                    <input class="form-control" type="text" ng-model="filters.leadLastName">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Payment Type</label>
                    <select class="form-control" ng-model="filters.paymentType">
                        <option value="">--none--</option>
                        <option value="Credit Card">Credit Card</option>
                        <option value="Invoice">Invoice</option>
                    </select>
                </div>
            </div>
            <div class="col-sm-6">
                <div class="form-group">
                    <label>Total To Pay</label>
                    <input class="form-control" type="number" step="0.01" ng-model="filters.amountRemaining">
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-success" type="submit">Search</button>
    </div>
</form>