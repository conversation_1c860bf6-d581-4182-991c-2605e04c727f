<form ng-submit="save()">
    <div class="modal-body">
        <div class="row">
            <div class="col-md-12">
                <p><strong>Amount to Pay: </strong>{{amountRemaining | currency:'$':2}}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>No of Payments</label>
                    <input type="number" class="form-control" ng-model="paymentCount" ng-change="updatePaymentCount()" min="1" max="12" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>Payment every X months</label>
                    <select class="form-control" ng-model="paymentEvery" ng-change="updatePaymentCount()">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-1"></div>
            <div class="col-md-5">
                <strong>Amount</strong>
            </div>
            <div class="col-md-6">
                <strong>Payment Date</strong>
            </div>
        </div>
        <div class="row" ng-repeat="payment in payments track by $index">
            <div class="col-md-1"><strong>{{$index + 1}}</strong></div>
            <div class="col-md-5">
                <div class="form-group">
                    <input type="number" step="0.01" class="form-control" ng-model="payment.amount" min="0" ng-change="updatePayment()" />
                </div>
            </div>
            <div ng-class="{'col-md-6': !paymentsBroken, 'col-md-5': paymentsBroken}" class="col-md-6">
                <div class="form-group">
                    <div class="input-group">
                        <input type="text" class="form-control" uib-datepicker-popup="{{format}}" min-date="{{minDate}}" ng-model="payment.paymentDate" is-open="dates[$index + '']" close-text="Close" required />
                        <span class="input-group-btn">
                            <button type="button" class="btn btn-default" ng-click="openDate($index, $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
            <div ng-if="paymentsBroken">
                <button class="btn" ng-click="fixPayments($index)">Fix</button>
            </div>
        </div>
        <div class="row">
            <div class="col-md-2"><strong>Total</strong></div>
            <div class="col-md-4">
                {{paymentsTotal | currency:'$'}}
            </div>
            <div class="col-md-2"><strong>Difference</strong></div>
            <div class="col-md-4">
                <span ng-class="{'text-danger': paymentsBroken, 'text-heavy': paymentsBroken}">{{difference}}</span>
            </div>
        </div>
        <div class="hr-line-dashed"></div>
        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="">Notes</label>
                    <textarea class="form-control" rows="3" ng-model="notes"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-success" type="button" ng-click="save()" ng-disabled="paymentsTotal != amountRemaining">Save</button>
    </div>
</form>