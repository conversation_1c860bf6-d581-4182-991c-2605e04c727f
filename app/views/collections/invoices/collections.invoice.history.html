<div class="row">
    <div class="col-sm-12" style="margin-bottom: 10px;">
        <span><button type="button" ng-click="alterAmount()" class="btn btn-primary">Alter Amount</button></span>
        <span><button type="button" ng-disabled="!invoice.amountRemaining" ng-click="addPayment()" class="btn btn-success">Add Payment</button></span>
        <span><button type="button" ng-disabled="!invoice.amountRemaining" ng-click="writeOff()" class="btn btn-info">Write Off</button></span>
        <span><button type="button" ng-click="badPayment()" class="btn btn-warning">Bad Payment</button></span>
    </div>
    <div class="col-sm-4">
        <div class="ibox">
            <div class="ibox-title">
                <div ibox-tools></div>
                <h5>Invoice Details</h5>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-5"><strong>Orig Amount</strong></div>
                    <div class="col-sm-7">{{invoice.grandTotal | currency:$}}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Remaining</strong></div>
                    <div class="col-sm-7">{{invoice.amountRemaining | currency:$}}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Status</strong></div>
                    <div class="col-sm-7">{{invoice.writtenOff && invoice.writtenOff !== '0' && invoice.writtenOff !== 'false' ? 'Written Off' : invoice.amountRemaining ? 'Pending' : 'Complete'}}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Payment Type</strong></div>
                    <div class="col-sm-7">{{invoice.callresult.paymentType}}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Due Date</strong></div>
                    <div class="col-sm-7">{{invoice.dueDate | date: 'short'}}</div>
                </div>
                <div class="row">
                    <div class="col-sm-5"><strong>Written Off</strong></div>
                    <div class="col-sm-7">{{ (invoice.writtenOffAmount ? invoice.writtenOffAmount : (invoice.writtenOff ? invoice.grandTotal : invoice.writtenOffAmount)) || '0' | currency:$ }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-4">
        <div class="ibox">
            <div class="ibox-title">
                <div ibox-tools></div>
                <h5>Original Pledge/Sale</h5>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-5"><strong>Campaign</strong></div>
                    <div class="col-sm-7">{{invoice.campaign.name}}</div>
                    <div class="col-sm-5"><strong>Stage</strong></div>
                    <div class="col-sm-7">{{stage.name}}</div>
                    <div class="col-sm-5"><strong>Agent</strong></div>
                    <div class="col-sm-7">{{invoice.callresult.agent.name}}</div>
                    <div class="col-sm-5"><strong>Date/Time</strong></div>
                    <div class="col-sm-7">{{invoice.createdAt | date:'short'}}</div>
                    <div class="col-sm-5"><strong>Amount</strong></div>
                    <div class="col-sm-7">{{invoice.callresult.grandTotal | currency:$}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-4">
        <div class="ibox">
            <div class="ibox-title">
                <div ibox-tools></div>
                <h5>Lead</h5>
                <span style="float:right; margin-right: 5px;">
                    <button type="button" ng-click="changeStage()" class="btn btn-xs btn-primary">Change Stage</button>
                </span>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-5"><strong>KAOS ID</strong></div>
                    <div class="col-sm-7">{{invoice.lead.id}}</div>
                    <div class="col-sm-5"><strong>Client Ref</strong></div>
                    <div class="col-sm-7">{{invoice.lead.clientRef}}</div>
                    <div class="col-sm-5"><strong>Name</strong></div>
                    <div class="col-sm-7">{{invoice.lead.first_name}} {{invoice.lead.last_name}}</div>
                    <div class="col-sm-5"><strong>Lead Type</strong></div>
                    <div class="col-sm-7">{{skill.name}} - {{subskill.name}}</div>
                    <div class="col-sm-5"><strong>Current Stage</strong></div>
                    <div class="col-sm-7">{{currentStage.name || 'None'}}</div>
                    <div class="col-sm-5"><strong>Agent Tag</strong></div>
                    <div class="col-sm-7">{{invoice.lead.agentPortfolioTag || 'None'}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="ibox">
            <div class="ibox-title">
                <h5>Payments</h5>
                <span style="float:right">
                    <button type="button" ng-click="cancelPayments()" ng-disabled="!hasPending()" class="btn btn-xs btn-danger">Cancel All</button>
                    <button type="button" ng-if="invoice.callresult.wrapup !== 'Recurring Gift'" ng-click="resetInsallments()" class="btn btn-xs btn-primary">{{hasPending() ? 'Change Installment Plan' : 'Setup Installment Plan'}}</button>
                    <button type="button" ng-if="invoice.callresult.wrapup === 'Recurring Gift'" ng-click="editRecurring()" class="btn btn-xs btn-primary">Change Recurring Plan</button>
                    <button type="button" ng-click="changeCard()" class="btn btn-xs btn-success">Change Card</button>
                </span>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Scheduled Date Time</th>
                            <th>Status</th>
                            <th>Amount</th>
                            <th>User</th>
                            <th>Source</th>
                            <th ng-if="invoice.callresult.wrapup === 'Recurring Gift'">Payment Interval</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="!paymentLogs.length">
                            <td colspan="99" align="center">No Payments Found</td>
                        </tr>
                        <tr ng-repeat="payment in paymentLogs">
                            <td>{{payment.actualPaymentDate || payment.paymentDate | date:'short'}}</td>
                            <td>{{payment.status === 'error' ? getError(payment) : payment.disabled ? 'Disabled' : payment.deleted ? 'Deleted' : payment.isPaid ? 'Paid' : 'Pending'}}</td>
                            <td>{{payment.amount | currency:$}}</td>
                            <td>{{payment.user ? payment.user.name : ''}}</td>
                            <td>
                                <span ng-if="!paymentSourceIsFile(payment.source)">{{payment.source}}</span>
                                <button ng-if="paymentSourceIsFile(payment.source)" ng-click="downloadFile(payment)" class="btn btn-xs btn-success">Download</button>
                            </td>
                            <td ng-if="invoice.callresult.wrapup === 'Recurring Gift'">{{invoice.recurringpayment.unit}}</td>
                            <td>
                                <uib-dropdown class="btn-group">
                                    <button type="button" class="btn btn-xs btn-primary" data-toggle="dropdown">
                                        Actions <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu" role="menu">
                                        <li><a ng-click="disablePayment(payment)">Disable</a></li>
                                    </ul>
                                </uib-dropdown>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>


    <div class="col-sm-12">
        <div class="ibox">
            <div class="ibox-title">
                <h5>Audit</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date Time</th>
                            <th>Action</th>
                            <th>Field</th>
                            <th>From</th>
                            <th>To</th>
                            <th>User</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="!auditEvents.length">
                            <td colspan="99" align="center">No Audits Events Found</td>
                        </tr>
                        <tr ng-repeat="audit in auditEvents">
                            <td>{{audit.createdAt | date:'short'}}</td>
                            <td>{{audit.changeType}}</td>
                            <td>{{audit.field}}</td>
                            <td>{{parseValue(audit.fromValue, audit.field || audit.changeType)}}</td>
                            <td>{{parseValue(audit.toValue, audit.field)}}</td>
                            <td>{{audit.user ? audit.user.name : 'SYSTEM'}}</td>
                            <td>
                                <button ng-if="audit.changeType.indexOf('Recurring') === -1 && audit.field !== 'paymentDate'" type="button" class="btn btn-xs" ng-click="undo(audit)">Undo</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-sm-12">
        <div class="ibox">
            <div class="ibox-title">
                <h5>Note History</h5>
                <span style="float:right">
                    <button type="button" ng-click="addNote()" class="btn btn-xs btn-primary">Add Note</button>
                </span>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <tr ng-if="notes.length">
                            <th style="min-width: 120px;">Date Time</th>
                            <th>Note</th>
                            <th>User</th>
                        </tr>
                        <tr ng-if="!notes.length">
                            <td colspan="99" align="center">No Notes</td>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-repeat="note in notes">
                            <td>{{note.createdAt | date:'short'}}</td>
                            <td>{{note.note}}</td>
                            <td>{{note.user ? note.user.name : ''}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>


</div>