<div class="modal-body">
    <div class="row" ng-if="existingCard.id">
        <div class="col-sm-12">
            <p>Existing Card Ending: ####-####-####-{{existingCard.maskedCardNumber}} Expiration: {{existingCard.expirationDate}}</p>
        </div>
    </div>
    <div class="row" ng-if="existingCard && !existingCard.id && !validtoken">
        <div class="col-sm-12">
            <p>No existing card on file</p>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-cardNumDiv">
                <label>Credit Card Number</label>
                <input ng-if="validtoken" disabled ng-model="cardToken.cardNumber" />
                <p ng-if="carderrors['tsep-cardNum']" class="text-danger">{{carderrors['tsep-cardNum']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardNumber" autocomplete="off"
                     ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-datepickerDiv">
                <label>Expiry Date</label>
                <input ng-if="validtoken" disabled ng-model="cardToken.expirationDate" />
                <p ng-if="carderrors['tsep-datepicker']" class="text-danger">{{carderrors['tsep-datepicker']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.expirationDate" placeholder="MMYY"
                     autocomplete="off" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group tsys" id="tsep-cvv2Div">
                <label>CVV2</label>
                <!-- <input ng-if="validtoken" disabled /> -->
                <p ng-if="carderrors['tsep-cvv2']" class="text-danger">{{carderrors['tsep-cvv2']}}</p>
                <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardHolderName" autocomplete="off"
                     ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
            </div>
        </div>
        <div class="col-md-3" style="padding-top: 23px;">
            <div ng-show="testingTsys" style="display: inline-block; vertical-align: middle;">
                <div class="sk-spinner sk-spinner-wave">
                    <div class="sk-rect1"></div>
                    <div class="sk-rect2"></div>
                    <div class="sk-rect3"></div>
                    <div class="sk-rect4"></div>
                    <div class="sk-rect5"></div>
                </div>
            </div>
            <button ng-show="!testingTsys" ng-if="!validtoken" ng-disabled="cardToken.tsepToken || saving" class="btn btn-success">Check Card</button>
            <!-- <button ng-if="!validtoken" class="btn btn-primary" ng-click="saveCard()" ng-disabled="!cardToken || !cardToken.tsepToken">Save Card</button> -->
            <button ng-show="!testingTsys" ng-if="validtoken" class="btn btn-grey" ng-click="clearCard()">Clear Card</button>
        </div>
        <div class="col-md-12">
            <span class="text-danger">{{carderrors.master}}</span> <span class="text-danger">{{tokenerror}}</span>
        </div>
    </div>
</div>

<div class="modal-footer" style="clear:both;">
    <button class="btn btn-success" ng-click="close()">Close</button>
</div>