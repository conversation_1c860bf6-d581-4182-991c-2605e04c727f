<div class="ibox">
	<div class="ibox-content">
		<div class="form-group">
			<button class="btn btn-success" type="button" ng-click="search()">Search</button>
		</div>
		<div class="form-group">
			<div ng-if="filters && !isEmptyObject(filters)" class="filters">
				<h4>Filters: </h4>
				<button type="button" class="btn btn-primary btn-xs" ng-repeat="(key, prop) in filters" ng-click="removeFilter(key)" style="margin-right: 5px;">
					{{friendly(key)}} : {{prop}} <i class="fa fa-times"></i>
				</button>
				<button type="button" class="btn btn-warning btn-xs" ng-click="removeAllFilters()">
					clear all <i class="fa fa-times"></i>
				</button>
			</div>
		</div>
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click='filterChanged("invoice.leadid")'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.clientRef")'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("invoice.id")'>
						Created Date/Time
						<span ng-show='sortType == "invoice.id" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "invoice.id" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="!campaignId">
					<span class="clickable" ng-click='filterChanged("client.name")'>
						Client
						<span ng-show='sortType == "client.name" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "client.name" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th ng-if="!campaignId">
					<span class="clickable" ng-click='filterChanged("campaign.name")'>
						Campaign
						<span ng-show='sortType == "campaign.name" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "campaign.name" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.first_name")'>
						Lead
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>Stage</th>
				<th>
					<span class="clickable" ng-click='filterChanged("invoiceType")'>
						Payment Type
						<span ng-show='sortType == "invoiceType" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "invoiceType" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("grandTotal")'>
						Total
						<span ng-show='sortType == "grandTotal" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "grandTotal" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("amountRemaining")'>
						Total To Pay
						<span ng-show='sortType == "amountRemaining" && sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "amountRemaining" && !sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>Written Off</th>
				<th></th>
			</thead>
			<tbody>
				<tr dir-paginate="invoice in invoices | itemsPerPage: 20" total-items="totalInvoices" current-page="pagination.current">
					<td>{{ invoice.lead.id }}</td>
					<td>{{ invoice.lead.clientRef }}</td>
					<td>{{ formatDate(invoice.createdAt) }}</td>
					<td ng-if="!campaignId">{{ invoice.client.name }}</td>
					<td ng-if="!campaignId">{{ invoice.campaign.name }}</td>
					<td>{{ invoice.lead.first_name }} {{ invoice.lead.last_name }}</td>
					<td>{{ invoice.campaignstage ? invoice.campaignstage.name : 'No Stage' }}</td>
					<td>{{ invoice.invoiceType }}</td>
					<td>{{ invoice.grandTotal | currency:"$":2 }}</td>
					<td>{{ invoice.amountRemaining | currency:"$":2 }}</td>
					<td ng-if="invoice.writtenOffAmount">{{ invoice.writtenOffAmount | currency:"$":2 }}</td>
					<td ng-if="!invoice.writtenOffAmount">{{ invoice.writtenOff ? 'Yes' : 'No' }}</td>
					<td>
						<div class="btn-group">
							<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-href="#/collections/invoices/{{ invoice.id }}/history">History</a></li>
								<li><a ng-disabled="!invoice.amountRemaining" href="" ng-click="generate(invoice)">Generate PDF</a></li>
								<li><a ng-disabled="!invoice.amountRemaining" href="" ng-click="generateTestInvoice(invoice)">Generate Test PDF</a></li>
								<li><a ng-href="#/collections/invoices/{{ invoice.id }}/exports">View PDFs</a></li>
								<li><a ng-href="#/collections/invoices/{{ invoice.id }}/payments">View Payments</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin" ng-disabled="!invoice.amountRemaining" ng-click="addPayment(invoice)">Add Payment</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin" ng-disabled="!invoice.amountRemaining" ng-click="writeoff(invoice)">Write Off</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin" ng-click="badPayment(invoice)">Bad Payment</a></li>
								<li><a ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin" ng-click="fixPayment(invoice)">Change Payment</a></li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-if="!invoices.length">
					<td colspan="99" align="center">No Invoices Created</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
	</div>
</div>