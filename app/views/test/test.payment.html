<div class="col-sm-6 col-md-4 col-sm-offset-3 col-md-offset-4" style="margin-top: 30px;">
    <div class="ibox">
        <div class="ibox-content">
            <div class="tab-pane">
                <div class="form-group tsys" id="tsep-cardNumDiv">
                    <label for="">Credit Card Number</label>
                    <span ng-if="errors['tsep-cardNum']">{{errors['tsep-cardNum']}}</span>
                    <!-- <input class="form-control" ng-model="creditCard.number" placeholder="XXXX-XXXX-XXXX-XXXX" /> -->
                </div>
                <div class="form-group tsys" id="tsep-datepickerDiv">
                    <label for="">Credit Card Expiry</label>
                    <span ng-if="errors['tsep-datepicker']">{{errors['tsep-datepicker']}}</span>
                    <!-- <input class="form-control" ng-model="creditCard.expiry" placeholder="MMYY" /> -->
                </div>
                <div class="form-group tsys" id="tsep-cvv2Div">
                    <label for="">Credit Card CVV</label>
                    <span ng-if="errors['tsep-cvv2']">{{errors['tsep-cvv2']}}</span>
                    <!-- <input class="form-control" ng-model="creditCard.pin" placeholder="###" /> -->
                </div>
                <div>
                    <button type="button" class="btn btn-primary" ng-click="send()">Finish</button>
                </div>
                <div class="form-group" ng-if="cardtoken.tsepToken">
                    <p style="padding-top: 20px; font-weight: bold; color: #18A689">Token: {{stringify(cardtoken)}}</p>
                </div>
                <div class="form-group" ng-if="errors.master">
                    <p style="padding-top: 20px; font-weight: bold; color: #cc0000">Token Error: {{errors.master}}</p>
                </div>
            </div>
        </div>
    </div>
</div>