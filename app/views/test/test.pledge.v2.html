<div class="modal-header" style="padding: 15px 15px 25px 15px;">
    <span><strong>KAOS Id:</strong> {{lead.id}}</span>
    <span><strong>Client Ref:</strong> {{lead.clientRef}}</span>
    <span><strong>Lead Name:</strong> {{lead.first_name}} {{lead.last_name}}</span>
    <div style="float:right">
        <button class="btn btn-primary" style="margin-bottom: 10px;" ng-click="validateLead()">Validate Donor Details</button>
    </div>
</div>
<div class="modal-body" modaldraggable style="overflow-x: hidden;overflow-y: scroll;max-height:80vh;">
    <form novalidate class="form">
        <div class="col-md-3">
            <div class="form-group">
                <label>Gift Amount</label>
                <input type="number" min="0" step="0.01" class="form-control" ng-model="formData.giftAmount" ng-change="updatePaymentCount()" />
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Verify Gift Amount</label>
                <input type="number" min="0" step="0.01" class="form-control" ng-model="formData.verifyGiftAmount" />
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Decision Maker</label>
                <input type="text" class="form-control" placeholder="Decision Maker" ng-model="formData.decisionMaker" />
            </div>
        </div>
        <div class="col-md-3">
            <div class="form-group">
                <label>Gift Matching Company</label>
                <input type="text" class="form-control" placeholder="Gift Matching Company" ng-model="formData.giftMatchingCompany" />
            </div>
        </div>
        <div class="col-md-4">
            <label> <input type="checkbox" ng-model="formData.declineBenefits"> Decline Benefits? </label>
        </div>
        <div class="col-md-4">
            <label style="margin-right:10px;">Free Tix Count</label>
            <label style="margin-right:10px;">
                <input type="radio" ng-model="formData.freeTickets" value="0"> 0
            </label>
            <label style="margin-right:10px;">
                <input type="radio" ng-model="formData.freeTickets" value="2"> 2
            </label>
            <label style="margin-right:10px;">
                <input type="radio" ng-model="formData.freeTickets" value="4"> 4
            </label>
        </div>
        <div class="col-md-4" ng-if="client.membershipCard">
            <label> <input type="checkbox" ng-model="formData.newMembershipCard"> New Membership Card Required? </label>
        </div>
        <div class="hr-line-dashed" style="clear:both;"></div>
        <div class="col-lg-12">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Payment Type</label>
                        <select class="form-control" ng-model="formData.paymentType" ng-change="changePaymentType()">
                            <option value="Credit Card">Credit Card</option>
                            <option value="Invoice">Invoice</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="row" ng-if="formData.paymentType === 'Credit Card'">
                <div class="col-md-3">
                    <div class="form-group tsys" id="tsep-cardNumDiv">
                        <label>Credit Card Number</label>
                        <input ng-if="validtoken" disabled ng-model="cardToken.cardNumber" />
                        <p ng-if="carderrors['tsep-cardNum']" class="text-danger">{{carderrors['tsep-cardNum']}}</p>
                        <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardNumber" autocomplete="off"
                             ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group tsys" id="tsep-datepickerDiv">
                        <label>Expiry Date</label>
                        <input ng-if="validtoken" disabled ng-model="cardToken.expirationDate" />
                        <p ng-if="carderrors['tsep-datepicker']" class="text-danger">{{carderrors['tsep-datepicker']}}</p>
                        <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.expirationDate" placeholder="MMYY"
                             autocomplete="off" ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group tsys" id="tsep-cvv2Div">
                        <label>CVV2</label>
                        <!-- <input ng-if="validtoken" disabled /> -->
                        <p ng-if="carderrors['tsep-cvv2']" class="text-danger">{{carderrors['tsep-cvv2']}}</p>
                        <!-- <input class="form-control" type="text" ng-disabled="validtoken" ng-model="cardToken.cardHolderName" autocomplete="off"
                             ng-focus="phone.pauseRecording()" ng-blur="phone.resumeRecording()" /> -->
                    </div>
                </div>
                <div class="col-md-3" style="padding-top: 23px;">
                    <div ng-show="testingTsys" style="display: inline-block; vertical-align: middle;">
                        <div class="sk-spinner sk-spinner-wave">
                            <div class="sk-rect1"></div>
                            <div class="sk-rect2"></div>
                            <div class="sk-rect3"></div>
                            <div class="sk-rect4"></div>
                            <div class="sk-rect5"></div>
                        </div>
                    </div>
                    <button ng-show="!testingTsys" ng-if="!validtoken" ng-disabled="cardToken.tsepToken || saving" class="btn btn-success">Check Card</button>
                    <!-- <button ng-if="!validtoken" class="btn btn-primary" ng-click="saveCard()" ng-disabled="!cardToken || !cardToken.tsepToken">Save Card</button> -->
                    <button ng-show="!testingTsys" ng-if="validtoken" class="btn btn-grey" ng-click="clearCard()">Clear Card</button>
                </div>
                <div class="col-md-12">
                    <span class="text-danger">{{carderrors.master}}</span> <span ng-hide="carderrors.master" class="text-danger">{{tokenerror}}</span>
                </div>
            </div>
            <div class="row" ng-if="formData.paymentType === 'Credit Card'">
                <div class="col-md-4">
                    <label> <input type="checkbox" ng-model="formData.splitPayments" ng-change="updatePaymentCount()">
                        Split/Deferred Payment?</label>
                </div>
            </div>
            <div class="hr-line-dashed" style="clear:both" ng-if="formData.splitPayments"></div>
            <div class="row" ng-if="formData.splitPayments">
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Initial Payment Date</label>
                        <div class="input-group">
                            <input type="text" class="form-control" min-date="initialOptions.minDate" max-date="initialOptions.maxDate" uib-datepicker-popup="{{format}}" ng-model="formData.initialPaymentDate" is-open="dates.initial" ng-change="updatePaymentCount()" close-text="Close" required />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" ng-click="openDate('initial', $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>No of Payments</label>
                        <input type="number" class="form-control" ng-model="payments.count" ng-change="updatePaymentCount()" min="1" max="12" />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Payment every</label>
                        <input type="number" min="1" class="form-control" ng-model="payments.every" ng-change="updatePaymentCount()" />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>Unit</label>
                        <select type="number" class="form-control" ng-model="payments.unit" ng-change="updatePaymentCount()">
                            <option value="months">Months</option>
                        </select>
                    </div>
                </div>
            </div>
            <div ng-if="formData.splitPayments && payments.count">
                <div class="row">
                    <div class="col-md-1"></div>
                    <div class="col-md-5">
                        <strong>Amount</strong>
                    </div>
                    <div class="col-md-6">
                        <strong>Payment Date</strong>
                    </div>
                </div>
                <div class="row" ng-repeat="payment in payments.values track by $index">
                    <div class="col-md-1"><strong>{{$index + 1}}</strong></div>
                    <div class="col-md-5">
                        <div class="form-group">
                            <input type="number" step="0.01" class="form-control" ng-model="payment.amount" min="0" ng-change="updatePayment()" />
                        </div>
                    </div>
                    <div ng-class="{'col-md-6': !paymentsBroken, 'col-md-5': paymentsBroken}" class="col-md-6">
                        <div class="form-group">
                            <div class="input-group">
                                <input type="text" class="form-control" uib-datepicker-popup="{{format}}" min-date="formData.initialPaymentDate" ng-model="payment.date" ng-disabled="!$index" is-open="dates[$index + '']" close-text="Close" required />
                                <span class="input-group-btn">
                                    <button type="button" class="btn btn-default" ng-disabled="!$index" ng-click="openDate($index, $event)"><i class="glyphicon glyphicon-calendar"></i></button>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div ng-if="paymentsBroken">
                        <button class="btn" ng-click="fixPayments($index)">Fix</button>
                    </div>
                </div>
                <div class="row m-b-sm">
                    <div class="col-md-2"><strong>Total</strong></div>
                    <div class="col-md-4">
                        {{payments.total | currency:'$'}}
                    </div>
                    <div class="col-md-2"><strong>Difference</strong></div>
                    <div class="col-md-4">
                        <span ng-class="{'text-danger': paymentsBroken, 'text-heavy': paymentsBroken}">{{payments.difference}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="hr-line-dashed" style="clear:both" ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && lead.email"></div>
        <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && lead.email">
            <div class="col-lg-12">
                <h3>Delivery Details</h3>
            </div>
            <div class="col-lg-12">
                <label><input ng-model="verify.sendByMail" type="checkbox" value=""> Send invoice via paper
                    instead of e-mail</label>
            </div>
        </div>
        <div class="hr-line-dashed" style="clear:both"></div>
        <div>
            <div class="col-lg-12">
                <div class="row">
                    <div class="col-md-6">
                        <h3 style="margin-right:20px; padding-top: 4px;">Verification</h3>
                        <div>
                            <label><input ng-model="formData.requiresFollowUp" type="checkbox" value=""> Requires immediate follow-up</label>
                        </div>
                        <div>
                            <label><input ng-model="verify.giftAmount" type="checkbox" value=""> Did you
                                verify the dollar amount of the gift with the donor?</label>
                        </div>
                        <div>
                            <label><input ng-model="verify.emailAddress" type="checkbox" value=""> Did you
                                ask donor for an e-mail address?</label>
                        </div>
                        <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType === 'email' && lead.email && !verify.sendByMail">
                            <label><input ng-model="verify.invoiceDelivery" type="checkbox" value=""> Did
                                you tell the donor to expect their invoice via email (from {{ client.returnEmail
                                }})?</label>
                        </div>
                        <div ng-if="formData.paymentType === 'Invoice' && client.defaultInvoiceType !== 'email'">
                            <label><input ng-model="verify.invoiceDelivery" type="checkbox" value=""> Did
                                you tell the donor to expect their invoice via US mail?</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Call Notes</label>
                            <textarea class="form-control" rows="3" ng-model="notes"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer" style="clear:both;">
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
    <button class="btn btn-success" ng-disabled="disableOkButton" ng-click="ok()">Save & Wrap-up</button>
</div>