<div class="row">
    <div class="col-sm-12">
        <div class="text-center p-md">
            <button class="btn" ng-class="{'btn-primary': show == 'campaign'}" ng-click="show = 'campaign'">Campaign Details</button>
            <button class="btn" ng-class="{'btn-primary': show == 'stage'}" ng-click="show = 'stage'">Stages</button>
            <button class="btn" ng-class="{'btn-primary': show == 'workflow'}" ng-click="show = 'workflow'">Workflows</button>
            <button class="btn" ng-class="{'btn-primary': show == 'agent'}" ng-click="show = 'agent'">Agents</button>
            <button class="btn" ng-class="{'btn-primary': show == 'projections'}" ng-click="show = 'projections'">Projections</button>
            <button class="btn" ng-class="{'btn-primary': show == 'targets'}" ng-click="show = 'targets'">Agent Targets</button>
            <button class="btn" ng-class="{'btn-primary': show == 'rules'}" ng-click="show = 'rules'">Dialing Rules</button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-12" ng-show="show == 'campaign'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Campaign Details</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody>
                        <tr ng-repeat="audit in campaignChanges">
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'stage'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Campaign Stages</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Stage</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(key, value) in stageChanges">
                        <tr>
                            <td colspan="{{value.display ? 1 : 99}}" class="clickable" ng-click="value.display = !value.display"><span class="font-bold text-navy">{{key}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in value | orderBy:'createdAt':true" ng-show="value.display">
                            <td></td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'workflow'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Campaign Workflows</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Stage</th>
                        <th>Wrapup</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(stageName, stage) in dispositionChanges">
                        <tr>
                            <td colspan="{{stage.display ? 1 : 99}}" class="clickable" ng-click="stage.display = !stage.display"><span class="font-bold text-navy">{{stageName}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in stage | orderBy:'createdAt':true" ng-show="stage.display">
                            <td></td>
                            <td>{{audit.detail.wrapup}}</td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'agent'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Agent Assignment</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Stage</th>
                        <th>Agent</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(stageName, stage) in agentChanges">
                        <tr>
                            <td colspan="{{stage.display ? 1 : 99}}" class="clickable" ng-click="stage.display = !stage.display"><span class="font-bold text-navy">{{stageName}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in stage | orderBy:'createdAt':true" ng-show="stage.display">
                            <td></td>
                            <td>{{audit.detail.agent}}</td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'projections'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Campaign Projections</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Lead Type</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(leadTypeName, leadType) in projectionChanges">
                        <tr>
                            <td colspan="{{leadType.display ? 1 : 99}}" class="clickable" ng-click="leadType.display = !leadType.display"><span class="font-bold text-navy">{{leadTypeName}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in leadType | orderBy:'createdAt':true" ng-show="leadType.display">
                            <td></td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'targets'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Agent Targets</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Agent</th>
                        <th>Week</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(agentName, agent) in targetChanges">
                        <tr>
                            <td colspan="{{agent.display ? 1 : 99}}" class="clickable" ng-click="agent.display = !agent.display"><span class="font-bold text-navy">{{agentName}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in agent | orderBy:'createdAt':true" ng-show="agent.display">
                            <td></td>
                            <td>{{audit.detail.week}}</td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-sm-12" ng-show="show == 'rules'">
        <div class="ibox float-e-margins">
            <div class="ibox-title">
                <h5>Dialing Rules</h5>
            </div>
            <div class="ibox-content">
                <table class="table">
                    <thead>
                        <th style="width: 15%;">Lead Type</th>
                        <th>Rule</th>
                        <th>Date</th>
                        <th>Field</th>
                        <th>From</th>
                        <th>To</th>
                    </thead>
                    <tbody ng-repeat="(stageName, stage) in dtChanges">
                        <tr>
                            <td colspan="{{stage.display ? 1 : 99}}" class="clickable" ng-click="stage.display = !stage.display"><span class="font-bold text-navy">{{stageName}}</span></td>
                        </tr>
                        <tr ng-repeat="audit in stage | orderBy:'createdAt':true" ng-show="stage.display">
                            <td></td>
                            <td>{{audit.detail.rule}}</td>
                            <td>{{audit.createdAt | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                            <td>{{processField(audit.field)}}</td>
                            <td>{{audit.from || '-blank-'}}</td>
                            <td>{{audit.to || '-blank-'}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>