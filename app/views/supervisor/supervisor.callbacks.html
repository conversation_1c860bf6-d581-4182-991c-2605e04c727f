<div class="ibox">
	<div class="ibox-content">
		<button type="button" class="btn btn-success" ng-click="search()">Search</button>
		<div class="form-group" ng-if="friendlyFilters.length" >
			<div class="filters">
				<h4>Filters: </h4>
				<button type="button" class="btn btn-primary btn-xs" ng-repeat="filter in friendlyFilters" ng-click="removeFilter(filter, $index)" style="margin-right: 5px;">
					{{filter.name}}: {{filter.value}} <i class="fa fa-times"></i>
				</button>
				<button type="button" class="btn btn-warning btn-xs" ng-click="removeAllFilters()">
					clear all <i class="fa fa-times"></i>
				</button>
			</div>	
		</div>		
	</div>
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click="changeFilter('lead.id')">
						Kaos Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('lead.clientRef')">
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('startDateTime')">
						Call From
						<span ng-show='sortType == "startDateTime" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "startDateTime" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('endDateTime')">
						Call To
						<span ng-show='sortType == "endDateTime" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "endDateTime" && sortReverse' class="fa fa-caret-up"></span>
					</span>				
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('lead.first_name')">
						Lead First Name
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('lead.last_name')">
						Lead Last Name
						<span ng-show='sortType == "lead.last_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.last_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					Phone Number
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('agent.name')">
						Agent
						<span ng-show='sortType == "agent.name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "agent.name" && sortReverse' class="fa fa-caret-up"></span>
					</span>				
				</th>
				<th>
					<span class="clickable" ng-click="changeFilter('callresult.notes')">
						Notes
						<span ng-show='sortType == "callresult.notes" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callresult.notes" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th></th>
			</thead>
			<tbody>
				<tr dir-paginate="callback in callbacks | itemsPerPage: 20" total-items="total" current-page="pagination.current">
					<td>{{ callback.lead.id }}</td>
					<td>{{ callback.lead.clientRef }}</td>
					<td>{{ formatDate(callback.startDateTime) }}</td>
					<td>{{ formatDate(callback.endDateTime) }}</td>
					<td>{{ callback.lead.first_name }}</td>
					<td>{{ callback.lead.last_name }}</td>
					<td>{{ callback.lead[callback.phone] }}</td>
					<td>{{ callback.agent.name }}</td>
					<td>{{ callback.callresult.notes }}</td>
					<td>
						<uib-dropdown class="btn-group dropdown">
							<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
								Actions <span class="caret"></span>
							</button>
							<ul class="dropdown-menu" role="menu">
								<li><a ng-href="/#/admin/leads/{{callback.lead.id}}/analysis">Analyze</a></li>
								<li><a ng-click="edit(callback)">Edit</a></li>
								<li><a ng-click="delete(callback)">Delete</a></li>
							</ul>
						</uib-dropdown>
					</td>
				</tr>
				<tr ng-if="!callbacks.length"> 
					<td colspan="99">No Callbacks Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
	</div>
</div>