<div class="ibox">
	<div class="ibox-content">
		<div class="row">
			<div class="col-sm-5 m-b-xs">
				<select class="form-control" ng-model="selectedCampaignId" ng-options="campaign.id as campaign.name for campaign in campaigns" required>
	            	<option value="">-- Select Campaign --</option>
	            </select>
            </div>
		</div>

		<div class="tab-pane">
	        <table class="table" ng-show="!!selectedCampaign">
	        	<thead class="table">
	        		<th width="100px"></th>
	        		<th>Name</th>
	        		<th>Change Count</th>
	        	</thead>
	        	<tbody ng-repeat="lead in selectedCampaign.changedLeads">
	        		<tr>
	        			<td>
	        				<button class="btn btn-xs" ng-click="lead.shown = !lead.shown">{{ lead.shown ? "Hide" : "Show" }}</button> 
	        			</td>
	        			<td>{{ lead.first_name }} {{ lead.last_name }}</td>
        				<td>Changes: {{ lead.leadaudits.length }}</td>
        			</tr>
	        		<tr ng-repeat="audit in lead.leadaudits" ng-show="!!lead.shown">
	        			<td></td>
	        			<td colspan="99">
	        				<strong>{{ audit.field }}:</strong> changed from <strong>{{ audit.previousValue || "--BLANK--" }}</strong> to <strong>{{ audit.newValue || "--BLANK--" }}</strong>
	        				<button style="margin-left:20px;" class="btn btn-xs btn-danger" ng-click="undoAudit(audit, lead)">Undo</button>
        				</td>
	        		</tr>
	        	</tbody>
	        	<tr ng-if="!selectedCampaign.allShown">
	        		<td colspan="99" style="text-align: center;"><button type="button" class="btn btn-sm btn-success" ng-click="getMore()">Show More</button></td>
	        	</tr>
	        	<tr ng-if="!selectedCampaign.changedLeads.length">
        			<td colspan="99">No changes detected for leads on this campaign</td>
	        	</tr>
	        </table>
	        <p style="padding-top:20px;" ng-show="!selectedCampaign">Please select a campaign from the list above.</p>
		</div>
	</div>
</div>