<div class="ibox">
	<div class="ibox-content form-inline">
		<div class="form-group">
			<!--
			<label class="control-label" style="margin-right: 10px;">Dialing Rule:</label>
			<select class="form-control" ng-model="selectedDialingRule" ng-options="rule as rule.name for rule in dialingRules track by rule.id" style="width: 250px; margin-right: 20px;">
			</select>
			-->
			
			<label class="control-label" style="margin-right: 10px;">Agent:</label>
			<select class="form-control" ng-model="selectedAgent" ng-options="agent as agent.name for agent in agents track by agent.id" style="width: 200px; margin-right: 20px;">
				<option value="">All Agents</option>
			</select>
			
			<button type="button" class="btn btn-primary" ng-click="update()">Refresh</button>
		</div>
		&nbsp; When selecting "All Agents", portfolio & non-portfolio leads will be displayed. When selecting a single agent, only their portfolio leads will be displayed.
	</div>
	<div class="ibox-content">
		<!-- Enhanced table with sticky headers and natural styling -->
		<style>
			.call-attempts-table {
				width: 100%;
				border-collapse: separate;
				border-spacing: 0;
				font-size: 13px;
				background-color: white;
			}

			.call-attempts-table thead th {
				background-color: #f8f9fa;
				border-bottom: 2px solid #e9ecef;
				padding: 12px 8px;
				text-align: center;
				font-weight: bold;
				position: sticky;
				top: 0;
				z-index: 10;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			}

			.call-attempts-table thead th:first-child {
				position: sticky;
				left: 0;
				z-index: 11;
				box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
			}

			.call-attempts-table tbody td {
				border-bottom: 1px solid #f1f3f4;
				padding: 10px 8px;
				text-align: center;
				vertical-align: middle;
			}

			.call-attempts-table tbody td:first-child {
				position: sticky;
				left: 0;
				background-color: inherit;
				z-index: 9;
				box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
			}

			.call-attempts-table .stage-row {
				background-color: #f1f3f4;
				font-weight: bold;
				border-bottom: 2px solid #dee2e6;
			}

			.call-attempts-table .group-row {
				background-color: #fefefe;
				font-weight: 600;
				border-bottom: 1px solid #e9ecef;
			}

			.call-attempts-table .type-row {
				background-color: #f9f9f9;
				border-bottom: 1px solid #f1f3f4;
			}

			.call-attempts-table .total-row {
				background-color: #f5f5f5;
				font-weight: bold;
				border-top: 2px solid #6c757d;
				border-bottom: 2px solid #6c757d;
			}

			.call-attempts-table .no-stage-row {
				background-color: #f8f9fa;
				font-weight: 600;
				border-top: 1px solid #e9ecef;
				border-bottom: 1px solid #e9ecef;
			}

			.call-attempts-table .name-cell {
				text-align: left;
				padding-left: 15px;
			}

			.call-attempts-table .stage-name {
				padding-left: 15px;
			}

			.call-attempts-table .group-name {
				padding-left: 35px;
			}

			.call-attempts-table .type-name {
				padding-left: 55px;
			}

			.table-container {
				overflow-x: auto;
				max-height: 80vh;
				border: 1px solid #e9ecef;
				border-radius: 6px;
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
			}

			.section-title {
				color: #333;
				padding: 15px 0 10px 0;
				margin-bottom: 10px;
				font-weight: bold;
				font-size: 16px;
				border-bottom: 1px solid #e9ecef;
			}
		</style>

		<div class="section-title">
			Call Attempt Breakdown - {{ campaign.name }}
			<span ng-show="hasUpdated && capturedAgent && capturedAgent.id" style="font-weight: normal; color: #6c757d; margin-left: 15px;">
				(Portfolio for: {{ capturedAgent.name }})
			</span>
		</div>

		<!-- No data message for agent portfolio with no results -->
		<div ng-show="hasUpdated && capturedAgent && capturedAgent.id && (!stages || stages.length === 0)"
			 style="text-align: center; padding: 40px; color: #6c757d; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; margin-bottom: 20px;">
			<i class="fa fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
			<div style="font-size: 16px; font-weight: 500;">No Data Available</div>
			<div style="font-size: 14px; margin-top: 5px;">
				No portfolio data found for {{ capturedAgent.name }}. This agent may not have any assigned leads in this campaign.
			</div>
		</div>

		<!-- No data message for campaign with no results at all -->
		<div ng-show="hasUpdated && (!capturedAgent || !capturedAgent.id) && (!stages || stages.length === 0)"
			 style="text-align: center; padding: 40px; color: #6c757d; background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 6px; margin-bottom: 20px;">
			<i class="fa fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
			<div style="font-size: 16px; font-weight: 500;">No Data Available</div>
			<div style="font-size: 14px; margin-top: 5px;">
				No call attempt data found for this campaign. The campaign may not have any leads or stages configured.
			</div>
		</div>

		<div class="table-container" ng-show="stages && stages.length > 0">
			<table class="call-attempts-table">
				<thead>
					<tr>
						<th style="width: 350px; min-width: 350px; text-align: left;">Stage / RG / LT</th>
						<th style="width: 80px;"># of<br>Leads</th>
						<th style="width: 100px;"># of Call<br>Attempts<br>Remaining</th>
						<th style="width: 100px;"># of<br>Exhausted<br>Leads</th>
						<th style="width: 100px;"># of Bad<br>Phone<br>Numbers</th>
						<th style="width: 80px;"># of<br>Callbacks</th>
						<th style="width: 80px;"># of Lead<br>Delays</th>
						<th style="width: 100px;"># of<br>Suppressed<br>Leads</th>
						<th style="width: 80px;"># of<br>Viable<br>Leads</th>
						<th style="width: 60px;">0<br>Dials</th>
						<th style="width: 60px;">1<br>Dial</th>
						<th style="width: 60px;">2-4<br>Dials</th>
						<th style="width: 60px;">5-19<br>Dials</th>
						<th style="width: 60px;">20+<br>Dials</th>
					</tr>
				</thead>
				<tbody>

				<!-- Campaign Stage View (agent selection just filters the data) -->
				<tr ng-repeat-start="stage in stages" class="stage-row">
					<td class="name-cell stage-name">
						<i class="fa fa-folder" style="margin-right: 8px; color: #6c757d;"></i>
						<strong>STAGE: {{ stage.name }}</strong>
						<button type="button" style="margin-left: 10px;" class="btn btn-success btn-xs" ng-click="setStageAgents(stage)">Assign Agents</button>
					</td>
					<td><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/leads" ng-click="showLeads(stage)">{{ stage.leads || 0 }}</a></td>
					<td>{{ stage.callAttempts || 0 }}</td>
					<td>{{ stage.exhausted || 0}} ({{ 100 - (((stage.leads - (stage.exhausted || 0)) / stage.leads) * 100) | number:0 }}%)</td>
					<td>{{ stage.badNumber || 0 }}</td>
					<td>{{ stage.callback || 0 }}</td>
					<td>{{ stage.dontContactUntil || 0 }}</td>
					<td>{{ stage.suppressions || 0 }}</td>
					<td>{{ stage.viable || 0 }}</td>
					<td>{{ stage.dialAttemptBuckets.zero || 0 }}</td>
					<td>{{ stage.dialAttemptBuckets.one || 0 }}</td>
					<td>{{ stage.dialAttemptBuckets.twoToFour || 0 }}</td>
					<td>{{ stage.dialAttemptBuckets.fiveToNineteen || 0 }}</td>
					<td>{{ stage.dialAttemptBuckets.twentyPlus || 0 }}</td>
				</tr>

				<!-- Reporting Groups within Stage -->
				<tr ng-repeat-start="reportingGroup in stage.reportingGroups" class="group-row">
					<td class="name-cell group-name">
						<i class="fa fa-layer-group" style="margin-right: 6px; color: #6c757d;"></i>
						<strong>RG: {{ reportingGroup.name }}</strong>
					</td>
					<td>{{ reportingGroup.leads || 0 }}</td>
					<td>{{ reportingGroup.callAttempts || 0 }}</td>
					<td>{{ reportingGroup.exhausted || 0}} ({{ (100 - (((reportingGroup.leads - (reportingGroup.exhausted || 0)) / reportingGroup.leads) * 100) | number:0)}}%)</td>
					<td>{{ reportingGroup.badNumber || 0 }}</td>
					<td>{{ reportingGroup.callback || 0 }}</td>
					<td>{{ reportingGroup.dontContactUntil || 0 }}</td>
					<td>{{ reportingGroup.suppressions || 0 }}</td>
					<td>{{ reportingGroup.viable || 0 }}</td>
					<td>{{ reportingGroup.dialAttemptBuckets.zero || 0 }}</td>
					<td>{{ reportingGroup.dialAttemptBuckets.one || 0 }}</td>
					<td>{{ reportingGroup.dialAttemptBuckets.twoToFour || 0 }}</td>
					<td>{{ reportingGroup.dialAttemptBuckets.fiveToNineteen || 0 }}</td>
					<td>{{ reportingGroup.dialAttemptBuckets.twentyPlus || 0 }}</td>
				</tr>

				<!-- Lead Types within Reporting Group -->
				<tr ng-repeat="leadType in reportingGroup.leadTypes" ng-repeat-end class="type-row">
					<td class="name-cell type-name">
						<i class="fa fa-tag" style="margin-right: 4px; color: #6c757d;"></i>
						LT: {{ leadType.name }}
					</td>
					<td><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/skill/{{leadType.id}}/leads">{{ leadType.leads || 0}}</a></td>
					<td>{{ leadType.callAttempts || 0 }}</td>
					<td>{{ leadType.exhausted || 0}} ({{ (100 - (((leadType.leads - (leadType.exhausted || 0)) / leadType.leads) * 100) | number:0)}}%)</td>
					<td>{{ leadType.badNumber || 0 }}</td>
					<td>{{ leadType.callback || 0 }}</td>
					<td>{{ leadType.dontContactUntil || 0 }}</td>
					<td>{{ leadType.suppressions || 0 }}</td>
					<td>{{ leadType.viable || 0 }}</td>
					<td>{{ leadType.dialAttemptBuckets.zero || 0 }}</td>
					<td>{{ leadType.dialAttemptBuckets.one || 0 }}</td>
					<td>{{ leadType.dialAttemptBuckets.twoToFour || 0 }}</td>
					<td>{{ leadType.dialAttemptBuckets.fiveToNineteen || 0 }}</td>
					<td>{{ leadType.dialAttemptBuckets.twentyPlus || 0 }}</td>
				</tr>
				<tr ng-repeat-end></tr>
			</div>
				</tbody>
			</table>
		</div>

		<!-- Summary Section -->
		<div ng-show="stages && stages.length > 0" style="margin-top: 30px;">
			<div class="table-container">
				<table class="call-attempts-table">
					<thead>
						<tr>
							<th style="width: 350px; min-width: 350px; text-align: left;">SUMMARY</th>
							<th style="width: 80px;"># of<br>Leads</th>
							<th style="width: 100px;"># of Call<br>Attempts<br>Remaining</th>
							<th style="width: 100px;"># of<br>Exhausted<br>Leads</th>
							<th style="width: 100px;"># of Bad<br>Phone<br>Numbers</th>
							<th style="width: 80px;"># of<br>Callbacks</th>
							<th style="width: 80px;"># of Lead<br>Delays</th>
							<th style="width: 100px;"># of<br>Suppressed<br>Leads</th>
							<th style="width: 80px;"># of<br>Viable<br>Leads</th>
							<th style="width: 60px;">0<br>Dials</th>
							<th style="width: 60px;">1<br>Dial</th>
							<th style="width: 60px;">2-4<br>Dials</th>
							<th style="width: 60px;">5-19<br>Dials</th>
							<th style="width: 60px;">20+<br>Dials</th>
						</tr>
					</thead>
					<tbody>
						<tr class="no-stage-row">
							<td class="name-cell">
								<i class="fa fa-exclamation-triangle" style="margin-right: 8px; color: #6c757d;"></i>
								<strong>No Stage</strong>
							</td>
							<td><strong>{{totalNoStage || 0}}</strong></td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td><strong>{{suppressedNoStage || 0}}</strong></td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
							<td>-</td>
						</tr>
						<tr class="total-row">
							<td class="name-cell">
								<i class="fa fa-calculator" style="margin-right: 8px; color: #6c757d;"></i>
								<strong>TOTAL</strong>
							</td>
							<td><strong><a href="#/supervisor/campaigns/{{campaign.id}}/leads">{{totalLeads}}</a></strong></td>
							<td><strong>{{totalCallAttempts}}</strong></td>
							<td><strong>{{totalNoCallAttempts}} ({{ 100 - (((totalLeads - totalNoCallAttempts) / totalLeads) * 100) | number:0 }}%)</strong></td>
							<td><strong>{{totalBadNumbers}}</strong></td>
							<td><strong>{{totalCallback}}</strong></td>
							<td><strong>{{totalDontContactUntil}}</strong></td>
							<td><strong>{{suppressedTotal}}</strong></td>
							<td><strong>{{totalViable}}</strong></td>
							<td><strong>{{totalDialAttemptBuckets.zero}}</strong></td>
							<td><strong>{{totalDialAttemptBuckets.one}}</strong></td>
							<td><strong>{{totalDialAttemptBuckets.twoToFour}}</strong></td>
							<td><strong>{{totalDialAttemptBuckets.fiveToNineteen}}</strong></td>
							<td><strong>{{totalDialAttemptBuckets.twentyPlus}}</strong></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
