<div class="ibox">
	<div class="ibox-content form-inline">
		<div class="form-group">
			<label class="control-label" style="margin-right: 10px;">Dialing Rule:</label>
			<select class="form-control" ng-model="selectedDialingRule" ng-options="rule as rule.name for rule in dialingRules track by rule.id" style="width: 250px; margin-right: 20px;">
			</select>
			
			<label class="control-label" style="margin-right: 10px;">Agent:</label>
			<select class="form-control" ng-model="selectedAgent" ng-options="agent as agent.name for agent in agents track by agent.id" style="width: 200px; margin-right: 20px;">
				<option value="">All Agents</option>
			</select>
			
			<button type="button" class="btn btn-primary" ng-click="update()">Update</button>
		</div>
		&nbsp; When selecting "All Agents", portfolio & non-portfolio leads will be displayed. When selecting a single agent, only their portfolio leads will be displayed.
	</div>
	<div class="ibox-content">
		<div style="overflow-x: auto; min-width: 100%;">
			<div style="width: 1900px; max-width: 1900px; overflow: hidden;">
				<div class="row" style="margin-bottom: 10px;">
					<div style="width: 400px; float: left; padding: 0 15px;"><strong>Call Attempt Breakdown</strong> - {{ campaign.name }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong># of Leads</strong></div>
					<div style="width: 150px; float: left; padding: 0 15px; text-align: center;"><strong># of Call Attempts Remaining</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong># of Exhausted Leads</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong># of Bad Phone Numbers</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong># of Callbacks</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong># of Lead Delays</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong># of Suppressed Leads</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong># of Viable Leads</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>0 Dials</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>1 Dial</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>2-4 Dials</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>5-19 Dials</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>20+ Dials</strong></div>
					<div style="clear: both;"></div>
				</div>
				<div class="hr-line-solid"></div>

		<!-- Campaign Stage View (agent selection just filters the data) -->
			<div ng-repeat="stage in stages">
				<div class="row" style="margin-bottom: 10px;">
					<div style="width: 400px; float: left; padding: 0 15px;">
						<div style="background-color: #f8f9fa; border-left: 4px solid #007bff; padding: 8px 12px; margin-bottom: 5px; font-weight: bold; font-size: 14px;">
							<i class="fa fa-folder" style="margin-right: 8px; color: #007bff;"></i>STAGE: {{ stage.name }}
							<button type="button" style="margin-left: 10px;" class="btn btn-success btn-xs" ng-click="setStageAgents(stage)">Assign Agents</button>
						</div>
					</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/leads" ng-click="showLeads(stage)">{{ stage.leads || 0 }}</a></div>
					<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">{{ stage.callAttempts || 0 }}</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ stage.exhausted || 0}} ({{ 100 - (((stage.leads - (stage.exhausted || 0)) / stage.leads) * 100) | number:0 }}%)</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ stage.badNumber || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ stage.callback || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ stage.dontContactUntil || 0 }}</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ stage.suppressions || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ stage.viable || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ stage.dialAttemptBuckets.zero || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ stage.dialAttemptBuckets.one || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ stage.dialAttemptBuckets.twoToFour || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ stage.dialAttemptBuckets.fiveToNineteen || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ stage.dialAttemptBuckets.twentyPlus || 0 }}</div>
					<div style="clear: both;"></div>
				</div>

				<!-- Reporting Groups within Stage -->
				<div ng-repeat="reportingGroup in stage.reportingGroups">
					<div style="border-top: 1px dashed #e7eaec; margin: 5px 0;"></div>
					<div class="row" style="margin-bottom: 10px;">
						<div style="width: 400px; float: left; padding: 0 15px;">
							<div style="background-color: #fff3cd; border-left: 3px solid #ffc107; padding: 6px 10px; margin-left: 20px; margin-bottom: 3px; font-weight: bold; font-size: 13px;">
								<i class="fa fa-layer-group" style="margin-right: 6px; color: #ffc107;"></i>GROUP: {{ reportingGroup.name }}
							</div>
						</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.leads || 0 }}</div>
						<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.callAttempts || 0 }}</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.exhausted || 0}} ({{ (100 - (((reportingGroup.leads - (reportingGroup.exhausted || 0)) / reportingGroup.leads) * 100) | number:0)}}%)</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.badNumber || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.callback || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dontContactUntil || 0 }}</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.suppressions || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.viable || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.zero || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.one || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.twoToFour || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.fiveToNineteen || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.twentyPlus || 0 }}</div>
						<div style="clear: both;"></div>
					</div>

					<!-- Lead Types within Reporting Group -->
					<div ng-repeat="leadType in reportingGroup.leadTypes">
						<div style="border-top: 1px dashed #e7eaec; margin: 5px 0;"></div>
						<div class="row" style="margin-bottom: 10px;">
							<div style="width: 400px; float: left; padding: 0 15px;">
								<div style="background-color: #d1ecf1; border-left: 2px solid #17a2b8; padding: 4px 8px; margin-left: 40px; margin-bottom: 2px; font-size: 12px;">
									<i class="fa fa-tag" style="margin-right: 4px; color: #17a2b8;"></i>TYPE: {{ leadType.name }}
								</div>
							</div>
							<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/skill/{{leadType.id}}/leads">{{ leadType.leads || 0}}</a></div>
							<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">{{ leadType.callAttempts || 0 }}</div>
							<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.exhausted || 0}} ({{ (100 - (((leadType.leads - (leadType.exhausted || 0)) / leadType.leads) * 100) | number:0)}}%)</div>
							<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.badNumber || 0 }}</div>
							<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.callback || 0 }}</div>
							<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dontContactUntil || 0 }}</div>
							<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.suppressions || 0 }}</div>
							<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.viable || 0 }}</div>
							<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.zero || 0 }}</div>
							<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.one || 0 }}</div>
							<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.twoToFour || 0 }}</div>
							<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.fiveToNineteen || 0 }}</div>
							<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.twentyPlus || 0 }}</div>
							<div style="clear: both;"></div>
						</div>
					</div>
				</div>
				<div class="hr-line-solid"></div>
			</div>
		</div>

		<!-- Agent Portfolio View -->
		<div ng-show="selectedAgent && selectedAgent.id">
			<div ng-repeat="reportingGroup in reportingGroups">
				<div class="row" style="margin-bottom: 10px;">
					<div style="width: 400px; float: left; padding: 0 15px;">
						<div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 8px 12px; margin-bottom: 5px; font-weight: bold; font-size: 14px;">
							<i class="fa fa-layer-group" style="margin-right: 8px; color: #ffc107;"></i>GROUP: {{ reportingGroup.name }}
						</div>
					</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.leads || 0 }}</div>
					<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.callAttempts || 0 }}</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.exhausted || 0}} ({{ 100 - (((reportingGroup.leads - (reportingGroup.exhausted || 0)) / reportingGroup.leads) * 100) | number:0 }}%)</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.badNumber || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.callback || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dontContactUntil || 0 }}</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.suppressions || 0 }}</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.viable || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.zero || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.one || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.twoToFour || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.fiveToNineteen || 0 }}</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ reportingGroup.dialAttemptBuckets.twentyPlus || 0 }}</div>
					<div style="clear: both;"></div>
				</div>
				<div ng-repeat="leadType in reportingGroup.leadTypes">
					<div style="border-top: 1px dashed #e7eaec; margin: 5px 0;"></div>
					<div class="row" style="margin-bottom: 10px;">
						<div style="width: 400px; float: left; padding: 0 15px;">
							<div style="background-color: #d1ecf1; border-left: 3px solid #17a2b8; padding: 6px 10px; margin-left: 20px; margin-bottom: 3px; font-size: 12px;">
								<i class="fa fa-tag" style="margin-right: 6px; color: #17a2b8;"></i>TYPE: {{ leadType.name }}
							</div>
						</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.leads || 0 }}</div>
						<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">{{ leadType.callAttempts || 0 }}</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.exhausted || 0}} ({{ (100 - (((leadType.leads - (leadType.exhausted || 0)) / leadType.leads) * 100) | number:0)}}%)</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.badNumber || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.callback || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dontContactUntil || 0 }}</div>
						<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">{{ leadType.suppressions || 0 }}</div>
						<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">{{ leadType.viable || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.zero || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.one || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.twoToFour || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.fiveToNineteen || 0 }}</div>
						<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">{{ leadType.dialAttemptBuckets.twentyPlus || 0 }}</div>
						<div style="clear: both;"></div>
					</div>
				</div>
				<div class="hr-line-solid"></div>
			</div>
		</div>

				<div style="width: 1900px; max-width: 1900px; border-top: 2px solid #e7eaec; margin: 10px 0;"></div>
				<div class="row" style="width: 1900px; max-width: 1900px; margin-bottom: 10px;">
					<div style="width: 400px; float: left; padding: 0 15px;"><strong>No Stage</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalNoStage || 0}}</strong></div>
					<div style="width: 150px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong>{{suppressedNoStage || 0}}</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;">-</div>
					<div style="clear: both;"></div>
				</div>
				<div style="width: 1900px; max-width: 1900px; border-top: 2px solid #e7eaec; margin: 10px 0;"></div>
				<div class="row" style="width: 1900px; max-width: 1900px; margin-bottom: 10px; background-color: #f5f5f5; padding: 10px 0;">
					<div style="width: 400px; float: left; padding: 0 15px;"><strong>Total</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong><a href="#/supervisor/campaigns/{{campaign.id}}/leads">{{totalLeads}}</a></strong></div>
					<div style="width: 150px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalCallAttempts}}</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalNoCallAttempts}} ({{ 100 - (((totalLeads - totalNoCallAttempts) / totalLeads) * 100) | number:0 }}%)</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalBadNumbers}}</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalCallback}}</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDontContactUntil}}</strong></div>
					<div style="width: 120px; float: left; padding: 0 15px; text-align: center;"><strong>{{suppressedTotal}}</strong></div>
					<div style="width: 100px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalViable}}</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDialAttemptBuckets.zero}}</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDialAttemptBuckets.one}}</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDialAttemptBuckets.twoToFour}}</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDialAttemptBuckets.fiveToNineteen}}</strong></div>
					<div style="width: 80px; float: left; padding: 0 15px; text-align: center;"><strong>{{totalDialAttemptBuckets.twentyPlus}}</strong></div>
					<div style="clear: both;"></div>
				</div>
			</div>
		</div>
	</div>
</div>
