<form role="form" ng-submit="ok()" name="form">
  <div class="modal-header">
    <h3 class="modal-title">Search Callbacks</h3>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="form-group col-sm-6">
        <label>KAOS Id</label>
        <input type="text" class="form-control" placeholder="KAOS Id" ng-model="filters.lead.id" />
      </div>
      <div class="form-group col-sm-6">
        <label>Client Ref</label>
        <input type="text" class="form-control" placeholder="Client Ref" ng-model="filters.lead.clientRef" />
      </div>
    </div>    
    <div class="row">
      <div class="form-group col-sm-6">
        <label>First Name</label>
        <input type="text" class="form-control" placeholder="First Name" ng-model="filters.lead.first_name" />
      </div>
      <div class="form-group col-sm-6">
        <label>Last Name</label>
        <input type="text" class="form-control" placeholder="Last Name" ng-model="filters.lead.last_name" />
      </div>
    </div>
    <div class="form-group">
      <label>Spouse Name</label>
      <input type="text" class="form-control" placeholder="Spouse Name" ng-model="filters.lead.spouse_name" />
    </div>
    <div class="form-group">
      <label>Company Name</label>
      <input type="text" class="form-control" placeholder="Company Name" ng-model="filters.lead.company_name" />
    </div>
    <div class="form-group">
      <label>Address 1</label>
      <input type="text" class="form-control" placeholder="Address 1" ng-model="filters.lead.address1" />
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Address 2</label>
        <input type="text" class="form-control" placeholder="Address 2" ng-model="filters.lead.address2" />
      </div>
      <div class="form-group col-sm-6">
        <label>Address 3</label>
        <input type="text" class="form-control" placeholder="Address 3" ng-model="filters.lead.address3" />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>City</label>
        <input type="text" class="form-control" placeholder="City" ng-model="filters.lead.city" />
      </div>
      <div class="form-group col-sm-6">
        <label>State</label>
        <input type="text" class="form-control" placeholder="State" ng-model="filters.lead.state" />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Zip</label>
        <input type="text" class="form-control" placeholder="Zip" ng-model="filters.lead.zip" />
      </div>
    </div>    
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Phone 1</label>
        <input type="text" class="form-control" placeholder="Phone 1" ng-model="filters.lead.phone_home" />
      </div>
      <div class="form-group col-sm-6">
        <label>Phone 2</label>
        <input type="text" class="form-control" placeholder="Phone 2" ng-model="filters.lead.phone_mobile" />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Phone 3</label>
        <input type="text" class="form-control" placeholder="Phone 3" ng-model="filters.lead.phone_work" />
      </div>
      <div class="form-group col-sm-6">
        <label>Phone 4</label>
        <input type="text" class="form-control" placeholder="Phone 4" ng-model="filters.lead.phone_workmobile" />
      </div>
    </div>    
    <div class="form-group">
      <label>Email</label>
      <input type="email" class="form-control" placeholder="Email" ng-model="filters.lead.email" />
    </div>
    <div style="clear:both;"></div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" type="submit">Search</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>