<div class="ibox">
    <div class="ibox-content">
        <div>
            <h3>Add a message</h3>
            <div class="chat-form">
                <form role="form">
                    <div class="form-group">
                        <uib-tabset>
                        <uib-tab heading="Campaigns" ng-click="campaignsShown()">
                        <br>
                        <div class="form-group">
                            <label>Campaign</label>
                            <select class="form-control" ng-options="campaign.id as campaign.name for campaign in campaigns" ng-model="message.campaign">
                                <option value="">-- All --</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Campaign Stage</label>
                            <select ng-disabled="!message.campaign" class="form-control" ng-options="campaignstage.id as campaignstage.name for campaignstage in ( campaignStages | filter: { campaignId : message.campaign })" ng-model="message.stage">
                                <option value="">-- All --</option>
                            </select>
                        </div>
                        </uib-tab>
                        <uib-tab heading="Agents" ng-click="agentsShown()">
                        <br>
                        <div class="form-group">
                            <label class="form-label">Agent(s)</label>
                            <ui-select multiple="true" ng-model="message.msgAgents" ng-disabled="disabled" theme="bootstrap" stlye="width: 300px;">
                            <ui-select-match placeholder="Select agents...">{{$item}}</ui-select-match>
                            <ui-select-choices repeat="model in friendlyAgents | filter:$select.search">
                            {{ model }}
                            </ui-select-choices>
                            </ui-select>
                        </div>
                        
                        </uib-tab>
                        <uib-tab ng-hide="loggedInUser.isClientAdmin" heading="Lead Types" ng-click="skillsShown()">
                        <br>
                        <div class="form-group">
                            <label>Lead Type(s)</label>
                            <ui-select multiple="true" ng-model="message.msgSkills" ng-disabled="disabled" theme="bootstrap" stlye="width: 300px;">
                            <ui-select-match placeholder="Select types...">{{$item}}</ui-select-match>
                            <ui-select-choices repeat="model in friendlySkills | filter:$select.search">
                            {{ model }}
                            </ui-select-choices>
                            </ui-select>
                        </div>
                        </uib-tab>
                        </uib-tabset>
                    </div>
                    <br>
                    <div class="form-group">
                        <input type="test" maxlength="100" ng-model="message.title" class="form-control" placeholder="Title (max 100)"/>
                        <small>{{ 100 - message.title.length }} remaining</small>
                    </div>
                    <div class="form-group">
                        <textarea rows="4" maxlength="250" ng-model="newMessage" class="form-control" placeholder="Message (max 250)"></textarea>
                        <small>{{ 250 - newMessage.length }} remaining</small>
                    </div>
                    <div class="text-right">
                        <button type="submit" class="btn btn-sm btn-primary m-t-n-xs" ng-click="addMessage()"><strong>Add message</strong></button>
                    </div>
                </form>
            </div>
        </div>
        <div class="chat-activity-list" style="padding-top: 30px;">
            <button ng-if="loggedInUser.isAdmin" type="button" class="btn btn-success" ng-click="toggleShow()">{{showAll ? 'Hide All' : 'Show All'}}</button>
            <div class="chat-element" ng-repeat="message in messages">
                <div class="media-body">
                    <a class="close-link pull-right" ng-click="deleteMessage(message)">
                        <i class="fa fa-times"></i>
                    </a>
                    <strong ng-show="showAll && message.author">{{ message.author }}: </strong>
                    <strong>{{ message.title }}</strong>
                    <p class="m-b-xs">
                        {{ message.content }}
                    </p>
                    <small class="text-muted">{{ formatDate(message.updatedAt) }}</small>
                </div>
            </div>
            <div class="chat-element" ng-show="!messages || messages.length === 0">
                <div class="media-body">
                    <p class="m-b-xs">
                        You have sent no previous messages
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>