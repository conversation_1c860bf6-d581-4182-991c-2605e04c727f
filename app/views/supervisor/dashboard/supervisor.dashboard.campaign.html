<div class="row">
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Campaign Goal</h5>
			</div>
			<div class="ibox-content" ng-if="campaigntype != 'Telefunding'">
				<h1 class="no-margins"><span class="text-info">{{ stats.totalSaleAmount || 0 | currency:"$":0 }}</span> / {{ campaign.goal | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.percentComplete | number:0 }}% complete ({{ stats.leftToGoal | currency:"$":0 }} to goal)</div>
				<small>Sales Goal</small>
			</div>
			<div class="ibox-content" ng-if="campaigntype == 'Telefunding'">
				<h1 class="no-margins"><span class="text-info">{{ stats.totalGiftAmount || 0 | currency:"$":0 }}</span> / {{ campaign.goal | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.percentComplete | number:0 }}% complete ({{ stats.leftToGoal | currency:"$":0 }} to goal)</div>
				<small>Pledge Goal</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6" ng-repeat="goal in campaign.campaigngoals">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>{{ goal.reportingGroup }} Goal</h5>
			</div>
			<div class="ibox-content" ng-if="campaigntype != 'Telefunding'">
				<h1 class="no-margins"><span class="text-info">{{ stats[goal.reportingGroup + 'SaleAmount'] || 0 | currency:"$":0 }}</span> / {{ goal.goal | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ ((100 / goal.goal) * stats[goal.reportingGroup + 'SaleAmount']) | number:0 }}% complete</div>
				<small>New Sales Acquired</small>
			</div>
			<div class="ibox-content" ng-if="campaigntype == 'Telefunding'">
				<h1 class="no-margins"><span class="text-info">{{ stats[goal.reportingGroup + 'GiftAmount'] || 0 | currency:"$":0 }}</span> / {{ goal.goal | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ ((100 / goal.goal) * stats[goal.reportingGroup + 'GiftAmount']) | number:0 }}% complete</div>
				<small>New Pledges Acquired</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Number of {{ campaigntype == "Telefunding" ? 'Pledges' : 'Sales' }} Goal</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins"><span class="text-info">{{ campaigntype == "Telefunding" ? stats.totalGiftCount : stats.totalSaleCount | number:0 }}</span> / {{ campaign.projectedQuantity | number:0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ campaign.projectedQuantity - (campaigntype == "Telefunding" ? stats.totalGiftCount : stats.totalSaleCount) | number: 0 }} to goal</div>
				<small>{{ campaigntype == "Telefunding" ? 'Pledges' : 'Sales' }} Goal</small>
			</div>
		</div>
	</div>
	<div class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Dials per Hour</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.dialsPerHour | number:0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.callCount | number:0 }} total calls</div>
				<small>Calls per hour</small>
			</div>
		</div>
	</div>
	<div class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average Weekly Amount</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.avgWeekly | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold {{ stats.avgWeekly > stats.avgWeeklyReq ? 'text-success' : 'text-danger' }}">{{ stats.avgWeeklyReq | currency:"$":0 }} required per week</div>
				<small>Average Acquired</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div ng-if="stats.addOnGiftsDb" class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Add-On Gifts</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">${{ stats.addOnGiftsDb | number:0 }}</h1>
				<small>Add-on gifts for this client</small>
			</div>
		</div>
	</div>
	<div class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Weeks Needed</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.weeksToComplete | number:0 }} Weeks</h1>
				<div class="stat-percent font-bold {{ stats.weeksToComplete < stats.weeksRemaining ? 'text-success' : 'text-danger' }}">{{ stats.weeksRemaining | number:0 }} weeks remaining</div>
				<small>Based on weekly average</small>
			</div>
		</div>
	</div>
	<div class="col-md-4 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average {{ campaigntype == 'Telefunding' ? 'Gift' : 'Sale' }}</h5>
			</div>
			<div class="ibox-content" ng-if="campaigntype != 'Telefunding'">
				<h1 class="no-margins">{{ stats.avgSaleAmount | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold {{ campaign.projectedAvgValue < stats.avgSaleAmount ? 'text-success' : 'text-danger' }}">{{ campaign.projectedAvgValue | currency:"$":0 }} projection</div>
				<small>average amount per sale</small>
			</div>
			<div class="ibox-content" ng-if="campaigntype == 'Telefunding'">
				<h1 class="no-margins">{{ stats.avgGiftAmount | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold {{ campaign.projectedAvgValue < stats.avgGiftAmount ? 'text-success' : 'text-danger' }}">{{ campaign.projectedAvgValue | currency:"$":0 }} projection</div>
				<small>average amount per pledge</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-4 col-sm-6" ng-repeat="goal in campaign.campaigngoals">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average {{ goal.reportingGroup }} {{ campaigntype == 'Telefunding' ? 'Gift' : 'Sale' }}</h5>
			</div>
			<div class="ibox-content" ng-if="campaigntype != 'Telefunding'">
				<h1 class="no-margins">{{ stats[goal.reportingGroup + 'SaleAmount'] / stats[goal.reportingGroup + 'SaleCount'] | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success ">{{ stats[goal.reportingGroup + 'SaleCount'] | number:0 }} sale(s)</div>
				<small>average amount per sale</small>
			</div>
			<div class="ibox-content" ng-if="campaigntype == 'Telefunding'">
				<h1 class="no-margins">{{ stats[goal.reportingGroup + 'GiftAmount'] / stats[goal.reportingGroup + 'GiftCount'] | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success ">{{ stats[goal.reportingGroup + 'GiftCount'] | number:0 }} gift(s)</div>
				<small>average amount per gift</small>
			</div>
		</div>
	</div>
	<div class="col-md-4 col-sm-6" ng-if="campaigntype != 'Telefunding'">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average {{ campaigntype == "Telefunding" ? 'Pledge' : 'Add-on Gift' }}</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.avgGiftAmount | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.totalGiftCount | number:0 }} pledge(s)</div>
				<small>average pledge</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-6">
		<div class="ibox float-e-margins">
			<div class="ibox-content">
				<div>
					<h3 class="font-bold no-margins">{{ campaigntype != 'Telefunding' ? 'Sale and' : '' }} Gift Amount</h3>
					<small>lifetime of campaign</small>
				</div>
				<div>
					<canvas class="chart chart-line" chart-data="totalChart.data" height="100" responsive="true" chart-labels="totalChart.labels" chart-series="totalChart.series" chart-options="totalChart.options" chart-legend="true"></canvas>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="ibox float-e-margins">
			<div class="ibox-content">
				<div>
					<h3 class="font-bold no-margins">Reporting Group Breakdown</h3>
					<small>lifetime of campaign</small>
				</div>
				<div>
					<canvas id="doughnut" class="chart chart-doughnut" chart-data="pie.data" chart-colours="pie.colours" height="116" chart-labels="pie.labels"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>