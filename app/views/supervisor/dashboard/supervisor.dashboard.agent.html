<div class="row">
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title" style="border-top: 4px solid {{ agent.session.agentStatus.color }}">
				<h5>Current Status</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ agent.session.agentStatus.name }}</h1>
				<div class="stat-percent font-bold text-success">{{ agent.session.callState }}</div>
				<small>Call State</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Campaign(s) Goal</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins"><span class="text-info">{{ stats.agentTotalEarned || 0 | currency:"$":0 }}</span> / {{ campaignGoal | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.agentPercentEarned | number:2 }}% contribution</div>
				<small>Contribution To Goal</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Campaign(s) Earned</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins"><span class="text-info">{{ stats.agentTotalEarned || 0 | currency:"$":0 }}</span> / {{ stats.campaignTotalEarned | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.campaignPercentEarned | number:0 }}% contribution</div>
				<small>Contribution To Earned</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average Earned</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.agentAvgEarned || 0 | currency:"$":0 }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.campaignAvgEarned | currency:"$":0 }} campaign average</div>
				<small>Average Sale Amount</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Current Week Hours Target</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins"><span class="text-info">{{ stats.actualWeeksHours || 0 | number:0 }}</span> / {{ stats.weeksHoursTarget || 0 | number:0 }} hours</h1>
				<div class="stat-percent font-bold text-success">{{ stats.todaysHours }} today</div>
				<small>Targets this week</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Current Week Value Target</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins"><span class="text-info">{{ stats.actualWeeksValue || 0 | currency:"$":0 }}</span> / {{ stats.weeksValueTarget || 0 | currency:"$":0 }} </h1>
				<div class="stat-percent font-bold text-success">{{ stats.weeksThresholdTarget | currency:"$":0 }} threshold</div>
				<small>Targets this week</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Average Call Duration</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.avgCallDuration }}</h1>
				<div class="stat-percent font-bold text-success">{{ stats.callCount }} total calls</div>
				<small>Average Call Duration</small>
			</div>
		</div>
	</div>
	<div class="col-md-3 col-sm-6">
		<div class="ibox float-e-margins">
			<div class="ibox-title">
				<h5>Credit Card Percentage</h5>
			</div>
			<div class="ibox-content">
				<h1 class="no-margins">{{ stats.creditCardPercentage | number:0 }}%</h1>
				<div class="stat-percent font-bold text-success">{{ stats.creditCardCount }} / {{ stats.saleOrGiftCount }}</div>
				<small>Pledges/Sales using credit cards</small>
			</div>
		</div>
	</div>
</div>
<div class="row">
	<div class="col-md-6">
		<div class="ibox float-e-margins">
			<div class="ibox-content">
				<div>
					<h3 class="font-bold no-margins">Monthly Sale and Gift Value</h3>
					<small>lifetime of campaign(s)</small>
				</div>
				<div>
					<canvas class="chart chart-line" chart-data="valueChart.data" height="100" responsive="true" chart-labels="valueChart.labels" chart-series="valueChart.series" chart-options="valueChart.options" chart-legend="true"></canvas>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="ibox float-e-margins">
			<div class="ibox-content">
				<div>
					<h3 class="font-bold no-margins">Monthly Sale and Gift Count</h3>
					<small>lifetime of campaign(s)</small>
				</div>
				<div>
					<canvas class="chart chart-line" chart-data="countChart.data" height="100" responsive="true" chart-labels="countChart.labels" chart-series="countChart.series" chart-options="countChart.options" chart-legend="true"></canvas>
				</div>
			</div>
		</div>
	</div>
</div>