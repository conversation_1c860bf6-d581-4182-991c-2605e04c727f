<div class="ibox">
	<div class="ibox-content form-inline">
		<div class="form-group">
			<label class="control-label" style="margin-right: 10px;">Date</label>
			<div class="input-group">
				<input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="date" is-open="dateOpened" close-text="Close" />
				<span class="input-group-btn">
					<button type="button" class="btn btn-default" ng-click="dateOpened = !dateOpened"><i class="glyphicon glyphicon-calendar"></i></button>
				</span>
			</div>
			<label class="control-label" style="margin-left: 15px; margin-right: 10px;">Time</label>
			<input type="number" class="form-control" ng-model="timeHour" style="width: 60px" min="0" max="23">
			<span> : </span>
			<input type="number" class="form-control" ng-model="timeMinute" style="width: 60px; margin-right: 20px;" min="0" max="59">
			<button type="button" class="btn btn-primary" ng-click="update()">Update</button>
		</div>
	</div>
	<div class="ibox-content">
		<div class="row" style="margin-bottom: 10px;">
			<div class="col-sm-3"><strong>Call Attempt Breakdown</strong> - {{ campaign.name }}</div>
			<div class="col-sm-1"><strong># of Leads</strong></div>
			<div class="col-sm-1"><strong># of Call Attempts Remain</strong></div>
			<div class="col-sm-1"><strong># of Exhausted Leads</strong></div>
			<div class="col-sm-1"><strong># of Bad Phone Numbers</strong></div>
			<div class="col-sm-1"><strong># of Callbacks</strong></div>
			<div class="col-sm-1"><strong># of Lead Delays</strong></div>
			<div class="col-sm-1"><strong># of Viable Leads</strong></div>
			<div class="col-sm-1"><strong># of Suppressed Leads</strong></div>
		</div>
		<div class="hr-line-solid"></div>
		<div class="row" ng-repeat="stage in stages">
			<div class="row">
				<div class="col-sm-2"><strong style="padding-left: 15px;">{{ stage.name }}</strong> <button type="button" style="margin-left: 10px;" class="btn btn-success btn-xs" ng-click="setStageAgents(stage)">Assign Agents</button></div>
				<div class="col-sm-1 col-sm-offset-1"><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/leads" ng-click="showLeads(stage)">{{ stage.leads }}</a></div>
				<div class="col-sm-1">{{ stage.callAttempts || 0 }}</div>
				<div class="col-sm-1">{{ stage.exhausted || 0}} ({{ 100 - (((stage.leads - (stage.exhausted || 0)) / stage.leads) * 100) | number:0 }}%)</div>
				<div class="col-sm-1">{{ stage.badNumber || 0 }}</div>
				<div class="col-sm-1">{{ stage.callback || 0 }}</div>
				<div class="col-sm-1">{{ stage.dontContactUntil || 0 }}</div>
				<div class="col-sm-1">{{ stage.viable || 0 }}</div>
				<div class="col-sm-1">{{ stage.suppressions || 0 }}</div>
			</div>
			<div ng-repeat="skill in stage.skills">
				<div class="col-sm-offset-1">
					<div class="hr-line-dashed"></div>
				</div>
				<div class="row" style="margin-bottom: 10px;">
					<div class="col-sm-2 col-sm-offset-1"><strong>{{ skill.name }}</strong></div>
					<div class="col-sm-1"><a href="#/supervisor/campaigns/{{campaign.id}}/stage/{{stage.id}}/skill/{{skill.id}}/leads">{{ skill.leads }}</a></div>
					<div class="col-sm-1">{{ skill.callAttempts || 0 }}</div>
					<div class="col-sm-1">{{ skill.exhausted || 0}} ({{ (100 - (((skill.leads - (skill.exhausted || 0)) / skill.leads) * 100) | number:0)}}%)</div>
					<div class="col-sm-1">{{ skill.badNumber || 0 }}</div>
					<div class="col-sm-1">{{ skill.callback || 0 }}</div>
					<div class="col-sm-1">{{ skill.dontContactUntil || 0 }}</div>
					<div class="col-sm-1">{{ skill.viable || 0 }}</div>
					<div class="col-sm-1">{{ skill.suppressions || 0 }}</div>
				</div>
				<div class="row" style="margin-bottom: 10px;" ng-repeat="rule in skill.rules">
					<div class="col-sm-2 col-sm-offset-2">{{ rule.name }}</div>
					<div class="col-sm-1">{{ rule.callAttempts }}</div>
				</div>
			</div>
			<div class="hr-line-solid"></div>
		</div>
		<div class="row" style="margin-bottom: 10px;">
			<div class="col-sm-3"><strong>No Stage</strong></div>
			<div class="col-sm-1"><strong>{{totalNoStage}}</strong></div>
			<div class="col-sm-1 col-sm-offset-6"><strong>{{suppressedNoStage}}</strong></div>
		</div>
		<div class="row">
			<div class="hr-line-solid"></div>
		</div>
		<div class="row" style="margin-bottom: 10px;">
			<div class="col-sm-3"><strong>Total</strong></div>
			<div class="col-sm-1"><strong><a href="#/supervisor/campaigns/{{campaign.id}}/leads">{{totalLeads}}</a></strong></div>
			<div class="col-sm-1"><strong>{{totalCallAttempts}}</strong></div>
			<div class="col-sm-1"><strong>{{totalNoCallAttempts}} ({{ 100 - (((totalLeads - totalNoCallAttempts) / totalLeads) * 100) | number:0 }}%)</strong></div>
			<div class="col-sm-1"><strong>{{totalBadNumbers}}</strong></div>
			<div class="col-sm-1"><strong>{{totalCallback}}</strong></div>
			<div class="col-sm-1"><strong>{{totalDontContactUntil}}</strong></div>
			<div class="col-sm-1"><strong>{{totalViable}}</strong></div>
			<div class="col-sm-1"><strong>{{suppressedTotal}}</strong></div>
		</div>
	</div>
</div>