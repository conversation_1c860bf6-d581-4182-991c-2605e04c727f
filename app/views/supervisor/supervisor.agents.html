<div ng-show="!agents.length" class="ibox">
	<div class="ibox-content">
		<span>No Agents To Show</span>
	</div>
</div>
<div class="row">
	<div class="col-lg-3 col-md-4 col-xs-6">
		<label>Filter logged out? <input type="checkbox" ng-model="filterLoggedOut"></label>
	</div>
</div>
<div class="row">
	<div class="col-lg-3 col-md-4 col-xs-6" ng-repeat="agent in agents | orderBy:'name'" ng-if="!filterLoggedOut || agent.session.agentStatus.name !== 'Logged Out'">
		<div class="contact-box center-version" style="border-top: 4px solid {{ agent.session.agentStatus.color }}">
			<a href="">
				<div class="top-banner"></div>
				<img alt="image" class="img-circle" ng-src="{{ agent.user && agent.user.avatar ? 'avatars/' + agent.user.avatar : 'images/icon-user-default.png' }}">
				<h3 class="m-b-xs"><strong>{{ agent.name }}</strong></h3>
				<div class="font-bold">{{ agent.device.extension }}</div>
				<div class="font-bold">{{ agent.session.agentStatus.name }}</div>
				<div ng-hide="!agent.session.currentCampaignStage.name || agent.session.currentCampaignStage.name == 'Not Active'">
					{{ agent.session.currentCampaignStage.campaign.name }}<br>
					{{ agent.session.currentCampaignStage.name }}<br>
					{{ agent.session.currentLead.first_name + ' ' + agent.session.currentLead.last_name }}
				</div>
			</a>
			<div class="contact-box-footer">
				<div class="m-t-xs btn-group">
					<a class="btn btn-xs btn-white" ng-if="phone" ng-show="agent.device.extension" ng-click="toggleListenToAgent(agent.device.extension)">
						<i class="fa fa-phone"></i> {{ eavesdropAgent === agent.device.extension ? 'Hangup' : 'Listen' }}
					</a>
					<a ng-show="agent.email" class="btn btn-xs btn-white" ng-href="mailto:{{agent.email}}">
						<i class="fa fa-envelope-o"></i> Email
					</a>
					<uib-dropdown class="btn-group">
						<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
							View / Edit <span class="caret"></span>
						</button>
						<ul class="dropdown-menu" role="menu">
							<li ng-if="agent.session.agentStatus.name !== 'Logged Out'"><a ng-click="editAgentStatus(agent.session)">Change Status</a></li>
							<li ng-if="agent.campaigns.length && (loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin)"><a ng-href="/#/supervisor/agentdashboard/{{agent.id}}/0">Dashboard - All</a></li>
							<li ng-repeat="campaign in agent.campaigns"><a ng-href="/#/supervisor/agentdashboard/{{agent.id}}/{{campaign.id}}">Dashboard - {{ campaign.name }}</a></li>
							<li class="divider" ng-show="agent.campaigns.length"></li>
							<li><a ng-href="/#/supervisor/agents/{{agent.id}}/callrecords">Call Records</a></li>
						</ul>
					</uib-dropdown>

				</div>
			</div>
		</div>
	</div>
</div>
<audio id="audio_remote" autoplay="autoplay" />