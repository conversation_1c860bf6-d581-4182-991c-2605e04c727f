<div class="ibox">
    <div class="ibox-content">
        <div class="project-list">
            <span ng-show="!campaigns.length">No Campaigns To Show</span>
            <table class="table table-hover" ng-hide="!campaigns.length">
                <thead>
                    <th>
                        <span>
                            <span>Status</span>
                        </span>
                    </th>
                    <th>
                        <span class="clickable" ng-click="sortType = 'client.name'; sortReverse = !sortReverse">
                            Client
                            <span ng-show="sortType == 'client.name' && !sortReverse" class="fa fa-caret-down"></span>
                            <span ng-show="sortType == 'client.name' && sortReverse" class="fa fa-caret-up"></span>
                        </span>
                    </th>
                    <th>
                        <span class="clickable" ng-click="sortType = 'name'; sortReverse = !sortReverse">
                            Campaign
                            <span ng-show="sortType == 'name' && !sortReverse" class="fa fa-caret-down"></span>
                            <span ng-show="sortType == 'name' && sortReverse" class="fa fa-caret-up"></span>
                        </span>
                    </th>
                    <th>
                        <span class="clickable" ng-click="sortType = 'progress'; sortReverse = !sortReverse">
                            Progress
                            <span ng-show="sortType == 'progress' && !sortReverse" class="fa fa-caret-down"></span>
                            <span ng-show="sortType == 'progress' && sortReverse" class="fa fa-caret-up"></span>
                        </span>
                    </th>
                    <th></th>
                    <th></th>
                </thead>
                <tbody>
                    <tr ng-repeat="campaign in campaigns | orderBy:['state',sortType]:sortReverse">
                        <td class="project-status">
                            <span class="label label-{{ campaign.stateColor }}">{{ campaign.state }}</span>
                        </td>
                        <td class="project-title">
                            <a href="">{{ campaign.client.name }}</a>
                        </td>
                        <td class="project-title">
                            <a href="">{{ campaign.name }}</a>
                            <br />
                            <small>Start {{ formatDate(campaign.startDate, true) }}</small>
                        </td>
                        <td class="project-completion">
                            <small>Completion: {{campaign.progress | number:0}}%</small>
                            <div class="progress progress-mini">
                                <div style="width: {{campaign.progress}}%;" class="progress-bar"></div>
                            </div>
                        </td>
                        <td class="project-people">
                            <a ng-repeat="agent in campaign.agents | limitTo:10" uib-tooltip="{{ agent.name }}" ng-href="/#/supervisor/agentdashboard/{{agent.id}}/{{campaign.id}}">
                                <img alt="{{ agent.name }}" class="img-circle" ng-src="{{ agent.user && agent.user.avatar ? 'avatars/' + agent.user.avatar : 'images/icon-user-default.png' }}">
                            </a>
                        </td>
                        <td class="project-actions">
                            <uib-dropdown class="btn-group" style="margin-right: 50px">
                                <button type="button" class="btn btn-sm btn-white" data-toggle="dropdown">
                                    Options <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <li>
                                        <a ng-href="/#/supervisor/campaigndashboard/{{campaign.id}}"><i class="fa fa-th"></i>&nbsp Dashboard </a>
                                    </li>
                                    <li class="divider"></li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/leads"><i class="fa fa-user"></i>&nbsp Leads</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/trainingdocs"><i class="fa fa-file-text"></i>&nbsp Training Docs</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/refusals"><i class="fa fa-ban"></i>&nbsp Refusals</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/sales"><i class="fa fa-credit-card"></i>&nbsp Sales</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/pledges"><i class="fa fa-credit-card"></i>&nbsp Pledges</a>
                                    </li>
                                    <li ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin">
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/invoices"><i class="fa fa-money"></i>&nbsp Invoices</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/notes"><i class="fa fa-pencil-square-o"></i>&nbsp Notes</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/callrecords"><i class="fa fa-history"></i>&nbsp Call Records</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/callbacks"><i class="fa fa-phone"></i>&nbsp Callbacks</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/callattempts"><i class="fa fa-mobile"></i>&nbsp Call Attempts</a>
                                    </li>
                                    <li ng-if="isCallAttemptsV2Allowed()">
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/callattemptsv2"><i class="fa fa-mobile"></i>&nbsp Call Attempts V2</a>
                                    </li>
                                    <li ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin">
                                        <a ng-href="/#/admin/campaigns/{{campaign.id}}/products"><i class="fa fa-product-hunt"></i>&nbsp Products</a>
                                    </li>
                                    <li ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin">
                                        <a ng-href="/#/admin/campaigns/{{campaign.id}}/importchanges"><i class="fa fa-exchange"></i>&nbsp Batch Changes</a>
                                    </li>
                                    <li ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin">
                                        <a ng-href="/#/admin/campaigns/{{campaign.id}}/suppressed"><i class="fa fa-stop-circle"></i>&nbsp Suppressed</a>
                                    </li>
                                    <li>
                                        <a ng-href="/#/supervisor/campaigns/{{campaign.id}}/audit"><i class="fa fa-asterisk"></i>&nbsp Changes</a>
                                    </li>
                                    <li class="divider"></li>
                                    <li>
                                        <a ng-href="/#/admin/campaigns/{{campaign.id}}"><i class="fa fa-pencil"></i>&nbsp Edit </a>
                                    </li>
                                </ul>
                            </uib-dropdown>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>