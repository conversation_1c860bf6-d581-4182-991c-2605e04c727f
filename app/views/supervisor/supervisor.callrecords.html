<div class="ibox">
	<div class="ibox-content">
		<table class="table">
			<thead>
				<th>
					<span class="clickable" ng-click='filterChanged("createdAt")'>
						Start Date/Time
						<span ng-show='sortType == "createdAt" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "createdAt" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("totalDurationSecs")'>
						Duration
						<span ng-show='sortType == "totalDurationSecs" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "totalDurationSecs" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.id")'>
						KAOS Id
						<span ng-show='sortType == "lead.id" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.id" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.clientRef")'>
						Client Ref
						<span ng-show='sortType == "lead.clientRef" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.clientRef" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("lead.first_name")'>
						Lead
						<span ng-show='sortType == "lead.first_name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "lead.first_name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callerId")'>
						Caller Id
						<span ng-show='sortType == "callerId" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callerId" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callTo")'>
						Dialed Number
						<span ng-show='sortType == "callTo" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callTo" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("callresult.wrapup")'>
						Wrapup
						<span ng-show='sortType == "callresult.wrapup" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "callresult.wrapup" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("agent.name")'>
						Agent
						<span ng-show='sortType == "agent.name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "agent.name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th>
					<span class="clickable" ng-click='filterChanged("campaign.name")'>
						Campaign
						<span ng-show='sortType == "campaign.name" && !sortReverse' class="fa fa-caret-down"></span>
						<span ng-show='sortType == "campaign.name" && sortReverse' class="fa fa-caret-up"></span>
					</span>
				</th>
				<th></th>
			</thead>
			<tbody>
				<tr dir-paginate="cr in callRecords | itemsPerPage: perPage" total-items="totalCalls" current-page="pagination.current">
					<td>{{ formatDate(cr.createdAt) }}</td>
					<td>{{ humanizeSeconds(cr.totalDurationSecs) }}</td>
					<td>{{ cr.lead.id }}</td>
					<td>{{ cr.lead.clientRef }}</td>
					<td>{{ cr.lead.first_name }} {{ cr.lead.last_name }}</td>
					<td>{{ cr.callerId }}</td>
					<td>{{ cr.callTo }}</td>
					<td>{{ cr.callresult.wrapup }}</td>
					<td>{{ cr.agent.name }}</td>
					<td>{{ cr.campaign.name }}</td>
					<td>
						<a ng-if="cr.recordingLocation && cr.recordingServer && call.recordingLocation !== '_undef_'" ng-hide="cr.playing" ng-click="playRecording(cr)" href=""><i class="fa fa-play"></i></a>
						<div id="jquery_jplayer_{{cr.id}}" class="jp-jplayer"></div>
						<div ng-show="cr.playing" id="jp_container_{{cr.id}}" class="jp-audio" role="application" aria-label="media player">
							<div class="jp-type-single">
								<div class="jp-gui jp-interface">
									<div class="jp-controls-holder">
										<div class="jp-controls">
											<button class="jp-play" role="button" tabindex="0">play</button>
											<button class="jp-stop" role="button" tabindex="0">stop</button>
										</div>
										<div class="jp-progress">
											<div class="jp-seek-bar">
												<div class="jp-play-bar"></div>
											</div>
										</div>
										<div class="jp-current-time" role="timer" aria-label="time">&nbsp;</div>
										<div class="jp-duration" role="timer" aria-label="duration">&nbsp;</div>
									</div>
									<div class="jp-volume-controls">
										<button class="jp-mute" role="button" tabindex="0">mute</button>
										<button class="jp-volume-max" role="button" tabindex="0">max volume</button>
										<div class="jp-volume-bar">
											<div class="jp-volume-bar-value"></div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<!-- <a ng-if="cr.audio" ng-click="audio.paused ? audio.play() : audio.pause()" href=""><i class="fa" ng-class="{'fa-play': audio.paused, 'fa-pause': !audio.paused}"></i></a>
						<a ng-if="cr.audio" ng-click="cr.audio.stop(); cr.audio = null;" href=""><i class="fa fa-stop"></i></a>
						<div ng-if="cr.audio" class="input-group">
							<input class="form-control" type="range" min="0" max="1" step="0.01" ng-model="cr.audio.progress">
						</div> -->
					</td>
				</tr>
				<tr ng-if="!callRecords.length">
					<td colspan="99">No Call Records Found</td>
				</tr>
			</tbody>
		</table>
		<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
	</div>
</div>