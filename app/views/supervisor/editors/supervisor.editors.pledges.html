<div class="modal-body">
    <form novalidate class="form">
        <div class="form-group">
            <label>Decision Maker</label>
            <input type="text" class="form-control" placeholder="Decision maker" ng-model="callresult.decisionMaker" />
        </div>
        <div class="form-group">
            <label>Matching Company</label>
            <input type="text" class="form-control" placeholder="Matching Company" ng-model="callresult.giftMatchingCompany" />
        </div>
        <div class="form-group">
            <label>Pledge Amount</label>
            <input type="number" min="0" step="0.01" class="form-control" ng-model="callresult.giftAmount" />
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-sm-4">
                    <label> <input type="checkbox" ng-model="callresult.declineBenefits"> Decline Benefits? </label>
                </div>
                <div class="col-sm-4">
                    <label style="margin-right:10px;">Free Tix Count</label>
                    <label style="margin-right:10px;">
            <input type="radio" ng-model="callresult.freeTickets" value="0"> 0
          </label>
                    <label style="margin-right:10px;">
            <input type="radio" ng-model="callresult.freeTickets" value="2"> 2
          </label>
                    <label style="margin-right:10px;">
            <input type="radio" ng-model="callresult.freeTickets" value="4"> 4
          </label>
                </div>
                <div class="col-sm-4">
                    <label><input ng-model="callresult.requiresFollowUp" type="checkbox" value=""> Requires immediate follow-up</label>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="row">
                <div class="col-sm-4">
                    <label># of installments</label>
                    <select class="form-control" ng-model="callresult.numberOfInstallments">
            <option value="">--NA--</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
            <option value="6">6</option>
            <option value="7">7</option>
            <option value="8">8</option>
            <option value="9">9</option>
            <option value="10">10</option>
            <option value="11">11</option>
            <option value="12">12</option>
          </select>
                </div>
                <div class="col-sm-8">
                    <label>Installment Notes</label>
                    <input type="text" class="form-control" ng-model="callresult.installmentNotes" />
                </div>
            </div>
        </div>
        <div class="form-group" ng-if="callresult.paymentType == 'Invoice'">
            <label>Expected Due Date</label>
            <p class="input-group">
                <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="callresult.payDate" is-open="popup1.opened" close-text="Close" />
                <span class="input-group-btn">
          <button type="button" class="btn btn-default" ng-click="popup1.opened = !popup1.opened"><i class="glyphicon glyphicon-calendar"></i></button>
        </span>
            </p>
        </div>
        <div class="form-group" ng-if="callresult.paymentType == 'Credit Card'">
            <div class="row">
                <div class="col-sm-6">
                    <label>Credit Card Type</label>
                    <input class="form-control" type="text" ng-model="callresult.creditCardType" disabled="true">
                </div>
                <div class="col-sm-6">
                    <label>Credit Card Number</label>
                    <input class="form-control" type="text" ng-model="callresult.creditCardNumber" />
                </div>
            </div>
        </div>
        <div class="form-group" ng-if="callresult.paymentType == 'Credit Card'">
            <div class="row">
                <div class="col-sm-6">
                    <label>Expiry Date</label>
                    <input class="form-control" type="text" ng-model="callresult.creditCardExpDate" placeholder="MMYY" />
                </div>
                <div class="col-sm-6">
                    <label>Security Number</label>
                    <input class="form-control" type="text" ng-model="callresult.creditCardSecurityCode" />
                </div>
            </div>


        </div>
        <div class="form-group">
            <label>Call Notes</label>
            <textarea class="form-control" rows="3" ng-model="callresult.notes"></textarea>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
    <button class="btn btn-success" ng-click="ok()">Save</button>
</div>