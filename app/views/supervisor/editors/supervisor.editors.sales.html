<div class="modal-body">
    <form novalidate class="form">
        <div>
            <button class="btn btn-primary" ng-click="addSaleItem()">Add additional product line</button>
        </div>
        <table class="table">
            <thead>
                <th>Series</th>
                <th>Seats</th>
                <th>Days</th>
                <th>Count</th>
                <th>Cost Per Tix</th>
                <th>Fee Per Tix</th>
                <th>Subtotal</th>
                <th style="width:50px;"></th>
            </thead>
            <tbody>
                <tr ng-repeat="order in callresult.sales">
                    <td>
                        <select class="form-control" ng-model="order.series" ng-options="product.series as product.series for product in products | unique: 'series'">
              <option value="">-- Select --</option>
            </select>
                    </td>
                    <td>
                        <select class="form-control" ng-disabled="!order.series" ng-model="order.seats" ng-options="product.seats as product.seats for product in products | filter: { series: order.series } | unique: 'seats'" ng-change="checkvalues(order)">
              <option value="">-- Select --</option>
            </select>
                    </td>
                    <td>
                        <select class="form-control" ng-disabled="!order.seats" ng-model="order.dayOfWeek" ng-options="product.days as product.days for product in order.possibleDays" ng-change="updateCost(order)">
              <option value="">-- Select --</option>
            </select>
                    </td>
                    <td>
                        <input ng-disabled="!order.dayOfWeek" type="number" class="form-control" ng-model="order.seatCount" min="0" ng-change="updateTotal(order)" />
                    </td>
                    <td>{{order.costEach | currency:"$":0 }}</td>
                    <td>{{order.feePerTicket | currency:"$":0 }}</td>
                    <td>{{order.subtotal | currency:"$":0}}</td>
                    <td><button ng-hide="!$index" class="btn" ng-click="removeSaleItem($index)">X</button></td>
                </tr>
            </tbody>
        </table>
        <div class="form-group col-sm-12">
            <label>Decision Maker</label>
            <input type="text" class="form-control" placeholder="Decision maker" ng-model="callresult.decisionMaker" />
        </div>
        <div class="form-group col-sm-12">
            <label> <input type="checkbox" ng-model="addOnGiftEnabled" ng-change="updateGrandTotal()"> Add-on Gift? </label>
        </div>
        <div class="form-group col-sm-12">
            <label>Gift Amount</label>
            <input type="number" ng-disabled="!addOnGiftEnabled" min="0" step="0.01" class="form-control" ng-model="callresult.giftAmount" ng-change="updateGrandTotal()" />
        </div>
        <div class="form-group col-sm-12">
            <label>Gift Matching Company</label>
            <input type="text" class="form-control" placeholder="Gift Matching Company" ng-model="callresult.giftMatchingCompany" />
        </div>
        <div class="form-group col-sm-12">
            <div>
                <label> <input type="checkbox" ng-model="callresult.declineBenfits"> Decline Benefits? </label>
            </div>
            <div>
                <label style="margin-right:10px;">Free Tix Count</label>
                <label style="margin-right:10px;">
          <input type="radio" ng-model="callresult.freeTix" value="0"> 0
        </label>
                <label style="margin-right:10px;">
          <input type="radio" ng-model="callresult.freeTix" value="2"> 2
        </label>
                <label style="margin-right:10px;">
          <input type="radio" ng-model="callresult.freeTix" value="4"> 4
        </label>
            </div>
        </div>
        <div class="form-group col-sm-12">
            <label>Gift Matching Company</label>
            <input type="text" class="form-control" ng-model="formData.giftMatchingCompany" placeholder="Matching Company" />
        </div>
        <div class="form-group" ng-if="callresult.paymentType == 'Credit Card'">
            <div class="form-group col-sm-12">
                <label>Credit Card Type</label>
                <input class="form-control" type="text" ng-model="callresult.creditCardType" disabled="true">
            </div>
            <div class="form-group col-sm-12">
                <label>Credit Card Number</label>
                <input class="form-control" type="text" ng-model="callresult.creditCardNumber" />
            </div>
            <div class="form-group col-sm-6">
                <label>Expiry Date</label>
                <input class="form-control" type="text" ng-model="callresult.creditCardExpDate" placeholder="MMYY" />
            </div>
            <div class="form-group col-sm-6">
                <label>Security Number</label>
                <input class="form-control" type="text" ng-model="callresult.creditCardSecurityCode" />
            </div>
        </div>
        <div class="form-group col-sm-12">
            <label>Notes</label>
            <textarea class="form-control" ng-model="callresult.notes" style="height:84px"></textarea>
        </div>
        <div style="clear:both;"></div>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
    <button class="btn btn-success" ng-click="ok()">Save</button>
</div>