<div class="ibox">
	<div class="ibox-content">
		<div class="form-group">
			<label>Note</label>
			<textarea class="form-control" ng-model="currentNote" rows="5"></textarea>
			<br>
			<button ng-if="!noteHighligted" type="button" class="btn btn-success" ng-click="addNotes()" ng-disabled="!currentNote">Add Note</button>
			<button ng-if="noteHighligted" type="button" class="btn btn-success" ng-click="save()" ng-disabled="!currentNote">Save Note</button>
		</div>
		<table class="table">
			<thead>
				<th>Date/Time</th>
				<th>Note</th>
				<th></th>
			</thead>
			<tbody>
				<tr ng-repeat="note in notes | orderBy:'createdAt':true" ng-class="{highlighted: noteHighligted == note.id}">
					<td>{{ formatDate(note.createdAt) }}</td>
					<td>{{ note.notes }}</td>
					<td>
						<button type="button" class="btn btn-primary" ng-click="edit(note)">Edit</button>
						<button type="button" class="btn btn-danger" ng-click="deleteNotes(note)">Delete</button>
					</td>
				</tr>
				<tr ng-if="!notes.length">
					<td colspan="99">No notes for this campaign</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>