<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-click="addReason()">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Exception</th>
					<th>Telefunding</th>
					<th>Telesales</th>
				</thead>
				<tbody>
					<tr dir-paginate="reason in reasons | orderBy: ['exception','telefunding','name'] | itemsPerPage: 15">
						<td>{{ reason.name }}</td>
						<td><i class="fa fa-{{ reason.exception ? 'check text-success' : 'times text-danger' }}" aria-hidden="true"></i></td>
						<td><i class="fa fa-{{ reason.telefunding ? 'check text-success' : 'times text-danger' }}" aria-hidden="true"></i></td>
						<td><i class="fa fa-{{ reason.telemarketing ? 'check text-success' : 'times text-danger' }}" aria-hidden="true"></i></td>						
						<td>
							<button class="btn btn-primary btn-xs" ng-click="editReason(reason)">Edit</button>
							<button class="btn btn-danger btn-xs" ng-click="deleteReason(reason)">Delete</button>
						</td>
					</tr>
					<tr ng-hide="reasons.length > 0">
						<td colspan="99">No refusal reasons found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>