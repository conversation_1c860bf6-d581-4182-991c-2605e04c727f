<form role="form" ng-submit="save(clientForm)">
  <div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Client</h3>
  </div>
  <div style="clear:both;"></div>
  <div class="modal-body">
    <div class="tabs-container">
      <uib-tabset>
        <uib-tab heading="Contact Info">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Name</label>
                  <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" autofocus required />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Timezone</label>
                  <select class="form-control" ng-model="editItem.timezone" ng-options="t for t in timezones"></select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Address 1</label>
                  <input type="text" class="form-control" placeholder="Address 1" ng-model="editItem.address1" />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Address 2</label>
                  <input type="text" class="form-control" placeholder="Address 2" ng-model="editItem.address2" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">City</label>
                  <input type="text" class="form-control" placeholder="City" ng-model="editItem.city" />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">State</label>
                  <input type="text" class="form-control" placeholder="State" ng-model="editItem.state" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Zip</label>
                  <input type="text" class="form-control" placeholder="Zip" ng-model="editItem.zip" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Title</label>
                  <input type="text" class="form-control" placeholder="Title" ng-model="editItem.contactTitle" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Primary Contact Name</label>
                  <input type="text" class="form-control" placeholder="Contact Name" ng-model="editItem.primaryContactName" />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Primary Contact Number</label>
                  <input type="text" class="form-control" placeholder="Contact Number" ng-model="editItem.primaryContactNumber" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Secondary Contact Name</label>
                  <input type="text" class="form-control" placeholder="Contact Name" ng-model="editItem.secondaryContactName" />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Secondary Contact Number</label>
                  <input type="text" class="form-control" placeholder="Contact Number" ng-model="editItem.secondaryContactNumber" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Email</label>
                  <input type="email" class="form-control" placeholder="Email" ng-model="editItem.email" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label><input type="checkbox" ng-model="editItem.showReportsAsViews"> Show Daily Reports as Views</label>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="form-group">
                  <label><input type="checkbox" ng-model="editItem.reportPasswordRequired"> Password Protect Reports</label>
                </div>
              </div>
              <div class="col-sm-2">
                <div class="form-group">
                  <button type="button" class="btn btn-primary btn-sm" ng-click="resetPassword()">Reset Password</button>
                </div>
              </div>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="Payment Details">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Default Invoice Type</label>
                  <select class="form-control" ng-model="editItem.defaultInvoiceType">
                    <option value="email">Email</option>
                    <option value="paper">Paper</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Sales Tax</label>
                  <input class="form-control" placeholder="Sales Tax" type="number" step="0.01" ng-model="editItem.salesTax" />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Order Fee</label>
                  <input class="form-control" placeholder="Order Fee" type="number" step="0.01" ng-model="editItem.orderFee" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <label class="control-label">Supported Cards</label>
                  <div>
                    <label ng-repeat="card in creditCards" style='padding-right:10px;'> <input ng-model="editItem.cards[card]" type="checkbox" value=""> {{ card }}</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label"><input ng-model="editItem.membershipCard" type="checkbox"> Offer prompt for new Membership Card in Sales & Pledge</label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label"><input ng-model="editItem.allowExistingCC" type="checkbox"> Allow use of existing credit card details</label>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label" style='padding-right:10px;'><input ng-model="editItem.paperInvoices" type="checkbox"> Allow Paper Invoices</label>
                  <label class="control-label"><input ng-model="editItem.emailInvoices" type="checkbox"> Allow Email Invoices</label>
                </div>
              </div>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="Invoice Info">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Letter Salutation</label>
                  <input type="text" class="form-control" placeholder="Salutation" ng-model="editItem.letterSalutation" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Website Address</label>
                  <input type="text" class="form-control" placeholder="Website" ng-model="editItem.website" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Return Email Address</label>
                  <input type="text" class="form-control" placeholder="Return Address" ng-model="editItem.returnEmail" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Invoice Message</label>
                  <input type="text" class="form-control" placeholder="Message" ng-model="editItem.invoiceMessage" />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Invoice Signature</label>
                  <div ng-if="editItem.signature">
                    <span>
                      <img alt="image" ng-src="{{ 'signatures/' + editItem.signature }}" style="width: 48px; height: 48px;">
                    </span>
                  </div>
                  <input type="file" id="signatureUpload" name="signatureUpload" nv-file-select uploader="signatureUploader" /><br />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <label class="control-label">Invoice Client Logo</label>
                  <div ng-if="editItem.logo">
                    <span>
                      <img alt="image" ng-src="{{ 'clientlogos/' + editItem.logo }}" style="width: 48px; height: 48px;">
                    </span>
                  </div>
                  <input type="file" id="clientLogoUpload" name="clientLogoUpload" nv-file-select uploader="clientLogoUploader" /><br />
                </div>
              </div>
            </div>
            <div class="row">
              <button type="button" ng-if="editItem.id" class="btn btn-success" ng-click="demoInvoice()">Demo Invoice</button>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="1st Appeal Invoice Text">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <textarea class="form-control" style="max-height: 3.45in; max-width: 7.82in;" rows="20" ng-model="editItem.firstAppealInvoiceText"></textarea>
                </div>
              </div>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="2nd Appeal Invoice Text">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <textarea class="form-control" style="max-height: 3.45in; max-width: 7.82in;" rows="20" ng-model="editItem.secondAppealInvoiceText"></textarea>
                </div>
              </div>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="Follow Up Invoice Text">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <div class="form-group">
                  <textarea class="form-control" style="max-height: 3.45in; max-width: 7.82in;" rows="20" ng-model="editItem.followUpInvoiceText"></textarea>
                </div>
              </div>
            </div>
          </div>
        </uib-tab>
        <uib-tab heading="Campaign Costing">
          <br>
          <div class="ibox-content">
            <div class="form-group">
              <button type="button" class="btn btn-success" ng-click="addCosting()">Add Cost</button>
            </div>
            <table class="table">
              <thead>
                <th>Date</th>
                <th>Campaign</th>
                <th>Hours</th>
                <th>Total Cost</th>
              </thead>
              <tbody>
                <tr ng-repeat="cost in costings">
                  <td><input class="form-control" type="text" ui-date-mask placeholder="YYYY-MM-DD" ng-model="cost.date" /></td>
                  <td>
                    <select class="form-control" ng-options="campaign.id as campaign.name for campaign in campaigns" ng-model="cost.campaignId">
                      <option value="">--Please select a campaign--</option>
                    </select>
                  </td>
                  <td><input class="form-control" type="number" ng-model="cost.hours" /></td>
                  <td><input class="form-control" type="number" ng-model="cost.cost" /></td>
                </tr>
              </tbody>
            </table>
          </div>
        </uib-tab>
      </uib-tabset>
    </div>
    <div style="clear:both;"></div>
  </div>
  <div class="modal-footer">
    <div>
      <button class="btn btn-success" type="submit">Save</button>
      <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
    </div>
  </div>
</form>