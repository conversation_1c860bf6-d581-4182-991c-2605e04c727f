<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/clients/create">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Primary Contact</th>
					<th>Email</th>
					<th>Campaigns</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="client in clients | orderBy: 'name' | itemsPerPage:20">
						<td>{{ client.name }}</td>
						<td>{{ client.primaryContactName }}</td>
						<td>{{ client.email }}</td>
						<td>{{ client.campaigns ? client.campaigns.length : 0 }}</td>
						<td>
							<a class="btn btn-xs btn-primary" ng-href="/#/admin/clients/{{client.id}}">Edit</a>
							<a class="btn btn-xs btn-danger" ng-disabled="client.campaigns.length" ng-click="deleteClient(client)">Delete</a>
						</td>
					</tr>
					<tr ng-hide="clients.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>