<form role="form" name="campaignTypeForm" ng-submit="save()">
  <div class="modal-header">
      <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Campaign Type</h3>
  </div>
  <div class="modal-body">
    <div class="form-group  {{ !editItem.name ? 'has-error' : '' }}">
      <label>Name</label>
      <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
    </div>
    <div class="form-group">
      <label>Description</label>
      <input type="text" class="form-control" placeholder="Description" ng-model="editItem.description" />
    </div>
    <div class="form-group">
      <div>
        <label class="available-agent-label">Available Skills</label>
        <label>Required Skills</label>
      </div>
      <div class="left-multi-select-container">
        <select size="8" class="form-control" multiple ng-model="highlightedAvailableSkill" ng-options="availableSkill.id as availableSkill.name for availableSkill in availableSkills"></select>
      </div>
      <div class="multi-select-arrow-container">
        <span class="glyphicon glyphicon-chevron-right top-multi-select-arrow" ng-click="addSkillToSelected()"></span>
        <span class="glyphicon glyphicon-chevron-left" ng-click="addSkillToAvailable()"></span>
      </div>
      <div class="right-multi-select-container">
        <select size="8" class="form-control" multiple ng-model="highlightedSelectedSkill" ng-options="selectedSkill.id as selectedSkill.name for selectedSkill in selectedSkills"></select>
      </div>
      <div style="clear: both;"></div>
    </div>
  </div>
  <div class="modal-footer">
      <button class="btn btn-success" type="submit">Save</button>
      <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>