<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/users/create">Add New</a>
			<table class="table">
				<thead>
					<th>Username</th>
					<th>Name</th>					
					<th>Agent</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="user in users | orderBy: 'name' | itemsPerPage:20">
						<td>{{ user.username }}</td>
						<td>{{ user.name }}</td>
						<td>{{ user.agent.name || "none" }}</td>
						<td>
							<a class="btn btn-primary btn-xs" ng-href="/#/admin/users/{{user.id}}">Edit</a> 
							<button class="btn btn-danger btn-xs" ng-disabled="loggedInUser.id == user.id" ng-click="deleteUser(user)">Delete</button>
						</td>
					</tr>
					<tr ng-hide="users.length > 0">
						<td colspan="99">No users found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>