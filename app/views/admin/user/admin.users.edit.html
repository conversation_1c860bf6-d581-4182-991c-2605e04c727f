<form role="form" class="form-horizontal" ng-submit="save()">
  <div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} User</h3>
  </div>
  <div class="modal-body">
    <div class="form-group">
      <label class="control-label">Name</label>
      <div class="">
        <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" autofocus required />
      </div>
    </div>
    <div class="form-group">
      <label class="control-label">Username</label>
      <div class="">
        <input type="text" class="form-control" placeholder="Username" ng-model="editItem.username" required />
        <mark ng-if="errors.username">{{ errors.username }}</mark>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label">Password</label>
      <div class="">
        <input type="password" class="form-control" placeholder="****" ng-model="editItem.password" required tooltip-placement="bottom" tooltip="{{ passwordFormatText }}" />
      </div>
    </div>
    <div class="form-group">
      <label class="control-label">Email Address</label>
      <div class="">
        <input type="email" class="form-control" placeholder="email" ng-model="editItem.email" required />
        <mark ng-if="errors.username">{{ errors.username }}</mark>
      </div>
    </div>
    <div class="form-group">
      <label class="control-label">Avatar</label>
    </div>
    <div class="form-group">
      <span>
        <img alt="image" class="img-circle" src="{{ editItem.avatar ? 'avatars/' + editItem.avatar : 'images/icon-user-default.png' }}" style="width: 48px; height: 48px;">
      </span>
    </div>
    <div class="form-group">
      <input type="file" id="avatarUpload" name="avatarUpload" nv-file-select uploader="uploader"/><br/>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label> <input ng-model="editItem.firstLogin" type="checkbox" value="" /> Force password change on login</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-disabled="!loggedInUser.isAdmin" ng-model="editItem.isAdmin" type="checkbox" value=""> Administrator</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isSupervisor" type="checkbox" value=""> Campaign Manager</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isSuperManager" type="checkbox" value=""> Super Campaign Manager</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isAgent" type="checkbox" value=""> Agent</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isClientAgent" type="checkbox" value=""> Client Agent</label>
      </div>
    </div>
    <div class="form-group" ng-if="editItem.isAgent  || editItem.isClientAgent">
      <label>Agent</label>
      <select class="form-control" ng-model="editItem.agentId" ng-required="editItem.isAgent  || editItem.isClientAgent">
        <option value="">-- None --</option>
        <option ng-repeat="agent in agents" value="{{ agent.id }}" ng-disabled="agent.assigned || (editItem.isClientAdmin && agent.clientId !== editItem.clientId)" ng-selected="agent.id == editItem.agentId">{{ agent.name }}</option>
      </select>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isClient" type="checkbox" value=""> Client</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label class=""> <input ng-model="editItem.isClientAdmin" type="checkbox" value=""> Client Admin</label>
      </div>
    </div>
    <div class="form-group" ng-if="editItem.isClient || editItem.isClientAdmin || editItem.isClientAgent">
      <label>Client</label>
      <select class="form-control" ng-model="editItem.clientId" ng-options="client.id as client.name for client in clients">
        <option value="">-- Select a Client --</option>
      </select>
    </div>
    <div style="clear:both;"></div>
  </div>
  <div class="modal-footer">
    <div class="col-sm-11">
      <button class="btn btn-success" type="submit">Save</button>
      <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
    </div>
  </div>
</form>