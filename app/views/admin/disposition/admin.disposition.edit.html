<form role="form" class="form-horizontal" ng-submit="save()">
	<div class="modal-header">
		<h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Disposition</h3>
	</div>
	<div class="modal-body">
		<div class="form-group  {{ !editItem.name ? 'has-error' : '' }}	">
			<label class="col-sm-3 control-label">Name</label>
			<div class="col-sm-8">
				<input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">Description</label>
			<div class="col-sm-8">
				<input type="text" class="form-control" placeholder="Description" ng-model="editItem.description" />
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">Exhaust Lead</label>
			<div class="col-sm-8">
				<input type="checkbox" ng-model="editItem.exhaustLead" style="margin-top:11px;" />
			</div>
		</div>
		<div class="form-group">
			<label class="col-sm-3 control-label">Validate Lead</label>
			<div class="col-sm-8">
				<input type="checkbox" ng-model="editItem.validateLead" style="margin-top:11px;" />
			</div>
		</div>
		<div class="form-group">
			<div>
				<label class="available-agent-label">Available Call Result Fields</label>
				<label>Assigned Call Result Fields</label>
			</div>
			<div class="left-multi-select-container">
				<select size="8" class="form-control" multiple ng-model="highlightedAvailableCRF" ng-options="availableCRF.id as availableCRF.name for availableCRF in availableCRFs"></select>
			</div>
			<div class="multi-select-arrow-container">
				<span class="glyphicon glyphicon-chevron-right top-multi-select-arrow" ng-click="addCRFToSelected()"></span>
				<span class="glyphicon glyphicon-chevron-left" ng-click="addCRFToAvailable()"></span>
			</div>
			<div class="right-multi-select-container">
				<select size="8" class="form-control" multiple ng-model="highlightedSelectedCRF" ng-options="selectedCRF.id as selectedCRF.name for selectedCRF in selectedCRFs"></select>
			</div>
			<div style="clear: both;"></div>
		</div>
	</div>
	<div class="modal-footer">
		<div class="col-sm-11">
			<button class="btn btn-success" type="submit">Save</button>
			<button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
		</div>
	</div>
</form>