<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/dispositions/create">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Description</th>
					<th># of Call Result Fields</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="disposition in dispositions | orderBy: 'name'">
						<td>{{ disposition.name }}</td>
						<td>{{ disposition.description }}</td>
						<td>{{ disposition.callresultfields.length || 0 }}</td>
						<td>
							<a class="btn btn-primary" ng-disabled="disposition.system" ng-href="/#/admin/dispositions/{{disposition.id}}">Edit</a>
							<a class="btn btn-danger" ng-disabled="disposition.system" ng-click="deleteDisposition(disposition)">Delete</a>
						</td>
					</tr>
					<tr ng-hide="dispositions.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>