<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/campaigns/{{campaign.id}}/products/create">Add New</a>
			<button class="btn btn-primary" ng-click="uploadProducts()">Upload</button>
			<button class="btn btn-danger" ng-click="clearProducts()">Clear Products</button>
			<table class="table">
				<thead>
					<th>
						<span class="clickable" ng-click='filterChanged("order_by")'>
							Order
							<span ng-show='sortType == "order_by" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "order_by" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("productCode")'>
							Product Code
							<span ng-show='sortType == "productCode" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "productCode" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("venue")'>
							Venue
							<span ng-show='sortType == "venue" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "venue" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("series")'>
							Series
							<span ng-show='sortType == "series" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "series" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("days")'>
							Days
							<span ng-show='sortType == "days" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "days" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("price")'>
							Price
							<span ng-show='sortType == "price" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "price" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("feePerTicket")'>
							Fee Per Ticket
							<span ng-show='sortType == "feePerTicket" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "feePerTicket" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("tix_sub")'>
							Tix / Sub
							<span ng-show='sortType == "tix_sub" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "tix_sub" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("seriesActive")'>
							Series Active
							<span ng-show='sortType == "seriesActive" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "seriesActive" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("seatsActive")'>
							Seats Active
							<span ng-show='sortType == "seatsActive" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "seatsActive" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th>
						<span class="clickable" ng-click='filterChanged("daysActive")'>
							Days Active
							<span ng-show='sortType == "daysActive" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "daysActive" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="product in products | itemsPerPage: productsPerPage"  total-items="totalProducts" current-page="pagination.current">
						<td>{{ product.order_by }}</td>
						<td>{{ product.productCode }}</td>
						<td>{{ product.venue }}</td>
						<td>{{ product.series }}</td>
						<td>{{ product.days }}</td>
						<td>{{ product.price | currency:"$":2 }}</td>
						<td>{{ product.feePerTicket | currency:"$":2 }}</td>
						<td>{{ product.tix_sub }}</td>
						<td>{{ product.seriesActive ? 'Yes': 'No' }}</td>
						<td>{{ product.seatsActive ? 'Yes': 'No' }}</td>
						<td>{{ product.daysActive ? 'Yes': 'No' }}</td>
						<td>
							<a class="btn btn-primary" ng-href="/#/admin/campaigns/{{campaign.id}}/products/{{product.id}}">Edit</a>
							<a class="btn btn-danger" ng-click="deleteProduct(product)">Delete</a>
						</td>
					</tr>
					<tr ng-hide="products.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
		</div>
	</div>
</div>