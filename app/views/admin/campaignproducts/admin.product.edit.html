<div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Client Product</h3>
</div>
<div class="modal-body">
  <form role="form" class="form-horizontal">
      <div class="form-group">
        <label class="col-sm-3 control-label">Product Code</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Product Code" ng-model="editItem.productCode" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Venue</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Venue" ng-model="editItem.venue" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Series</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Series" ng-model="editItem.series" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Price</label>
        <div class="col-sm-8">
          <input type="number" step="0.01" placeholder="Price" class="form-control" ng-model="editItem.price" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Seats</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Seats" ng-model="editItem.seats" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Days</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Days" ng-model="editItem.days" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Fee Per Ticket</label>
        <div class="col-sm-8">
          <input type="number" step="0.01" class="form-control"  placeholder="Fee Per Ticket" ng-model="editItem.feePerTicket" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Tix / Sub</label>
        <div class="col-sm-8">
          <select class="form-control" ng-model="editItem.tix_sub">
            <option value="TIX">TIX</option>
            <option value="SUB">SUB</option>
          </select>
          <!-- <input type="text" class="form-control" placeholder="Tix / Sub" ng-model="editItem.tix_sub" /> -->
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Series Active</label>
        <div class="col-sm-8">
          <input ng-model="editItem.seriesActive" type="checkbox" value="">
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Seats Active</label>
        <div class="col-sm-8">
          <input ng-model="editItem.seatsActive" type="checkbox" value="">
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Days Active</label>
        <div class="col-sm-8">
          <input ng-model="editItem.daysActive" type="checkbox" value="">
        </div>
      </div>
  </form>
</div>
<div class="modal-footer">
  <div class="col-sm-11">
    <button class="btn btn-success" ng-click="save()">Save</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</div>