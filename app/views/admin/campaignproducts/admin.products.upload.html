<div class="modal-header">
    <h3 class="modal-title">Upload Products</h3>
</div>
<div class="modal-body">
	<input type="file" id="productsDb" name="productsDb" nv-file-select uploader="uploader"/><br/>
    <table class="table">
      <thead>
        <tr>
          <th width="33%">Name</th>
          <th ng-show="uploader.isHTML5">Size</th>
          <th ng-show="uploader.isHTML5">File upload progress</th>
          <th ng-show="uploader.isHTML5">Processing progress</th>
          <th>Status</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr ng-hide="uploader.queue.length > 0">
          <td colspan="99">No files to upload</td>
        </tr>
        <!-- ngRepeat: item in uploader.queue -->
        <tr ng-repeat="item in uploader.queue" class="ng-scope">
          <td><strong>{{ item.file.name }}</strong></td>
          <td ng-show="uploader.isHTML5" nowrap="">{{ item.file.size/1024/1024|number:2 }} MB</td>
          <td ng-show="uploader.isHTML5">
            <div class="progress" style="margin-bottom: 0;">
            <div class="progress-bar" role="progressbar" ng-style="{ 'width': item.progress + '%' }" style="width: 0%;"></div>
            </div>
          </td>
          <td ng-show="uploader.isHTML5">
            <div class="progress" style="margin-bottom: 0;">
            <div class="progress-bar" role="progressbar" ng-style="{ 'width': (item.progressPercent || 0) + '%' }" style="width: 0%;"></div>
            </div>
          </td>
          <td class="text-center">
            <span ng-show="item.isSuccess && item.progressPercent < 100"><i class="fa fa-hourglass-half"></i></span>
            <span ng-show="item.isSuccess && item.progressPercent >= 100"><i class="fa fa-check"></i></span>
            <span ng-show="item.isCancel"><i class="fa fa-ban"></i></span>
            <span ng-show="item.isError"><i class="fa fa-exclamation-triangle"></i></span>
          </td>
          <td nowrap="">
	          <button type="button" class="btn btn-success btn-xs" ng-click="item.upload()" ng-disabled="item.isReady || item.isUploading || item.isSuccess">
	            <span class="glyphicon glyphicon-upload"></span> Upload
	          </button>
	          <button type="button" class="btn btn-warning btn-xs" ng-click="item.cancel()" ng-disabled="!item.isUploading" disabled="disabled">
	            <span class="glyphicon glyphicon-ban-circle"></span> Cancel
	          </button>
	          <button type="button" class="btn btn-danger btn-xs" ng-click="item.remove()">
	            <span class="glyphicon glyphicon-trash"></span> Remove
	          </button>
          </td>
        </tr>
      </tbody>
    </table>
</div>
<div class="modal-footer" style="clear:both;">
	<button class="btn btn-success" ng-click="finish()">Finish</button>
</div>