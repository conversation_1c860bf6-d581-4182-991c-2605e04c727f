<div class="modal-header">
	<h3 class="modal-title">Edit Lead - KAOS ID: {{editItem.id}}</h3>
</div>
<div class="modal-body">
	<form role="form" class="form-horizontal">
		<div class="col-sm-6" ng-repeat="field in fields">
			<div class="form-group">
				<label class="col-sm-3 control-label">{{field.label}}</label>
				<div class="col-sm-8">
					<div class="input-group" ng-if="field.name !== 'dontContactUntil'">
						<input ng-if="field.type !== 'textarea'" type="{{field.type}}" class="form-control" placeholder="{{field.label}}" ng-disabled="field.disabled" ng-model="editItem[field.name]" autofocus />
						<textarea ng-if="field.type === 'textarea'" rows="{{ field.rows || 3 }}" cols="{{ field.cols || 24 }}" style="max-width:300px;" ng-model="editItem[field.name]" ng-disabled="field.disabled" />
					</div>
					<div class="input-group" ng-if="field.name === 'dontContactUntil'">
						<input type="text" class="form-control" uib-datepicker-popup="dd-MMMM-yyyy" ng-model="editItem.dontContactUntilObj" is-open="dateOpened" ng-required="false" close-text="Close" />
						<span class="input-group-btn">
							<button type="button" class="btn btn-default" ng-click="openDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
						</span>
					</div>
		        </div>
	        </div>
		</div>
		<div class="col-sm-6" ng-if="loggedInUser.isAdmin || loggedInUser.isSupervisor || loggedInUser.isClientAdmin">
			<div class="form-group">
				<label class="col-sm-3 control-label">Agent Portfolio Tag</label>
				<div class="col-sm-8">
					<div class="input-group">
						<select ng-model="editItem.agentPortfolioTag" ng-options="agent.name as agent.name for agent in agents">
							<option value="">(unassigned)</option>
						</select>
					</div>
		        </div>
	        </div>
		</div>
		<div class="clearfix"></div>
	</form>
</div>
<div class="modal-footer">
  <div class="col-sm-11">
    <button class="btn btn-success" ng-click="save()">Confirm</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</div>