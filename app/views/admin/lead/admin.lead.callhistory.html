<div id="vertical-timeline" class="vertical-container light-timeline center-orientation" ng-if="history.length">
	<div class="vertical-timeline-block" ng-repeat="result in history">
		<div class="vertical-timeline-icon {{ campaignColor(result.campaign.name) }}">
			<i class="fa {{ wrapupIcon(result.wrapup) }}"></i>
		</div>
		<div class="vertical-timeline-content">
			<h2>{{ result.wrapup }}</h2>
			<p>Campaign: {{result.campaign.name}} ({{result.campaignstage.name}})</p>
			<p>Agent: {{ result.agent.name }}</p>
			<p ng-if="result.saleAmount">Total Sale Amount: {{ result.saleAmount | currency:"$":0 }}</p>
			<p ng-if="result.giftAmount">Gift Amount: {{ result.giftAmount | currency:"$":0 }}</p>
			<p ng-if="result.giftMatchingCompany">Matching Company: {{ result.giftMatchingCompany }}</p>
			<p ng-if="result.refusalReason">Refusal Reason: {{ result.refusalReason }}</p>
			<p ng-if="result.decisionMaker">Decision Maker: {{ result.decisionMaker }}</p>
			<div class="btn-group">
				<button class="btn btn-xs btn-success" ng-click="result.showcalls = !result.showcalls; result.showsales = false" type="button">Call History</button>
				<button class="btn btn-xs btn-primary" ng-if="result.sales.length" ng-click="result.showsales = !result.showsales; result.showcalls = false" type="button">Sales</button>
			</div>
			<br>
			<p ng-if="result.showcalls">Call Details</p>
			<div class="well well-sm" ng-if="result.showcalls" ng-repeat="call in result.callrecords">
				<a ng-if="call.recordingLocation && call.recordingServer && call.recordingLocation !== '_undef_'" ng-hide="call.playing" ng-click="playRecording(call)" href=""><i class="fa fa-play"></i></a>
				<small>CLI: {{ call.callerId }}</small><br>
				<small>Phone: {{ call.callTo }}</small><br>
				<small>Start Time: {{ formatTime(call.startDateTime) }}</small><br>
				<small>Duration: {{ humanizeTimespan(call.totalDurationSecs) }}</small><br>
				<div id="jquery_jplayer_{{call.id}}" class="jp-jplayer"></div>
				<div ng-show="call.playing" id="jp_container_{{call.id}}" class="jp-audio" role="application" aria-label="media player">
					<div class="jp-type-single">
						<div class="jp-gui jp-interface">

							<div class="jp-controls-holder">
								<div class="jp-controls">
									<button class="jp-play" role="button" tabindex="0">play</button>
									<button class="jp-stop" role="button" tabindex="0">stop</button>
								</div>
								<div class="jp-progress">
									<div class="jp-seek-bar">
										<div class="jp-play-bar"></div>
									</div>
								</div>
								<div class="jp-current-time" role="timer" aria-label="time">&nbsp;</div>
								<div class="jp-duration" role="timer" aria-label="duration">&nbsp;</div>
								<div class="jp-toggles">
									<button class="jp-repeat" role="button" tabindex="0">repeat</button>
								</div>
							</div>
							<div class="jp-volume-controls">
								<button class="jp-mute" role="button" tabindex="0">mute</button>
								<button class="jp-volume-max" role="button" tabindex="0">max volume</button>
								<div class="jp-volume-bar">
									<div class="jp-volume-bar-value"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- <a ng-if="call.recordingLocation && call.recordingServer" ng-hide="call.audio" ng-click="playRecording(call)" href=""><i class="fa fa-play"></i></a>
				<a ng-if="call.audio" ng-click="audio.paused ? audio.play() : audio.pause()" href=""><i class="fa" ng-class="{'fa-play': audio.paused, 'fa-pause': !audio.paused}"></i></a>
				<a ng-if="call.audio" ng-click="call.audio.stop(); call.audio = null;" href=""><i class="fa fa-stop"></i></a>
				<div ng-if="call.audio" class="input-group">
					<input class="form-control" type="range" min="0" max="1" step="0.01" ng-model="call.audio.progress">
				</div> -->
			</div>
			<p ng-if="result.showsales">Sales</p>
			<div class="well well-sm" ng-if="result.showsales" ng-repeat="sale in result.sales">
				<small>Series: {{ sale.series }}</small><br>
				<small>Seats: {{ sale.seats }}</small><br>
				<small>Day: {{ sale.dayOfWeek }}</small><br>
				<small>Count: {{ sale.seatCount }}</small><br>
				<small>Cost: {{ sale.subtotal | currency:"$":0 }}</small>
			</div>
			<span class="vertical-date">
				<small>{{ formatDate(result.createdAt) }}</small>
			</span>
		</div>
	</div>
</div>
<div class="ibox-content" ng-if="!history.length">
	<span>No Call History</span>
</div>