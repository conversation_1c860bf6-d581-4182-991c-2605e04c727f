<form role="form" ng-submit="save()" name="form">
  <div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Agent</h3>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="col-sm-12">
        <div class="form-group {{ !editItem.name ? 'has-error' : '' }}">
          <label>Name</label>
          <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
          <div ng-show="agentError.text">
            <br>
            <span><mark>{{ agentError.text }}</mark></span>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="form-group">
          <label>Extension</label>
          <select class="form-control" ng-model="editItem.deviceId" ng-options="device.id as device.extension for device in devices" required>
            <option value="">--Please Select--</option>
          </select>
        </div>
      </div>
    </div>
    
    
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Hire Date</label>
          <p class="input-group">
            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" is-open="startDateOpened" close-text="Close" placeholder="Hire Date" ng-model="editItem.hireDate" />
            <span class="input-group-btn">
              <button type="button" class="btn btn-default" ng-click="openHireDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
          </p>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Termination Date</label>
          <p class="input-group">
            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" is-open="endDateOpened" close-text="Close" placeholder="Termination Date" ng-model="editItem.termDate" />
            <span class="input-group-btn">
              <button type="button" class="btn btn-default" ng-click="openTermDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
          </p>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Address</label>
          <input type="text" class="form-control" placeholder="Address" ng-model="editItem.address" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>City</label>
          <input type="text" class="form-control" placeholder="City" ng-model="editItem.city"  />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>State</label>
          <input type="text" class="form-control" placeholder="State" ng-model="editItem.state"  />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Zip</label>
          <input type="text" class="form-control" placeholder="Zip" ng-model="editItem.zip"  />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Phone Number</label>
          <input type="text" class="form-control" placeholder="Phone Number" ng-model="editItem.phone"  />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Email Address</label>
          <input type="email" class="form-control" placeholder="Email" ng-model="editItem.email" required />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-4">
        <div class="form-group">
          <label>Date Of Birth</label>
          <input type="text" class="form-control" placeholder="DOB" ng-model="editItem.DOB"  />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Threshold Hourly Rate</label>
          <select class="form-control" ng-model="editItem.hourlyRate">
            <option value="">--Select Hourly Rate--</option>
            <option value="11">$11.00</option>
            <option value="13">$13.00</option>
            <option value="15">$15.00</option>
          </select>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Hourly Rate</label>
          <input type="number" ng-model="editItem.otherHourlyRate" class="form-control" />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group {{ !(editItem.defaultAgentStateId > 0) ? 'has-error' : '' }}">
          <label>Default Agent State</label>
          <select class="form-control" ng-model="editItem.defaultAgentStateId" ng-options="agentState.id as agentState.name for agentState in agentStates" required>
            <option value="">-- Select Default Agent State --</option>
          </select>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Paylocity Employee ID</label>
          <input type="text" class="form-control" placeholder="Employee ID" ng-model="editItem.payrollId"  />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Location</label>
          <select class="form-control" ng-model="editItem.location">
            <option value="">--Select Location--</option>
            <option value="MKE">MKE</option>
            <option value="CHI">CHI</option>
            <option value="Remote">Remote</option>
          </select>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>SSN</label>
          <input type="text" class="form-control" ng-model="editItem.ssn" />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Deductions</label>
          <input type="string" class="form-control" ng-model="editItem.deductions" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Wage Type</label>
          <select ng-model="editItem.wageType" class="form-control">
            <option value="">--Select Wage Type--</option>
            <option value="hourly">Hourly</option>
            <option value="salary">Salary</option>
          </select>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Health Deduction</label>
          <input type="number" class="form-control" ng-model="editItem.healthDeduction" step="0.01" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Dental Deduction</label>
          <input type="number" class="form-control" ng-model="editItem.dentalDeduction" step="0.01" />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Garnishment</label>
          <input type="number" class="form-control" ng-model="editItem.garnishment" step="0.01" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>EG Deduction</label>
          <input type="number" class="form-control" ng-model="editItem.egDeduction" step="0.01" />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Transit Deduction</label>
          <input type="number" class="form-control" ng-model="editItem.transitDeduction" step="0.01" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Payroll Payment Type</label>
          <select ng-model="editItem.payrollPaymentType" class="form-control">
            <option value="">--Select Payment Type--</option>
            <option value="directDeposit">Direct Deposit</option>
            <option value="check">Check</option>
          </select>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-sm-2">
        <div class="checkbox" style="margin-top:30px;">
          <label> <input  ng-model="editItem.autoDial" type="checkbox" value=""> Auto Dial</label>
        </div>
      </div>
      <div class="col-sm-4">
        <div class="form-group" ng-hide="!editItem.autoDial">
          <label>Call Preperation Time</label>
          <input type="number" ng-model="editItem.callPrepTime" class="form-control" placeholder="Call Preperation Time" />
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Client</label>
          <select class="form-control" ng-model="editItem.clientId" ng-options="client.id as client.name for client in clients">
            <option value="">-- Select a Client --</option>
          </select>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-sm-4">
        <div class="form-group" ng-hide="!editItem.autoDial">
          <label>Campaign Switch Interval (minutes)</label>
          <input type="number" ng-model="editItem.campaignSwitchInterval" class="form-control" placeholder="Campaign Switch Interval" />
        </div>
      </div>
    </div>
    
    <div style="clear:both;"></div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" type="submit">Save</button>
    <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>