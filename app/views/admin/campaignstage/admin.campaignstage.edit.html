<form role="form" ng-submit="save()">
  <div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Campaign Stage</h3>
  </div>
  <div class="modal-body">
    <div class="form-group {{ !editItem.name ? 'has-error' : '' }}">
      <label>Name</label>
      <input ng-disabled="editItem.isSystem" type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required autofocus />
    </div>

    <div class="form-group">
      <label>Caller ID</label>
      <input type="text" class="form-control" placeholder="Caller ID" ng-model="editItem.callerId" only-digits />
    </div>

    <div class="row">
      <div class="col-sm-6 form group {{ !editItem.startDate ? 'has-error' : '' }}">
        <label>Start Date</label>
        <p class="input-group">
          <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.startDate" is-open="startDateOpened" ng-required="true" close-text="Close" required />
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>
        </p>
      </div>
      <div class="col-sm-6 form-group {{ !editItem.endDate ? 'has-error' : '' }}">
        <label>End Date</label>
        <p class="input-group">
          <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.endDate" is-open="endDateOpened" ng-required="true" close-text="Close" min-date="mindate" required />
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>
        </p>
      </div>
    </div>

    <div class="form-group {{ !selectedDispositions.length ? 'has-error' : '' }}">
      <div>
        <label class="available-agent-label">Available Dispositions</label>
        <label>Assigned Dispositions</label>
      </div>
      <div class="left-multi-select-container">
        <select size="8" class="form-control" multiple ng-model="highlightedAvailableDisposition" ng-options="availableDisposition.id as availableDisposition.name for availableDisposition in availableDispositions"></select>
      </div>
      <div class="multi-select-arrow-container">
        <span class="glyphicon glyphicon-chevron-right top-multi-select-arrow" ng-click="addDispositionToSelected()"></span>
        <span class="glyphicon glyphicon-chevron-left" ng-click="addDispositionToAvailable()"></span>
      </div>
      <div class="right-multi-select-container">
        <select size="8" class="form-control" multiple ng-model="highlightedSelectedDisposition" ng-options="selectedDisposition.id as selectedDisposition.name for selectedDisposition in selectedDispositions"></select>
      </div>
      <div style="clear: both;"></div>
    </div>

    <div class="row" ng-show="campaign.initialCampaignStageId != editItem.id">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Lead Type Blacklist</label>
          <ui-select multiple="true" ng-model="editItem.blacklistedSkills" ng-disabled="disabled" theme="bootstrap">
            <ui-select-match placeholder="Select blacklisted skills...">{{$item.name}}</ui-select-match>
            <ui-select-choices repeat="skill.id as skill in skills | filter:$select.search">
              {{ skill.name }}
            </ui-select-choices>
          </ui-select>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label for="">Blacklist Destination</label>
          <select ng-model="editItem.blacklistCampaignstageId" class="form-control" ng-options="stage.id as stage.name for stage in stages">
            <option value="">No Stage</option>
          </select>
        </div>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="clear:both;">
    <button class="btn btn-success" type="submit" ng-disabled="!selectedDispositions || !selectedDispositions.length">Save</button>
    <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>