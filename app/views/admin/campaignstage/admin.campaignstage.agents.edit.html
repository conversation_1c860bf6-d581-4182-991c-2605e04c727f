<div class="modal-header">
  <h3 class="modal-title">Set {{editItem.name}} Agents</h3>
</div>
<div class="modal-body">
  <div ng-show="availableAgents.length" style="margin-bottom:20px;">
    <div class="form-group">
      <label>Agents</label>
      <ui-select multiple="true" ng-model="$parent.selectedAgents" theme="bootstrap">
          <ui-select-match placeholder="Select agents...">{{$item.name}}</ui-select-match>
          <ui-select-choices repeat="agent as agent in availableAgents | filter:$select.search">
              {{ agent.name }}
          </ui-select-choices>
      </ui-select>
    </div>
    <button class="btn btn-success" ng-disabled="!selectedAgents || !selectedAgents.length" ng-click="addAgents(selectedAgents)">Add Agents to Stage</button>
  </div>
  <form role="form">
    <table class="table">
      <thead>
        <th style="width:170px;">Agent</th>
        <th>Lead Types</th>
        <th style="width:100px;"></th>
      </thead>
      <tbody>
        <tr ng-repeat="agent in campaignStageAgents">
          <td>{{agent.name}}</td>
          <td>
            <ui-select multiple="true" ng-model="agent.campaignstageagents.agentskills" ng-disabled="disabled" theme="bootstrap">
              <ui-select-match placeholder="Select lead types...">{{$item.name}}</ui-select-match>
              <ui-select-choices repeat="skill.id as skill in availableSkills | filter:$select.search">
                  {{ skill.name }}
              </ui-select-choices>
            </ui-select>
          </td>
          <td>
            <button class="btn btn-danger" ng-click="removeAgent(agent)">Remove</button>
          </td>
        </tr>
        <tr ng-show="!campaignStageAgents || !campaignStageAgents.length">
          <td colspan="99">No agents added</td>
        </tr>
      </tbody>
    </table>
  </form>
</div>
<div class="modal-footer" style="clear:both;">
  <button class="btn btn-success" ng-click="save()">Save</button>
  <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
</div>