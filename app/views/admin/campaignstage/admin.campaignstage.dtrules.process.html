<form role="form" class="form-horizontal">
  <div class="modal-header">
      <h3 class="modal-title">Saving campaign</h3>
  </div>
  <div class="modal-body">
    <div ng-if="!processingComplete">
      <div style="text-align:center;">
        <h4>Processing dialling rules...</h4>
        <span>Please wait and do not close the browser, this may take up to a minute.</span>
      </div>
      <div class="sk-folding-cube">
        <div class="sk-cube1 sk-cube"></div>
        <div class="sk-cube2 sk-cube"></div>
        <div class="sk-cube4 sk-cube"></div>
        <div class="sk-cube3 sk-cube"></div>
      </div>
    </div>
    <div ng-if="processingComplete">
      <div style="margin-bottom:20px;" ng-show="changes.toIncrement.length">
        <h5>Call attempts added:</h5>
        <ul>
          <li ng-repeat="change in changes.toIncrement">
            <strong>{{ change.amount }}</strong> call attempts added for <strong>{{ change.rule.subskill.name }}</strong> leads at <strong>{{ change.rule.datetimeruleset.name }}</strong>
          </li>
        </ul>
      </div>
      <div ng-show="changes.toDecrement.length">
        <h5>Call attempts removed:</h5>
        <ul>
          <li ng-repeat="change in changes.toDecrement">
            <strong>{{ change.amount }}</strong> call attempts removed for <strong>{{ change.rule.subskill.name }}</strong> leads at <strong>{{ change.rule.datetimeruleset.name }}</strong>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div ng-if="processingComplete" class="modal-footer">
    <div class="col-sm-11">
      <button class="btn btn-success" ng-disabled="!processingComplete" ng-click="finish()">Finish</button>
    </div>
  </div>
</form>