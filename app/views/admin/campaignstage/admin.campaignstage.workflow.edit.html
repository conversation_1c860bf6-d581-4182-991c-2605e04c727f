<div class="modal-header">
    <h3 class="modal-title">Set {{editItem.name}} Flow</h3>
</div>
<div class="modal-body">
  <form role="form">
      <table class="table">
        <thead>
          <th>Disposition</th>
          <th>Destination Stage</th>
          <th>Lead Delay</th>
          <th>Unit</th>
          <th>Cut Off Date</th>
          <th>Overflow Destination</th>
        </thead>
        <tbody>
          <tr ng-repeat="disposition in editItem.dispositions">
            <td>{{disposition.name}}</td>
            <td>
              <select class="form-control" ng-model="disposition.destination" ng-options="stage.id as stage.name for stage in stages" required>
                <option value="">-- None --</option>
              </select>
            </td>
            <td>
              <input type"number" ng-model="disposition.dontContactLeadForHours" min="0" class="form-control" style="width: 65px;" />
            </td>
            <td>
              <select class="form-control" ng-model="disposition.delayType" style="width: 90px;">
                <option value="">Hours</option>
                <option value="days">Days</option>
              </select></td>
            <td>
              <input type="text" class="form-control" ui-date-mask placeholder="YYYY-MM-DD" ng-model="disposition.transitionCutOffDate" style="width: 120px;">
            </td>
            <td>
              <select class="form-control" ng-model="disposition.transitionCutOffDateDispositionId" ng-options="stage.id as stage.name for stage in stages">
                <option value="">-- None --</option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>
  </form>
</div>
<div class="modal-footer" style="clear:both;">
    <button class="btn btn-success" ng-click="save()">Save</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
</div>