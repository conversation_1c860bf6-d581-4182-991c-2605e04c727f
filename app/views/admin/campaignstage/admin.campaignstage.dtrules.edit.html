<form role="form" name="stageForm">
  <div class="modal-header">
    <h3 class="modal-title">Campaign Stage Dialling Rules</h3>
  </div>
  <div class="modal-body">
    <div ng-show="campaignStageSkills.length">
      <a style="margin: 0 0 25px 0;" class="btn btn-success btn-add-new" ng-click="addNewDateTimeRule()">Add New Date/Time Rule</a>
    </div>
    <span ng-hide="campaignStageSkills.length">Dialing rules cannot be set without lead types being associated with this campaign stage</span>
    <div class="form-group" ng-if="campaignStageSkills.length && dtRules.length">
      <div style="margin-bottom: 18px;" ng-repeat="skill in campaignStageSkills | orderBy:'priority'">
        <h3 style="padding: 10px 14px 10px 0; display: inline-table; width: 300px;">{{ skill.name }}</h3>
        <button style="margin-top:-3px;" ng-click="addCallAttempts(skill)" class="btn btn-success btn-sm">Add Call Attempts</button>
        <button style="margin-top:-3px;" ng-click="copyFromRule(skill)" class="btn btn-primary btn-sm" ng-if="!skill.campaignStageDTRules || !skill.campaignStageDTRules.length">Copy From Existing Rule</button>
        <div ng-if="!skill.campaignStageDTRules || !skill.campaignStageDTRules.length">No rules currently active</div>
        <table class="table" ng-if="skill.campaignStageDTRules.length">
          <thead>
            <th style="width: 300px;">Date Time Rule</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Quantity</th>
          </thead>
          <tbody>
            <tr ng-repeat="campaignStageDTRule in skill.campaignStageDTRules | orderBy:'name'">
              <td>
                {{ campaignStageDTRule.datetimeruleset.name }}
                <!-- <span ng-hide="campaignStageDTRule.editMode">{{ campaignStageDTRule.datetimeruleset.name }}</span>
                <select ng-show="campaignStageDTRule.editMode" ng-model="campaignStageDTRule.datetimeruleset" ng-options="dtRule as dtRule.name for dtRule in dtRules"></select> -->
              </td>
              <td>{{ formatDate(campaignStageDTRule.startDate, true, 'n/a') }}</td>
              <td>{{ formatDate(campaignStageDTRule.endDate, true, 'n/a') }}</td>
              <td>
                <button ng-click="decrementRuleQuantity(campaignStageDTRule)" class="btn btn-xs btn-danger" ng-disabled="!campaignStageDTRule.quantity"><i class="fa fa-minus"></i></button>
                <span style="padding: 0 8px;">{{ campaignStageDTRule.quantity || 0 }}</span>
                <button ng-click="incrementRuleQuantity(campaignStageDTRule)" class="btn btn-xs btn-primary"><i class="fa fa-plus"></i></button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  <div class="modal-footer" style="clear:both;">
      <button class="btn btn-success" ng-click="save()">Save</button>
      <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>