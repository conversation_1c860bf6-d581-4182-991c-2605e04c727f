<form role="form" class="form-horizontal">
  <div class="modal-header">
      <h3 class="modal-title">Add call attempts for {{ editItem.skill.name }}</h3>
  </div>
  <div class="modal-body">
    <div class="form-group {{ !editItem.datetimeruleset ? 'has-error' : '' }}">
      <label class="col-sm-4 control-label">Date/Time Rule</label>
      <div class="col-sm-6">
        <select class="form-control" ng-model="editItem.datetimeruleset" ng-options="dtRule.name for dtRule in dtRules"></select>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-4 control-label">Start Date (optional)</label>
      <div class="col-sm-6">
        <p class="input-group">
          <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.startDate" is-open="startDateOpened" close-text="Close" />
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>
        </p>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-4 control-label">End Date (optional)</label>
      <div class="col-sm-6">
        <p class="input-group">
          <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.endDate" is-open="endDateOpened" close-text="Close" />
          <span class="input-group-btn">
            <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
          </span>
        </p>
      </div>
    </div>
    <div class="form-group">
      <label class="col-sm-4 control-label">Quantity</label>
      <div class="col-sm-6">
        <button ng-click="decrementRuleQuantity()" class="btn btn-xs btn-danger" ng-disabled="editItem.quantity === 1"><i class="fa fa-minus"></i></button>
        <span style="padding: 0 8px;">{{ editItem.quantity }}</span>
        <button ng-click="incrementRuleQuantity()" class="btn btn-xs btn-primary"><i class="fa fa-plus"></i></button>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <div class="col-sm-10">
      <button class="btn btn-success" ng-disabled="!editItem.datetimeruleset" ng-click="save()">Save</button>
      <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
    </div>
  </div>
</form>