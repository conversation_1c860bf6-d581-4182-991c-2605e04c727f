<div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Lead Field</h3>
</div>
<div class="modal-body">
  <form role="form" class="form-horizontal">
      <div class="form-group">
        <label class="col-sm-3 control-label">Name</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Type</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Type" ng-model="editItem.type" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-3 control-label">Custom Validation</label>
        <div class="col-sm-8">
          <input type="text" class="form-control" placeholder="Validation expression" ng-model="editItem.regex" />
        </div>
      </div>
  </form>
</div>
<div class="modal-footer">
  <div class="col-sm-11">
    <button class="btn btn-success" ng-click="save()">Save</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</div>