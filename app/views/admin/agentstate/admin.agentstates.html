<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/agentstates/create" ng-disabled="loggedInUser.username !== 'dualtone_admin'">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Description</th>
					<th>Color</th>
					<th>Active for Outbound</th>
					<th>Timeout</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="state in agentStates | orderBy: 'name'">
						<td>{{ state.name }}</td>
						<td>{{ state.description }}</td>
						<td>
							<div style="width:20px;height:20px;background-color:{{ state.color }};border:1px solid;" alt="{{ state.color }}">&nbsp;</div>
						</td>
						<td>{{ state.outbound ? 'Yes' : 'No' }}</td>
						<td>{{state.timeout ? state.timeout + ' minutes' : 'None'}}</td>
						<td>
							<a class="btn btn-primary" ng-href="/#/admin/agentstates/{{state.id}}">Edit</a>
							<button class="btn btn-danger" ng-disabled="state.system || loggedInUser.username !== 'dualtone_admin'" ng-click="deleteAgentState(state)">Delete</button>
						</td>
					</tr>
					<tr ng-hide="agentStates.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>