<form role="form" ng-submit="save()">
  <div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Agent State</h3>
  </div>
  <div class="modal-body">
    <div class="form-group {{ !editItem.name ? 'has-error' : '' }}">
      <label>Name</label>
      <input type="text" ng-disabled="loggedInUser.username !== 'dualtone_admin'" ng-disabled="editItem.system" class="form-control" placeholder="Name" ng-model="editItem.name" required />
    </div>
    <div class="form-group">
      <label>Description</label>
      <input type="text" ng-disabled="loggedInUser.username !== 'dualtone_admin'" class="form-control" placeholder="Description" ng-model="editItem.description" />
    </div>
    <div class="form-group {{ !editItem.color ? 'has-error' : '' }}">
      <label>Color</label>
      <input colorpicker="" ng-disabled="editItem.system || loggedInUser.username !== 'dualtone_admin'" type="text" placeholder="Click here to pick a color" ng-model="editItem.color" class="form-control" required>
    </div>
    <div class="form-group">
      <label>Timeout (in minutes)</label>
      <input type="number" class="form-control" placeholder="Timeout" ng-model="editItem.timeout" min="0" />
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label> <input ng-model="editItem.outbound" ng-disabled="editItem.system || loggedInUser.username !== 'dualtone_admin'" type="checkbox" value=""> Active for Outbound Dialling</label>
      </div>
    </div>
    <div class="form-group">
      <div class="checkbox">
        <label> <input ng-model="editItem.isChargeable" ng-disabled="editItem.system || loggedInUser.username !== 'dualtone_admin'" type="checkbox" value=""> Active for payroll</label>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" type="submit">Save</button>
    <button type="button" class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>