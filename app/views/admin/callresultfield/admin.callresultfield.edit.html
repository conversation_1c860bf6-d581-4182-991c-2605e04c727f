<div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Field</h3>
</div>
<div class="modal-body">
  <form role="form">
      <div class="form-group">
        <label>Name</label>
        <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
      </div>

      <div class="form-group">
        <label>Group <i class="fa fa-plus" ng-click="addNewGroup()"></i></label>
        <select class="form-control" ng-model="selectedFieldGroup" ng-options="group as group.name for group in callResultFieldGroups" required>
          <option value="">-- Select Field Group --</option>
        </select>
      </div>

      <div class="form-group">
        <label>Type</label>
        <select class="form-control" ng-model="selectedFieldType" ng-options="type as type.name for type in callResultFieldTypes" required>
          <option value="">-- Select Field Type --</option>
        </select>
      </div>

      <div ng-show="(selectedFieldType && selectedFieldGroup)">
        <div ng-show="selectedFieldType.multipleOptions">
          <button ng-click="addOption()" class="btn btn-success">Add</button>
          <div ng-repeat="option in editItem.callresultfieldoptions">
            <input type="text" class="form-control" placeholder="Enter option value" ng-model="option.value" />
          </div>
        </div>
        <div ng-show="selectedFieldType.showPlaceholder" class="form-group">
          <label>Placeholder</label>
          <input type="text" class="form-control" placeholder="Placeholder Text" ng-model="editItem.placeholder" />
        </div>

        <div ng-show="selectedFieldType.showDefaultValue" class="form-group">
          <label>Default Value</label>
          <input type="text" class="form-control" placeholder="Default Value" ng-model="editItem.defaultValue" />
        </div>

        <div ng-show="selectedFieldType.showRegex" class="form-group">
          <label>Validation Pattern</label>
          <input type="text" class="form-control" placeholder="Validation Expression" ng-model="editItem.regex" />
        </div>

        <div ng-show="selectedFieldType.showRegex" class="form-group">
          <label>Validation Message</label>
          <input type="text" class="form-control" placeholder="Validation Message" ng-model="editItem.regexMessage" />
        </div>

        <div class="checkbox">
          <label> <input ng-model="editItem.pci" type="checkbox" value=""> PCI Compliance</label>
        </div>

        <div class="checkbox">
          <label> <input ng-model="editItem.required" type="checkbox" value=""> Required Field</label>
        </div>
      </div>
  </form>
</div>
<div class="modal-footer" style="clear:both;">
    <button class="btn btn-success" ng-click="save()">Save</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
</div>