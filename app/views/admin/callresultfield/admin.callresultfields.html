<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/callresultfields/create">Add New</a>
			<table class="table">
				<thead>
					<th>Group</th>
					<th>Field Name</th>
					<th>PCI Compliance</th>
					<th>Required</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="callResultField in callResultFields | orderBy: 'name'">
						<td>{{ callResultField.callresultfieldgroup.name }}</td>
						<td>{{ callResultField.name }}</td>
						<td>{{ callResultField.pci ? 'Yes' : 'No' }}</td>
						<td>{{ callResultField.required ? 'Yes' : 'No' }}</td>
						<td>
							<a class="btn btn-primary" ng-href="/#/admin/callresultfields/{{callResultField.id}}">Edit</a>
							<a class="btn btn-danger" ng-click="deleteField(callResultField)">Delete</a>
						</td>
					</tr>
					<tr ng-hide="callResultFields.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>