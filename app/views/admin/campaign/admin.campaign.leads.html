<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<button class="btn btn-success btn-add-new" ng-click="search()">Search</button>
			<div ng-if="filters && !isEmptyObject(filters)" class="filters">
				<h4 style="float:left;">Filters: </h4>
				<span class="label label-primary" ng-repeat="(key, prop) in filters">
					{{friendly(key)}} : {{prop}}
				</span>
			</div>
			<table class="table">
				<thead>
					<th class="sortableColumn" ng-repeat="column in displayedColumns">
						<span ng-click='filterChanged(column.field)'>
							{{ column.label }}
							<span ng-show='sortType == "{{ column.field }}" && !sortReverse' class="fa fa-caret-down"></span>
							<span ng-show='sortType == "{{ column.field }}" && sortReverse' class="fa fa-caret-up"></span>
						</span>
					</th>
					<th ng-if="!hideSearch">Current Stage</th>
					<th>Agent Tag</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="lead in leads | itemsPerPage: leadsPerPage" total-items="totalLeads" current-page="pagination.current">
						<td ng-repeat="column in displayedColumns">{{ getObjectPropertyByString(lead, column.field) }}</td>
						<td ng-if="!hideSearch" ng-class="{'text-danger': getCampaignStage(lead) === 'Suppressed'}">{{ getCampaignStage(lead) }}</td>
						<td>{{ lead.agentPortfolioTag || 'None' }}</td>
						<td>
							<uib-dropdown class="btn-group dropdown">
								<button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
									Actions <span class="caret"></span>
								</button>
								<ul class="dropdown-menu" role="menu">
									<li><a ng-click="editLead(lead)">Edit</a></li>
									<li ng-if="loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin"><a ng-click="removeLead(lead)">Remove</a></li>
									<li ng-if="(loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin) && getCampaignStage(lead) !== 'Suppressed'"><a ng-click="changeStage(lead)">Change Stage</a></li>
									<li ng-if="lead.currentCampaignStageId || (lead.campaignleads && lead.campaignleads[0].currentCampaignStageId)"><a ng-click="createCallback(lead)">Create Callback</a></li>
									<li ng-if="(loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin) && lead.campaignleads && lead.campaignleads[0].currentCampaignStageId"><a ng-click="generateDisposition(lead)">Create Disposition</a></li>
									<li><a ng-href="/#/admin/leads/{{lead.lead ? lead.lead.id : lead.id}}/callhistory">Call History</a></li>
									<li><a ng-href="/#/admin/leads/{{lead.lead ? lead.lead.id : lead.id}}/audithistory">Audit History</a></li>
									<li><a ng-href="/#/admin/leads/{{lead.lead ? lead.lead.id: lead.id}}/analysis">Analyze</a></li>
									<li ng-if="(loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin) && getCampaignStage(lead) === 'Suppressed'"><a ng-click="unsuppress(lead)">Unsuppress</a></li>
									<li ng-if="(loggedInUser.isAdmin || loggedInUser.isSuperManager || loggedInUser.isClientAdmin) && getCampaignStage(lead) !== 'Suppressed'"><a ng-style="{'text-decoration' : hasFutureSuppression(lead) ? 'line-through' : ''}" ng-click="suppress(lead)">Suppress</a></li>
								</ul>
							</uib-dropdown>
						</td>
					</tr>
					<tr ng-hide="leads.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
		</div>
	</div>
</div>