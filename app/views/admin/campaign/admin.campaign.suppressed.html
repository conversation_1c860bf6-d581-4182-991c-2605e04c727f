<div>
    <div class="row" style="margin-bottom: 20px;">
        <div class="col-xs-6">
            <button type="button" class="btn btn-primary" ng-click="search()">Search</button>
        </div>
        <div class="col-xs-6">
            <button type="button" class="btn btn-success" ng-click="import()" style="float: right;">Import</button>
            <button type="button" class="btn btn-info" style="margin-right: 10px; float: right;" ng-click="history()">
                <a style="color: white;" ng-href="/#/admin/campaigns/{{campaign.id}}/suppressed/history">History</a>
            </button>
        </div>
    </div>

    <div class="ibox">
        <div class="ibox-content">
            <div class="row">
                <div class="col-xs-3 form-group">
                    <label for="">Post Suppression Destination Stage</label>
                    <select class="form-control" ng-model="filters.campaignstageId">
                        <option value="">All Stages</option>
                        <option ng-repeat="stage in stages" ng-value="stage.id">{{stage.name}}</option>
                    </select>
                </div>
                <div class="col-xs-3 form-group">
                    <label for="">KAOS ID</label>
                    <input type="text" class="form-control" placeholder="Filter By ID" ng-model="filters.leadId"></select>
                </div>
                <div class="col-xs-3 form-group">
                    <label for="">Client Ref</label>
                    <input type="text" class="form-control" placeholder="Filter By ID" ng-model="filters.clientRef"></select>
                </div>
                <div class="col-xs-3 form-group">
                    <label for="">Finished</label>
                    <select class="form-control" ng-model="filters.finished">
                        <option ng-value="true">Yes</option>
                        <option value="">No</option>
                        <option value="skipped">Skipped</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <div class="ibox">
        <div class="ibox-content">
            <div class="row">
                <div class="col-xs-12">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>KAOS ID</th>
                                <th>Client Ref</th>
                                <th>Name</th>
                                <th>Post Suppression Stage</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-if="loading">
                                <td colspan="99" style="text-align: center">Loading</td>
                            </tr>
                            <tr ng-if="!loading && !suppressions.length">
                                <td colspan="99" style="text-align: center">No Suppressions Found</td>
                            </tr>
                            <tr ng-show="!loading" dir-paginate="row in suppressions | itemsPerPage: limit" total-items="total" current-page="page">
                                <td>{{row.leadId}}</td>
                                <td>{{row.lead.clientRef}}</td>
                                <td>{{row.lead.first_name + ' ' + row.lead.last_name}}</td>
                                <td>{{row.campaignstage.name || '--None--'}}</td>
                                <td>{{(row.actualStartDate || row.startDate) | date : 'medium'}}</td>
                                <td>{{(row.actualEndDate || row.endDate) ? (row.actualEndDate || row.endDate)  : 'Never'  | date : 'medium'}}</td>
                                <td>{{row.skipped ? 'Skipped' : row.finished ? 'Finished' : row.actualStartDate ? 'Suppressed' : 'Pending'}}</td>
                                <td>
                                    <uib-dropdown class="btn-group dropdown" ng-if="!row.finished">
                                        <button type="button" class="btn btn-xs btn-white" data-toggle="dropdown">
                                            Actions <span class="caret"></span>
                                        </button>
                                        <ul class="dropdown-menu" role="menu">
                                            <li><a ng-click="edit(row)">Edit</a></li>
                                            <li ng-if="row.actualStartDate"><a ng-click="finish(row)">Finish</a></li>
                                            <li ng-if="!row.actualStartDate"><a ng-click="cancel(row)">Cancel</a></li>
                                        </ul>
                                    </uib-dropdown>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
                </div>
            </div>
        </div>
    </div>

</div>