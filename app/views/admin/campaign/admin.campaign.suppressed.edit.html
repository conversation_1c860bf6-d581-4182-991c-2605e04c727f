<div>
    <div class="modal-header">
        <h3 class="modal-title">{{suppressed.id ? 'Edit' : 'Create'}} Suppression</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12 form-group">
                <label for="">Destination Stage</label>
                <select class="form-control" ng-model="suppressed.campaignstageId" ng-options="item.id as item.name for item in stages">
                    <option value="">No Destination Stage</option>
                </select>
            </div>
            <div class="col-xs-6 form-group">
                <label for="">Start Date</label>
                <p class="input-group">
                    <input type="text" class="form-control" ng-disabled="suppressed.actualStartDate" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="suppressed.startDate" is-open="startDateOpened" close-text="Close" />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-disabled="suppressed.actualStartDate" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </p>
            </div>
            <div class="col-xs-6 form-group">
                <label for="">End Date</label>
                <p class="input-group">
                    <input type="text" class="form-control" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="suppressed.endDate" is-open="endDateOpened" close-text="Close" />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </p>
            </div>
            <div class="col-xs-12">
                <button class="btn btn-success" ng-click="save()">Save</button>
                <button class="btn btn-grey" ng-click="cancel()">Cancel</button>
            </div>
        </div>
    </div>

</div>

</div>