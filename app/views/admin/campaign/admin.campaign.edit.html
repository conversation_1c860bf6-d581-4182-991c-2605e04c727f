<div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'Create' }} Campaign</h3>
</div>
<div class="modal-body">
  <form role="form">
      <div class="form-group">
        <label>Name</label>
        <input type="text" class="form-control" placeholder="Name" ng-model="editItem.name" required />
      </div>

      <div class="form-group">
        <label>Client</label>
        <select class="form-control" ng-model="editItem.clientId" ng-options="client.id as client.name for client in clients" required>
          <option value="">-- Select Client --</option>
        </select>
      </div>

      <div class="form-group">
        <label>Campaign Type</label>
        <select class="form-control" ng-model="editItem.campaigntypeId" ng-options="campaignType.id as campaignType.name for campaignType in campaignTypes" required>
          <option value="">-- Select Campaign Type --</option>
        </select>
      </div>

      <div class="row">
        <div class="col-sm-4">
          <label>Start Date</label>
          <p class="input-group">
            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.startDate" is-open="startDateOpened" ng-required="true" close-text="Close" />
            <span class="input-group-btn">
              <button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
          </p>
        </div>
      </div>

      <div class="row">
        <div class="col-sm-4">
          <label>End Date</label>
          <p class="input-group">
            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" ng-model="editItem.endDate" is-open="endDateOpened" ng-required="true" close-text="Close" />
            <span class="input-group-btn">
              <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
            </span>
          </p>
        </div>
      </div>

      <div class="form-group">
        <label>Campaign Goal</label>
        <input type="number" class="form-control" placeholder="Campaign Goal" ng-model="editItem.goal" />
      </div>

      <div class="form-group">
        <label>Minimum time between contacting a lead (hours)</label>
        <div class="row">
          <div class="col-sm-3">
            <input type="number" class="form-control" placeholder="Enter minimum interval in hours" ng-model="editItem.leadContactIntervalHours" />
          </div>
        </div>
      </div>

      <div class="form-group">
        <div>
          <label class="available-agent-label">Available Agents</label>
          <label>Assigned Agents</label>
        </div>
        <div class="left-multi-select-container">
          <select size="8" class="form-control" multiple ng-model="highlightedAvailableAgent" ng-options="availableAgent.id as availableAgent.name for availableAgent in availableAgents"></select>
        </div>
        <div class="multi-select-arrow-container">
          <span class="glyphicon glyphicon-chevron-right top-multi-select-arrow" ng-click="addAgentToSelected()"></span>
          <span class="glyphicon glyphicon-chevron-left" ng-click="addAgentToAvailable()"></span>
        </div>
        <div class="right-multi-select-container">
          <select size="8" class="form-control" multiple ng-model="highlightedSelectedAgent" ng-options="selectedAgent.id as selectedAgent.name for selectedAgent in selectedAgents"></select>
        </div>
        <div style="clear: both;"></div>
      </div>

      <div class="form-group">
        <div>
          <label class="available-agent-label">Available Dispositions</label>
          <label>Assigned Dispositions</label>
        </div>
        <div class="left-multi-select-container">
          <select size="8" class="form-control" multiple ng-model="highlightedAvailableDisposition" ng-options="availableDisposition.id as availableDisposition.name for availableDisposition in availableDispositions"></select>
        </div>
        <div class="multi-select-arrow-container">
          <span class="glyphicon glyphicon-chevron-right top-multi-select-arrow" ng-click="addDispositionToSelected()"></span>
          <span class="glyphicon glyphicon-chevron-left" ng-click="addDispositionToAvailable()"></span>
        </div>
        <div class="right-multi-select-container">
          <select size="8" class="form-control" multiple ng-model="highlightedSelectedDisposition" ng-options="selectedDisposition.id as selectedDisposition.name for selectedDisposition in selectedDispositions"></select>
        </div>
        <div style="clear: both;"></div>
      </div>
  </form>
</div>
<div class="modal-footer" style="clear:both;">
    <button class="btn btn-success" ng-click="save()">Save</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
</div>