<div class="ibox">
    <div class="ibox-content">
        <div class="row">
            <div class="col-xs-12">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Stage</th>
                            <th>Update Only</th>
                            <th>Total</th>
                            <th>Created</th>
                            <th>Updated</th>
                            <th>Ignored</th>
                            <th>Invalid</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="loading">
                            <td colspan="99" style="text-align: center">Loading</td>
                        </tr>
                        <tr ng-if="!loading && !history.length">
                            <td colspan="99" style="text-align: center">No History Found</td>
                        </tr>
                        <tr ng-show="!loading" dir-paginate="row in history | itemsPerPage: limit" total-items="total" current-page="page">
                            <td>{{(row.createdAt) | date : 'medium'}}</td>
                            <td>{{getStage(row.stage)}}</td>
                            <td>{{row.updateOnly ? 'Yes' : 'No'}}</td>
                            <td>{{row.total}}</td>
                            <td>{{row.created}}</td>
                            <td>{{row.updated}}</td>
                            <td>{{row.ignored}}</td>
                            <td>{{row.invalid}}</td>
                            <td>
                                <button type="button" class="btn btn-primary btn-xs" ng-click="download(row)">Download</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
            </div>
        </div>
    </div>
</div>