<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/campaigns/{{campaign.id}}/stages/create">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Start Date</th>
					<th>End Date</th>
					<th>Leads in Stage</th>
					<th></th>
				</thead>
				<tbody>
					<tr ng-repeat="stage in campaignStages | orderBy: 'startDate'">
						<td>{{ stage.name }}</td>
						<td>{{ formatDate(stage.startDate, true) }}</td>
						<td>{{ formatDate(stage.endDate, true) }}</td>
						<td>{{ stage.leadCount }}</td>
						<td>
							<uib-dropdown class="btn-group">
						      <button type="button" class="btn btn-xs btn-primary" data-toggle="dropdown">
						        Actions <span class="caret"></span>
						      </button>
						      <ul class="dropdown-menu" role="menu">
						        <li><a ng-href="/#/admin/campaignstages/{{stage.id}}/leads">Leads</a></li>
						        <li><a ng-click="resetLeads(stage)">Reset Lead Delays</a></li>
						        <li><a ng-if="stage.name === 'REMOVE'" ng-click="deleteLeads(stage)">Remove Leads from Campaign</a></li>
                        		<li class="divider"></li>
						        <li><a ng-href="/#/admin/campaignstages/{{stage.id}}">Edit</a></li>
						        <li ng-id="!stage.isSystem"><a ng-click="deleteCampaignStage(stage)">Delete</a></li>
						      </ul>
						    </uib-dropdown>							
						</td>
					</tr>
					<tr ng-hide="campaignStages.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>
</div>
