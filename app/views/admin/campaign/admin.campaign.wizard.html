<div class="modal-header">
    <h3 class="modal-title">{{ update ? 'Edit' : 'New' }} Campaign Wizard</h3>
</div>
<div class="modal-body">
    <form role="form" class="form-horizontal">
        <wizard on-finish="wizardCompleted()" edit-mode="update">

            <!-- STEP 1 - CAMPAIGN DETAILS -->
            <wz-step title="Campaign Details" wz-disabled="{{!loggedInUser.isAdmin && !loggedInUser.isSuperManager && !loggedInUser.isClientAdmin}}">
                <div class="form-group {{ !(campaign.clientId > 0) ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">Client</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <select class="form-control" ng-disabled="update && campaign.clientId" ng-model="campaign.clientId" ng-options="client.id as client.name for client in clients" required>
                            <option value="">-- Select Client --</option>
                        </select>
                    </div>
                </div>
                <div class="form-group {{ !campaign.name ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">Campaign Name</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <input type="text" class="form-control" placeholder="Name" ng-model="campaign.name" />
                    </div>
                </div>
                <div class="form-group {{ !(campaign.campaigntypeId > 0) ? 'has-error' : '' }}">
                    <label class="control-label col-sm-3">Campaign Type</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <select class="form-control" ng-model="campaign.campaigntypeId" ng-options="campaignType.id as campaignType.name for campaignType in campaignTypes" ng-disabled="campaign.id" required>
                            <option value="">-- Select Campaign Type --</option>
                        </select>
                    </div>
                </div>
                <div class="form-group {{ !(campaign.owningUserId > 0) ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">Campaign Manager</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <select class="form-control" ng-model="campaign.owningUserId" ng-options="user.id as user.name for user in users" required>
                            <option value="">-- Select User --</option>
                        </select>
                    </div>
                </div>
                <div class="form-group {{ !campaign.startDate ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">Start Date</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <p class="input-group">
                            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" is-open="dates.start" date-disabled="startDateDisabled(date, mode)" close-text="Close" placeholder="Start Date" ng-model="campaign.startDate" tooltip-placement="top" tooltip="This must be a Monday" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                            </span>
                        </p>
                    </div>
                </div>
                <div class="form-group {{ !campaign.endDate ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">End Date</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <p class="input-group">
                            <input type="text" class="form-control" uib-datepicker-popup="{{format}}" is-open="dates.end" close-text="Close" placeholder="End Date" ng-model="campaign.endDate" />
                            <span class="input-group-btn">
                                <button type="button" class="btn btn-default" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                            </span>
                        </p>
                    </div>
                </div>
                <div class="form-group {{ !campaign.defaultCallerId ? 'has-error' : '' }}">
                    <label class="col-sm-3 control-label">Caller ID</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <input type="text" class="form-control" placeholder="Caller ID" ng-model="campaign.defaultCallerId" ng-required="true" only-digits />
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-3 control-label">Callback Window</label>
                    <div class="col-sm-6 col-md-5 col-lg-4">
                        <input type="number" class="form-control" placeholder="Callback Window" ng-model="campaign.callbackWindow" />
                    </div>
                </div>
                <div class="form-group">

                    <label class="col-sm-offset-3" style="padding-left: 20px;"><input ng-model="campaign.dontRecord" type="checkbox" /> Do Not Record Calls</label>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" ng-disabled="!campaign.name || !(campaign.clientId > -1) || !campaign.defaultCallerId || !(campaign.campaigntypeId > -1)" wz-next="saveCampaign()">Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>


            <!-- STEP 2 - SELECT CAMPAIGN STAGES -->
            <wz-step title="Campaign Stages" canenter="checkLeadImportStatus" wz-disabled="{{!loggedInUser.isAdmin && !loggedInUser.isSuperManager && !loggedInUser.isClientAdmin}}">
                <div class="col-sm-offset-2 col-sm-10 col-lg-7">
                    <div>
                        <a class="btn btn-success btn-add-new" ng-click="addNewCampaignStage()">Add New Stage</a>
                        <label style="margin-left: 20px;"><input ng-model="showStageChanges" type="checkbox" /> Allow Initial Stage Change</label>
                    </div>
                    <div class="form-group">
                        <div ng-hide="campaignStages.length > 0">No campaign stages created. Please add at least 1 campaign stage to continue.</div>
                        <div ng-show="campaignStages.length > 0">
                            <table class="table">
                                <thead>
                                    <th width="100px" ng-show="showStageChanges">Initial Stage?</th>
                                    <th>Name</th>
                                    <th width="350px"></th>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="stage in campaignStages">
                                        <td ng-show="showStageChanges"><input type="radio" name="initalStage" ng-model="campaign.initialCampaignStageId" ng-value="stage.id" /></td>
                                        <td>{{ stage.name }}</td>
                                        <td>
                                            <button class="btn btn-success" ng-click="setStageFlow(stage)">Workflow</button>
                                            <button class="btn btn-primary" ng-click="editCampaignStage(stage)">Edit</button>
                                            <button ng-disabled="stage.isSystem" class="btn btn-danger" ng-click="deleteCampaignStage(stage)">Delete</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" ng-disabled="!campaignStages.length || !campaign.initialCampaignStageId" wz-next="saveCampaign()">Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>


            <!-- STEP 3 - IMPORT LEADS -->
            <wz-step title="Import Leads" canenter="canImportLeads" wz-disabled="{{!loggedInUser.isAdmin && !loggedInUser.isSuperManager && !loggedInUser.isClientAdmin}}">
                <input type="file" id="leadsDb" name="leadsDb" accept=".csv,text/csv" nv-file-select uploader="uploader" /><br />
                <div class="checkbox">
                    <label> <input ng-model="updateOnly" ng-click="updateOnlyChange($event)" type="checkbox" value=""> Only Update Leads (checking this means we won't create any new leads)</label>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50%">Name</th>
                            <th ng-show="uploader.isHTML5">Size</th>
                            <th ng-show="uploader.isHTML5">File upload progress</th>
                            <th ng-show="uploader.isHTML5">Processing progress</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-hide="uploader.queue.length > 0">
                            <td colspan="99">No files to upload</td>
                        </tr>
                        <!-- ngRepeat: item in uploader.queue -->
                        <tr ng-repeat="item in uploader.queue" class="ng-scope">
                            <td><strong>{{ item.file.name }}</strong></td>
                            <td ng-show="uploader.isHTML5" nowrap="">{{ item.file.size/1024/1024|number:2 }} MB</td>
                            <td ng-show="uploader.isHTML5">
                                <div class="progress" style="margin-bottom: 0;">
                                    <div class="progress-bar" role="progressbar" ng-style="{ 'width': item.progress + '%' }" style="width: 0%;"></div>
                                </div>
                            </td>
                            <td ng-show="uploader.isHTML5">
                                <div class="progress" style="margin-bottom: 0;">
                                    <div class="progress-bar" role="progressbar" ng-style="{ 'width': (item.progressPercent || 0) + '%' }" style="width: 0%;"></div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span ng-show="item.isSuccess && item.progressPercent < 100"><i class="fa fa-hourglass-half"></i></span>
                                <span ng-show="item.isSuccess && item.progressPercent >= 100"><i class="fa fa-check"></i></span>
                                <span ng-show="item.isCancel"><i class="fa fa-ban"></i></span>
                                <span ng-show="item.isError"><i class="fa fa-exclamation-triangle"></i></span>
                            </td>
                            <td nowrap="">
                                <button type="button" class="btn btn-success btn-xs" ng-click="item.upload()" ng-disabled="item.isReady || item.isUploading || item.isSuccess">
                                    <span class="glyphicon glyphicon-upload"></span> Upload
                                </button>
                                <button type="button" class="btn btn-warning btn-xs" ng-click="item.cancel()" ng-disabled="!item.isUploading" disabled="disabled">
                                    <span class="glyphicon glyphicon-ban-circle"></span> Cancel
                                </button>
                                <button type="button" class="btn btn-danger btn-xs" ng-click="item.remove()">
                                    <span class="glyphicon glyphicon-trash"></span> Remove
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <br>
                <h3>Import History</h3>
                <table class="table">
                    <thead>
                        <th>Date/Time</th>
                        <th>Name</th>
                        <th>Update Only</th>
                        <th>Status</th>
                        <th>Lines Read</th>
                        <th>Lines Ignored</th>
                        <th>Leads Created</th>
                        <th>Leads Updated</th>
                        <th>Call Attempts Generated</th>
                    </thead>
                    <tbody>
                        <tr ng-repeat="upload in uploads">
                            <td>{{ formatDate(upload.createdAt) }}</td>
                            <td>{{ upload.name }}</td>
                            <td>{{ upload.updateOnly ? 'Yes' : 'No' }}</td>
                            <td>{{ upload.error ? 'Errored' : upload.result ? upload.result.status : upload.percentComplete >= 100 ? 'Finished' : 'Processing' }}</td>
                            <td>{{ upload.result ? upload.result.data.linesRead : upload.totalRecords || 0 }}</td>
                            <td>{{ upload.result ? upload.result.data.linesIgnored : upload.recordsIgnored || 0 }}</td>
                            <td>{{ upload.result ? upload.result.data.leadsCreated : upload.leadsCreated || 0 }}</td>
                            <td>{{ upload.result ? upload.result.data.leadsUpdated : upload.leadsUpdated || 0 }}</td>
                            <td>{{ upload.result ? upload.result.data.callAttemptInserts : upload.callAttemptsCreated || 0 }}</td>
                        </tr>
                        <tr ng-hide="uploads && uploads.length">
                            <td colspan="99">No import history for this campaign.</td>
                        </tr>
                    </tbody>
                </table>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" wz-next>Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>

            <wz-step title="Agent Assignment" canenter="checkLeadImportStatus">
                <div class="col-sm-offset-2 col-sm-10 col-lg-7">
                    <div class="form-group">
                        <div ng-hide="campaignStages.length > 0">No campaign stages created. Please add at least 1 campaign stage to continue.</div>
                        <div ng-show="campaignStages.length > 0">
                            <table class="table">
                                <thead>
                                    <th>Name</th>
                                    <th></th>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="stage in campaignStages">
                                        <td>{{ stage.name }}</td>
                                        <td>
                                            <button class="btn btn-success" ng-click="setStageAgents(stage)">Agents</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" wz-next>Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>


            <wz-step title="Campaign Projections" canenter="canEnterCampaignProjections" wz-disabled="{{!loggedInUser.isAdmin && !loggedInUser.isSuperManager && !loggedInUser.isClientAdmin}}">
                <div class="ibox-content form-group">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">Campaign Goal</label>
                        <div class="col-sm-10">
                            <span class="form-control">{{ campaign.goal | currency:'$':0 }}</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <div ng-repeat="(key, value) in campaign.goals">
                            <label class="col-sm-2 control-label">{{ key }} Goal</label>
                            <div class="col-sm-2">
                                <span class="form-control">{{ value.goal | currency:'$':0 }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="ibox-content form-group">
                    <div class="form-group">
                        <h4 class="col-sm-2">2nd Appeal Telefunding</h4>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-sm-2">Projected # Transactions</label>
                        <div class="col-sm-2">
                            <input type="number" class="form-control" ng-model="campaign.secondAppealProjectedQuantity" ng-change="updateProjectionTotals()" />
                        </div>
                        <label class="control-label col-sm-2">Projected $/Transaction</label>
                        <div class="col-sm-2">
                            <input type="number" class="form-control" ng-model="campaign.secondAppealProjectedAvgValue" ng-change="updateProjectionTotals()" />
                        </div>
                        <label class="control-label col-sm-2">Projected $</label>
                        <div class="col-sm-2">
                            <span class="form-control">{{ projectionTotals.secondTotal | currency:'$':0 }}</span>
                        </div>
                    </div>
                </div>

                <div class="form-group ibox-content">
                    <div class="form-group col-sm-12">
                        <table class="table">
                            <thead>
                                <th>Lead Type</th>
                                <th>Projected Quantity</th>
                                <th>Projected Avg Value</th>
                                <th>Projected Value</th>
                                <th>Projected Reponse Rate</th>
                                <th>Projected # Transactions</th>
                                <th>Projected $/Transaction</th>
                                <th>Projected $</th>
                                <th ng-if="currentCampaignType == 'Telefunding'">Min Ask Amount</th>
                                <th ng-if="currentCampaignType == 'Telefunding'">Ask Amount Increase %</th>
                            </thead>
                            <tbody>
                                <tr ng-repeat="skill in skillProjections | orderBy:'priority'">
                                    <td>{{ skill.name }}</td>
                                    <td><input type="number" ng-model="skill.projection.clientAvgValue" class="form-control" min="0" /></td>
                                    <td><input type="number" ng-model="skill.projection.clientQuantity" class="form-control" min="0" /></td>
                                    <td>{{(skill.projection.clientQuantity * skill.projection.clientAvgValue) | currency:'$':0 }}</td>
                                    <td><input type="number" ng-model="skill.projection.projectedRR" class="form-control" min="0" /></td>
                                    <td><input type="number" ng-model="skill.projection.projectedQuantity" ng-change="updateProjectionTotals()" class="form-control" min="0" /></td>
                                    <td><input type="number" ng-model="skill.projection.projectedAvgValue" ng-change="updateProjectionTotals()" class="form-control" min="0" /></td>
                                    <td>{{(skill.projection.projectedQuantity * skill.projection.projectedAvgValue) | currency:'$':0 }}</td>
                                    <td ng-if="currentCampaignType == 'Telefunding'"><input type="number" ng-model="skill.projection.defaultAskAmount" class="form-control" min="0" /></td>
                                    <td ng-if="currentCampaignType == 'Telefunding'"><input type="number" ng-model="skill.projection.askAmountIncrease" class="form-control" min="0" /></td>
                                </tr>
                            </tbody>
                        </table>
                        <button type="button" class="btn btn-success" ng-click="addCampaignSkill()">Add</button>
                    </div>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" wz-next="saveCampaign(); saveProjections()">Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>


            <!-- STEP 2c - AGENT TARGET -->
            <wz-step title="Agent Target" canenter="getAgentTargetPrerequisities">
                <div class="form-group">
                    <div class="col-sm-4" ng-hide="agents.length > 0">No agents assigned to campaign stages.</div>
                    <div ng-show="agents.length > 0">
                        <div class="form-group col-sm-12">
                            <label class="control-label">Weekly Target</label>
                            <h2>{{ weeklyTarget | currency:'$':0 }}</h2>
                        </div>

                        <div class="col-sm-5">
                            <div class="input-group m-b">
                                <span class="input-group-btn">
                                    <button type="button" ng-click="previousWeek()" class="btn btn-primary"><i class="fa fa-arrow-left"></i></button>
                                </span>
                                <input readonly="readonly" type="text" ng-model="agentTargets.currentWeek.name" class="form-control" />
                                <span class="input-group-btn">
                                    <button type="button" ng-click="nextWeek()" class="btn btn-primary"><i class="fa fa-arrow-right"></i></button>
                                </span>
                            </div>
                        </div>
                        <div class="form-group col-sm-4">
                            <button type="button" class="btn btn-success" ng-click="updateAgentTotals(true)">Refresh This Weeks Goals</button>
                        </div>
                    </div>
                </div>
                <div class="form-group ibox-content" ng-show="agents.length > 0">
                    <br>
                    <div class="col-sm-12">
                        <table class="table">
                            <thead>
                                <th>Agent</th>
                                <th>Theshold/Hr</th>
                                <th>Level</th>
                                <th>Hours</th>
                                <th>Impact Hours</th>
                                <th>Threshold</th>
                                <th>Goal</th>
                                <th>Override Goal</th>
                                <th>Notes</th>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in agentTargets.currentWeek.agents">
                                    <td>{{ item.agent.name }}</td>
                                    <td>{{ item.target.thresholdPerHour | currency:'$':0 }}</td>
                                    <td>
                                        <input class="form-control" ng-focus="item.changed = true" ng-change="updateAgentTotals()" ng-model="item.target.level" type="number" step="0.5" min="1" />
                                    </td>
                                    <td>
                                        <input class="form-control" ng-focus="item.changed = true" ng-change="updateAgentTotals()" ng-model="item.target.scheduledHours" type="number" min="0" />
                                    </td>
                                    <td>{{ item.target.scheduledHours * item.target.level | number:0 }}</td>
                                    <td>{{ item.target.thresholdPerHour * item.target.scheduledHours | currency:'$':0 }}</td>
                                    <td style="font-weight: bold"><span ng-class="{'text-danger': item.target.goal < (item.target.thresholdPerHour * item.target.scheduledHours)}">{{ item.target.goal | currency:'$':0 }}</span></td>
                                    <td><input class="form-control" ng-focus="item.changed = true" ng-change="updateAgentTotals(item)" ng-model="item.target.overrideGoal" type="number" min="0" /></td>
                                    <td><input class="form-control" ng-focus="item.changed = true" ng-model="item.target.notes" type="text" /></td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="table-total">
                                    <td>Total</td>
                                    <td></td>
                                    <td></td>
                                    <td>{{ agentTargets.currentWeek.totalHours }}</td>
                                    <td>{{ agentTargets.currentWeek.totalImpactHours }}</td>
                                    <td>{{ agentTargets.currentWeek.totalThreshold | currency:'$':0 }}</td>
                                    <td><span ng-class="{'text-danger': agentTargets.currentWeek.totalGoal < agentTargets.currentWeek.totalThreshold}">{{ agentTargets.currentWeek.totalGoal | currency:'$':0 }}</span></td>
                                    <td>{{ agentTargets.currentWeek.totalOverride | currency:'$':0 }}</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" ng-click="saveAgentTargets()" wz-next>Save &amp; Continue</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>


            <!-- STEP 5 - DIALLING RULES -->
            <wz-step title="Dialing Rules" canenter="checkLeadImportStatus" canexit="saveCampaignStageDTRules">
                <div class="col-sm-offset-2 col-sm-10 col-md-8 col-lg-6">
                    <div class="form-group">
                        <div ng-hide="campaignStages.length > 0">No campaign stages created.</div>
                        <div ng-show="campaignStages.length > 0">
                            <table class="table">
                                <thead>
                                    <th>Stage</th>
                                    <th width="100px"></th>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="stage in campaignStages">
                                        <td>{{stage.name}}</td>
                                        <td><button class="btn btn-primary" ng-click="setStageDiallingRules(stage)">Dialing Rules</button></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="wz-next-container col-sm-8 col-sm-offset-3">
                    <button class="btn btn-primary" wz-next="saveCampaign()">Save &amp; Close</button>
                </div>
                <div style="clear:both;"></div>
            </wz-step>

            <!--
      <wz-step title="Campaign Summary">
        <div class="wz-next-container col-sm-8 col-sm-offset-3">
          <button class="btn btn-primary" wz-next>Save &amp; Close</button>
        </div>
      </wz-step>
 -->
        </wizard>
    </form>
</div>