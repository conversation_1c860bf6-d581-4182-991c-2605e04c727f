<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/campaigns/create">Add New</a>
			<table class="table">
				<thead>
					<th>Client</th>
					<th>Name</th>
					<th>Type</th>
					<th>No. Leads</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="campaign in campaigns | orderBy: 'client.name' | itemsPerPage:20">
						<td>{{ campaign.client.name }}</td>
						<td>{{ campaign.name }}</td>
						<td>{{ campaign.campaigntype.name }}</td>
						<td>{{ campaign.leads.count }}</td>
						<td>
							<uib-dropdown class="btn-group">
								<button type="button" class="btn btn-xs btn-primary" data-toggle="dropdown">
									Actions <span class="caret"></span>
								</button>
								<ul class="dropdown-menu" role="menu">
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/stages">View Stages</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/leads">View Leads</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/products">View Products</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/invoices">View Invoices</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/trainingdocs">View Training Materials</a></li>
									<li class="divider"></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/importleads">Import Leads</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/importtrainingdocs">Import Training Materials</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/importchanges">Import Batch Changes</a></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}/suppressed">Suppressed</li>
									<li class="divider"></li>
									<li><a ng-href="/#/admin/campaigns/{{campaign.id}}">Edit</a></li>
									<li><a ng-click="deleteCampaign(campaign)" ng-hide="campaign.leads.count">Delete</a></li>
								</ul>
							</uib-dropdown>
						</td>
					</tr>
					<tr ng-hide="campaigns.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>