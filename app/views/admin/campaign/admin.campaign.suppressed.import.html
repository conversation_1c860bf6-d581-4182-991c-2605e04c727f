<div>
    <div class="modal-header">
        <h3 class="modal-title">Import Suppressions</h3>
    </div>
    <div class="modal-body">
        <div class="row">
            <div class="col-xs-12 form-group">
                <label for="">Destination Stage</label>
                <select class="form-control" ng-model="campaignstageId" ng-disabled="uploading || processing" ng-options="item.id as item.name for item in stages">
                    <option value="">No Destination Stage</option>
                </select>
            </div>
            <div class="col-xs-6 form-group">
                <label for="">Start Date</label>
                <p class="input-group">
                    <input type="text" class="form-control" ng-disabled="uploading || processing" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="startDate" is-open="startDateOpened" close-text="Close" />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-disabled="uploading || processing" ng-click="openStartDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </p>
            </div>
            <div class="col-xs-6 form-group">
                <label for="">End Date</label>
                <p class="input-group">
                    <input type="text" class="form-control" ng-disabled="uploading || processing" uib-datepicker-popup="{{'dd-MMMM-yyyy'}}" ng-model="endDate" is-open="endDateOpened" close-text="Close" />
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-default" ng-disabled="uploading || processing" ng-click="openEndDate($event)"><i class="glyphicon glyphicon-calendar"></i></button>
                    </span>
                </p>
            </div>
            <div class="col-xs-6 form-group">
                <input type="file" id="suppressionFile" ng-disabled="uploading || processing" />
            </div>
            <div class="col-xs-6 form-group">
                <label class="control-label"><input ng-model="updateOnly" type="checkbox"> Update Only</label>
            </div>
            <div class="col-xs-12" style="margin-bottom: 10px" ng-if="uploading || processing || error">
                <p ng-if="uploading">Uploading</p>
                <p ng-if="processing">Processing ({{percentComplete}}% Complete)</p>
                <p ng-if="error" class="text-danger">{{error}}</p>
                <div class="row" ng-if="finished">
                    <div class="col-xs-4">Total: {{progress.linesRead}}</div>
                    <div class="col-xs-4">Processed: {{progress.leadsMoved}}</div>
                    <div class="col-xs-4">Ignored: {{progress.leadNotFound}}</div>
                </div>
            </div>
            <div class="col-xs-12">
                <button class="btn btn-success" ng-click="import()" ng-if="!finished" ng-disabled="uploading || processing">Import</button>
                <button class="btn btn-primary" ng-click="finish()" ng-if="finished">Finish</button>
                <button class="btn btn-grey" ng-click="cancel()">Cancel</button>
            </div>
        </div>
    </div>

</div>

</div>