<div class="ibox">
    <div class="ibox-content">
        <div class="form-group">
            <h4>Filters</h4>
            <select class="form-control" ng-model="type" ng-change="pageChanged(1)">
                <option value="">All Actions</option>
                <option value="batchpayments">Payments</option>
                <option value="batchchangestage">Change Stage</option>
                <option value="batchexportinvoices">Export Invoices</option>
            </select>
        </div>
    </div>
</div>
<div class="ibox">
    <div class="ibox-content">
        <div class="row">
            <div class="col-xs-12">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Stage</th>
                            <th>Total</th>
                            <th>Processed</th>
                            <th>Ignored</th>
                            <th>Invalid</th>
                            <th>User</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr ng-if="loading">
                            <td colspan="99" style="text-align: center">Loading</td>
                        </tr>
                        <tr ng-if="!loading && !history.length">
                            <td colspan="99" style="text-align: center">No History Found</td>
                        </tr>
                        <tr ng-show="!loading" dir-paginate="row in history | itemsPerPage: limit" total-items="total" current-page="page">
                            <td>{{(row.createdAt) | date : 'medium'}}</td>
                            <td>{{row.type === 'batchchangestage' ? 'Change Stage' : (row.type === 'batchpayments' ? 'Import Payments' : (row.type === 'batchexportinvoices' ? 'Export Invoices' : 'Other'))}}</td>
                            <td>{{row.type === 'batchchangestage' ? getStage(row.stage) : ''}}</td>
                            <td>{{row.total}}</td>
                            <td>{{row.updated}}</td>
                            <td>{{row.ignored}}</td>
                            <td>{{row.invalid}}</td>
                            <td>{{row.user ? row.user.name : ''}}</td>
                            <td>
                                <button type="button" class="btn btn-primary btn-xs" ng-click="download(row)">Download</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <dir-pagination-controls on-page-change="pageChanged(newPageNumber)"></dir-pagination-controls>
            </div>
        </div>
    </div>
</div>