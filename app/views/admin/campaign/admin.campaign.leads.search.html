<form role="form" ng-submit="search()" name="form">
  <div class="modal-header">
    <h3 class="modal-title">Search Leads</h3>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="form-group col-sm-6">
        <label>KAOS Id</label>
        <input type="text" class="form-control" placeholder="KAOS Id" ng-model="filters.id" />
      </div>
      <div class="form-group col-sm-6">
        <label>Client Ref</label>
        <input type="text" class="form-control" placeholder="Client Ref" ng-model="filters.clientRef" />
      </div>
    </div>    
    <div class="row">
      <div class="form-group col-sm-6">
        <label>First Name</label>
        <input type="text" class="form-control" placeholder="First Name" ng-model="filters.first_name" />
      </div>
      <div class="form-group col-sm-6">
        <label>Last Name</label>
        <input type="text" class="form-control" placeholder="Last Name" ng-model="filters.last_name" />
      </div>
    </div>
    <div class="form-group">
      <label>Spouse Name</label>
      <input type="text" class="form-control" placeholder="Spouse Name" ng-model="filters.spouse_name" />
    </div>
    <div class="form-group">
      <label>Company Name</label>
      <input type="text" class="form-control" placeholder="Company Name" ng-model="filters.company_name" />
    </div>
    <div class="form-group">
      <label>Address 1</label>
      <input type="text" class="form-control" placeholder="Address 1" ng-model="filters.address1" />
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Address 2</label>
        <input type="text" class="form-control" placeholder="Address 2" ng-model="filters.address2" />
      </div>
      <div class="form-group col-sm-6">
        <label>Address 3</label>
        <input type="text" class="form-control" placeholder="Address 3" ng-model="filters.address3" />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>City</label>
        <input type="text" class="form-control" placeholder="City" ng-model="filters.city" />
      </div>
      <div class="form-group col-sm-6">
        <label>State</label>
        <input type="text" class="form-control" placeholder="State" ng-model="filters.state" />
      </div>
    </div>
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Zip</label>
        <input type="text" class="form-control" placeholder="Zip" ng-model="filters.zip" />
      </div>
    </div>    
    <div class="row">
      <div class="form-group col-sm-6">
        <label>Phone</label>
        <input type="text" class="form-control" placeholder="Phone" ng-model="filters.phone" />
      </div>
    </div>    
    <div class="form-group">
      <label>Email</label>
      <input type="email" class="form-control" placeholder="Email" ng-model="filters.email" />
    </div>
    <div style="clear:both;"></div>
  </div>
  <div class="modal-footer">
    <button class="btn btn-success" type="submit">Search</button>
    <button class="btn btn-danger" ng-click="cancel()">Cancel</button>
  </div>
</form>