<div class="ibox">
	<div class="ibox-content">
		<div class="tab-pane">
			<a class="btn btn-success btn-add-new" ng-href="/#/admin/datetimerulesets/create">Add New</a>
			<table class="table">
				<thead>
					<th>Name</th>
					<th>Description</th>
					<th>Start Time</th>
					<th>End Time</th>
					<th>Days Of Week</th>
					<th></th>
				</thead>
				<tbody>
					<tr dir-paginate="dtRuleset in dateTimeRuleSets | orderBy: 'name' | itemsPerPage:20">
						<td>{{ dtRuleset.name }}</td>
						<td>{{ dtRuleset.description }}</td>
						<td>{{ dtRuleset.startTime }}</td>
						<td>{{ dtRuleset.endTime }}</td>
						<td>{{ getDaysOfWeekAsString(dtRuleset) }}</td>
						<td>
							<a class="btn btn-xs btn-primary" ng-href="/#/admin/datetimerulesets/{{dtRuleset.id}}">Edit</a>
							<a class="btn btn-xs btn-danger" ng-disabled="dtRuleset.campaignstagedatetimerules.length" ng-click="deleteDateTimeRuleSet(dtRuleset)">Delete</a>
						</td>
					</tr>
					<tr ng-hide="dateTimeRuleSets.length > 0">
						<td colspan="99">No items found</td>
					</tr>
				</tbody>
			</table>
			<dir-pagination-controls></dir-pagination-controls>
		</div>
	</div>
</div>