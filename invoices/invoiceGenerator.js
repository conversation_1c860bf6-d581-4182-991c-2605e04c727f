var moment = require('moment')
var _ = require('underscore')
var pdf = require('../reporting/reportToPDF')()
var APP_CONFIG = require('../config/constants')
var Promise = require('sequelize').Promise
var path = require('path')

module.exports = function (Models) {
	var emailer = require('../emailing/email')(Models)

	var invoiceBlankContext = {
		logo: '',
		longDate: '',
		salutation: '',
		invoiceText: '',
		signature: '',
		clientRef: '',
		clientCompany: '',
		clientContactName: '',
		clientContactTitle: '',
		clientWebsite: '',
		clientAddress1: '',
		clientAddress2: '',
		clientPhone: '',
		leadName: '',
		leadAddress1: '',
		leadAddress2: '',
		invoiceAmount: '',
		payByDate: '',
		ticketCount: '',
		pledgeDate: '',
		campaignName: '',
		leadRef: '',
		printDate: ''
	}

	var generatePdfsForInvoices = function (arrayOfInvoices, demo, testing) {
		var generatePdfPromises = []
		var createHistoryPromises = []
		var contexts = []

		arrayOfInvoices.forEach(function (invoice) {
			var context = invoiceToContext(invoice)
			var name = cleanString(invoice.client.name + '.' + (invoice.lead.last_name ? invoice.lead.last_name : invoice.lead.id) + ' ' + moment().format('HH-mm-ss DDMMYY'))

			contexts.push({
				name: name,
				object: context
			})

			if (demo) {
				generatePdfPromises.push(pdf.exportToPDF('invoices', 'demoTestLayout.html', context, name))
			}
			else if (testing) {
				generatePdfPromises.push(pdf.exportToPDF('invoices', 'testLayout.html', context, name))
			}
			else {
				generatePdfPromises.push(pdf.exportToPDF('invoices', 'invoiceLayout.html', context, name))
			}
		})

		return new Promise(function (resolve, reject) {
			Promise.all(generatePdfPromises).then(function (results) {
				for (var i = 0; i < arrayOfInvoices.length; i++) {
					createHistoryPromises.push(Models.InvoiceHistory.create({
						requestAmount: arrayOfInvoices[i].amountRemaining,
						invoiceHtml: contexts[i].name,
						invoiceId: arrayOfInvoices[i].id
					}))
				}

				Promise.all(createHistoryPromises).then(resolve).catch(reject)
			})
				.catch(reject)
		})
	}

	var generatePdfForInvoice = function (invoice) {
		var context = invoiceToContext(invoice)
		var name = cleanString(invoice.client.name + '.' + (invoice.lead.last_name ? invoice.lead.last_name : invoice.lead.id) + ' ' + moment().format('HH-mm-ss DDMMYY'))

		return new Promise((resolve, reject) => {
			pdf.exportToPDF('invoices', 'invoiceLayout.html', context, name)
				.then(pdf => {
					Models.InvoiceHistory.create({
						requestAmount: invoice.amountRemaining,
						invoiceHtml: name,
						invoiceId: invoice.id
					}).then(resolve).catch(reject)
				})
				.catch(reject)
		})
	}


	var invoiceToContext = function (invoice) {
		var context = _.clone(invoiceBlankContext)
		context.pathToPublic = path.relative(__dirname, APP_CONFIG.PUBLIC_LOCATION)
		context.logo = invoice.client && invoice.client.logo ? path.relative(__dirname, APP_CONFIG.CLIENT_LOGO_LOCATION) + '/' + invoice.client.logo : ''
		context.longDate = moment().format('MMMM D, YYYY')
		context.salutation = invoice.client.letterSalutation
		context.signature = invoice.client.signature ? path.relative(__dirname, APP_CONFIG.SIGNATURE_LOCATION) + '/' + invoice.client.signature : ''
		context.clientRef = invoice.client.id + ''
		context.kaosId = invoice.lead.id
		context.sourceCode = invoice.lead.clientSourceCode
		context.clientCompany = invoice.client.name
		context.clientContactName = invoice.client.primaryContactName || ''
		context.clientContactTitle = invoice.client.contactTitle || ''
		context.clientWebsite = invoice.client.website || ''
		context.clientAddress1 = invoice.client.address1 || ''
		if (invoice.client.address2) {
			context.clientAddress2 = invoice.client.address2
			context.clientAddress3 = (invoice.client.city || '') + ', ' + (invoice.client.state || '') + ' ' + (invoice.client.zip || '')
		} else {
			context.clientAddress2 = (invoice.client.city || '') + ', ' + (invoice.client.state || '') + ' ' + (invoice.client.zip || '')
			context.clientAddress3 = ''
		}

		context.clientPhone = invoice.client.primaryContactNumber || ''
		context.leadName = (invoice.lead.first_name || '') + ' ' + (invoice.lead.last_name || '')
		context.leadAddress1 = invoice.lead.address1 || ''
		context.leadAddress2 = !!invoice.lead.address2 ? invoice.lead.address2 : ((invoice.lead.city || '') + ', ' + (invoice.lead.state || '') + ' ' + (invoice.lead.zip || ''))
		context.leadAddress3 = !!invoice.lead.address2 ? ((invoice.lead.city || '') + ', ' + (invoice.lead.state || '') + ' ' + (invoice.lead.zip || '')) : ''

		context.invoiceAmount = invoice.amountRemaining === 0.01 ? 'N/A' : '$' + invoice.amountRemaining
		if (invoice.callresult && invoice.callresult.campaignstage && invoice.callresult.campaignstage.name === 'Collections' && invoice.amountRemaining < invoice.grandTotal) {
			context.invoiceAmount += ' / $' + invoice.grandTotal
		}
		context.grandTotal = invoice.grandTotal ? '$' + invoice.grandTotal : 'N/A'

		if (invoice.callresult) {
			context.payByDate = invoice.amountRemaining === 0.01 ? 'N/A' : moment(invoice.callresult.payDate || '').format('MMM DD YYYY')
			context.ticketCount = invoice.callresult.freeTickets || '0'
			context.pledgeDate = moment(invoice.callresult.createdAt || '').format('MM/DD/YYYY')
		} else {
			context.payByDate = moment().add(30, 'days').format('MMM DD YYYY')
			context.ticketCount = '0'
			context.pledgeDate = moment(invoice.createdAt).format('MMM DD YYYY')
		}
		context.campaignName = invoice.campaign.name
		context.leadRef = invoice.lead.clientRef
		context.message = invoice.client.invoiceMessage || ''
		context.printDate = moment().format('MM/DD/YYYY')

		if (invoice.callresult && invoice.callresult.campaignstage) {
			switch (invoice.callresult.campaignstage.name) {
				case '1st Appeal':
					context.invoiceText = invoice.client.firstAppealInvoiceText || ''
					break;
				case '2nd Appeal':
					context.invoiceText = invoice.client.secondAppealInvoiceText || ''
					break;
				default:
					context.invoiceText = invoice.client.followUpInvoiceText || ''
					break;
			}
		} else {
			context.invoiceText = invoice.client.firstAppealInvoiceText
		}

		context.invoiceText = invoice.requestCount > 0 ? invoice.client.followUpInvoiceText || '' : context.invoiceText
		context.invoiceText = parseInvoiceText(context.invoiceText)

		return context;
	}

	function parseInvoiceText(text) {
		result = "<p>" + text + "</p>"
		result = result.replace(/\r\n\r\n/g, "</p><p>").replace(/\n\n/g, "</p><p>")
		result = result.replace(/\r\n/g, "<br />").replace(/\n/g, "<br />")
		return result
	}

	function cleanString(input) {
		return input.replace(/[|&;$%@"<>()/+,]/g, "")
	}

	return {
		generatePdfsForInvoices: generatePdfsForInvoices,
		generatePdfForInvoice: generatePdfForInvoice,
		invoiceToContext: invoiceToContext
	}
}