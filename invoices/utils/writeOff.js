var howMuchPaid = require('./howMuchPaid')
var disableRemainingPayments = require('./disableRemainingPayments')
var removeFromCollections = require('./removeFromCollections')
var leadUtils = require('../../utils/lead')

module.exports = (Models, invoice, userId, writeOffAmount) => {
    writeOffAmount = parseFloat(writeOffAmount)

    // Prevent errors with += later
    if (!invoice.writtenOffAmount) invoice.writtenOffAmount = 0

    // Keep a copy of the original invoice and determine if this is a partial or full write off
    var partialWriteOff = !!writeOffAmount
    var oldInvoice = JSON.parse(JSON.stringify(invoice))

    return howMuchPaid(Models, invoice).then(alreadyPaid => {
        // Determine the true amount remaining based on payment logs
        var trueAmountRemaining = invoice.grandTotal - alreadyPaid - invoice.writtenOffAmount

        console.log('Write Off: Invoice ID ' + invoice.id + ' trueAmountRemaining:' + trueAmountRemaining)

        // 1) Adjust remaining balance using paymentlogs
        if (partialWriteOff) {
            invoice.amountRemaining = trueAmountRemaining - writeOffAmount
            if (invoice.amountRemaining < 0)
                invoice.amountRemaining = 0
        }
        else {
            invoice.amountRemaining = 0
        }

        console.log('Write Off: Invoice ID ' + invoice.id + ' amountRemaining:' + invoice.amountRemaining)

        // 2) Set writtenOffAmount
        if (partialWriteOff) {
            if (trueAmountRemaining < writeOffAmount)
                invoice.writtenOffAmount += trueAmountRemaining
            else
                invoice.writtenOffAmount += writeOffAmount
        }
        else {
            invoice.writtenOffAmount += trueAmountRemaining
        }

        console.log('Write Off: Invoice ID ' + invoice.id + ' writtenOffAmount:' + invoice.writtenOffAmount)

        // 3)
            // if (full write off) {
            //     Set writtenOff
            //     Set sendInvoice to false
            //     Move to null if in collections stage?
            // }
            // else {
            //     leadUtils.resetDontContactUntil(Models, invoice.leadId).catch(() => { })
            // }
        
        if (partialWriteOff) {
            // Partial write off
            leadUtils.resetDontContactUntil(Models, invoice.leadId).catch(_ => { })
        }
        else {
            // Full write off
            invoice.writtenOff = true
            invoice.sendInvoice = false
            
            // Move to null if in collections stage?
            removeFromCollections(Models, invoice).catch(_ => { })
        }

        // 4) Disable/delete remaining payments
        disableRemainingPayments(Models, invoice).catch(_ => { })

        // 5) Add invoice events:
        if (!oldInvoice.writtenOff && invoice.amountRemaining === 0) {
            Models.InvoiceEvent.create({
                invoiceId: invoice.id,
                userId: userId,
                changeType: 'Write Off',
                field: 'writtenOff',
                fromValue: false,
                toValue: true
            })
        }
        else if (partialWriteOff) {
            Models.InvoiceEvent.create({
                invoiceId: invoice.id,
                userId: userId,
                changeType: 'Partial Write Off',
                field: 'writtenOffAmount',
                fromValue: oldInvoice.writtenOffAmount,
                toValue: invoice.writtenOffAmount
            })
        }
        Models.InvoiceEvent.create({
            invoiceId: invoice.id,
            userId: userId,
            changeType: partialWriteOff ? 'Partial Write Off' : 'Amount Remaining',
            field: 'amountRemaining',
            fromValue: trueAmountRemaining,
            toValue: invoice.amountRemaining
        })

        // 6) Save invoice?

        return invoice.save()
    })
}