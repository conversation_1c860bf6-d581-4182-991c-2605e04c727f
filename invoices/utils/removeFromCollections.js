var transitionlead = require('../../data-merge/transitionLead')

module.exports = (Models, invoice) => {
    return new Promise((resolve, reject) => {
        Models.CampaignLead.findOne({
            where: {
                leadId: invoice.leadId,
                campaignId: invoice.campaignId
            }
        })
        .then(cl => {
            if (cl && cl.currentCampaignStageId) {
                Models.CampaignStage.findOne({
                    where: {
                        id: cl.currentCampaignStageId
                    }
                })
                .then(stage => {
                    if (stage && (stage.name.indexOf('Collections') > -1 || stage.name.indexOf('Bad Credit Cards') > -1))
                        resolve(transitionlead(Models, invoice.leadId, null, invoice.campaignId))
                    else
                        resolve({
                            success: true
                        })
                })
                .catch(reject)
            }
            else {
                resolve({
                    success: true
                })
            }
        })
        .catch(reject)
    })
}