module.exports = (Models, invoice) => {
    return new Promise((resolve, reject) => {
        Models.PaymentLog.findAll({
            where: {
                invoiceId: invoice.id,
                deleted: false,
                disabled: false,
                isPaid: true
            }
        })

        .then(paymentLogs => {
            var alreadyPaid = 0

            if (paymentLogs && paymentLogs.length) {
                paymentLogs.forEach(pl => {
                    alreadyPaid += pl.amount
                })
            }
            // else {
            //     alreadyPaid = invoice.grandTotal - invoice.amountRemaining
            // }

            resolve(alreadyPaid)
        })
        .catch(reject)
    })
}