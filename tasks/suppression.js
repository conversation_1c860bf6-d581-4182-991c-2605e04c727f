var moment = require('moment')
var tsys = require('../utils/tsys')
var schedule = require('node-schedule')
var transitionLead = require('../data-merge/transitionLead')
var LIMIT = 500
var Models

module.exports = function (_Models) {
    Models = _Models
    // just set it to run the next minute from when this script starts
    var suppressJob = schedule.scheduleJob('*/2 * * * *', Suppress)
    var unsuppressJob = schedule.scheduleJob('*/2 * * * *', Unsuppress)
}

function Suppress() {
    console.log(moment().format('YYYY-MM-DD HH:mm'), 'Suppressing')
    return Models.Suppression.findAll({
        where: {
            startDate: {
                $lt: new Date()
            },
            actualStartDate: {
                $eq: null
            },
            finished: false
        },
        limit: LIMIT
    }).then(supressions => {
        console.log('supressing', supressions.length)
        return Promise.all(supressions.map(suppression => {
            console.log('Suppress', suppression.leadId, suppression.campaignId)
            if (!suppression.leadId || !suppression.campaignId) {
                return suppression.update({
                    finished: true
                })
            }

            return Models.Lead.findOne({
                where: {
                    id: suppression.leadId
                },
                include: [{
                    model: Models.Campaign,
                    where: { id: suppression.campaignId },
                    attributes: ['id', 'name'],
                    include: [{
                        model: Models.CampaignStage,
                        attributes: ['id', 'name'],
                    }]
                }]
            }).then(lead => {
                var campaign, stage
                if (lead && lead.campaigns && lead.campaigns.length) campaign = lead.campaigns[0]
                if (campaign && campaign.campaignleads && campaign.campaignstages) {
                    stage = campaign.campaignstages.find(s => s.id === campaign.campaignleads.currentCampaignStageId)
                }
                if (stage && (stage.name === 'Collections' || stage.name === 'Bad Credit Cards')) {
                    // ignore if in collections or bad cc stages
                    return suppression.update({
                        finished: true,
                        skipped: true
                    })
                }
                if (suppression.currentStage && campaign && campaign.campaignleads) {
                    return suppression.update({
                        campaignstageId: campaign.campaignleads.currentCampaignStageId
                    })
                }
            }).then(() => {
                if (!suppression.finished) {
                    return transitionLead(Models, suppression.leadId, null, suppression.campaignId).then(() => {
                        removeCallbacks(suppression.leadId, suppression.campaignId)
                        return suppression.update({
                            actualStartDate: new Date()
                        })
                        // if there are pending callbacks then delete them as well
                    })
                }
            }).catch(err => {
                console.log('ERR IN SUPPRESS:', suppression ? suppression.leadId : '', err ? err.message || err : 'Unknown')
            })
        }))
    }).then(results => {
        // run again if there are more than the limit
        if (results.length >= LIMIT) {
            return Suppress()
        }
    }).catch(err => {
        console.log('ERR IN SUPPRESS:', err ? err.message || err : 'Unknown')
    })
}

function Unsuppress() {
    console.log(moment().format('YYYY-MM-DD HH:mm'), 'Unsuppressing')
    return Models.Suppression.findAll({
        where: {
            endDate: {
                $lt: new Date()
            },
            actualEndDate: {
                $eq: null
            },
            finished: false
        },
        limit: LIMIT
    }).then(supressions => {
        console.log('unsupressing', supressions.length)
        return Promise.all(supressions.map(suppression => {
            if (!suppression.leadId || !suppression.campaignId) {
                return suppression.update({
                    finished: true
                })
            }
            return transitionLead(Models, suppression.leadId, suppression.campaignstageId, suppression.campaignId).then(() => {
                return suppression.update({
                    finished: true,
                    actualEndDate: new Date()
                })
            })
        }))
    }).then(results => {
        // run again if there are more than the limit
        if (results.length >= LIMIT) {
            return Unsuppress()
        }
    }).catch(err => {
        console.log('ERR IN UNSUPPRESS:', err ? err.message || err : 'Unknown')
    })
}

function removeCallbacks(leadId, campaignId) {
    return Models.Callback.findAll({
        where: {
            deleted: false,
            leadId, campaignId
        }
    }).then(callbacks => {
        if (callbacks && callbacks.length) {
            console.log('found', callbacks.length, 'to remove')
            return Promise.all(callbacks.map(cb => {
                return cb.update({
                    deleted: true
                })
            }))
        }
    }).catch(console.log)
}