var moment = require('moment')
var schedule = require('node-schedule')
var _ = require('underscore')
var Promise = require('sequelize').Promise
var path = require('path')
var fs = require('fs')
var archiver = require('archiver')
var APP_SETTINGS = require('../config/constants')
var transitionLead = require('../data-merge/transitionLead')

module.exports = function (Models) {
	var email = require('../emailing/email')(Models)
	var invoiceGenerator = require('../invoices/invoiceGenerator')(Models)

	var emailInvoices = schedule.scheduleJob({
		hour: 10,
		minute: 0
	}, function () {
		console.log('sending invoices')
		Models.Invoice.findAll({
			where: {
				sendInvoice: true,
				deliveryMethod: 'Email',
				amountRemaining: {
					$gt: 0
				}
			},
			include: [{
				model: Models.Lead,
				required: true
			}, {
				model: Models.Client,
				required: true
			}, {
				model: Models.Campaign,
				required: true
			}, {
				model: Models.CallResult,
				include: [Models.CampaignStage]
			}]
		})
			.then(function (invoices) {
				if (invoices && invoices.length) {
					var p = Promise.resolve()
					invoices.forEach(invoice => {
						(function () {
							p = p.then(() => {
								return new Promise((resolve, reject) => {
									invoiceGenerator.generatePdfForInvoice(invoice)
										.then(history => {
											var filepath = path.resolve('./invoices/pdf/' + history.invoiceHtml + '.pdf')
											var attachments = [{
												filename: history.invoiceHtml + '.pdf',
												path: filepath
											}]
											var context = {
												leadName: invoice.lead.first_name + ' ' + invoice.lead.last_name,
												client: invoice.client.name,
												amountRemaining: history.requestAmount
											}
											var emailContext = {
												client: invoice.client.name
											}
											var subject = invoice.client.name + ' Pledge Reminder'
											if (invoice.client.invoiceSubject) {
												subject = invoice.client.invoiceSubject
											}
											email.emailWithAttachments('invoiceAttached', invoice.lead.email, invoice.client.returnEmail, attachments, subject, emailContext)
												.then(emailResult => {
													invoice.requestCount = (invoice.requestCount || 0) + 1
													invoice.sendInvoice = false
													invoice.dueDate = moment().add(30, 'days').toDate()
													invoice.updateAttributes({
														requestCount: invoice.requestCount,
														dueDate: invoice.dueDate,
														sendInvoice: false
													}).then(() => {
														resolve()
													})
												})
												.catch(err => {
													// send <NAME_EMAIL> saying the email failed
													if (invoice && invoice.update && invoice.Campaign && invoice.leadId) {
														email.plainEmail(null, '<EMAIL>', 'KAOS Failed Email Invoice', `Failed to send an Invoice to KAOS ID ${invoice.leadId} on Campaign - ${invoice.Campaign.name}`).catch(() => { })
														invoice.updateAttributes({
															sendInvoice: false
														}).catch(() => { })
													}
													console.log(err)
													resolve()
												})
										})
										.catch(err => {
											console.log(err)
											resolve()
										})
								})
							})
						}())
					})

					p.then(() => {
						console.log('completed generating invoices')
					})
				}
			})
			.catch(function (err) {
				console.log(err.message || err)
			})
	})

	var createPaperInvoice = schedule.scheduleJob({
		dayOfWeek: 1,
		minute: 0
	}, function () {
		console.log('creating paper invoices')
		Models.Invoice.findAll({
			where: {
				sendInvoice: true,
				deliveryMethod: 'Paper',
				amountRemaining: {
					$gt: 0
				},
				createdAt: {
					$lt: moment().startOf('day').toDate()
				}
			},
			include: [{
				model: Models.Lead
			}, {
				model: Models.Client
			}, {
				model: Models.Campaign
			}, {
				model: Models.CallResult,
				include: [Models.CampaignStage]
			}],
			limit: 100
		})
			.then(function (invoices) {
				if (invoices && invoices.length) {
					var p = Promise.resolve()
					var names = []
					invoices.forEach(invoice => {
						(function () {
							p = p.then(() => {
								return new Promise((resolve, reject) => {
									invoiceGenerator.generatePdfForInvoice(invoice)
										.then(history => {
											names.push(history.invoiceHtml)
											resolve()
										})
										.catch(err => {
											console.log(err)
											resolve()
										})
								})
							})
						}())
					})

					p.then(() => {
						var name = 'invoices_' + moment().format('DDMMYY_HHmmss') + '.zip'
						var output = fs.createWriteStream(APP_SETTINGS.BULK_INVOICE_LOCATION + name)
						var archive = archiver('zip')

						output.on('close', function () {
							Models.System.findOne({
								where: {
									key: 'invoiceBulkRecipient'
								}
							})
								.then(function (address) {
									if (address && address.value) {
										var filepath = path.resolve(APP_SETTINGS.BULK_INVOICE_LOCATION + name)
										var attachments = [{
											filename: name,
											path: filepath
										}]
										var emailContext = {
											client: 'Kaos',
											download: APP_SETTINGS.EXTERNAL_URL + '/bulkinvoices/' + name
										}
										email.emailWithoutAttachments('invoiceLink', address.value, null, 'Paper Invoices', emailContext)
											.then(function () {
												invoices.forEach(function (inv) {
													inv.updateAttributes({
														requestCount: inv.requestCount + 1,
														dueDate: moment().add(30, 'days').toDate(),
														sendInvoice: false
													})
												})
											})
											.catch(function (err) {
												console.log(err)
											})
									} else {
										//default email address?
									}
								})
						})

						archive.on('error', function (err) {
							console.log(err)
							throw err
						})

						archive.pipe(output)
						for (var i = 0; i < names.length; i++) {
							archive.append(fs.createReadStream('./invoices/pdf/' + names[i] + '.pdf'), {
								name: names[i] + '.pdf'
							})
						}

						archive.finalize()
					})
				}
			})
	})

	var checkForExpiredInvoices = schedule.scheduleJob({
		hour: 8,
		minute: 0
	}, function () {
		console.log('checking invoice dates')
		Models.Invoice.findAll({
			where: {
				dueDate: {
					$lt: new Date()
				},
				sendInvoice: false,
				amountRemaining: {
					$gt: 0
				},
				requestCount: {
					$lt: 3
				}
			}
		})
			.then(function (invoices) {
				if (invoices && invoices.length) {
					invoices.forEach(function (invoice) {
						if (invoice.requestCount == 2) {
							invoice.deliveryMethod = 'Email'
						}
						invoice.updateAttributes({
							sendInvoice: true,
							deliveryMethod: invoice.deliveryMethod
						})
					})
				}
			})
	})

	var checkForBadCreditCards = schedule.scheduleJob({
		hour: 8,
		minute: 0
	}, function () {
		// check for any campaign leads in the bad cc stage for over 30 days
		// if so then transition to collections stage
		Models.CampaignStage.findAll({
			where: {
				name: 'Bad Credit Cards',
				endDate: {
					$gte: new Date()
				}
			},
			attributes: ['id'],
			raw: true,
			logging: false
		}).then(stages => {
			return Models.CampaignLead.findAll({
				where: {
					transitionDate: {
						$lt: moment().subtract(30, 'days').toDate()
					},
					currentCampaignStageId: {
						$in: stages.map(s => s.id)
					}
				},
				attributes: ['leadId', 'transitionDate', 'campaignId', 'currentCampaignStageId']
			})
		}).then(campaignLeads => {
			console.log(moment().format(), 'found', campaignLeads.length, 'leads in bad CC to move')
			var index = 0
			function loop() {
				var campaignlead = campaignLeads[index]
				if (!campaignlead) return Promise.resolve()
				index++;
				return Models.CampaignStage.findOne({
					attributes: ['id', 'name'],
					where: {
						campaignId: campaignlead.campaignId,
						name: 'Collections'
					},
					raw: true,
					logging: false
				}).then(stage => {
					if (stage) {
						console.log('MOVING', campaignlead.leadId, 'out of Bad CC to Collections stage on Campaign', campaignlead.campaignId)
						return transitionLead(Models, campaignlead.leadId, stage ? stage.id : null, campaignlead.campaignId)
					}
					else {
						// if there is no Collections stage on that campaign then just reset the transition date so it doesnt get selected again
						console.log('NOT MOVING', campaignlead.leadId, 'out of Bad CC to Collections stage on Campaign', campaignlead.campaignId)
						return campaignlead.update({
							transitionDate: new Date()
						})
					}
				}).then(() => {
					return loop()
				})
			}
			return loop()
		}).then(() => {
			console.log('finished checking for bad cc stages')
		})
	})
}