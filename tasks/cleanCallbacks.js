var pmx = require('pmx')

module.exports = function (Models) {
	(function checkForExpiredCallbacks() {
		var now = new Date()
		console.log('CHECKING EXPIRED CALLBACKS');

		Models.Callback.findAll({
				where: {
					endDateTime: {
						lte: now
					},
					deleted: {
						$or: {
							$ne: true,
							$eq: null
						}
					}
				},
				include: [Models.CallResult],
				logging: false
			})
			.then(function (callbacks) {
				for (var i = 0; i < callbacks.length; i++) {
					var callback = callbacks[i];
					try {
						if (callback.callAttemptJson) {
							var callAttempt = JSON.parse(callback.callAttemptJson)
							callAttempt.leadId = callback.leadId
							delete callAttempt.id
							Models.CallAttempt.create(callAttempt)
							callback.updateAttributes({
								deleted: 1,
								expired: 1
							})
						}
					} catch (e) {
						console.log(e)
						callback.updateAttributes({
							deleted: 1,
							expired: 1
						})
					}
				}
			})

		setTimeout(function () {
			checkForExpiredCallbacks()
		}, 30000)
	})()
}