var sessionAPI = require('../database/redisAPI')('agentsession')
var agentAPI = require('../database/redisAPI')('agent')
var campaignAPI = require('../database/redisAPI')('campaign')
var moment = require('moment')
var _ = require('underscore');

var APP_SETTINGS = require('../config/constants')
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize);

(function fix() {
    //reset the payroll and write it to the database
    console.log('chekcing agent payrolls!')
    var date = moment().add(-1, 'days')
    var dateFormat = date.format('DDMMYY')
    Models.Agent.findAll()
        .then(function (agents) {
            Models.Campaign.findAll()
                .then(function (campaigns) {
                    _.each(agents, function (agent) {
                        _.each(campaigns, function (campaign) {
                            var agentPromises = [];
                            campaignAPI.get(campaign.id + '-' + agent.id, 'payroll-' + dateFormat)
                                .then(function (stat) {
                                    if (stat) {
                                        Models.Payroll.create({
                                            date: date,
                                            totalSeconds: stat,
                                            agentId: agent.id,
                                            campaignId: campaign.id
                                        })
                                    }
                                    campaignAPI.delete(campaign.id + '-' + agent.id, 'payroll-' + dateFormat)
                                    agentAPI.delete(agent.id, 'payroll-' + dateFormat)
                                    campaignAPI.delete(campaign.id, 'payroll-' + dateFormat)
                                })
                            campaignAPI.get(campaign.id + '-' + agent.id, 'loginduration-' + dateFormat)
                                .then(stat => {
                                    if (stat) {
                                        Models.LoginDuration.create({
                                            date: date,
                                            totalSeconds: stat,
                                            agentId: agent.id,
                                            campaignId: campaign.id
                                        })
                                    }
                                    campaignAPI.delete(campaign.id + '-' + agent.id, 'loginduration-' + dateFormat)
                                    agentAPI.delete(agent.id, 'loginduration-' + dateFormat)
                                    campaignAPI.delete(campaign.id, 'loginduration-' + dateFormat)
                                })
                        })
                        sessionAPI.get(agent.id)
                            .then(function (session) {
                                //this means there is a session progress so reset the starttime for the state
                                if (session) {
                                    session.stateStartTime = new Date()
                                    sessionAPI.save(session)
                                }
                            })
                    })
                })

        })
})();