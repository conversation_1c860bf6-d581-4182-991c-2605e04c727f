var pmx = require('pmx')
var moment = require('moment')
var lastMessage = 'ok'

module.exports = function (Models) {
	(function checkRecordings() {
		Models.CallRecord.findAll({
			limit: 10,
			order: 'id DESC',
			where: {
				connectedDurationSecs: {
					$gt: 0,
					$ne: null
				}
			}
		}).then(callrecords => {
			var allok = false
			callrecords.forEach(cdr => {
				if (cdr.recordingLocation)
					allok = true
			})

			if (!allok) {
				//tell someone it aint working
				if (lastMessage === 'ok') {
					pmx.notify('No recordings detected for last 10 calls');
					lastMessage = 'not ok'
				}
			} else {
				lastMessage = 'ok'
			}
		})

		setTimeout(function () {
			checkRecordings()
		}, 60 * 5 * 1000)
	})()
}