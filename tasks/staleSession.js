var sessionAPI = require('../database/redisAPI')('agentsession')
var agentAPI = require('../database/redisAPI')('agent')
var campaignAPI = require('../database/redisAPI')('campaign')
var usersessionAPI = require('../database/redisAPI')('usersession')
var _ = require('underscore')
var moment = require('moment')
var userUtils = require('../utils/user')
// var pmx = require('pmx')

module.exports = function (Models) {
	function cleanUpAgentSession(session) {
		console.log('clearing agent session: ' + session.agentId)
		try {
			// pmx.emit('agentsession:deleted', session);
		} catch (e) {

		}
		if (session.currentCallResult) {
			if (!session.currentCallResult.completed) {
				if (session.currentCallResult.callAttemptJson)
					recreateCallAttemptFromJson(session.currentCallResult.callAttemptJson)

				Models.CallResult.destroy({
					where: {
						id: session.currentCallResult.id
					}
				})
			} else {
				console.log('NOT DELETING CALL RESULT AS ITS MARKED AS COMPLETED FOR AGENT: ' + session.agentId)
			}
		}

		updateLastAgentSessionEndTime(session.agentId)
		sessionAPI.delete(session.agentId)

		//put logout events here
		var newEvent = {}
		newEvent.eventType = "Login"
		newEvent.eventName = "Logged Out"
		newEvent.agentId = session.agentId
		newEvent.additionalInfo = JSON.stringify(session.agentStatus)
		Models.AgentEvent.create(newEvent)

		if (session.agentStatus && session.agentStatus.isChargeable && session.currentCampaignStage) {
			updatePayroll(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
		}
	}

	function updatePayroll(stateStartTime, campaignId, agentId) {
		var date = moment().format('DDMMYY')
		var now = moment()
		var start = moment(stateStartTime)
		var timeInState = now.diff(start, 'seconds')

		campaignAPI.incrementBy(campaignId + '-' + agentId, 'payroll-' + date, timeInState)
		campaignAPI.incrementBy(campaignId + '-' + agentId, 'payroll', timeInState)
		campaignAPI.incrementBy(campaignId, 'payroll-' + date, timeInState)
		campaignAPI.incrementBy(campaignId, 'payroll', timeInState)
		agentAPI.incrementBy(agentId, 'payroll', timeInState)
		agentAPI.incrementBy(agentId, 'payroll-' + date, timeInState)
	}


	function updateLastAgentSessionEndTime(agentId) {
		Models.Agent.findById(agentId)
			.then(function (agent) {
				if (agent)
					agent.updateAttributes({
						lastSessionEnd: new Date()
					})
			})
	}

	function recreateCallAttemptFromJson(callAttemptJson) {
		var callAttemptObj = JSON.parse(callAttemptJson)
		Models.CallAttempt.create(callAttemptObj)
			.catch(function (err) {
				delete callAttemptObj.id
				Models.CallAttempt.create(callAttemptObj)
			})
	}

	(function checkForStaleAgentSessions() {
		console.log('checking stale agent sessions')
		sessionAPI.getAll()
			.then(function (sessions) {
				_.each(sessions, function (session) {
					if ((Date.now() - 30000) > session.lastKeepAlivePing) {
						cleanUpAgentSession(session)
					} // TODO: Make expected keep alive interval to be configurable, or at least use a constan
				})
			})

		setTimeout(function () {
			checkForStaleAgentSessions()
		}, 5000)
	})();

	(function checkForStaleUserSessions() {
		console.log('checking stale user sessions')
		usersessionAPI.getAll()
			.then(function (sessions) {
				_.each(sessions, function (session) {
					if ((Date.now() - 30000) > session.lastKeepAlivePing) {
						console.log('deleting stale user session for userId: ' + session.id)
						try {
							// pmx.emit('usersession:deleted', session);
						} catch (e) {

						}
						usersessionAPI.delete(session.id)
						userUtils.logoutEvent(session.id, session.agentId, 'staleSession')
					}
				})
			})

		setTimeout(function () {
			checkForStaleUserSessions()
		}, 5000)
	})()
}