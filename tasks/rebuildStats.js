var campaignAPI = require('../database/redisAPI')('campaign')
var moment = require('moment')
var schedule = require('node-schedule')

var query = `SELECT 
				cr.campaignId,
			    cr.skill,
			    SUM(cr.giftAmount) AS 'giftAmount',
			    SUM(cr.saleAmount) AS 'saleAmount',
			    SUM(CASE
			        WHEN cr.saleAmount > 0 THEN 1
			        ELSE 0
			    END) AS 'salesCount',
			    SUM(CASE
			        WHEN cr.giftAmount > 0 THEN 1
			        ELSE 0
			    END) AS 'giftCount',
			    COUNT(cr.id) AS 'callCount',
			    SUM(CASE
			        WHEN cr.paymentType = 'Credit Card' THEN 1
			        ELSE 0
			    END) AS 'ccCount',
			    SUM(CASE
			        WHEN cr.saleAmount > 0 AND cr.giftAmount > 0 THEN 1
			        ELSE 0
			    END) AS 'addonCount'
			FROM
			    callresults cr
			WHERE cr.createdAt > date_sub(now(), interval 1 year)
			GROUP BY cr.campaignid , cr.skill WITH ROLLUP`

module.exports = function(db, Models) {
	var job = schedule.scheduleJob({
		minute: 15,
		hour: 0
	}, function() {
		db.query(query, {
				type: db.QueryTypes.SELECT
			})
			.then(function(results) {
				if(results.length > 1) {
					results.pop()
				}
				results.forEach(function(result) {
					if (result.campaignId) {
						if (result.skill) {
							if (result.giftAmount) {
								campaignAPI.setStat(result.campaignId, result.skill + 'GiftAmount', result.giftAmount)
							}
							if (result.saleAmount) {
								campaignAPI.setStat(result.campaignId, result.skill + 'SaleAmount', result.saleAmount)
							}
							if (result.giftCount) {
								campaignAPI.setStat(result.campaignId, result.skill + 'GiftCount', result.giftCount)
							}
							if (result.salesCount) {
								campaignAPI.setStat(result.campaignId, result.skill + 'SaleCount', result.salesCount)
							}
						} else {
							if (result.giftAmount) {
								campaignAPI.setStat(result.campaignId, 'totalGiftAmount', result.giftAmount)
							}
							if (result.saleAmount) {
								campaignAPI.setStat(result.campaignId, 'totalSaleAmount', result.saleAmount)
							}
							if (result.giftCount) {
								campaignAPI.setStat(result.campaignId, 'totalGiftCount', result.giftCount)
							}
							if (result.salesCount) {
								campaignAPI.setStat(result.campaignId, 'totalSaleCount', result.salesCount)
							}
							if (result.callCount) {
								campaignAPI.setStat(result.campaignId, 'callCount', result.callCount)
							}
							if (result.ccCount) {
								campaignAPI.setStat(result.campaignId, 'ccCount', result.callCount)
							}
							if (result.addonCount) {
								campaignAPI.setStat(result.campaignId, 'addonCount', result.addonCount)
							}
						}
					}
				})
			})
	})
}