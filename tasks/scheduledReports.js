var moment = require('moment')
var node_schedule = require('node-schedule')
var _ = require('underscore')
var path = require('path')
var fs = require('fs')
var Sequelize = require('sequelize')
var utils = require('../utils/fileUtils')
var Promise = Sequelize.Promise
Promise.promisifyAll(fs)

function dateStr() {
	return moment().format('YYYY-MM-DD HH:mm:ss')
}

module.exports = function (Models, db) {
	var reportBuilder = require('../reporting/reportBuilder')(Models, db)
	var excel = require('../reporting/exportToExcel')(Models)
	var email = require('../emailing/email')(Models)

	var salesOrderGenerator = require('../salesorders/salesOrdersGenerator')(Models)

	function shouldReportRun(schedule, now) {
		var schedule = JSON.parse(JSON.stringify(schedule))
		var scheduleDate = moment(schedule.startDate)
		var scheduleTime = moment(schedule.startTime)

		if (schedule.runEvery) {
			if (scheduleDate > moment().add(1, 'days')) return false
			
			switch (schedule.runEvery) {
				case 'minute':
					return true
				case 'hour':
					return (scheduleTime.format('mm') == now.format('mm'))
				case 'day':
					return (scheduleTime.format('HH:mm') == now.format('HH:mm'))
				case 'week':
					return (scheduleTime.format('HH:mm') == now.format('HH:mm') && scheduleDate.day() == now.day())
				case 'month':
					return (scheduleTime.format('HH:mm') == now.format('HH:mm') && scheduleDate.format('D') == now.format('D'))
				default:
					return false
			}
		} else {
			//means it just needs to run once so check the time is now
			return (scheduleDate.format('DDMMYY') == now.format('DDMMYY') && scheduleTime.format('HHmm') == now.format('HHmm'))
		}

		return false
	}

	function runReport(schedule) {
		var filters
		var report = schedule.report
		console.log(dateStr(), 'RUNNING REPORT', report.name, schedule.id)
		var reportModel = JSON.parse(schedule.report.definition)
		var gererateResultsPromise = new Promise(function (resolve, reject) {
			if (reportModel instanceof Array) {
				var promises = []
				reportModel.forEach(function (model) {
					if (model.rawQuery && model.availableFilters && model.availableFilters.length && model.availableFilters.some(f => !!f.placeholder)) {
						model.availableFilters.forEach(function (replac) {
							if (replac.placeholder) {
								if (replac.placeholder == '{{startDate}}') {
									var ranges = getDateRanges(schedule.filters.dateRange)
									model.rawQuery = model.rawQuery.replaceAll('{{startDate}}', ranges.start.format('YYYY-MM-DD HH:mm:ss'))
								}
								else if (replac.placeholder == '{{endDate}}') {
									var ranges = getDateRanges(schedule.filters.dateRange)
									model.rawQuery = model.rawQuery.replaceAll('{{endDate}}', ranges.end.format('YYYY-MM-DD HH:mm:ss'))
								}
								else if (replac.placeholder == '{{campaignId}}') {
									model.rawQuery = model.rawQuery.replaceAll('{{campaignId}}', schedule.filters.Campaign)
								}
								else if (replac.placeholder == '{{clientId}}') {
									model.rawQuery = model.rawQuery.replaceAll('{{clientId}}', schedule.filters.Client)
								}
								else if (replac.placeholder == '{{campaignstageId}}') {
									model.rawQuery = model.rawQuery.replaceAll('{{campaignstageId}}', schedule.filters.CampaignStage)
								}
							}
						})
					}
					
					if (model.rawQuery && schedule.filters) {
						model.whereClause = buildWhere(model, schedule.filters)
					}
					else if (schedule.filters) {
						reportModel = mergeFilters(reportModel, schedule.filters)
						var temp = []
						for (var prop in schedule.filters) {
							temp.push({
								field: prop,
								value: schedule.filters[prop]
							})
						}
					}
					promises.push(reportBuilder.runReport(model))
				})
				resolve(Promise.all(promises))
			} else {
				if (reportModel.rawQuery && reportModel.availableFilters && reportModel.availableFilters.length && reportModel.availableFilters[0].placeholder) {
					var ranges = getDateRanges(schedule.filters.dateRange)
					reportModel.availableFilters.forEach(function (replac) {
						if (!replac.placeholder) {
							reportModel.whereClause = buildWhere(reportModel, schedule.filters)
						} else {
							if (replac.placeholder == '{{startDate}}') {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll('{{startDate}}', ranges.start.format('YYYY-MM-DD HH:mm:ss'))
							} else if (replac.placeholder == '{{endDate}}') {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll('{{endDate}}', ranges.end.format('YYYY-MM-DD HH:mm:ss'))
							} else if (replac.placeholder == '{{campaignId}}') {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll('{{campaignId}}', schedule.filters.Campaign)
							} else if (replac.placeholder == '{{clientId}}') {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll('{{clientId}}', schedule.filters.Client)
							} else if (replac.placeholder == '{{campaignstageId}}') {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll('{{campaignstageId}}', schedule.filters.CampaignStage)
							}
						}
					})
				} else if (reportModel.rawQuery && schedule.filters) {
					reportModel.whereClause = buildWhere(reportModel, schedule.filters)
				} else if (schedule.filters) {
					reportModel = mergeFilters(reportModel, schedule.filters)
					var temp = []
					for (var prop in schedule.filters) {
						temp.push({
							field: prop,
							value: schedule.filters[prop]
						})
					}
				}
				resolve(reportBuilder.runReport(reportModel))
			}
		})
		gererateResultsPromise
			.then(function (results) {
				console.log(dateStr(), 'PROCESSING RESULTS', report.name, schedule.id)
				if (!results || !results.length) {
					console.log('No data found')
				} else {
					var hasData = false
					if (reportModel instanceof Array) {
						results.forEach(result => {
							if (hasData) return
							hasData = !!result.length
						})
					} else {
						hasData = true
					}
					if (!hasData) {
						console.log('No data found')
						return
					}

					var targetDir = path.resolve('./saved_reports/')
					var namedFilters = ''
					if (schedule.filters) {
						for (var prop in schedule.filters) {
							namedFilters = namedFilters + '.' + schedule.filters[prop]
						}
					}

					if (reportModel.format === 'PDF') {
						/*
							PDF Export (Zip)
						*/
						var name = report.name + '-' + moment().format('DD-MMM-YY h-mm-ss.SSS a') + '.zip'
						var destinationFilePath = path.join(targetDir, name)

						utils.ensureDirectoryExists(targetDir, function () {

							var pdfPromise = new Promise(function(resolve, reject) {
								salesOrders = salesOrderGenerator.groupSalesRecords(results);

								console.log('executing sales order generator');

								resolve(salesOrderGenerator.generatePdfsForSalesOrders(salesOrders, reportModel.template, destinationFilePath))
							})

							pdfPromise.then(function () {
								console.log(dateStr(), 'EXPORTED RESULTS', report.name, schedule.id)
								var history = {}
								history.path = destinationFilePath
								history.name = name
								history.filters = JSON.stringify(schedule.filters)
								history.reportId = schedule.reportId
								history.isClientReady = schedule.isClientReady
								Models.ReportHistory.create(history)
									.then(function (history) {
										Models.ReportHistory.findOne({
											where: {
												id: history.id
											},
											include: [Models.Report]
										})
											.then(function (result) {
												var userIdsToEmail = schedule.recipients.split(',')
												if (!userIdsToEmail || !userIdsToEmail.length) return
												Models.User.findAll({
													attributes: ['email'],
													where: {
														id: {
															$in: userIdsToEmail
														}
													}
												}).then(function (emails) {
													var recipients = emails.map(function (email) {
														return email.dataValues.email
													}).join(',')
													email.emailReportLinkWithTemplate(result, recipients)
												})
													.catch(function (err) {
														console.log(report ? report.name : '', err.message)
													})
											})
									})
							})
							.catch(function (err) {
								console.log(report ? report.name : '', err.message)
							})
						});

					} else {
						/*
							Excel Export
						*/
						var name = report.name + '-' + namedFilters + '-' + moment().format('DD-MMM-YY h-mm a') + '.xlsx'
						var destinationFilePath = path.join(targetDir, name)
						utils.ensureDirectoryExists(targetDir, function () {
							
							var excelPromise = new Promise(function (resolve, reject) {
								//detect if its an array or not
								if (reportModel instanceof Array) {
									resolve(excel.exportMultipleToExcel(results, destinationFilePath, reportModel))
								} else {
									resolve(excel.exportToExcel(results, destinationFilePath, reportModel))
								}
							})

							excelPromise
								.then(function () {
									console.log(dateStr(), 'EXPORTED RESULTS', report.name, schedule.id)
									var history = {}
									history.path = destinationFilePath
									history.name = name
									history.filters = JSON.stringify(schedule.filters)
									history.reportId = schedule.reportId
									history.isClientReady = schedule.isClientReady
									Models.ReportHistory.create(history)
										.then(function (history) {
											Models.ReportHistory.findOne({
												where: {
													id: history.id
												},
												include: [Models.Report]
											})
												.then(function (result) {
													var userIdsToEmail = schedule.recipients.split(',')
													if (!userIdsToEmail || !userIdsToEmail.length) return
													Models.User.findAll({
														attributes: ['email'],
														where: {
															id: {
																$in: userIdsToEmail
															}
														}
													}).then(function (emails) {
														var recipients = emails.map(function (email) {
															return email.dataValues.email
														}).join(',')
														email.emailReportLinkWithTemplate(result, recipients)
													})
														.catch(function (err) {
															console.log(report ? report.name : '', err.message)
														})
												})
										})
								})
								.catch(function (err) {
									console.log(report ? report.name : '', err.message)
								})
						})
					}
				}
			})
			.catch(function (err) {
				console.log(report ? report.name : '', err.message)
			})
	}

	function buildWhere(definition, filters) {
		var result = ''
		var results = []
		definition.availableFilters.filter(f => !f.placeholder).forEach(function (flt) {
			if (flt.model) {
				if (filters[flt.model]) {
					results.push(flt.queryTableName + '.id=' + filters[flt.model] + '');
				}
			} else if (flt.type == 'datetime' && filters.dateRange) {
				var ranges = getDateRanges(filters.dateRange)
				results.push(flt.queryTableName + '.' + flt.field + " >= '" + moment(ranges.start).utc().format('YYYY-MM-DD HH:mm:ss') + "'")
				results.push(flt.queryTableName + '.' + flt.field + " <= '" + moment(ranges.end).utc().format('YYYY-MM-DD HH:mm:ss') + "'")
			}
		})

		if (results.length) {
			for (var i = 0; i < results.length; i++) {
				if (i === 0) {
					result += results[i];
				} else {
					result += ' and ' + results[i];
				}
			}
		}

		return result;
	}

	function mergeFilters(definition, filters) {
		var now = moment()

		var addFilter = function (module, field, value, operator) {
			if (!module.filters.and) {
				module.filters.and = []
			}
			module.filters.and.push({
				fieldName: field,
				operator: operator || '',
				comparator: value
			})
		}

		for (var prop in filters) {
			if (prop == definition.primaryModule.name) {
				addFilter(definition.primaryModule, filters[prop])
			} else {
				var model = _.findWhere(definition.primaryModule.relatedModules, {
					name: prop
				})
				if (model) {
					addFilter(model, 'id', filters[prop])
				}
			}
		}

		if (filters.dateRange) {
			var model
			if (definition.primaryModule.name == 'CallResult' || definition.primaryModule.name == 'Sale') {
				model = definition.primaryModule
			} else {
				model = _.findWhere(definition.primaryModule.relatedModules, {
					name: 'CallResult'
				})

				if (!model) {
					model = _.findWhere(definition.primaryModule.relatedModules, {
						name: 'Sale'
					})
				}
			}

			if (model) {
				var ranges = getDateRanges(filters.dateRange)
				addFilter(model, 'createdAt', ranges.start.toDate(), 'gte')
				addFilter(model, 'createdAt', ranges.end.toDate(), 'lte')
			}
		}

		return definition
	}

	function getDateRanges(dateRange) {
		var start, end

		switch (dateRange) {
			case 'today':
				start = moment().startOf('day')
				end = moment().endOf('day')
				break
			case 'yesterday':
				start = moment().add(-1, 'days').startOf('day')
				end = moment().add(-1, 'days').endOf('day')
				break
			case 'last3Days':
				start = moment().add(-3, 'days').startOf('day')
				end = moment().endOf('day')
				break
			case 'lastWeek':
				start = moment().day('Sunday').add(-6, 'days').startOf('day')
				end = moment().day('Sunday').endOf('day')
				break
			case 'last7Days':
				start = moment().add(-7, 'days').startOf('day')
				end = moment().endOf('day')
				break
			case 'thisWeek':
				start = moment().startOf('isoweek').startOf('day')
				end = moment().endOf('isoweek').endOf('day')
				break
			case 'lastFortnight':
				start = moment().add(-14, 'days').startOf('day')
				end = moment().endOf('day')
				break
			case 'lastMonth':
				start = moment().add(-1, 'month').startOf('month').startOf('day')
				end = moment().add(-1, 'month').endOf('month').endOf('day')
				break
			case 'last4weeks':
				start = moment().subtract(4, 'weeks').startOf('day')
				end = moment().endOf('day')
				break
			case 'thisMonth':
				start = moment().startOf('month').startOf('day')
				end = moment().endOf('month').endOf('day')
				break
		}

		return {
			start: start,
			end: end
		}
	}

	function checkScheduledReport() {
		console.log(dateStr(), 'CHECKING SCHEDULED REPORTS')
		var now = moment()
		Models.ReportSchedule.findAll({
			include: [Models.Report],
			logging: false
		}).then(function (schedules) {
			if (schedules && schedules.length) {
				schedules.forEach(function (schedule) {
					schedule.filters = JSON.parse(schedule.filters)
					var runIt = shouldReportRun(schedule, now)
					if (schedule.reportId === 10) {
						console.log(now.format('YYYY-MM-DD HH:mm:ss'), schedule.reportId, schedule.id, runIt)
					}
					if (runIt) {
						schedule.update({ lastRun: new Date() }).catch(() => { })
						runReport(schedule)
					}
				})
			}
		})
	}

	node_schedule.scheduleJob('* * * * *', checkScheduledReport)
}

String.prototype.replaceAll = function (search, replacement) {
	var target = this;
	return target.replace(new RegExp(search, 'g'), replacement);
}