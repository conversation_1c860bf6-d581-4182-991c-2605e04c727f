var moment = require('moment')
var tsys = require('../utils/tsys')
var schedule = require('node-schedule')
var transitionLead = require('../data-merge/transitionLead')
var leadUtils = require('../utils/lead')
var Models

module.exports = function (_Models) {
    Models = _Models;

    (function process() {
        console.log(moment().format(), 'Checking Payments')
        Models.PaymentLog.findAll({
            where: {
                isPaid: false,
                deleted: false,
                disabled: false,
                status: 'pending',
                paymentDate: {
                    $lte: new Date()
                }
            },
            include: [Models.Lead, Models.RecurringPayment],
            limit: 10,
            logging: false
        }).then(payments => {
            if (payments.length) console.log('Processing ' + payments.length + ' Payments')
            return Promise.all(payments.map(takePayment))
        }).then(() => {
            setTimeout(process, 1000 * 60)
        }).catch(err => {
            console.log(err)
            setTimeout(process, 1000 * 60)
        })
    })();

    schedule.scheduleJob({
        hour: 22,
        minute: 0
    }, function () {
        tsys.batchClose(Models).then(response => {
            console.log('Batch Close Payments', JSON.stringify(response, null, 2))
        }).catch(err => {
            console.log('ERROR CLOSING PAYMENTS', err)
        })
    })
}

function takePayment(paymentLog) {
    // check that there is a cardtoken for this lead
    var cardToken
    if (!paymentLog.lead) {
        return paymentLog.update({
            error: 'No Payment Method for Lead',
            status: 'error'
        }).then(() => {
            return cancelSubsequentPayments(paymentLog)
        })
    }
    return Models.CardToken.findOne({
        where: {
            leadId: paymentLog.leadId
        },
        order: [['id', 'desc']]
    }).then(token => {
        cardToken = token
        if (!cardToken) {
            return paymentLog.update({
                error: 'No Payment Method for Lead',
                status: 'error'
            }).then(() => {
                return cancelSubsequentPayments(paymentLog)
            })
        }

        if (moment(cardToken.expirationDate, 'MMYYYY') < moment()) {
            return paymentLog.update({
                error: 'Stored Card Expired',
                status: 'error'
            }).then(() => {
                return cancelSubsequentPayments(paymentLog)
            })
        }

        if (!paymentLog.lead.address1 || !paymentLog.lead.zip) {
            return paymentLog.update({
                error: 'Invalid Lead Address',
                status: 'error'
            }).then(() => {
                return cancelSubsequentPayments(paymentLog)
            })
        }

        return tsys.makeSale(paymentLog.amount, cardToken.tsepToken, cardToken.expirationDate, paymentLog.lead.address1, paymentLog.lead.zip, 'MANUAL', cardToken.cardType, undefined, paymentLog.clientId, Models).then(response => {
            return Models.PaymentLogHistory.create({
                amount: paymentLog.amount,
                isPaid: response.status === 'PASS',
                receipt: JSON.stringify(response, null, 2),
                transactionID: response.transactionID,
                error: response.status === 'PASS' ? '' : response.responseMessage,
                maskedCardNumber: cardToken.maskedCardNumber,
                cardType: cardToken.cardType,
                expirationDate: cardToken.expirationDate,
                paymentlogId: paymentLog.id,
                leadId: paymentLog.leadId,
                userId: paymentLog.userId,
                clientId: paymentLog.clientId,
                campaignId: paymentLog.campaignId,
                invoiceId: paymentLog.invoiceId,
                callresultId: paymentLog.callresultId
            }).then(() => {
                if (response.status === 'PASS') {
                    return paymentLog.update({
                        isPaid: true,
                        status: 'paid',
                        actualPaymentDate: new Date(),
                        transactionID: response.transactionID,
                        receipt: JSON.stringify(response, null, 2)
                    }).then(() => {
                        if (paymentLog.recurringpayment) {
                            return processRecurringPayments(paymentLog, cardToken)
                        }
                    })
                } else {
                    return paymentLog.update({
                        error: response.responseMessage,
                        status: 'error',
                        transactionID: response.transactionID,
                        receipt: JSON.stringify(response, null, 2)
                    }).then(() => {
                        return cancelSubsequentPayments(paymentLog)
                    })
                }
            })
        })
    })


    // try and take the payment using the transactionID
    // if its works then great write the receipt to the log
    // if it fails then cancel all subsequent payment logs attached to the invoice/callresult and move lead to bad payments stage
}

function cancelSubsequentPayments(paymentLog) {
    // move the subscriber to badpayments stage of campaign
    var amount = paymentLog.amount
    return Models.CampaignStage.findOne({
        where: {
            campaignId: paymentLog.campaignId,
            name: 'Bad Credit Cards'
        }
    }).then(stage => {
        if (!stage) {
            // there is no stage for valid stage in this campaign so dont do anything
            // should be let someone know this has happened?
            return
        }
        return transitionLead(Models, paymentLog.leadId, stage.id, paymentLog.campaignId)
    }).then(() => {
        return leadUtils.resetDontContactUntil(Models, paymentLog.leadId)
    }).then(() => {
        // set any subsequent payments to errored
        return Models.PaymentLog.findAll({
            where: {
                id: {
                    $gt: paymentLog.id
                },
                campaignId: paymentLog.campaignId,
                leadId: paymentLog.leadId,
                invoiceId: paymentLog.invoiceId,
                status: 'pending',
                deleted: false,
                disabled: false,
                isPaid: false
            }
        })
    }).then(payments => {
        return Promise.all(payments.map(payment => {
            amount += payment.amount
            return payment.update({
                deleted: true,
                error: 'Previous Payment Failed',
                status: 'error'
            })
        }))
    }).then(() => {
        // now update the invoice for this payment log to have the amount remaining as the total amount of these logs
        return Models.Invoice.update({
            amountRemaining: amount,
            dueDate: moment().add(1, 'year').toDate()
        }, {
            where: {
                id: paymentLog.invoiceId
            }
        })
    }).catch(e => {
        console.log(e ? e.message || e : 'Unknown Error')
    })
}


function processRecurringPayments(paymentLog, cardToken) {
    // increment the total payments on the recurringpayment
    // get the next payment date
    // if the card will expire before the next payment then move the lead to the Expired Credit Card Stage
    // if the date is beyond the end date of the campaign look for a newer campaign for the client and move them there, it we cant find them then leave it in the current campaign    
    // if all is fine to continue then create a future paymentlog for the next payment date
    console.log('PROCESS RECURRING', paymentLog.leadId)
    return paymentLog.recurringpayment.update({
        completed: paymentLog.recurringpayment.completed + 1
    }).then(() => {
        var nextPayment = moment().add(1, paymentLog.recurringpayment.unit)

        if (moment(cardToken.expirationDate, 'MMYYYY') < nextPayment || moment(cardToken.expirationDate, 'MMYYYY') < moment().add(30, 'days')) {
            // move them to Expired Credit Card Stage
            // dont create another paymentlog
            return Models.CampaignStage.findOne({
                where: {
                    campaignId: paymentLog.campaignId,
                    name: 'Expiring Credit Cards'
                }
            }).then(stage => {
                if (stage) {
                    return transitionLead(Models, paymentLog.leadId, stage.id, paymentLog.campaignId)
                }
            }).then(() => {
                if (moment(cardToken.expirationDate, 'MMYYYY') > nextPayment && moment(cardToken.expirationDate, 'MMYYYY') < moment().add(30, 'days')) {
                    return Models.PaymentLog.create({
                        amount: paymentLog.recurringpayment.amount,
                        isPaid: false,
                        paymentDate: nextPayment.toDate(),
                        status: 'pending',
                        recurringpaymentId: paymentLog.recurringpaymentId,
                        invoiceId: paymentLog.invoiceId,
                        leadId: paymentLog.recurringpayment.leadId,
                        userId: paymentLog.recurringpayment.userId,
                        clientId: paymentLog.recurringpayment.clientId,
                        campaignId: paymentLog.recurringpayment.campaignId,
                        callresultId: paymentLog.recurringpayment.callresultId,
                        source: 'recurring'
                    })
                }
            })
        } else {
            // check the date is within the campaign enddate
            return Models.Campaign.findById(paymentLog.campaignId).then(campaign => {
                if (moment(campaign.endDate) < nextPayment) {
                    return moveLeadToNewCampaign(campaign, paymentLog.leadId, nextPayment.toDate())
                }
            }).then(newCampaignId => {
                // if there is a new campaignId update the RecurringPayment to the new campaign
                if (newCampaignId) {
                    paymentLog.recurringpayment.update({
                        campaignId: newCampaignId
                    }).catch(() => { })
                }
                // create the next paymentlog
                return Models.PaymentLog.create({
                    amount: paymentLog.recurringpayment.amount,
                    isPaid: false,
                    paymentDate: nextPayment.toDate(),
                    status: 'pending',
                    recurringpaymentId: paymentLog.recurringpaymentId,
                    invoiceId: paymentLog.invoiceId,
                    leadId: paymentLog.recurringpayment.leadId,
                    userId: paymentLog.recurringpayment.userId,
                    clientId: paymentLog.recurringpayment.clientId,
                    campaignId: newCampaignId || paymentLog.recurringpayment.campaignId,
                    callresultId: paymentLog.recurringpayment.callresultId,
                    source: 'recurring'
                })
            })
        }
    })
}

function moveLeadToNewCampaign(currentCampaign, leadId, date) {
    var newCampaign, existingCampaignLead
    // look for an active campaign for that client and transition them there
    return Models.Campaign.findOne({
        where: {
            clientId: currentCampaign.clientId,
            endDate: {
                $gt: date
            }
        },
        include: [Models.CampaignStage]
    }).then(_newCampaign => {
        newCampaign = _newCampaign
        // if no new campaign just leave them alone
        if (!newCampaign) return
        // check which stage they are in
        return Models.CampaignLead.findOne({
            where: {
                campaignId: currentCampaign.id,
                leadId
            }
        }).then(_existingCampaignLead => {
            existingCampaignLead = _existingCampaignLead
            if (existingCampaignLead && existingCampaignLead.currentCampaignStageId) {
                // find the corresponding stage in the new campaign
                return Models.CampaignStage.findById(existingCampaignLead.currentCampaignStageId).then(currentStage => {
                    return Models.CampaignStage.findOne({
                        where: {
                            campaignId: newCampaign.id,
                            name: currentStage.name
                        }
                    })
                })
            }
        }).then(newStage => {
            // if there is no new stage then just move them to no stage
            // but first let check if they are in that campaign already
            return Models.CampaignLead.findOne({
                where: {
                    campaignId: newCampaign.id,
                    leadId
                }
            }).then(newCampaignLead => {
                if (!newCampaignLead) {
                    // create one
                    return Models.CampaignLead.create({
                        campaignId: newCampaign.id,
                        leadId,
                        skill: existingCampaignLead ? existingCampaignLead.skill : undefined,
                        subSkill: existingCampaignLead ? existingCampaignLead.subSkill : undefined
                    })
                }
            }).then(() => {
                return transitionLead(Models, leadId, newStage ? newStage.id : null, newCampaign.id)
            })
        })
    }).then(() => {
        return newCampaign ? newCampaign.id : null
    }).catch(err => {
        if (err) console.log(err)
        return null
    })
}