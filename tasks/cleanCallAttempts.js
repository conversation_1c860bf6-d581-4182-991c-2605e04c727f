var schedule = require('node-schedule')

module.exports = function (Models, db) {
	var job = schedule.scheduleJob({
		hour: 1,
		minute: 0
	}, function () {
		var now = new Date()
		Models.CallAttempt.destroy({
			where: {
				endDate: {
					$lt: now,
					$ne: null
				}
			}
		})
		.then((rowsDeleted) => {
			console.log(`${rowsDeleted} call attempts deleted due to being expired`)

			db.query(`update callattempts ca set ca.lastDispositionDate = (select createdAt from callresults where leadid = ca.leadid order by createdAt desc limit 1)`, {
				type: db.QueryTypes.UPDATE,
				raw: true
			})
			.then(() => {
				console.log(`Call attempts updated with lastDispositionDate successfully`)
			})
			.catch((err) => {
				console.log(`Error occurred whilst updating call attempts with lastDispositionDate`)
				console.log(err)
			})
		})
	})
}