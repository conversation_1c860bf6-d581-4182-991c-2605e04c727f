var transitionlead = require('../data-merge/transitionLead')
var promiseUtils = require('../utils/promiseUtils.js')
var promiseWhile = promiseUtils.promiseWhile
var schedule = require('node-schedule')

module.exports = (Models) => {
	schedule.scheduleJob({
		hour: 1,
		minute: 0
	}, () => {
        // find revelant stages to look for expired call attempts (Upgrade Appeal)
        Models.CampaignStage.findAll({
            where: {
                name: 'Upgrade Appeal'
            }
        })
        .then(upgradeAppealStages => {
            console.log('Upgrade Appeal Stages', JSON.stringify(upgradeAppealStages, null, 2))
            // store all Renewal Appeal stages to lookup later
            Models.CampaignStage.findAll({
                where: {
                    name: 'Renewal Appeal'
                }
            })
            .then(renewalAppealStages => {
                console.log('Renewal Appeal Stages: ', JSON.stringify(renewalAppealStages, null, 2))

                const predicate = () => {
                    return !upgradeAppealStages || upgradeAppealStages.length === 0
                }

                const action = () => {
                    const stage = upgradeAppealStages.pop()
                    const destStage = renewalAppealStages.find(ras => ras.campaignId === stage.campaignId)

                    if (destStage) {
                        console.log('Processing stage ID ' + stage.id + ' for expired Upgrade Appeal call attempts')
                        return new Promise(resolveAction => {
                            Models.CampaignStageDateTimeRule.findAll({
                                where: {
                                    endDate: {
                                        $lt: new Date()
                                    },
                                    campaignstageId: stage.id
                                }
                            })
                            .then(dateTimeRules => {
                                console.log('Found expired date time rules', JSON.stringify(dateTimeRules, null, 2))

                                if (dateTimeRules && dateTimeRules.length) {
                                    dateTimeRules.forEach(dtRule => {
                                        Models.CampaignLead.findAll({
                                            where: {
                                                currentCampaignStageId: stage.id
                                            },
                                            include: [{
                                                model: Models.Lead,
                                                where: {
                                                    tfSubSkillId: dtRule.subskillId
                                                },
                                                attributes: ['id']
                                            }]
                                        })
                                        .then(campaignLeads => {
                                            console.log('Found leads for stage ID: ' + stage.id + ' and skill ID: ' + dtRule.subskillId, campaignLeads.map(cl => cl.leadId))
                                            campaignLeads.forEach(cl => {
                                                console.log('Transitioning ' + cl.leadid + ' to stage ID ' + destStage.id)
                                                transitionlead(Models, cl.leadId, destStage.id, stage.campaignId)
                                            })
                                        })
                                    })
                                }

                                resolveAction()
                            })
                            .catch(err => {
                                console.log('Error processing expired renewal call attempts', err)
                                resolveAction()
                            })
                        })
                    }
                    else {
                        console.log('No destination stage for Upgrade Appeal leads in campaign ID ' + stage.campaignId, 'Skipping...')
                        return Promise.resolve()
                    }
                }

                const onComplete = () => {
                    console.log('Completed renewal automation process')
                }

                promiseWhile(predicate, action, onComplete).catch(console.error)
            })
        })
    })
}