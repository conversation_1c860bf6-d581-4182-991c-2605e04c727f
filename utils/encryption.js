var crypto = require('crypto')
var algorithm = 'aes-256-ctr'
var secret = require('../config/secret')()

module.exports = function () {
	var encryption = {
		encrypt: function (input) {
				var cipher = crypto.createCipher(algorithm, secret)
				var crypted = cipher.update(input, 'utf8', 'hex')
				crypted += cipher.final('hex')
				return crypted
			},

		decrypt: function (input) {
				var decipher = crypto.createDecipher(algorithm, secret)
				var dec = decipher.update(input, 'hex', 'utf8')
				dec += decipher.final('utf8')
				return dec
			}	
	}

	return encryption
}