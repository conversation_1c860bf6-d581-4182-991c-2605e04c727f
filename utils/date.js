var moment = require('moment')

module.exports = {
    getNextRunDate: function (schedule) {
        var schedule = JSON.parse(JSON.stringify(schedule))
        var scheduleTime = moment(schedule.startTime).format('HH:mm')
        var nextRun = moment(scheduleTime, 'HH:mm');

        if (schedule.runEvery) {
            switch (schedule.runEvery) {
                case 'minute':
                    nextRun.add(1, 'minute');
                    break;
                case 'hour':
                    nextRun.add(1, 'hour');
                    break;
                case 'day':
                    nextRun.add(1, 'day');
                    break;
                case 'week':
                    nextRun.add(1, 'week');
                    break;
                case 'month':
                    nextRun.add(1, 'month');
                    break;
                default:
                    nextRun = null
                    break;
            }
        } else {
            //means it just needs to run once so check the time is now
            nextRun = null
        }
    }
}