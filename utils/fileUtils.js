var fs = require('fs')
var csv = require('csv')

module.exports = {
	parseCSVFile: function (sourceFilePath, columns, onNewRecord, handleError, done, limit) {
		var source = fs.createReadStream(sourceFilePath)

		var linesRead = 0

		var parser = csv.parse({
			delimiter: ',',
			columns: columns,
			skip_empty_lines: true,
			trim: true
		})

		parser.on("readable", function () {
			var record
			while (record = parser.read()) {
				linesRead++
				onNewRecord(record)

				if (limit && (linesRead === limit))
					break
			}
		})

		parser.on("error", function (error) {
			handleError(error)
		})

		parser.on("end", function () {
			done(linesRead)
		})

		source.pipe(parser)
	},
	ensureDirectoryExists: function (path, mask, cb) {
		if (typeof mask == 'function') { // allow the `mask` parameter to be optional
			cb = mask;
			mask = 0777;
		}

		fs.mkdir(path, mask, function (err) {
			if (err) {
				if (err.code == 'EEXIST')
					cb(null); // ignore the error if the folder already exists
				else
					cb(err); // something else went wrong
			} else
				cb(null); // successfully created folder
		});
	},
	copyFile: function (source, target, cb) {
		var cbCalled = false;

		var rd = fs.createReadStream(source);

		rd.on("error", function (err) {
			done(err);
		});

		var wr = fs.createWriteStream(target);

		wr.on("error", function (err) {
			done(err);
		});

		wr.on("close", function (ex) {
			done();
		});

		rd.pipe(wr);

		function done(err) {
			if (!cbCalled) {
				cb(err);
				cbCalled = true;
			}
		}
	}
}