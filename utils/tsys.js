var request = require('request')
var moment = require('moment')
var crypto = require('crypto')
var tsysConst = require('../config/tsys')


var url = 'https://gateway.transit-pass.com/servlets/TransNox_API_Server'

module.exports = {
    manifest: function (clientId, Models) {
        var amount = 0
        var now = moment().format('MMDDYYYY');
        return getKeys(clientId, Models).then(keys => {
            var manifestStr = keys.merchantID.padEnd(20) + '' + keys.deviceID.padEnd(24) + '' + (amount + '').padStart(12, '0') + '' + now;

            var key = keys.transactionKey.substr(0, 16)

            var cipher = crypto.createCipheriv('aes-128-cbc', key, key).setAutoPadding(false);
            var aesManifestString = cipher.update(manifestStr, 'utf8', 'hex');
            var hashTxnKey = crypto.createHmac('md5', new Buffer(keys.transactionKey)).update(keys.transactionKey).digest("hex");
            var manifest = hashTxnKey.substr(0, 4) + '' + aesManifestString + '' + hashTxnKey.substr(hashTxnKey.length - 4, 4)

            console.log('key', key)
            console.log('manifestStr', manifestStr.length)
            console.log('aesManifestString', aesManifestString)
            console.log('hashTxnKey', hashTxnKey)
            console.log('manifest', manifest)

            return { manifest, deviceID: keys.deviceID, uiDomain: keys.uiDomain };
        })

    },
    verifyCard: function (cardNumber, expirationDate, cvv2, clientId, Models) {
        return getKeys(clientId, Models).then(keys => {
            return new Promise(function (resolve, reject) {
                var options = {
                    json: true,
                    method: 'POST',
                    body: {
                        CardAuthentication: {
                            deviceID: keys.deviceID,
                            transactionKey: keys.transactionKey,
                            cardDataSource: "PHONE",
                            cardNumber,
                            expirationDate,
                            cvv2
                        }
                    }
                }
                request(url, options, function (err, response, body) {
                    if (err) return reject(err)
                    resolve(body)
                })
            })
        })

    },
    createToken: function (cardNumber, expirationDate, cardHolderName, source, clientId, Models) {
        var keys = getKeys(clientId, Models)
        return new Promise(function (resolve, reject) {
            var options = {
                json: true,
                method: 'POST',
                body: {
                    GetOnusToken: {
                        deviceID: keys.deviceID,
                        transactionKey: keys.transactionKey,
                        cardDataSource: source || "PHONE",
                        cardNumber,
                        expirationDate,
                        cardHolderName,
                        developerID: keys.developerID
                    }
                }
            }
            request(url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body.GetOnusTokenResponse, body)
            })
        })
    },
    authTransaction: function (transactionAmount, token, expirationDate, addressLine1, zip, source, clientId, Models) {
        var keys = getKeys(clientId, Models)
        return new Promise(function (resolve, reject) {
            if (!transactionAmount) return reject('Invalid transactionAmount')
            transactionAmount = parseFloat(transactionAmount)
            if (!transactionAmount) return reject('Invalid transactionAmount')
            if (transactionAmount < 0) return reject('transactionAmount must not be negative')
            transactionAmount = transactionAmount.toFixed(2)

            var options = {
                json: true,
                method: 'POST',
                body: {
                    Auth: {
                        deviceID: keys.deviceID,
                        transactionKey: keys.transactionKey,
                        cardDataSource: source || "PHONE",
                        transactionAmount,
                        cardNumber: token,
                        expirationDate,
                        addressLine1,
                        zip,
                    }
                }
            }
            request(url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body.AuthResponse.body)
            })
        })
    },
    chargeTransaction: function (transactionID, transactionAmount, clientId, Models) {
        var keys = getKeys(clientId, Models)
        return new Promise(function (resolve, reject) {
            var options = {
                json: true,
                method: 'POST',
                body: {
                    Capture: {
                        deviceID: keys.deviceID,
                        transactionKey: keys.transactionKey,
                        transactionID
                    }
                }
            }
            if (transactionAmount) {
                transactionAmount = parseFloat(transactionAmount)
                if (!transactionAmount) return reject('Invalid transactionAmount')
                if (transactionAmount < 0) return reject('transactionAmount must not be negative')
                transactionAmount = transactionAmount.toFixed(2)
                options.body.Capture.transactionAmount = transactionAmount
            }
            request(url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body.CaptureResponse, body)
            })
        })
    },
    recurringCharge: function (transactionAmount, startDate, paymentCount, cardNumber, expirationDate, lead, clientId, Models) {
        var keys = getKeys(clientId, Models)
        return new Promise(function (resolve, reject) {
            var options = {
                json: true,
                method: 'POST',
                body: {
                    AddRecurringSchedule: {
                        deviceID: keys.deviceID,
                        transactionKey: keys.transactionKey,
                        customerDetails: {
                            personalDetails: {
                                firstName: lead.first_name,
                                lastName: lead.last_name,
                                addressLine1: lead.address1,
                                addressLine2: lead.address2,
                                zip: lead.zip,
                                state: lead.state
                            }
                        },
                        walletDetails: {
                            cardDetails: {
                                cardNumber,
                                expirationDate
                            },
                            addressLine1: lead.address1,
                            zip: lead.zip,
                            paymentSequence: "1"
                        },
                        recurringTransactionDetails: {
                            amount: transactionAmount.toFixed(2),
                            interval: {
                                monthly: {
                                    everyMonth: "Y"
                                }
                            },
                            startDate: moment(startDate).format('YYYY-MM-DD'),
                            paymentCount,
                            billingType: "INSTALLMENT",
                            billPayment: "Y",
                            secCode: "CCD",
                            scheduleNotes: "This is scheduleNotes",
                            recurringProfileType: "AUTOMATIC"
                        }
                    }
                }
            }
            request(url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body)
            })
        })
    },
    checkRecurring: function (recurringScheduleID, clientId, Models) {
        var keys = getKeys(clientId, Models)
        return new Promise(function (resolve, reject) {
            var options = {
                json: true,
                method: 'POST',
                body: {
                    SearchRecurringSchedule: {
                        deviceID: keys.deviceID,
                        transactionKey: keys.transactionKey,
                        recurringScheduleID
                    }
                }
            }
            request(url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body)
            })
        })
    },
    makeSale: function (transactionAmount, token, expirationDate, address1, zip, source, cardType, cvv2, clientId, Models) {
        return getKeys(clientId, Models).then(keys => {
            return new Promise(function (resolve, reject) {
                if (!transactionAmount) return reject('Invalid transactionAmount')
                transactionAmount = parseFloat(transactionAmount)
                if (!transactionAmount) return reject('Invalid transactionAmount')
                if (transactionAmount < 0) return reject('transactionAmount must not be negative')
                transactionAmount = transactionAmount.toFixed(2)

                if (!token) return reject('Invalid Token')
                if (!address1 || !address1.trim()) return reject('address1 required')
                if (!zip || !zip.trim()) return reject('zip required')

                var options = {
                    json: true,
                    method: 'POST',
                    body: {
                        Sale: {
                            deviceID: keys.deviceID,
                            transactionKey: keys.transactionKey,
                            cardDataSource: source || "PHONE",
                            transactionAmount,
                            cardNumber: token,
                            expirationDate,
                            cvv2,
                            addressLine1: address1,
                            zip,
                            terminalCapability: "KEYED_ENTRY_ONLY",
                            terminalOperatingEnvironment: "ON_MERCHANT_PREMISES_ATTENDED",
                            cardholderAuthenticationMethod: "MANUAL_OTHER",
                            terminalAuthenticationCapability: "OTHER",
                            terminalOutputCapability: "DISPLAY_ONLY",
                            maxPinLength: "NOT_SUPPORTED",
                            developerID: keys.developerID,
                            authorizationIndicator: "FINAL"
                        }
                    }
                }
                if (!cvv2) delete options.body.Sale.cvv2
                if (!cardType || cardType !== 'M') delete options.body.Sale.authorizationIndicator
                console.log(options)
                request(keys.apiDomain || url, options, function (err, response, body) {
                    if (err) return reject(err)
                    resolve(body.SaleResponse)
                })
            })
        })

    },
    batchClose: function (Models) {
        return Models.Merchant.findAll({ raw: true }).then(merchants => {
            return Promise.all(merchants.map(keys => {
                return new Promise(function (resolve, reject) {
                    var options = {
                        json: true,
                        method: 'POST',
                        body: {
                            BatchClose: {
                                deviceID: keys.deviceID,
                                transactionKey: keys.transactionKey,
                                operatingUserID: "003022G001"
                            }
                        }
                    }
                    request(url, options, function (err, response, body) {
                        if (err) {
                            console.log(keys, err)
                            return resolve()
                        }
                        resolve(body)
                    })
                })
            }))
        })
    },
    createTransactionKey(mid, userID, password, overrideUrl) {
        return new Promise(function (resolve, reject) {
            var options = {
                json: true,
                method: 'POST',
                body: {
                    GenerateKey: {
                        mid,
                        userID,
                        password
                    }
                }
            }
            request(overrideUrl || url, options, function (err, response, body) {
                if (err) return reject(err)
                resolve(body)
            })
        })
    }
}

if (!String.prototype.padStart) {
    String.prototype.padStart = function padStart(targetLength, padString) {
        targetLength = targetLength >> 0; //truncate if number, or convert non-number to 0;
        padString = String(typeof padString !== 'undefined' ? padString : ' ');
        if (this.length >= targetLength) {
            return String(this);
        } else {
            targetLength = targetLength - this.length;
            if (targetLength > padString.length) {
                padString += padString.repeat(targetLength / padString.length); //append to original to ensure we are longer than needed
            }
            return padString.slice(0, targetLength) + String(this);
        }
    };
}

if (!String.prototype.padEnd) {
    String.prototype.padEnd = function padEnd(targetLength, padString) {
        targetLength = targetLength >> 0; //floor if number or convert non-number to 0;
        padString = String((typeof padString !== 'undefined' ? padString : ' '));
        if (this.length > targetLength) {
            return String(this);
        }
        else {
            targetLength = targetLength - this.length;
            if (targetLength > padString.length) {
                padString += padString.repeat(targetLength / padString.length); //append to original to ensure we are longer than needed
            }
            return String(this) + padString.slice(0, targetLength);
        }
    };
}

function getKeys(clientId, Models) {
    // if (clientId && tsysConst.clients['clientId' + clientId]) {
    //     return Promise.resolve(tsysConst.clients['clientId' + clientId])
    // } else {
    return Models.Merchant.findOne({
        where: {
            clientId
        },
        raw: true
    }).then(result => {
        if (!result)
            result = tsysConst.clients['clientId' + clientId] || tsysConst.clients.default

        return result
    })
    // }
}