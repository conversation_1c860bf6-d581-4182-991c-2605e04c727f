var publicLocation = './public/'

module.exports = {
	REDIS_SERVER: '0.0.0.0',
	REDIS: {
		host: '0.0.0.0'
	},
	KUE: {
		redis: {
			host: '0.0.0.0'
		}
	},
	DB: {
		schema: 'dialer',
		user: 'root',
		pass: 'root',
		config: {
			host: '0.0.0.0'
		}
	},
	PUBLIC_LOCATION: publicLocation,
	BULK_INVOICE_LOCATION: publicLocation + 'bulkinvoices/',
	AVATAR_LOCATION: publicLocation + 'avatars/',
	SIGNATURE_LOCATION: publicLocation + 'signatures/',
	CLIENT_LOGO_LOCATION: publicLocation + 'clientlogos/',
	TRAINING_DOCS_LOCATION: publicLocation + 'trainingdocs/',
	EXTERNAL_URL: 'https://kaos.blueprintadvancement.com',
		EXPECTED_IMPORT_FIELDS: {
		'SEGMENT': 'clientSourceCode',
		'SALUTATION': 'salutation',
		'FIRST NAME': 'first_name',
		'LAST NAME': 'last_name',
		'SUFFIX': 'suffix',
		'SPOUSE NAME': 'spouse_name',
		'COMPANY NAME': 'company_name',
		'ADDRESS1': 'address1',
		'ADDRESS2': 'address2',
		'ADDRESS3': 'address3',
		'CITY': 'city',
		'STATE': 'state',
		'ZIP': 'zip',
		'HOME PHONE1': 'phone_home',
		'HOME PHONE2': 'phone_mobile',
		'WORK PHONE1': 'phone_work',
		'WORK PHONE2': 'phone_workmobile',
		'EMAIL': 'email',
		'CLIENT DONOR/PATRON ID': 'clientRef',
		'clientId': 'clientId',
		'tfSkillId': 'tfSkillId',
		'tfSubSkillId': 'tfSubSkillId',
		'tmSkillId': 'tmSkillId',
		'tmSubSkillId': 'tmSubSkillId',
		'LAST GIFT AMOUNT': 'lastGiftAmount',
		'LAST GIFT DATE': 'lastGiftDate',
		'LAST GIFT REFERENCE': 'lastGiftReference',
		'LAST GIFT SOURCE': 'lastGiftSource',
		'LAST GIFT CC': 'lastGiftCC',
		'LY AMOUNT': 'lyAmount',
		'LY MATCH AMOUNT': 'lyMatchAmount',
		'LY PLEDGE DATE': 'lyPledgeDate',
		'LAP1 AMOUNT': 'lap1Amount',
		'LAP2 AMOUNT': 'lap2Amount',
		'LAP3 AMOUNT': 'lap3Amount',
		'LAP4 AMOUNT': 'lap4Amount',
		'LAP1 MATCH AMOUNT': 'lap1MatchAmount',
        'LAP2 MATCH AMOUNT': 'lap2MatchAmount',
        'LAP3 MATCH AMOUNT': 'lap3MatchAmount',
        'LAP4 MATCH AMOUNT': 'lap4MatchAmount',
		'LIFE TIME GIVING': 'lifttimeGiving',
		'LIFE TIME GIVING DATE': 'lifetimeGivingDate',
		'LIFE TIME PLEDGES': 'lifetimePledges',
		'LIFE TIME BUYING $': 'lifetimeBuying',
		'LIFE TIME BUYING DATE': 'lifetimeBuyingDate',
		'TICKET INFO1': 'tix1',
		'TICKET INFO2': 'tix2',
		'TICKET INFO3': 'tix3',
		'TICKET INFO4': 'tix4',
		'TICKET INFO5': 'tix5',
		'TICKET INFO6': 'tix6',
		'TICKET INFO7': 'tix7',
		'TICKET INFO8': 'tix8',
		'EXISTING CC DIGITS': 'existingCCDigits',
		'EXISTING CC TYPE': 'existingCCType',
		'EXISTING CC EXP': 'existingCCExp',
		'DIVISION': 'division',
		'CLIENT REFERENCE': 'tixClientRef',
		'BENEFITS': 'benefits',
		'GIFT INFO1 DATE':'gift1Date',
        'GIFT INFO1 AMOUNT':'gift1Amount',
        'GIFT INFO1 SOURCE':'gift1Source',
        'GIFT INFO1 REF':'gift1Ref',
        'GIFT INFO1 BENEFITS':'gift1Benefits',
        'GIFT INFO2 DATE':'gift2Date',
        'GIFT INFO2 AMOUNT':'gift2Amount',
        'GIFT INFO2 SOURCE':'gift2Source',
        'GIFT INFO2 REF':'gift2Ref',
        'GIFT INFO2 BENEFITS':'gift2Benefits',
        'GIFT INFO3 DATE':'gift3Date',
        'GIFT INFO3 AMOUNT':'gift3Amount',
        'GIFT INFO3 SOURCE':'gift3Source',
        'GIFT INFO3 REF':'gift3Ref',
        'GIFT INFO3 BENEFITS':'gift3Benefits',
        'GIFT INFO4 DATE':'gift4Date',
        'GIFT INFO4 AMOUNT':'gift4Amount',
        'GIFT INFO4 SOURCE':'gift4Source',
        'GIFT INFO4 REF':'gift4Ref',
        'GIFT INFO4 BENEFITS':'gift4Benefits',
        'GIFT INFO5 DATE':'gift5Date',
        'GIFT INFO5 AMOUNT':'gift5Amount',
        'GIFT INFO5 SOURCE':'gift5Source',
        'GIFT INFO5 REF':'gift5Ref',
        'GIFT INFO5 BENEFITS':'gift5Benefits',
        'TICKET INFO1 TYPE': 'tix1Type',
        'TICKET INFO1 YEAR': 'tix1Yr',
        'TICKET INFO1 EVENT': 'tix1Event',
        'TICKET INFO1 DATE': 'tix1Date',
        'TICKET INFO1 COST': 'tix1Cost',
        'TICKET INFO1 LOC': 'tix1Loc',
        'TICKET INFO1 SEATS': 'tix1Seats',
        'TICKET INFO1 ADDL': 'tix1Addl',
        'TICKET INFO2 TYPE': 'tix2Type',
        'TICKET INFO2 YEAR': 'tix2Yr',
        'TICKET INFO2 EVENT': 'tix2Event',
        'TICKET INFO2 DATE': 'tix2Date',
        'TICKET INFO2 COST': 'tix2Cost',
        'TICKET INFO2 LOC': 'tix2Loc',
        'TICKET INFO2 SEATS': 'tix2Seats',
        'TICKET INFO2 ADDL': 'tix2Addl',
        'TICKET INFO3 TYPE': 'tix3Type',
        'TICKET INFO3 YEAR': 'tix3Yr',
        'TICKET INFO3 EVENT': 'tix3Event',
        'TICKET INFO3 DATE': 'tix3Date',
        'TICKET INFO3 COST': 'tix3Cost',
        'TICKET INFO3 LOC': 'tix3Loc',
        'TICKET INFO3 SEATS': 'tix3Seats',
        'TICKET INFO3 ADDL': 'tix3Addl',
        'TICKET INFO4 TYPE': 'tix4Type',
        'TICKET INFO4 YEAR': 'tix4Yr',
        'TICKET INFO4 EVENT': 'tix4Event',
        'TICKET INFO4 DATE': 'tix4Date',
        'TICKET INFO4 COST': 'tix4Cost',
        'TICKET INFO4 LOC': 'tix4Loc',
        'TICKET INFO4 SEATS': 'tix4Seats',
        'TICKET INFO4 ADDL': 'tix4Addl',
        'TICKET INFO5 TYPE': 'tix5Type',
        'TICKET INFO5 YEAR': 'tix5Yr',
        'TICKET INFO5 EVENT': 'tix5Event',
        'TICKET INFO5 DATE': 'tix5Date',
        'TICKET INFO5 COST': 'tix5Cost',
        'TICKET INFO5 LOC': 'tix5Loc',
        'TICKET INFO5 SEATS': 'tix5Seats',
        'TICKET INFO5 ADDL': 'tix5Addl',
        'TICKET INFO6 TYPE': 'tix6Type',
        'TICKET INFO6 YEAR': 'tix6Yr',
        'TICKET INFO6 EVENT': 'tix6Event',
        'TICKET INFO6 DATE': 'tix6Date',
        'TICKET INFO6 COST': 'tix6Cost',
        'TICKET INFO6 LOC': 'tix6Loc',
        'TICKET INFO6 SEATS': 'tix6Seats',
        'TICKET INFO6 ADDL': 'tix6Addl',
        'TICKET INFO7 TYPE': 'tix7Type',
        'TICKET INFO7 YEAR': 'tix7Yr',
        'TICKET INFO7 EVENT': 'tix7Event',
        'TICKET INFO7 DATE': 'tix7Date',
        'TICKET INFO7 COST': 'tix7Cost',
        'TICKET INFO7 LOC': 'tix7Loc',
        'TICKET INFO7 SEATS': 'tix7Seats',
        'TICKET INFO7 ADDL': 'tix7Addl',
        'TICKET INFO8 TYPE': 'tix8Type',
        'TICKET INFO8 YEAR': 'tix8Yr',
        'TICKET INFO8 EVENT': 'tix8Event',
        'TICKET INFO8 DATE': 'tix8Date',
        'TICKET INFO8 COST': 'tix8Cost',
        'TICKET INFO8 LOC': 'tix8Loc',
        'TICKET INFO8 SEATS': 'tix8Seats',
        'TICKET INFO8 ADDL': 'tix8Addl',
        'TICKET INFO9 TYPE': 'tix9Type',
        'TICKET INFO9 YEAR': 'tix9Yr',
        'TICKET INFO9 EVENT': 'tix9Event',
        'TICKET INFO9 DATE': 'tix9Date',
        'TICKET INFO9 COST': 'tix9Cost',
        'TICKET INFO9 LOC': 'tix9Loc',
        'TICKET INFO9 SEATS': 'tix9Seats',
        'TICKET INFO9 ADDL': 'tix9Addl',
        'CLIENT WORKER':'clientWorker',
        'MEMBER LEVEL':'memberLevel',
        'CUSTOM 1':'custom1',
        'CUSTOM 2':'custom2',
        'INTERACTION 1 TYPE':'interaction1Type',
        'INTERACTION 1 DATE':'interaction1Date',
        'INTERACTION 1 DETAIL':'interaction1Detail',
        'INTERACTION 2 TYPE':'interaction2Type',
        'INTERACTION 2 DATE':'interaction2Date',
        'INTERACTION 2 DETAIL':'interaction2Detail',
        'INTERACTION 3 TYPE':'interaction3Type',
        'INTERACTION 3 DATE':'interaction3Date',
        'INTERACTION 3 DETAIL':'interaction3Detail',
        'INTERACTION 4 TYPE':'interaction4Type',
        'INTERACTION 4 DATE':'interaction4Date',
        'INTERACTION 4 DETAIL':'interaction4Detail',
        'INTERACTION 5 TYPE':'interaction5Type',
        'INTERACTION 5 DATE':'interaction5Date',
        'INTERACTION 5 DETAIL':'interaction5Detail',
        'INTERACTION 6 TYPE':'interaction6Type',
        'INTERACTION 6 DATE':'interaction6Date',
        'INTERACTION 6 DETAIL':'interaction6Detail',
        'INTERACTION 7 TYPE':'interaction7Type',
        'INTERACTION 7 DATE':'interaction7Date',
        'INTERACTION 7 DETAIL':'interaction7Detail',
        'INTERACTION 8 TYPE':'interaction8Type',
        'INTERACTION 8 DATE':'interaction8Date',
        'INTERACTION 8 DETAIL':'interaction8Detail',
        'MONTHLY GIVING AMOUNT': 'monthlyGivingAmount',
        'MONTHLY GIVING START': 'monthlyGivingStart',
        'MONTHLY GIVING END': 'monthlyGivingEnd',
		'TY AMOUNT': 'tyAmount',
		'AGENT PORTFOLIO TAG': 'agentPortfolioTag'
	},
	TF_LEAD_TYPE_FIELD: 'TF REPORTING GROUP',
	TF_LEAD_SUB_TYPE_FIELD: 'TF LEAD TYPE',
	TM_LEAD_TYPE_FIELD: 'TM REPORTING GROUP',
	TM_LEAD_SUB_TYPE_FIELD: 'TM LEAD TYPE',
	MANDATORY_LEAD_FIELD: 'CLIENT DONOR/PATRON ID',
	EXPECTED_PRODUCT_IMPORT_FIELDS: {
		'PRODUCT CODE': {
			field: 'productCode',
			type: 'STRING'
		},
		'VENUE': {
			field: 'venue',
			type: 'STRING'
		},
		'SERIES': {
			field: 'series',
			type: 'STRING'
		},
		'PRICE': {
			field: 'price',
			type: 'DOUBLE'
		},
		'SEATS': {
			field: 'seats',
			type: 'STRING'
		},
		'DAYS': {
			field: 'days',
			type: 'STRING'
		},
		'FEE PER TICKET': {
			field: 'feePerTicket',
			type: 'DOUBLE'
		},
		'TIX SUB': {
			field: 'tix_sub',
			type: 'STRING'
		},
		'SERIES ACTIVE': {
			field: 'seriesActive',
			type: 'BOOLEAN'
		},
		'SEATS ACTIVE': {
			field: 'seatsActive',
			type: 'BOOLEAN'
		},
		'DAYS ACTIVE': {
			field: 'daysActive',
			type: 'BOOLEAN'
		},
		'ORDER': {
			field: 'order_by',
			type: 'DOUBLE'
		},
		'clientId': {
			field: 'clientId',
			type: 'BOOLEAN'
		}
	},
	LEAD_AUDIT_FIELDS: [{
		name: "first_name",
		label: "First Name",
		type: "text",
		disabled: false
	}, {
		name: "last_name",
		label: "Last Name",
		type: "text",
		disabled: false
	}, {
		name: "salutation",
		label: "Salutation",
		type: "text",
		disabled: false
	}, {
		name: "suffix",
		label: "Suffix",
		type: "text",
		disabled: false
	}, {
		name: "spouse_name",
		label: "Spouse Name",
		type: "text",
		disabled: false
	}, {
		name: "company_name",
		label: "Company Name",
		type: "text",
		disabled: false
	}, {
		name: "address1",
		label: "Address 1",
		type: "text",
		disabled: false
	}, {
		name: "address2",
		label: "Address 2",
		type: "text",
		disabled: false
	}, {
		name: "address3",
		label: "Address 3",
		type: "text",
		disabled: false
	}, {
		name: "city",
		label: "City",
		type: "text",
		disabled: false
	}, {
		name: "state",
		label: "State",
		type: "text",
		disabled: false
	}, {
		name: "zip",
		label: "Zip",
		type: "text",
		disabled: false
	}, {
		name: "phone_home",
		label: "Home Phone",
		type: "tel",
		disabled: false
	}, {
		name: "phone_mobile",
		label: "Home Mobile",
		type: "tel",
		disabled: false
	}, {
		name: "phone_work",
		label: "Work Phone",
		type: "tel",
		disabled: false
	}, {
		name: "phone_workmobile",
		label: "Work Mobile",
		type: "tel",
		disabled: false
	}, {
		name: "email",
		label: "Email Address",
		type: "email",
		disabled: false
	}]
}