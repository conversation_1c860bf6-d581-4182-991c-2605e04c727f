# Dialer Frontend API Documentation

## Overview
This document provides comprehensive documentation for all API endpoints in the Dialer Frontend application.

**Base URL**: `/api/v1` (defined as BASE_URL in server.js)

## Authentication
Most endpoints require authentication via JWT token passed in headers:
- `X-Access-Token`: JWT token
- `X-Key`: Username

## Quick Reference - All Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| **Authentication** |
| POST | `/api/v1/login` | User login | No |
| **System** |
| GET | `/api/v1/manifest` | Get TSYS manifest | Yes |
| GET | `/api/v1/system` | Get system config | Yes |
| GET | `/api/v1/system/:key` | Get config by key | Yes |
| POST | `/api/v1/system` | Create system config | Yes |
| PUT | `/api/v1/system/:key` | Update system config | Yes |
| DELETE | `/api/v1/system/:key` | Delete system config | Yes |
| GET | `/api/v1/system/jobstatus/:jobId` | Get job status | Yes |
| GET | `/api/v1/system/threadtaskprogress/:taskId` | Get task progress | Yes |
| **Campaigns** |
| GET | `/api/v1/campaigns` | List campaigns | Yes |
| GET | `/api/v1/campaigns/:id` | Get campaign | Yes |
| POST | `/api/v1/campaigns` | Create campaign | Yes |
| PUT | `/api/v1/campaigns/:id` | Update campaign | Yes |
| DELETE | `/api/v1/campaigns/:id` | Delete campaign | Yes |
| GET | `/api/v1/campaigns/:id/trainingdocs` | Get training docs | Yes |
| POST | `/api/v1/campaigns/:id/uploadtrainingdocs` | Upload training doc | Yes |
| GET | `/api/v1/campaigns/:id/products` | Get products | Yes |
| POST | `/api/v1/campaigns/:id/uploadproducts` | Upload products | Yes |
| POST | `/api/v1/campaigns/:id/uploadleads` | Upload leads (CSV only) | Yes |
| POST | `/api/v1/campaigns/:id/batchpayments` | Batch payments | Yes |
| POST | `/api/v1/campaigns/:id/batchremoveleads` | Batch remove leads | Yes |
| POST | `/api/v1/campaigns/:id/batchchangestage/:stageId` | Batch change stage | Yes |
| POST | `/api/v1/campaigns/:id/batchexportinvoices` | Batch export invoices | Yes |
| GET | `/api/v1/campaigns/:id/stats` | Campaign stats | Yes |
| GET | `/api/v1/campaigns/:id/leadstats` | Lead stats | Yes |
| GET | `/api/v1/campaigns/:id/stages` | Campaign stages | Yes |
| GET | `/api/v1/campaigns/:id/agents` | Campaign agents | Yes |
| GET | `/api/v1/campaigns/:id/leadtypes` | Lead types | Yes |
| GET | `/api/v1/campaigns/:id/leadsubtypes` | Lead sub-types | Yes |
| GET | `/api/v1/campaigns/:id/projections` | Projections | Yes |
| PUT | `/api/v1/campaigns/:id/projections` | Update projections | Yes |
| GET | `/api/v1/campaigns/:id/uploads` | Upload history | Yes |
| GET | `/api/v1/campaigns/:id/uploads/:uploadId` | Upload details | Yes |
| POST | `/api/v1/campaigns/:id/transitionlead` | Transition lead | Yes |
| GET | `/api/v1/campaigns/:id/audit` | Campaign audit | Yes |
| GET | `/api/v1/campaigns/:id/suppressed` | Suppressed leads | Yes |
| **Clients** |
| GET | `/api/v1/clients` | List clients | Yes |
| GET | `/api/v1/clients/:id` | Get client | Yes |
| POST | `/api/v1/clients` | Create client | Yes |
| PUT | `/api/v1/clients/:id` | Update client | Yes |
| GET | `/api/v1/clients/:id/campaigns` | Client campaigns | Yes |
| **Users** |
| GET | `/api/v1/users` | List users | Yes |
| GET | `/api/v1/users/:id` | Get user | Yes |
| POST | `/api/v1/users` | Create user | Yes |
| PUT | `/api/v1/users/:id` | Update user | Yes |
| **Agents** |
| GET | `/api/v1/agents` | List agents | Yes |
| GET | `/api/v1/agents/:id` | Get agent | Yes |
| POST | `/api/v1/agents` | Create agent | Yes |
| PUT | `/api/v1/agents/:id` | Update agent | Yes |
| **Leads** |
| GET | `/api/v1/leads` | List leads | Yes |
| GET | `/api/v1/leads/:id` | Get lead | Yes |
| PUT | `/api/v1/leads/:id` | Update lead | Yes |
| POST | `/api/v1/leads/:id/cardtoken` | Create card token | Yes |
| DELETE | `/api/v1/leads/:id/cardtoken` | Delete card token | Yes |
| **Campaign Stages** |
| GET | `/api/v1/campaignstages` | List stages | Yes |
| GET | `/api/v1/campaignstages/:id` | Get stage | Yes |
| POST | `/api/v1/campaignstages` | Create stage | Yes |
| PUT | `/api/v1/campaignstages/:id` | Update stage | Yes |
| GET | `/api/v1/campaignstages/:id/agents` | Stage agents | Yes |
| GET | `/api/v1/campaignstages/:id/workflows` | Stage workflows | Yes |
| GET | `/api/v1/campaignstages/:id/datetimerules` | Stage DT rules | Yes |
| **Sales** |
| GET | `/api/v1/sales` | List sales | Yes |
| GET | `/api/v1/sales/:id` | Get sale | Yes |
| POST | `/api/v1/sales` | Create sale | Yes |
| PUT | `/api/v1/sales/:id` | Update sale | Yes |
| DELETE | `/api/v1/sales/:id` | Delete sale | Yes |
| **Invoices** |
| GET | `/api/v1/invoices` | List invoices | Yes |
| GET | `/api/v1/invoices/:id` | Get invoice | Yes |
| GET | `/api/v1/invoices/getbycallresultid/:callresultId` | Get by call result | Yes |
| GET | `/api/v1/invoices/paper` | Paper invoices | Yes |
| POST | `/api/v1/invoices` | Create invoice | Yes |
| GET | `/api/v1/invoices/:id/payments` | Invoice payments | Yes |
| GET | `/api/v1/invoices/:id/notes` | Invoice notes | Yes |
| POST | `/api/v1/invoices/:id/writeoff` | Write off invoice | Yes |
| POST | `/api/v1/invoices/:id/audit` | Invoice audit | Yes |
| POST | `/api/v1/invoices/:id/notes` | Add invoice note | Yes |
| **Payment Logs** |
| GET | `/api/v1/paymentlog` | List payments | Yes |
| GET | `/api/v1/paymentlog/download` | Download CSV | Yes |
| GET | `/api/v1/paymentlog/:id` | Get payment | Yes |
| POST | `/api/v1/paymentlog` | Create payment | Yes |
| PUT | `/api/v1/paymentlog/:id` | Update payment | Yes |
| DELETE | `/api/v1/paymentlog/:id` | Delete payment | Yes |
| **Reports** |
| GET | `/api/v1/reports` | List reports | Yes |
| GET | `/api/v1/reports/client/:id` | Client reports | Yes |
| GET | `/api/v1/reportmodules` | Report modules | Yes |
| GET | `/api/v1/reporthistories` | Report histories | Yes |
| GET | `/api/v1/reportschedules` | Report schedules | Yes |
| GET | `/api/v1/reportschedules/:id` | Get schedule | Yes |
| **Call Results** |
| GET | `/api/v1/callresults` | List call results | Yes |
| GET | `/api/v1/callresults/:id` | Get call result | Yes |
| POST | `/api/v1/callresults` | Create call result | Yes |
| PUT | `/api/v1/callresults/:id` | Update call result | Yes |
| **Call Records** |
| GET | `/api/v1/callrecords` | List call records | Yes |
| GET | `/api/v1/callrecords/:id` | Get call record | Yes |
| POST | `/api/v1/callrecords` | Create call record | Yes |
| PUT | `/api/v1/callrecords/:id` | Update call record | Yes |
| **Callbacks** |
| GET | `/api/v1/callbacks` | List callbacks | Yes |
| GET | `/api/v1/callbacks/:id` | Get callback | Yes |
| POST | `/api/v1/callbacks` | Create callback | Yes |
| PUT | `/api/v1/callbacks/:id` | Update callback | Yes |
| DELETE | `/api/v1/callbacks/:id` | Delete callback | Yes |
| **Dispositions** |
| GET | `/api/v1/dispositions` | List dispositions | Yes |
| GET | `/api/v1/dispositions/:id` | Get disposition | Yes |
| POST | `/api/v1/dispositions` | Create disposition | Yes |
| PUT | `/api/v1/dispositions/:id` | Update disposition | Yes |
| **Skills** |
| GET | `/api/v1/skills` | List skills | Yes |
| POST | `/api/v1/skills` | Create skill | Yes |
| PUT | `/api/v1/skills/:id` | Update skill | Yes |
| GET | `/api/v1/subskills` | List sub-skills | Yes |
| POST | `/api/v1/subskills` | Create sub-skill | Yes |
| PUT | `/api/v1/subskills/:id` | Update sub-skill | Yes |
| **Devices** |
| GET | `/api/v1/devices` | List devices | Yes |
| POST | `/api/v1/devices` | Create device | Yes |
| PUT | `/api/v1/devices/:id` | Update device | Yes |
| **Campaign Types** |
| GET | `/api/v1/campaigntypes` | List campaign types | Yes |
| GET | `/api/v1/campaigntypes/:id` | Get campaign type | Yes |
| **Date/Time Rules** |
| GET | `/api/v1/datetimerulesets` | List rule sets | Yes |
| POST | `/api/v1/datetimerulesets` | Create rule set | Yes |
| PUT | `/api/v1/datetimerulesets/:id` | Update rule set | Yes |
| **Testing** |
| POST | `/api/v1/testpayment` | Test payment | Yes |

## Global Endpoints

### Manifest
- **GET** `/api/v1/manifest`
  - **Description**: Get TSYS manifest for payment processing
  - **Query Parameters**: 
    - `clientId` (optional): Client ID for specific manifest
  - **Response**: TSYS manifest data

## Campaign Endpoints

### Campaign Management
- **GET** `/api/v1/campaigns`
  - **Description**: Get list of campaigns
  - **Access Control**: Filtered by client for client agents/admins
  - **Response**: Array of campaigns with CampaignType, Client, and CampaignGoals

- **GET** `/api/v1/campaigns/:id`
  - **Description**: Get campaign by ID
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Campaign object with CampaignType, CampaignProjections, Client, CampaignGoals

- **POST** `/api/v1/campaigns`
  - **Description**: Create new campaign
  - **Required Fields**:
    - `name`: Campaign name
    - `clientId`: Client ID
    - `campaigntypeId`: Campaign type ID
    - `defaultCallerId`: Default caller ID
  - **Optional Fields**:
    - `startDate`: Campaign start date
    - `endDate`: Campaign end date
    - `goal`: Campaign goal amount
    - `dontRecord`: Boolean for call recording
  - **Response**: Created campaign object

- **PUT** `/api/v1/campaigns/:id`
  - **Description**: Update campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Campaign fields to update
  - **Response**: Updated campaign object

- **DELETE** `/api/v1/campaigns/:id`
  - **Description**: Delete campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Success confirmation

### Campaign Sub-resources

#### Training Documents
- **GET** `/api/v1/campaigns/:id/trainingdocs`
  - **Description**: Get training documents for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Query Parameters**:
    - `countOnly`: Return only count if true
  - **Response**: Array of training documents or count object

- **POST** `/api/v1/campaigns/:id/uploadtrainingdocs`
  - **Description**: Upload training document
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Multipart form with file
  - **Response**: Success confirmation

#### Products
- **GET** `/api/v1/campaigns/:id/products`
  - **Description**: Get products for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Query Parameters**:
    - `countOnly`: Return only count if true
  - **Response**: Array of products or count object

- **POST** `/api/v1/campaigns/:id/uploadproducts`
  - **Description**: Upload products CSV
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Multipart form with CSV file
  - **Response**: Task ID for processing

#### Lead Import
- **POST** `/api/v1/campaigns/:id/uploadleads`
  - **Description**: Upload leads CSV (CSV files only)
  - **Parameters**: 
    - `id`: Campaign ID
  - **Query Parameters**:
    - `updateOnly`: Boolean for update-only mode
  - **Body**: Multipart form with CSV file
  - **File Validation**: Only CSV files accepted
  - **Response**: Task ID for processing or error for non-CSV files

#### Batch Operations
- **POST** `/api/v1/campaigns/:id/batchpayments`
  - **Description**: Upload batch payments CSV
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Multipart form with CSV file
  - **Response**: Task ID for processing

- **POST** `/api/v1/campaigns/:id/batchremoveleads`
  - **Description**: Upload CSV to remove leads
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Multipart form with CSV file
  - **Response**: Task ID for processing

- **POST** `/api/v1/campaigns/:id/batchchangestage/:stageId`
  - **Description**: Upload CSV to change lead stages
  - **Parameters**: 
    - `id`: Campaign ID
    - `stageId`: Target stage ID
  - **Body**: Multipart form with CSV file
  - **Response**: Task ID for processing

- **POST** `/api/v1/campaigns/:id/batchexportinvoices`
  - **Description**: Upload CSV to export invoices
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Multipart form with CSV file
  - **Response**: Task ID for processing

#### Campaign Statistics
- **GET** `/api/v1/campaigns/:id/stats`
  - **Description**: Get campaign statistics
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Campaign statistics object

- **GET** `/api/v1/campaigns/:id/leadstats`
  - **Description**: Get lead statistics for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Lead statistics object

#### Campaign Stages
- **GET** `/api/v1/campaigns/:id/stages`
  - **Description**: Get campaign stages
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of campaign stages

#### Agents
- **GET** `/api/v1/campaigns/:id/agents`
  - **Description**: Get agents assigned to campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of agents

#### Lead Types
- **GET** `/api/v1/campaigns/:id/leadtypes`
  - **Description**: Get lead types for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of lead types

- **GET** `/api/v1/campaigns/:id/leadsubtypes`
  - **Description**: Get lead sub-types for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of lead sub-types

#### Projections
- **GET** `/api/v1/campaigns/:id/projections`
  - **Description**: Get campaign projections
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of projections

- **PUT** `/api/v1/campaigns/:id/projections`
  - **Description**: Update campaign projections
  - **Parameters**: 
    - `id`: Campaign ID
  - **Body**: Array of projection objects
  - **Response**: Success confirmation

#### Upload History
- **GET** `/api/v1/campaigns/:id/uploads`
  - **Description**: Get upload history for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of upload history records

- **GET** `/api/v1/campaigns/:id/uploads/:uploadId`
  - **Description**: Get specific upload details
  - **Parameters**: 
    - `id`: Campaign ID
    - `uploadId`: Upload ID
  - **Response**: Upload details object

#### Lead Transition
- **POST** `/api/v1/campaigns/:id/transitionlead`
  - **Description**: Transition lead to new campaign stage
  - **Parameters**: 
    - `id`: Campaign ID
  - **Required Body Fields**:
    - `leadId`: Lead ID to transition
  - **Optional Body Fields**:
    - `newCampaignStageId`: Target stage ID (null to remove from stage)
  - **Response**: Success confirmation

#### Audit Trail
- **GET** `/api/v1/campaigns/:id/audit`
  - **Description**: Get campaign audit trail
  - **Parameters**: 
    - `id`: Campaign ID
  - **Response**: Array of audit records

#### Suppression
- **GET** `/api/v1/campaigns/:id/suppressed`
  - **Description**: Get suppressed leads for campaign
  - **Parameters**: 
    - `id`: Campaign ID
  - **Query Parameters**:
    - `finished`: Boolean to filter by finished status
  - **Response**: Array of suppressed leads

## Client Endpoints

### Client Management
- **GET** `/api/v1/clients`
  - **Description**: Get list of clients
  - **Access Control**: Filtered by client for client agents/admins
  - **Response**: Array of clients

- **GET** `/api/v1/clients/:id`
  - **Description**: Get client by ID
  - **Parameters**: 
    - `id`: Client ID
  - **Response**: Client object with related data

- **POST** `/api/v1/clients`
  - **Description**: Create new client
  - **Required Fields**:
    - `name`: Client name
  - **Optional Fields**:
    - `timezone`: Client timezone
    - `defaultInvoiceType`: Default invoice type
    - `additionalInfo`: Additional client information
  - **Response**: Created client object

- **PUT** `/api/v1/clients/:id`
  - **Description**: Update client
  - **Parameters**: 
    - `id`: Client ID
  - **Body**: Client fields to update
  - **Response**: Updated client object

### Client Sub-resources
- **GET** `/api/v1/clients/:id/campaigns`
  - **Description**: Get campaigns for client
  - **Parameters**: 
    - `id`: Client ID
  - **Response**: Array of campaigns

## User Endpoints

### User Management
- **GET** `/api/v1/users`
  - **Description**: Get list of users
  - **Response**: Array of users

- **GET** `/api/v1/users/:id`
  - **Description**: Get user by ID
  - **Parameters**: 
    - `id`: User ID
  - **Response**: User object

- **POST** `/api/v1/users`
  - **Description**: Create new user
  - **Required Fields**:
    - `username`: Username
    - `password`: Password
    - `name`: Display name
  - **Optional Fields**:
    - `isAdmin`: Admin flag
    - `isSupervisor`: Supervisor flag
    - `isAgent`: Agent flag
    - `agentId`: Associated agent ID
  - **Response**: Created user object

- **PUT** `/api/v1/users/:id`
  - **Description**: Update user
  - **Parameters**: 
    - `id`: User ID
  - **Body**: User fields to update
  - **Response**: Updated user object

## Agent Endpoints

### Agent Management
- **GET** `/api/v1/agents`
  - **Description**: Get list of agents
  - **Response**: Array of agents

- **GET** `/api/v1/agents/:id`
  - **Description**: Get agent by ID
  - **Parameters**: 
    - `id`: Agent ID
  - **Response**: Agent object

- **POST** `/api/v1/agents`
  - **Description**: Create new agent
  - **Required Fields**:
    - `name`: Agent name
    - `deviceId`: Device ID
  - **Optional Fields**:
    - `autoDial`: Auto-dial setting
    - `callPrepTime`: Call preparation time
  - **Response**: Created agent object

- **PUT** `/api/v1/agents/:id`
  - **Description**: Update agent
  - **Parameters**: 
    - `id`: Agent ID
  - **Body**: Agent fields to update
  - **Response**: Updated agent object

## Lead Endpoints

### Lead Management
- **GET** `/api/v1/leads`
  - **Description**: Get list of leads
  - **Query Parameters**:
    - Various filtering options
  - **Response**: Array of leads

- **GET** `/api/v1/leads/:id`
  - **Description**: Get lead by ID
  - **Parameters**: 
    - `id`: Lead ID
  - **Response**: Lead object with related data

- **PUT** `/api/v1/leads/:id`
  - **Description**: Update lead
  - **Parameters**: 
    - `id`: Lead ID
  - **Body**: Lead fields to update
  - **Response**: Updated lead object

### Lead Sub-resources
- **POST** `/api/v1/leads/:id/cardtoken`
  - **Description**: Create card token for lead
  - **Parameters**: 
    - `id`: Lead ID
  - **Body**: Card token data
  - **Response**: Token information

- **DELETE** `/api/v1/leads/:id/cardtoken`
  - **Description**: Delete card token for lead
  - **Parameters**: 
    - `id`: Lead ID
  - **Response**: Success confirmation

## Campaign Stage Endpoints

### Stage Management
- **GET** `/api/v1/campaignstages`
  - **Description**: Get list of campaign stages
  - **Response**: Array of campaign stages

- **GET** `/api/v1/campaignstages/:id`
  - **Description**: Get campaign stage by ID
  - **Parameters**: 
    - `id`: Stage ID
  - **Response**: Campaign stage object

- **POST** `/api/v1/campaignstages`
  - **Description**: Create new campaign stage
  - **Required Fields**:
    - `name`: Stage name
    - `campaignId`: Campaign ID
  - **Optional Fields**:
    - `blacklistedSkills`: JSON array of blacklisted skills
    - `blacklistCampaignstageId`: Fallback stage ID
  - **Response**: Created stage object

- **PUT** `/api/v1/campaignstages/:id`
  - **Description**: Update campaign stage
  - **Parameters**: 
    - `id`: Stage ID
  - **Body**: Stage fields to update
  - **Response**: Updated stage object

### Stage Sub-resources
- **GET** `/api/v1/campaignstages/:id/agents`
  - **Description**: Get agents assigned to stage
  - **Parameters**: 
    - `id`: Stage ID
  - **Response**: Array of agents

- **GET** `/api/v1/campaignstages/:id/workflows`
  - **Description**: Get workflows for stage
  - **Parameters**: 
    - `id`: Stage ID
  - **Response**: Array of workflows

- **GET** `/api/v1/campaignstages/:id/datetimerules`
  - **Description**: Get date/time rules for stage
  - **Parameters**: 
    - `id`: Stage ID
  - **Response**: Array of date/time rules

## System Endpoints

### System Information
- **GET** `/api/v1/system/info`
  - **Description**: Get system information
  - **Response**: System info object

### Thread Tasks
- **GET** `/api/v1/system/threadtasks/:taskId`
  - **Description**: Get thread task progress
  - **Parameters**: 
    - `taskId`: Task ID
  - **Response**: Task progress object

## Device Endpoints

### Device Management
- **GET** `/api/v1/devices`
  - **Description**: Get list of devices
  - **Response**: Array of devices

- **POST** `/api/v1/devices`
  - **Description**: Create new device
  - **Required Fields**:
    - `extension`: Device extension
    - `name`: Device name
    - `type`: Device type
  - **Response**: Created device object

- **PUT** `/api/v1/devices/:id`
  - **Description**: Update device
  - **Parameters**: 
    - `id`: Device ID
  - **Body**: Device fields to update
  - **Response**: Updated device object

## Skill Endpoints

### Skill Management
- **GET** `/api/v1/skills`
  - **Description**: Get list of skills
  - **Response**: Array of skills

- **POST** `/api/v1/skills`
  - **Description**: Create new skill
  - **Required Fields**:
    - `name`: Skill name
  - **Response**: Created skill object

- **PUT** `/api/v1/skills/:id`
  - **Description**: Update skill
  - **Parameters**: 
    - `id`: Skill ID
  - **Body**: Skill fields to update
  - **Response**: Updated skill object

### Sub-skill Management
- **GET** `/api/v1/subskills`
  - **Description**: Get list of sub-skills
  - **Response**: Array of sub-skills

- **POST** `/api/v1/subskills`
  - **Description**: Create new sub-skill
  - **Required Fields**:
    - `name`: Sub-skill name
    - `skillId`: Parent skill ID
  - **Response**: Created sub-skill object

- **PUT** `/api/v1/subskills/:id`
  - **Description**: Update sub-skill
  - **Parameters**: 
    - `id`: Sub-skill ID
  - **Body**: Sub-skill fields to update
  - **Response**: Updated sub-skill object

## Campaign Type Endpoints

### Campaign Type Management
- **GET** `/api/v1/campaigntypes`
  - **Description**: Get list of campaign types
  - **Response**: Array of campaign types

- **GET** `/api/v1/campaigntypes/:id`
  - **Description**: Get campaign type by ID
  - **Parameters**: 
    - `id`: Campaign type ID
  - **Response**: Campaign type object with skills

## Date Time Rule Set Endpoints

### Rule Set Management
- **GET** `/api/v1/datetimerulesets`
  - **Description**: Get list of date/time rule sets
  - **Response**: Array of rule sets

- **POST** `/api/v1/datetimerulesets`
  - **Description**: Create new date/time rule set
  - **Required Fields**:
    - `name`: Rule set name
    - `startTime`: Start time
    - `endTime`: End time
  - **Optional Fields**:
    - `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`: Boolean flags
  - **Response**: Created rule set object

- **PUT** `/api/v1/datetimerulesets/:id`
  - **Description**: Update date/time rule set
  - **Parameters**: 
    - `id`: Rule set ID
  - **Body**: Rule set fields to update
  - **Response**: Updated rule set object

## Call Record Endpoints

### Call Record Management
- **GET** `/api/v1/callrecords`
  - **Description**: Get list of call records
  - **Response**: Array of call records

- **GET** `/api/v1/callrecords/:id`
  - **Description**: Get call record by ID
  - **Parameters**: 
    - `id`: Call record ID
  - **Response**: Call record object

- **POST** `/api/v1/callrecords`
  - **Description**: Create new call record
  - **Required Fields**:
    - `leadId`: Lead ID
    - `agentId`: Agent ID
    - `duration`: Call duration
  - **Response**: Created call record object

- **PUT** `/api/v1/callrecords/:id`
  - **Description**: Update call record
  - **Parameters**: 
    - `id`: Call record ID
  - **Body**: Call record fields to update
  - **Response**: Updated call record object

## Authentication Endpoints

### Login
- **POST** `/api/v1/login`
  - **Description**: User login
  - **Required Fields**:
    - `username`: Username (case-insensitive)
    - `password`: Password
  - **Special Users**:
    - `dualtone_admin` with password `Lavabug87` for super admin access
  - **Response**: JWT token, expiration, and user information
  - **Response Format**:
    ```json
    {
      "token": "JWT_TOKEN_STRING",
      "expires": 1234567890,
      "user": {
        "id": 1,
        "username": "username",
        "name": "Display Name",
        "isAdmin": true,
        "isSupervisor": false,
        "isAgent": false,
        "homeState": "admin.clients"
      }
    }
    ```

## System Endpoints

### System Configuration
- **GET** `/api/v1/system`
  - **Description**: Get all system configuration values
  - **Response**: Array of system configuration objects

- **GET** `/api/v1/system/:key`
  - **Description**: Get system configuration by key
  - **Parameters**:
    - `key`: Configuration key
  - **Response**: System configuration object with parsed JSON value

- **POST** `/api/v1/system`
  - **Description**: Create system configuration
  - **Required Fields**:
    - `key`: Configuration key
    - `value`: Configuration value (will be JSON stringified)
  - **Response**: Created configuration object

- **PUT** `/api/v1/system/:key`
  - **Description**: Update or create system configuration
  - **Parameters**:
    - `key`: Configuration key
  - **Body**: Configuration object with value
  - **Response**: Updated/created configuration object

- **DELETE** `/api/v1/system/:key`
  - **Description**: Delete system configuration
  - **Parameters**:
    - `key`: Configuration key
  - **Response**: Success confirmation

### System Monitoring
- **GET** `/api/v1/system/jobstatus/:jobId`
  - **Description**: Get job status from queue
  - **Parameters**:
    - `jobId`: Job ID
  - **Response**: Job status object

- **GET** `/api/v1/system/threadtaskprogress/:taskId`
  - **Description**: Get thread task progress
  - **Parameters**:
    - `taskId`: Task ID
  - **Response**: Task progress object with percentComplete

## Test Endpoints

### Payment Testing
- **POST** `/api/v1/testpayment`
  - **Description**: Test payment processing (development/testing only)
  - **Required Fields**:
    - `amount`: Payment amount
    - `creditCardNumber`: Credit card number
    - `creditCardDate`: Expiry date
    - `creditCardPin`: CVV/PIN
  - **Optional Fields**:
    - `address1`: Billing address
    - `zip`: ZIP code
    - `clientId`: Client ID
  - **Response**: Payment processing results with auth and charge responses

## Error Responses

All endpoints may return the following error responses:

- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required or invalid
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server error

Error response format:
```json
{
  "error": "Error message description"
}
```

## File Upload Requirements

### CSV File Uploads
For lead import endpoints (`/campaigns/:id/uploadleads`):
- **File Type**: Only CSV files (.csv extension)
- **MIME Type**: text/csv, application/csv
- **Validation**: Multi-layer validation (browser, client-side, server-side)
- **Error Response**: 400 with descriptive message for non-CSV files

### Other File Uploads
- Training documents: Various file types accepted
- Product uploads: CSV files
- Batch operations: CSV files

## Rate Limiting and Pagination

- Most list endpoints support pagination (implementation details may vary)
- Rate limiting may be applied to prevent abuse
- Large file uploads may have size restrictions

## Sale Endpoints

### Sale Management
- **GET** `/api/v1/sales`
  - **Description**: Get list of sales
  - **Response**: Array of sales

- **GET** `/api/v1/sales/:id`
  - **Description**: Get sale by ID
  - **Parameters**:
    - `id`: Sale ID
  - **Response**: Sale object

- **POST** `/api/v1/sales`
  - **Description**: Create new sale
  - **Required Fields**:
    - `callresultId`: Call result ID
  - **Optional Fields**:
    - `leadId`: Lead ID
    - `campaignStageId`: Campaign stage ID
    - `clientId`: Client ID
    - `amount`: Sale amount
  - **Response**: Created sale object

- **PUT** `/api/v1/sales/:id`
  - **Description**: Update sale
  - **Parameters**:
    - `id`: Sale ID
  - **Body**: Sale fields to update
  - **Response**: Updated sale object

- **DELETE** `/api/v1/sales/:id`
  - **Description**: Delete sale
  - **Parameters**:
    - `id`: Sale ID
  - **Response**: Success confirmation

## Invoice Endpoints

### Invoice Management
- **GET** `/api/v1/invoices`
  - **Description**: Get list of invoices
  - **Access Control**: Filtered by client for client agents/admins
  - **Response**: Array of invoices with CallResult, Lead, Campaign, Client

- **GET** `/api/v1/invoices/:id`
  - **Description**: Get invoice by ID
  - **Parameters**:
    - `id`: Invoice ID
  - **Response**: Invoice object with related data

- **GET** `/api/v1/invoices/getbycallresultid/:callresultId`
  - **Description**: Get invoice by call result ID
  - **Parameters**:
    - `callresultId`: Call result ID
  - **Response**: Invoice object

- **GET** `/api/v1/invoices/paper`
  - **Description**: Get list of paper invoice files
  - **Response**: Array of file names

- **POST** `/api/v1/invoices`
  - **Description**: Create new invoice
  - **Required Fields**:
    - `callresultId`: Call result ID
    - `leadId`: Lead ID
    - `amount`: Invoice amount
  - **Optional Fields**:
    - `clientId`: Client ID
    - `campaignId`: Campaign ID
    - `dueDate`: Due date
  - **Response**: Created invoice object

### Invoice Sub-resources
- **GET** `/api/v1/invoices/:id/payments`
  - **Description**: Get payments for invoice
  - **Parameters**:
    - `id`: Invoice ID
  - **Response**: Array of payment logs

- **GET** `/api/v1/invoices/:id/notes`
  - **Description**: Get notes for invoice
  - **Parameters**:
    - `id`: Invoice ID
  - **Response**: Array of invoice notes

- **POST** `/api/v1/invoices/:id/writeoff`
  - **Description**: Write off invoice
  - **Parameters**:
    - `id`: Invoice ID
  - **Optional Body Fields**:
    - `amount`: Write-off amount
  - **Response**: Updated invoice object

- **POST** `/api/v1/invoices/:id/audit`
  - **Description**: Create audit entry for invoice
  - **Parameters**:
    - `id`: Invoice ID
  - **Body**: Audit event data
  - **Response**: Created audit event

- **POST** `/api/v1/invoices/:id/notes`
  - **Description**: Add note to invoice
  - **Parameters**:
    - `id`: Invoice ID
  - **Body**: Note data
  - **Response**: Created note object

## Payment Log Endpoints

### Payment Management
- **GET** `/api/v1/paymentlog`
  - **Description**: Get list of payment logs
  - **Query Parameters**:
    - `limit`: Number of records to return
    - `offset`: Number of records to skip
    - `orderBy`: Field to order by
    - `orderDir`: Order direction (ASC/DESC)
    - Various filtering options
  - **Response**: Paginated payment logs with count

- **GET** `/api/v1/paymentlog/download`
  - **Description**: Download payment import CSV
  - **Query Parameters**:
    - `file`: File path to download
  - **Response**: CSV file download

- **GET** `/api/v1/paymentlog/:id`
  - **Description**: Get payment log by ID
  - **Parameters**:
    - `id`: Payment log ID
  - **Response**: Payment log object

- **POST** `/api/v1/paymentlog`
  - **Description**: Create payment log entry (single or bulk)
  - **Body**: Payment log object or array of objects
  - **Required Fields**:
    - `invoiceId`: Invoice ID
    - `amount`: Payment amount
    - `status`: Payment status
  - **Response**: Created payment log(s)

- **PUT** `/api/v1/paymentlog/:id`
  - **Description**: Update payment log
  - **Parameters**:
    - `id`: Payment log ID
  - **Body**: Payment log fields to update
  - **Response**: Updated payment log object

- **DELETE** `/api/v1/paymentlog/:id`
  - **Description**: Delete payment log
  - **Parameters**:
    - `id`: Payment log ID
  - **Response**: Success confirmation

## Report Endpoints

### Report Management
- **GET** `/api/v1/reports`
  - **Description**: Get list of reports
  - **Query Parameters**:
    - `reportType`: Filter by report type
  - **Response**: Array of reports with latest history

- **GET** `/api/v1/reports/client/:id`
  - **Description**: Get client-ready reports
  - **Parameters**:
    - `id`: Client ID
  - **Response**: Array of reports ready for client

### Report Modules
- **GET** `/api/v1/reportmodules`
  - **Description**: Get available report modules
  - **Response**: Array of modules with attributes and associations

### Report History
- **GET** `/api/v1/reporthistories`
  - **Description**: Get report history
  - **Response**: Array of report histories with audit trails

### Report Schedules
- **GET** `/api/v1/reportschedules`
  - **Description**: Get report schedules
  - **Response**: Array of report schedules

- **GET** `/api/v1/reportschedules/:id`
  - **Description**: Get report schedule by ID
  - **Parameters**:
    - `id`: Schedule ID
  - **Response**: Report schedule object

## Callback Endpoints

### Callback Management
- **GET** `/api/v1/callbacks`
  - **Description**: Get list of callbacks
  - **Response**: Array of callbacks

- **GET** `/api/v1/callbacks/:id`
  - **Description**: Get callback by ID
  - **Parameters**:
    - `id`: Callback ID
  - **Response**: Callback object

- **POST** `/api/v1/callbacks`
  - **Description**: Create new callback
  - **Required Fields**:
    - `leadId`: Lead ID
    - `agentId`: Agent ID
    - `callbackDate`: Callback date/time
  - **Optional Fields**:
    - `notes`: Callback notes
    - `campaignId`: Campaign ID
  - **Response**: Created callback object

- **PUT** `/api/v1/callbacks/:id`
  - **Description**: Update callback
  - **Parameters**:
    - `id`: Callback ID
  - **Body**: Callback fields to update
  - **Response**: Updated callback object

- **DELETE** `/api/v1/callbacks/:id`
  - **Description**: Delete callback
  - **Parameters**:
    - `id`: Callback ID
  - **Response**: Success confirmation

## Disposition Endpoints

### Disposition Management
- **GET** `/api/v1/dispositions`
  - **Description**: Get list of dispositions
  - **Response**: Array of dispositions

- **GET** `/api/v1/dispositions/:id`
  - **Description**: Get disposition by ID
  - **Parameters**:
    - `id`: Disposition ID
  - **Response**: Disposition object

- **POST** `/api/v1/dispositions`
  - **Description**: Create new disposition
  - **Required Fields**:
    - `name`: Disposition name
  - **Optional Fields**:
    - `description`: Disposition description
    - `isActive`: Active status
  - **Response**: Created disposition object

- **PUT** `/api/v1/dispositions/:id`
  - **Description**: Update disposition
  - **Parameters**:
    - `id`: Disposition ID
  - **Body**: Disposition fields to update
  - **Response**: Updated disposition object

## Call Result Endpoints

### Call Result Management
- **GET** `/api/v1/callresults`
  - **Description**: Get list of call results
  - **Response**: Array of call results

- **GET** `/api/v1/callresults/:id`
  - **Description**: Get call result by ID
  - **Parameters**:
    - `id`: Call result ID
  - **Response**: Call result object

- **POST** `/api/v1/callresults`
  - **Description**: Create new call result
  - **Required Fields**:
    - `leadId`: Lead ID
    - `agentId`: Agent ID
    - `campaignId`: Campaign ID
    - `wrapup`: Call disposition
  - **Optional Fields**:
    - `duration`: Call duration
    - `notes`: Call notes
    - `callbackDate`: Callback date if applicable
  - **Response**: Created call result object

- **PUT** `/api/v1/callresults/:id`
  - **Description**: Update call result
  - **Parameters**:
    - `id`: Call result ID
  - **Body**: Call result fields to update
  - **Response**: Updated call result object

### Call Result Fields
- **GET** `/api/v1/callresultfields`
  - **Description**: Get call result field definitions
  - **Response**: Array of field definitions

- **GET** `/api/v1/callresultfieldtypes`
  - **Description**: Get call result field types
  - **Response**: Array of field types

- **GET** `/api/v1/callresultfieldgroups`
  - **Description**: Get call result field groups
  - **Response**: Array of field groups

- **GET** `/api/v1/callresultfieldoptions`
  - **Description**: Get call result field options
  - **Response**: Array of field options

- **GET** `/api/v1/callresultfieldvalues`
  - **Description**: Get call result field values
  - **Response**: Array of field values

## Agent Session Endpoints

### Session Management
- **GET** `/api/v1/agentsessions`
  - **Description**: Get agent sessions
  - **Response**: Array of agent sessions

- **POST** `/api/v1/agentsessions`
  - **Description**: Create agent session
  - **Required Fields**:
    - `agentId`: Agent ID
  - **Response**: Created session object

## Agent Event Endpoints

### Event Management
- **GET** `/api/v1/agentevents`
  - **Description**: Get agent events
  - **Response**: Array of agent events

- **POST** `/api/v1/agentevents`
  - **Description**: Create agent event
  - **Required Fields**:
    - `agentId`: Agent ID
    - `eventType`: Event type
  - **Response**: Created event object

## Agent State Endpoints

### State Management
- **GET** `/api/v1/agentstates`
  - **Description**: Get agent states
  - **Response**: Array of agent states

- **POST** `/api/v1/agentstates`
  - **Description**: Create agent state
  - **Required Fields**:
    - `name`: State name
  - **Response**: Created state object

## Lead Field Endpoints

### Field Management
- **GET** `/api/v1/leadfields`
  - **Description**: Get lead field definitions
  - **Response**: Array of lead fields

- **GET** `/api/v1/leadfieldvalues`
  - **Description**: Get lead field values
  - **Response**: Array of field values

## Campaign Product Endpoints

### Product Management
- **GET** `/api/v1/campaignproducts`
  - **Description**: Get campaign products
  - **Response**: Array of products

- **POST** `/api/v1/campaignproducts`
  - **Description**: Create campaign product
  - **Required Fields**:
    - `name`: Product name
    - `campaignId`: Campaign ID
  - **Response**: Created product object

## Campaign Training Doc Endpoints

### Training Document Management
- **GET** `/api/v1/campaigntrainingdocs`
  - **Description**: Get training documents
  - **Response**: Array of training documents

- **POST** `/api/v1/campaigntrainingdocs`
  - **Description**: Create training document
  - **Required Fields**:
    - `name`: Document name
    - `campaignId`: Campaign ID
    - `path`: File path
  - **Response**: Created document object

## Campaign Note Endpoints

### Note Management
- **GET** `/api/v1/campaignnotes`
  - **Description**: Get campaign notes
  - **Response**: Array of notes

- **POST** `/api/v1/campaignnotes`
  - **Description**: Create campaign note
  - **Required Fields**:
    - `campaignId`: Campaign ID
    - `note`: Note content
  - **Response**: Created note object

## Email History Endpoints

### Email Management
- **GET** `/api/v1/emailhistory`
  - **Description**: Get email history
  - **Response**: Array of email records

## Panic Reports Endpoints

### Panic Report Management
- **GET** `/api/v1/panicreports`
  - **Description**: Get panic reports
  - **Response**: Array of panic reports

## Refusal Reason Endpoints

### Refusal Reason Management
- **GET** `/api/v1/refusalreasons`
  - **Description**: Get refusal reasons
  - **Response**: Array of refusal reasons

- **POST** `/api/v1/refusalreasons`
  - **Description**: Create refusal reason
  - **Required Fields**:
    - `reason`: Refusal reason text
  - **Response**: Created reason object

## Lead Update Endpoints

### Lead Update Management
- **GET** `/api/v1/leadupdates`
  - **Description**: Get lead updates
  - **Response**: Array of lead updates

## Suppression Endpoints

### Suppression Management
- **GET** `/api/v1/suppressions`
  - **Description**: Get suppression records
  - **Response**: Array of suppression records

## Merchant Endpoints

### Merchant Management
- **GET** `/api/v1/merchant`
  - **Description**: Get merchant configurations
  - **Response**: Array of merchant configs

- **POST** `/api/v1/merchant`
  - **Description**: Create merchant configuration
  - **Required Fields**:
    - `deviceID`: Device ID
    - `merchantID`: Merchant ID
    - `developerID`: Developer ID
    - `clientId`: Client ID
    - `uiDomain`: UI domain
    - `apiDomain`: API domain
  - **Response**: Created merchant config

- **DELETE** `/api/v1/merchant/:id`
  - **Description**: Delete merchant configuration
  - **Parameters**:
    - `id`: Merchant ID
  - **Response**: Success confirmation

## Client Costing Endpoints

### Costing Management
- **GET** `/api/v1/clientcostings`
  - **Description**: Get client costing configurations
  - **Response**: Array of costing configs

- **POST** `/api/v1/clientcostings`
  - **Description**: Create client costing
  - **Required Fields**:
    - `clientId`: Client ID
    - `costPerLead`: Cost per lead
  - **Response**: Created costing object

## Invoice History Endpoints

### Invoice History Management
- **GET** `/api/v1/invoicehistory`
  - **Description**: Get invoice history
  - **Response**: Array of invoice history records

## Broadcast Message Endpoints

### Message Management
- **GET** `/api/v1/broadcastmessages`
  - **Description**: Get broadcast messages
  - **Response**: Array of messages

- **POST** `/api/v1/broadcastmessages`
  - **Description**: Create broadcast message
  - **Required Fields**:
    - `message`: Message content
  - **Response**: Created message object

## Campaign Stage Date Time Rule Endpoints

### Rule Management
- **GET** `/api/v1/campaignstagedatetimerules`
  - **Description**: Get campaign stage date/time rules
  - **Response**: Array of rules

- **POST** `/api/v1/campaignstagedatetimerules`
  - **Description**: Create date/time rule
  - **Required Fields**:
    - `campaignstageId`: Campaign stage ID
    - `datetimerulesetId`: Date/time rule set ID
  - **Response**: Created rule object

## Lead Audit Endpoints

### Audit Management
- **GET** `/api/v1/leadaudit`
  - **Description**: Get lead audit records
  - **Response**: Array of audit records

## Recurring Payment Endpoints

### Recurring Payment Management
- **GET** `/api/v1/recurringpayments`
  - **Description**: Get recurring payments
  - **Response**: Array of recurring payments

- **POST** `/api/v1/recurringpayments`
  - **Description**: Create recurring payment
  - **Required Fields**:
    - `invoiceId`: Invoice ID
    - `amount`: Payment amount
    - `frequency`: Payment frequency
  - **Response**: Created recurring payment object

## Common Data Field Requirements

### Campaign Fields
- `name`: String, required for creation
- `clientId`: Integer, required for creation
- `campaigntypeId`: Integer, required for creation
- `defaultCallerId`: String, required for creation
- `startDate`: ISO date string, optional
- `endDate`: ISO date string, optional
- `goal`: Decimal, optional
- `dontRecord`: Boolean, optional

### Lead Fields
- `clientRef`: String, unique identifier (mandatory field)
- `first_name`: String, lead's first name
- `last_name`: String, lead's last name
- `email`: String, email address
- `phone_home`: String, home phone number
- `phone_mobile`: String, mobile phone number
- `phone_work`: String, work phone number
- `address1`: String, primary address
- `city`: String, city
- `state`: String, state/province
- `zip`: String, postal code
- `clientId`: Integer, associated client ID

### User Fields
- `username`: String, unique username (required)
- `password`: String, encrypted password (required)
- `name`: String, display name (required)
- `isAdmin`: Boolean, admin privileges
- `isSupervisor`: Boolean, supervisor privileges
- `isAgent`: Boolean, agent privileges
- `agentId`: Integer, associated agent ID
- `clientId`: Integer, associated client ID

### Agent Fields
- `name`: String, agent name (required)
- `deviceId`: Integer, associated device ID (required)
- `autoDial`: Boolean, auto-dial setting
- `callPrepTime`: Integer, call preparation time in seconds
- `defaultAgentStateId`: Integer, default state ID

### Call Result Fields
- `leadId`: Integer, associated lead ID (required)
- `agentId`: Integer, associated agent ID (required)
- `campaignId`: Integer, associated campaign ID (required)
- `wrapup`: String, call disposition (required)
- `duration`: Integer, call duration in seconds
- `notes`: Text, call notes
- `callbackDate`: ISO datetime string, callback date if applicable

### Invoice Fields
- `callresultId`: Integer, associated call result ID (required)
- `leadId`: Integer, associated lead ID (required)
- `amount`: Decimal, invoice amount (required)
- `clientId`: Integer, associated client ID
- `campaignId`: Integer, associated campaign ID
- `dueDate`: ISO date string, payment due date

### Payment Log Fields
- `invoiceId`: Integer, associated invoice ID (required)
- `amount`: Decimal, payment amount (required)
- `status`: String, payment status (required) - values: 'pending', 'completed', 'failed'
- `paymentMethod`: String, payment method
- `transactionId`: String, external transaction ID
- `notes`: Text, payment notes

### Sale Fields
- `callresultId`: Integer, associated call result ID (required)
- `leadId`: Integer, associated lead ID
- `campaignStageId`: Integer, associated campaign stage ID
- `clientId`: Integer, associated client ID
- `amount`: Decimal, sale amount

### Campaign Stage Fields
- `name`: String, stage name (required)
- `campaignId`: Integer, associated campaign ID (required)
- `blacklistedSkills`: JSON array, blacklisted skill IDs
- `blacklistCampaignstageId`: Integer, fallback stage ID for blacklisted skills

### Device Fields
- `extension`: String, device extension (required)
- `name`: String, device name (required)
- `type`: String, device type (required)
- `server`: String, server address
- `password`: String, device password

### Skill/Sub-skill Fields
- `name`: String, skill name (required)
- `skillId`: Integer, parent skill ID (required for sub-skills)

### Date/Time Rule Set Fields
- `name`: String, rule set name (required)
- `startTime`: Time string (HH:MM format), start time (required)
- `endTime`: Time string (HH:MM format), end time (required)
- `monday`, `tuesday`, `wednesday`, `thursday`, `friday`, `saturday`, `sunday`: Boolean flags for days of week

### Client Fields
- `name`: String, client name (required)
- `timezone`: String, client timezone
- `defaultInvoiceType`: String, default invoice type ('email', 'paper', etc.)
- `additionalInfo`: JSON string, additional client information

## File Upload Specifications

### CSV File Structure for Lead Import
Required columns (case-sensitive):
- `FIRST NAME`: Lead's first name
- `LAST NAME`: Lead's last name
- `EMAIL`: Email address
- `PHONE`: Primary phone number
- `CLIENT REF`: Unique client reference (mandatory)

Optional columns:
- `SALUTATION`: Title (Mr., Mrs., etc.)
- `SUFFIX`: Name suffix
- `SPOUSE NAME`: Spouse's name
- `COMPANY NAME`: Company name
- `ADDRESS1`, `ADDRESS2`, `ADDRESS3`: Address lines
- `CITY`: City
- `STATE`: State/province
- `ZIP`: Postal code
- `HOME PHONE1`, `HOME PHONE2`: Additional phone numbers
- Various custom fields as defined in system configuration

### File Size and Format Requirements
- **Lead Import**: CSV files only, validated at multiple levels
- **Maximum File Size**: Varies by endpoint (typically 50MB limit)
- **Character Encoding**: UTF-8 recommended
- **Line Endings**: Unix (LF) or Windows (CRLF) acceptable

## Pagination and Filtering

### Standard Query Parameters
- `limit`: Number of records to return (default varies by endpoint)
- `offset`: Number of records to skip for pagination
- `orderBy`: Field name to sort by
- `orderDir`: Sort direction ('ASC' or 'DESC')

### Common Filters
- Date range filters using `startDate` and `endDate`
- Client filtering for multi-tenant access control
- Status-based filtering where applicable

## Response Formats

### Success Response
Most endpoints return data directly or in this format:
```json
{
  "success": true,
  "data": { ... }
}
```

### Paginated Response
```json
{
  "count": 150,
  "rows": [ ... ]
}
```

### Task Response (for file uploads)
```json
{
  "success": true,
  "taskId": "unique-task-id"
}
```

## Data Validation

All endpoints perform server-side validation of required fields and data types. Client-side validation is also implemented in the frontend application.

### Validation Rules
- Required fields must be present and non-empty
- Email addresses must be valid format
- Phone numbers are cleaned (non-digits removed)
- Dates must be valid ISO format
- Numeric fields must be valid numbers
- Foreign key references must exist in database

## Security Considerations

- All authenticated endpoints require valid JWT token
- Client-level access control for multi-tenant data
- File upload validation prevents malicious file types
- SQL injection protection through parameterized queries
- Password encryption using secure hashing

## Rate Limiting

- API calls may be rate-limited to prevent abuse
- File uploads have size restrictions
- Bulk operations may have record count limits

## API Versioning

Current API version is v1. Future versions will maintain backward compatibility where possible.

## Support and Troubleshooting

- Check error responses for detailed error messages
- Verify authentication tokens are valid and not expired
- Ensure required fields are provided in correct format
- Check file format requirements for upload endpoints
- Monitor system endpoints for task progress on long-running operations
