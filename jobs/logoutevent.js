var constants = require('../config/constants')
var kue = require('kue'),
    queue = kue.createQueue(constants.KUE)
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(constants.DB.schema, constants.DB.user, constants.DB.pass, constants.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)

sequelize.sync().then(() => {
    queue.process('logoutevent', function (job, done) {
        var userId = job.data.userId;
        var agentId = job.data.agentId;
        var source = job.data.source;
        var timestamp = job.data.timestamp || Date.now()
        console.log('agent', agentId, source)
        var duration = 0
        return Models.UserEvent.findOne({
            where: {
                userId: userId,
                eventType: 'Login'
            },
            order: 'id DESC'
        }).then(event => {
            if (event && !event.resolved) {
//                console.log(`Working out time between ${new Date(event.createdAt).getTime()} and ${timestamp}`, event, job.data)
                duration = Math.round((parseInt(timestamp) - new Date(event.createdAt).getTime()) / 1000)
                return event.update({
                    resolved: true
                })
            }
        }).then(() => {
            console.log('duration', duration)
            return Models.UserEvent.create({
                userId, agentId,
                duration,
                eventType: 'Logout',
                eventInfo: source
            })
        }).then(() => {
            console.log('done')
            done()
        }).catch(err => {
//            console.log('error', err)
            done(err)
        })
    })
})
