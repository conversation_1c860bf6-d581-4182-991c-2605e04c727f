var constants = require('../config/constants')
var uuid = require('node-uuid')
var kue = require('kue'),
	queue = kue.createQueue(constants.KUE)
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(constants.DB.schema, constants.DB.user, constants.DB.pass, constants.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

sequelize.sync()
	.then((err) => {
		queue.process('amendcallattempts', 1, function (job, done) {
			//generate call attempts and run done when complete
			var dtr = job.data.dateTimeRule
			var dtRuleUuid = job.data.uuid
			var campaignStage = job.data.campaignStage
			var quantity = job.data.quantity
			var startDate = (job.data.startDate || null)
			var endDate = (job.data.endDate || null)
			var leadIdObjs = job.data.leadIdObjs
			var action = job.data.action
			var inserts = []

			if (!action || !(action == 'add' || action == 'remove')) 
				return done(new Error('Invalid action'))

			if (!dtRuleUuid)
				return done(new Error('No uuid specified'))

			if (action == 'add' && !dtr)
				return done(new Error('No date/time rule specified'))

			if (!quantity)
				return done(new Error('No quantity specified'))

			if (!leadIdObjs)
				return done(new Error('No leads specified'))

			if (action == 'add' && !(campaignStage || campaignStage.campaign))
				return done(new Error('No campaign specified'))

			job.progress(0, leadIdObjs.length)
			var completedChunks = 0
			var p = Promise.resolve();

			if (action == 'add') {
				leadIdObjs.forEach(leadId => {
					(function () {
						p = p.then(() => {
							return new Promise((resolve, reject) => {
								var inserts = []
								for (var i = 0; i < quantity; i++) {
									if (leadId) {
										inserts.push({
											startTime: dtr.startTime,
											endTime: dtr.endTime,
											startDate: startDate,
											endDate: endDate,
											monday: dtr.monday,
											tuesday: dtr.tuesday,
											wednesday: dtr.wednesday,
											thursday: dtr.thursday,
											friday: dtr.friday,
											saturday: dtr.saturday,
											sunday: dtr.sunday,
											campaignId: campaignStage.campaign.id,
											campaignstageId: campaignStage.id,
											createdFromDTUuid: dtRuleUuid,
											leadId: leadId,
											randomSelector: uuid.v4()
										})
									}
								}
								Models.CallAttempt.bulkCreate(inserts)
									.then(function () {
										completedChunks++
										job.progress(completedChunks, leadIdObjs.length)
										resolve()
									})
									.catch(function (err) {
										console.log(JSON.stringify(err))
										resolve()
									})
							})
						})
					}());
				})
			} else if (action == 'remove') {
				leadIdObjs.forEach(leadId => {
					(function () {
						p = p.then(() => {
							return new Promise((resolve, reject) => {
								Models.CallAttempt.destroy({
										where: {
											createdFromDTUuid: dtRuleUuid,
											leadId: leadId
										},
										limit: quantity
									})
									.then(function () {
										completedChunks++
										job.progress(completedChunks, leadIdObjs.length)
										resolve()
									})
									.catch(function (err) {
										console.error(err)
										resolve()
									})
							})
						})
					}());
				})
			}

			p.then(function () {
					done()
				})
				.catch(function (err) {
					done(err)
				})
		})
	})