var APP_SETTINGS = require('../config/constants.js')
var uuid = require('node-uuid')
var kue = require('kue'),
    queue = kue.createQueue(APP_SETTINGS.KUE)
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var parseCSVFile = require('../utils/fileUtils.js').parseCSVFile
var _ = require('underscore')
var chunkSize = 500

sequelize.sync().then(() => {
    queue.process('importLeads', 1, function (job, done) {
        var importHistory, campaign
        var importId = job.data.importId
        var records = []
        var clientRefs = []
        var skills = []
        var subSkills = []
        var skillObjs = []
        var subSkillObjs = []
        var campaignStageDTRules = []
        var totalRecords = 0, recordsIgnored = 0, recordsProcessed = 0, leadsUpdated = 0, leadsCreated = 0, callAttemptsCreated = 0

        console.log('starting', importId)

        function updateProgress() {
            if (importHistory && importHistory.update) {
                var percentComplete = Math.round((100 / totalRecords) * (recordsIgnored + recordsProcessed))
                if (percentComplete >= 100 && totalRecords - (recordsIgnored + recordsProcessed) > 0) percentComplete = 99
                if (percentComplete > 100) percentComplete = 100
                console.log(percentComplete + '% complete')
                return importHistory.update({
                    percentComplete,
                    totalRecords,
                    recordsIgnored,
                    recordsProcessed,
                    leadsUpdated,
                    leadsCreated,
                    callAttemptsCreated
                })
            } else return Promise.resolve()
        }

        Models.LeadImportHistory.findById(importId).then(_res => {
            importHistory = _res
            if (!importHistory) return Promise.reject('Import Not Found')
            return Models.Campaign.findById(importHistory.campaignId, {
                include: [Models.CampaignType]
            })
        }).then(_campaign => {
            campaign = _campaign
            if (!campaign) return Promise.reject('Campaign Not Found')
            return Models.CampaignStageDateTimeRule.findAll({
                include: [Models.DateTimeRuleSet],
                where: {
                    campaignstageId: campaign.initialCampaignStageId
                }
            })
        }).then(_dtRules => {
            campaignStageDTRules = _dtRules
            return new Promise((resolve, reject) => {
                parseCSVFile(importHistory.path, true, record => {
                    totalRecords++;
                    if (!record[APP_SETTINGS.MANDATORY_LEAD_FIELD]) {
                        recordsIgnored++;
                        return;
                    }

                    var clientRef = record[APP_SETTINGS.MANDATORY_LEAD_FIELD]
                    if (clientRefs.indexOf(clientRef) > -1) {
                        recordsIgnored++;
                        return;
                    }
                    clientRefs.push(clientRef)


                    // set campaign id against the lead object
                    record.clientId = campaign.clientId

                    // add to list to insert later (this method is executed async so we need to store these for now and lookup/add skills before inserting)
                    records.push(record)

                    var tfSkill = record[APP_SETTINGS.TF_LEAD_TYPE_FIELD]
                    var tfSubSkill = record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD]
                    var tmSkill = record[APP_SETTINGS.TM_LEAD_TYPE_FIELD]
                    var tmSubSkill = record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD]

                    // maintain a distinct list of skill names to reference later
                    if (tfSkill && _.indexOf(skills, tfSkill) === -1)
                        skills.push(tfSkill)
                    if (tmSkill && _.indexOf(skills, tmSkill) === -1)
                        skills.push(tmSkill)

                    // maintain a distinct list of subskill names to reference later
                    if (tfSubSkill && _.indexOf(subSkills, tfSubSkill) === -1)
                        subSkills.push(tfSubSkill)
                    if (tmSubSkill && _.indexOf(subSkills, tmSubSkill) === -1)
                        subSkills.push(tmSubSkill)
                }, () => { }, resolve)
            })
        }).then(() => {
            clientRefs = null
            updateProgress().catch(() => { })
            var index = 0
            function loopSkills() {
                var skill = skills[index]
                if (!skill) return
                index++;
                return Models.Skill.findOrCreate({
                    where: {
                        name: skill
                    },
                    defaults: {
                        description: 'Automatically created by import process'
                    }
                }).then(result => {
                    if (result && result.length) {
                        result[0].name = result[0].name.toLowerCase()
                        skillObjs.push(result[0])
                        return loopSkills()
                    }
                })
            }
            return loopSkills()
        }).then(() => {
            var index = 0
            function loopSubSkills() {
                var skill = subSkills[index]
                if (!skill) return
                index++;
                return Models.SubSkill.findOrCreate({
                    where: {
                        name: skill
                    },
                    defaults: {
                        description: 'Automatically created by import process'
                    }
                }).then(result => {
                    if (result && result.length) {
                        result[0].name = result[0].name.toLowerCase()
                        subSkillObjs.push(result[0])
                        return loopSubSkills()
                    }
                })
            }
            return loopSubSkills()
        }).then(() => {
            var chunks = []
            while (records.length) {
                chunks.push(records.splice(0, chunkSize))
            }

            var totalChunks = chunks.length
            console.log(totalChunks, 'CHUNKS')

            var index = 0
            function loopRecords() {
                updateProgress().catch(() => { })
                if (!chunks.length) return Promise.resolve()
                var chunk = chunks.splice(0, 1)[0]
                index++;
                var clientRefs = chunk.map(c => c[APP_SETTINGS.MANDATORY_LEAD_FIELD])
                var leads

                function processRecord(record) {
                    if (!record) return Promise.resolve()
                    var clientRef = record[APP_SETTINGS.MANDATORY_LEAD_FIELD]
                    var tfSkill = record[APP_SETTINGS.TF_LEAD_TYPE_FIELD]
                    var tfSubSkill = record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD]
                    var tmSkill = record[APP_SETTINGS.TM_LEAD_TYPE_FIELD]
                    var tmSubSkill = record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD]

                    if (tfSkill) {
                        var skillObj = _.findWhere(skillObjs, {
                            name: tfSkill.toLowerCase()
                        })
                        if (skillObj)
                            record.tfSkillId = skillObj.id
                    }

                    if (tfSubSkill) {
                        var subSkillObj = _.findWhere(subSkillObjs, {
                            name: tfSubSkill.toLowerCase()
                        })
                        if (subSkillObj)
                            record.tfSubSkillId = subSkillObj.id
                    }

                    if (tmSkill) {
                        var skillObj = _.findWhere(skillObjs, {
                            name: tmSkill.toLowerCase()
                        })
                        if (skillObj)
                            record.tmSkillId = skillObj.id
                    }

                    if (tmSubSkill) {
                        var subSkillObj = _.findWhere(subSkillObjs, {
                            name: tmSubSkill.toLowerCase()
                        })
                        if (subSkillObj)
                            record.tmSubSkillId = subSkillObj.id
                    }

                    var lead = {}
                    var customFields = {}

                    for (var field in record) {
                        var dbFieldMap = APP_SETTINGS.EXPECTED_IMPORT_FIELDS[field]

                        if (dbFieldMap) {
                            try {
                                if (field.indexOf('PHONE') > -1) {
                                    lead[dbFieldMap] = record[field].replace(/\D/g, '')
                                }
                                else if (['LY AMOUNT', 'LAP1 AMOUNT', 'LAP2 AMOUNT', 'LAP3 AMOUNT', 'LAP4 AMOUNT'].indexOf(field) > -1) {
                                    lead[dbFieldMap] = record[field].replace(/[^0-9.]/g, '')
                                }
                                else {
                                    lead[dbFieldMap] = cleanString(record[field])
                                }
                            }
                            catch (e) {
                                lead[dbFieldMap] = null
                            }
                        }
                        else if (
                            [APP_SETTINGS.TF_LEAD_TYPE_FIELD,
                            APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD,
                            APP_SETTINGS.TM_LEAD_TYPE_FIELD,
                            APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD
                            ].indexOf(field) === -1
                        )
                            customFields[field] = cleanString(record[field])
                    }

                    lead.customFields = JSON.stringify(customFields)
                    lead.reimported = false

                    var createCallAttempts = true
                    var stageToCreate = null
                    var skill, subSkill

                    var existingLead = leads.find(l => l.clientRef == clientRef)

                    var promise
                    if (!existingLead) {
                        if (importHistory.updateOnly) {
                            recordsIgnored++;
                            return Promise.resolve()
                        } else {
                            leadsCreated++
                            promise = Models.Lead.create(lead)
                        }
                    } else {
                        createCallAttempts = false
                        stageToCreate = campaign.initialCampaignStageId

                        delete lead.id
                        delete lead.clientId
                        delete lead.clientRef
                        delete lead.dontContactUntil
                        delete lead.notes
								
                        if ((!existingLead.phone_home && !existingLead.phone_work && !existingLead.phone_mobile && !existingLead.phone_workmobile) &&
                                (lead.phone_home || lead.phone_work || lead.phone_mobile || lead.phone_workmobile)) {
                                    lead.reimported = true
                        }
                        
                        if (existingLead.campaignleads && existingLead.campaignleads.length) {
                            if (existingLead.campaignleads[0].currentCampaignStageId && lead.reimported) {
                                createCallAttempts = true
                                stageToCreate = existingLead.campaignleads[0].currentCampaignStageId
                            }
                        } else {
                            createCallAttempts = true
                        }

                        leadsUpdated++;
                        promise = existingLead.update(lead)
                    }
                    return promise.then(newLead => {
                        lead = newLead
                        skill = (campaign.campaigntype.name === 'Telefunding' ? record[APP_SETTINGS.TF_LEAD_TYPE_FIELD] : record[APP_SETTINGS.TM_LEAD_TYPE_FIELD])
                        subSkill = (campaign.campaigntype.name === 'Telefunding' ? record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD] : record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD])
                        if (lead.campaignleads && lead.campaignleads.length) {
                            var cl = lead.campaignleads[0]
                            if (cl.skill !== skill || cl.subSkill !== subSkill) {
                                // update the skill and subskill for reporting purposes
                                return cl.update({
                                    skill, subSkill
                                })
                            }
                        } else {
                            return Models.CampaignLead.create({
                                campaignId: campaign.id,
                                leadId: lead.id,
                                currentCampaignStageId: campaign.initialCampaignStageId,
                                skill,
                                subSkill
                            })
                        }
                    }).then(() => {
                        if (createCallAttempts && stageToCreate) {
                            var leadType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')

                            var skillDTRules = _.where(campaignStageDTRules, {
                                subskillId: lead[leadType]
                            })

                            if (skillDTRules && skillDTRules.length) {
                                var callAttemptInserts = []
                                skillDTRules.forEach((campaignDTRule) => {
                                    for (var i = 0; i < campaignDTRule.quantity; i++) {
                                        var dtr = campaignDTRule.datetimeruleset
                                        callAttemptInserts.push({
                                            startTime: dtr.startTime,
                                            endTime: dtr.endTime,
                                            startDate: (campaignDTRule.startDate || null),
                                            endDate: (campaignDTRule.endDate || null),
                                            monday: dtr.monday,
                                            tuesday: dtr.tuesday,
                                            wednesday: dtr.wednesday,
                                            thursday: dtr.thursday,
                                            friday: dtr.friday,
                                            saturday: dtr.saturday,
                                            sunday: dtr.sunday,
                                            campaignId: campaign.id,
                                            campaignstageId: stageToCreate || campaign.initialCampaignStageId,
                                            createdFromDTUuid: campaignDTRule.uuid,
                                            leadId: lead.id,
                                            randomSelector: uuid.v4()
                                        })
                                    }
                                })
                                callAttemptsCreated += callAttemptInserts.length
                                return Models.CallAttempt.bulkCreate(callAttemptInserts)
                            }
                        }
                    }).then(() => {
                        recordsProcessed++
                    })
                }

                return Models.Lead.findAll({
                    where: {
                        clientId: campaign.clientId,
                        $and: [{
                            clientRef: {
                                $not: null
                            }
                        }, {
                            clientRef: {
                                $in: clientRefs
                            }
                        }]
                    },
                    include: [{
                        model: Models.CampaignLead,
                        where: {
                            campaignId: campaign.id
                        }
                    }]
                }).then(_leads => {
                    leads = _leads
                    return Promise.all(chunk.map(r => processRecord(r)))
                }).then(() => {
                    chunk = null
                    leads = null
                    clientRefs = null
                    console.log('finished chunk', index + '/' + totalChunks)
                    return loopRecords()
                })
            }
            return loopRecords()
        }).then(() => {
            updateProgress().catch(() => { })
            done()
        }).catch(err => {
            console.log(err)
            if (importHistory && importHistory.update) {
                importHistory.update({
                    error: err ? err.message || err : 'Import Error'
                }).catch(() => { })
            }
            done(err)
        })
    })
})

function cleanString(str) {
    return (str && str.trim) ? str.trim() : str
}