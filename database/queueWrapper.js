var constants = require('../config/constants')
var kue = require('kue'),
    queue = kue.createQueue(constants.KUE)

module.exports = {
    addJob: function(name, data, priority, complete, update, started, error) {
        var job = queue.create(name, data).removeOnComplete(true).priority(priority || 'normal').save()
        job.on('complete', res => {
            if (complete && typeof complete === 'function') {
                complete()
            }
        })

        job.on('start', res => {
            if (started && typeof started === 'function') {
                started()
            }
        })

        job.on('failed attempt', res => {
            if (error && typeof error === 'function') {
                error(res)
            }
        })

        job.on('failed', res => {
            if (error && typeof error === 'function') {
                error(res)
            }
        })

        job.on('progress', (res, data) => {
            if (update && typeof update === 'function') {
                update(res, data)
            }
        })
        return job
    },
    getState: function(jobId) {
        kue.Job.get(jobId, (err, job) => {
            if (err) {
                return err
            }
            if (job) {
                return job
            } else {
                return null
            }
        })
    },
    removeJob: function(jobId) {
        kue.Job.get(jobId, (err, job) => {
            if (err) return
            job.remove((err) => {
                if (err) throw err
            })
        })
    }
}