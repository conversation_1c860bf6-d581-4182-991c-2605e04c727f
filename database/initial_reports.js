module.exports = function (Models) {
	Models.Report.create({
		name: 'Daily Gift Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'c',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
			    cr.skill AS 'Reporting Group',
			    cr.subSkill AS 'Lead Type',
			    cr.createdAt AS 'Pledge Date',
    			cr.payDate AS 'Pay Date',
			    cr.giftAmount AS 'Gift Amount',
			    l.id AS 'Kaos ID',
			    l.clientRef AS 'Lead Client Ref',
			    l.clientSourceCode AS 'Lead Source Code',
			    l.division AS 'Lead Division',
			    l.salutation AS 'Lead Salutation',
			    l.first_name AS 'Lead First Name',
			    l.last_name AS 'Lead Last Name',
			    cr.decisionMaker AS 'Decision Maker',
			    l.lyAmount AS 'Lead LY Amount',
			    cr.numberOfInstallments AS 'Number of Installments',
			    cr.installmentNotes AS 'Installment Notes',
			    cr.creditCardType AS 'Credit Card Type',
			    cr.creditCardNumber AS 'Credit Card Number',
			    cr.creditCardExpDate AS 'Credit Card Exp Date',
			    cr.creditCardSecurityCode AS 'Credit Card Security Code',
			    (CASE
			        WHEN cr.useExistingCreditCard <> 0 THEN 'Yes'
			        ELSE 'No'
			    END) AS 'Use Existing Credit Card',
			    cr.freeTickets AS 'Free Tickets',
			    (CASE
			        WHEN cr.declineBenefits <> 0 THEN 'Yes'
			        ELSE 'No'
			    END) AS 'Decline Benefits',
			    (CASE
			        WHEN cr.newMembershipCard <> 0 THEN 'Yes'
			        ELSE 'No'
			    END) AS 'New Membership Card',
			    cr.giftMatchingCompany AS 'Gift Matching Company',
			    cr.notes AS 'Notes',
			    (CASE
			        WHEN cr.requiresFollowUp <> 0 THEN 'Yes'
			        ELSE 'No'
			    END) AS 'Requires Follow-up',
			    l.address1 AS 'Lead Address1',
			    l.address2 AS 'Lead Address2',
			    l.city AS 'Lead City',
			    l.state as 'Lead State',
			    l.zip as 'Lead Zip',
			    l.phone_home as 'Lead Phone1',
			    l.phone_mobile as 'Lead Phone2',
			    l.phone_work as 'Lead Phone3',
			    l.phone_workmobile as 'Lead Phone4',
			    l.email as 'Lead Email',
			    c.name as 'Campaign Name',
			    cs.name as 'Campaign Stage',
			    cl.name as 'Client Name'
			FROM
			    callresults cr
			        LEFT JOIN
			    leads l ON cr.leadId = l.id
			        LEFT JOIN
			    clients cl ON cr.clientId = cl.id
			        LEFT JOIN
			    campaigns c ON cr.campaignId = c.id
			    	LEFT JOIN
			    campaignstages cs ON cs.id = cr.campaignstageid
			WHERE
			    cr.giftAmount > 0 and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'ISO Daily Gift Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'ca',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			dbChangesGoAtEnd: true,
			rawQuery: `SELECT 
					    cr.decisionMaker AS 'Decision Maker',
					    l.clientRef AS 'Lead Client Ref',
					    l.clientSourceCode AS 'Lead Source Code',
					    cr.giftAmount AS 'Gift Amount',
					    ca.name AS 'Campaign Name',
					    cr.creditCardType AS 'Credit Card Type',
					    cr.creditCardNumber AS 'Credit Card Number',
					    cr.creditCardExpDate AS 'Credit Card Exp Date',
					    cr.payDate AS 'Pay Date',
					    cr.numberOfInstallments AS 'Number of Installments',
					    cr.installmentNotes AS 'Installment Notes',
					    cr.giftMatchingCompany AS 'Gift Matching Company',
					    cr.notes AS 'Notes',
					    l.salutation AS 'Lead Salutation',
					    l.first_name AS 'Lead First Name',
					    l.last_name AS 'Lead Last Name',
					    l.address1 AS 'Lead Address1',
					    l.address2 AS 'Lead Address2',
					    l.address3 AS 'Lead Address3',
					    l.city AS 'Lead City',
					    l.state AS 'Lead State',
					    l.zip AS 'Lead Zip',
					    l.phone_home AS 'Lead Phone1',
					    l.phone_mobile AS 'Lead Phone2',
					    l.email AS 'Lead Email',
					    l.id AS 'Kaos ID',
					    cr.createdAt AS 'Lead Pledge Date',
					    cl.name AS 'Client Name',
					    cr.skill AS 'Reporting Group',
					    cr.subSkill AS 'Lead Type',
					    cs.name AS 'SubCampaign',
					    l.lyAmount AS 'Lead LY Amount',
					    IF(cr.requiresFollowUp = 1, 'Yes', 'No') AS 'Requires Follow-up',
					    IF(cr.declineBenefits = 1, 'Yes', 'No') AS 'Decline Benefits',
					    cr.freeTickets AS 'Free Tickets'
					FROM
					    callresults cr
					        LEFT JOIN
					    leads l ON cr.leadId = l.id
					        LEFT JOIN
					    campaigns ca ON ca.id = cr.campaignid
					        LEFT JOIN
					    clients cl ON cl.id = cr.clientid
					        LEFT JOIN
					    campaignstages cs ON cr.campaignstageid = cs.id
					WHERE
					    giftAmount > 0 and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'Collections Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify([{
			worksheetName: 'Payments',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'c',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
						    cr.skill AS 'Reporting Group',
						    cr.subSkill AS 'Lead Type',
						    cr.createdAt AS 'Payment Taken Date',
						    cr.payDate AS 'Pay Date',
						    cr.payAmount AS 'Payment Amount',
						    l.id AS 'Kaos ID',
						    l.clientRef AS 'Lead Client Ref',
						    l.clientSourceCode AS 'Lead Source Code',
						    l.division AS 'Lead Division',
						    l.salutation AS 'Lead Salutation',
						    l.first_name AS 'Lead First Name',
						    l.last_name AS 'Lead Last Name',
						    cr.decisionMaker AS 'Decision Maker',
						    l.lyAmount AS 'Lead LY Amount',
						    cr.numberOfInstallments AS 'Number of Installments',
						    cr.installmentNotes AS 'Installment Notes',
						    cr.creditCardType AS 'Credit Card Type',
						    cr.creditCardNumber AS 'Credit Card Number',
						    cr.creditCardExpDate AS 'Credit Card Exp Date',
						    cr.creditCardSecurityCode AS 'Credit Card Security Code',
						    (CASE
						        WHEN cr.useExistingCreditCard <> 0 THEN 'Yes'
						        ELSE 'No'
						    END) AS 'Use Existing Credit Card',
						    cr.freeTickets AS 'Free Tickets',
						    (CASE
						        WHEN cr.declineBenefits <> 0 THEN 'Yes'
						        ELSE 'No'
						    END) AS 'Decline Benefits',
						    (CASE
						        WHEN cr.newMembershipCard <> 0 THEN 'Yes'
						        ELSE 'No'
						    END) AS 'New Membership Card',
						    cr.giftMatchingCompany AS 'Gift Matching Company',
						    cr.notes AS 'Notes',
						    (CASE
						        WHEN cr.requiresFollowUp <> 0 THEN 'Yes'
						        ELSE 'No'
						    END) AS 'Requires Follow-up',
						    l.address1 AS 'Lead Address1',
						    l.address2 AS 'Lead Address2',
						    l.city AS 'Lead City',
						    l.state AS 'Lead State',
						    l.zip AS 'Lead Zip',
						    l.phone_home AS 'Lead Phone1',
						    l.phone_mobile AS 'Lead Phone2',
						    l.phone_work AS 'Lead Phone3',
						    l.phone_workmobile AS 'Lead Phone4',
						    l.email AS 'Lead Email',
						    c.name AS 'Campaign Name',
						    cs.name AS 'Campaign Stage',
						    cl.name AS 'Client Name'
						FROM
						    callresults cr
						        LEFT JOIN
						    leads l ON cr.leadId = l.id
						        LEFT JOIN
						    clients cl ON cr.clientId = cl.id
						        LEFT JOIN
						    campaigns c ON cr.campaignId = c.id
						        LEFT JOIN
						    campaignstages cs ON cs.id = cr.campaignstageid
						WHERE
						    (cs.name = 'Collections'
						        OR cs.name = 'Bad Credit Cards')
						        AND cr.leadid IS NOT NULL
						        AND cr.paymentType = 'Credit Card'
						        AND cr.wrapup = 'Invoice Payment' 
						        AND {{whereClause}}`
		}, {
			worksheetName: 'Exception Refusals',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'c',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
						    cr.createdAt AS 'Refusal Date',
						    l.id AS 'Kaos ID',
						    l.clientRef AS 'Client ID',
						    l.clientSourceCode AS 'Lead Source Code',
						    l.division AS 'Lead Division',
						    cr.refusalReason AS 'Refusal Reason',
						    cr.notes AS 'Notes',
						    cr.decisionMaker AS 'Decision Maker',
						    l.salutation AS 'Lead Salutation',
						    l.first_name AS 'Lead First Name',
						    l.last_name AS 'Lead Last name',
						    l.spouse_name AS 'Lead Spouse Name',
						    cr.skill AS 'Reporting Group',
						    cr.subskill AS 'Lead Type',
						    l.address1 AS 'Lead Address1',
						    l.address2 AS 'Lead Address2',
						    l.city AS 'Lead City',
						    l.state AS 'Lead State',
						    l.zip AS 'Lead Zip',
						    l.phone_home AS 'Phone1',
						    l.phone_mobile AS 'Phone2',
						    l.phone_work AS 'Phone3',
						    l.phone_workmobile AS 'Phone4',
						    c.name AS 'Campaign Name',
						    cs.name AS 'Campaign Stage',
						    cl.name AS 'Client Name'
						FROM
						    callresults cr
						        LEFT JOIN
						    leads l ON cr.leadId = l.id
						        LEFT JOIN
						    campaigns c ON c.id = cr.campaignId
						        LEFT JOIN
						    clients cl ON cl.id = cr.clientId
						        LEFT JOIN
						    campaignstages cs ON cs.id = cr.campaignstageid
						WHERE
						    (cs.name = 'Collections'
						        OR cs.name = 'Bad Credit Cards')
						        AND cr.leadid IS NOT NULL
						        AND cr.wrapup = 'Exception Refusal'
						        AND {{whereClause}}`
		}, {
			worksheetName: 'Standard Refusals',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'c',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
						    cr.createdAt AS 'Refusal Date',
						    l.id AS 'Kaos ID',
						    l.clientRef AS 'Client ID',
						    l.clientSourceCode AS 'Lead Source Code',
						    l.division AS 'Lead Division',
						    cr.refusalReason AS 'Refusal Reason',
						    cr.notes AS 'Notes',
						    cr.decisionMaker AS 'Decision Maker',
						    l.salutation AS 'Lead Salutation',
						    l.first_name AS 'Lead First Name',
						    l.last_name AS 'Lead Last name',
						    l.spouse_name AS 'Lead Spouse Name',
						    cr.skill AS 'Reporting Group',
						    cr.subskill AS 'Lead Type',
						    l.address1 AS 'Lead Address1',
						    l.address2 AS 'Lead Address2',
						    l.city AS 'Lead City',
						    l.state AS 'Lead State',
						    l.zip AS 'Lead Zip',
						    l.phone_home AS 'Phone1',
						    l.phone_mobile AS 'Phone2',
						    l.phone_work AS 'Phone3',
						    l.phone_workmobile AS 'Phone4',
						    c.name AS 'Campaign Name',
						    cs.name AS 'Campaign Stage',
						    cl.name AS 'Client Name'
						FROM
						    callresults cr
						        LEFT JOIN
						    leads l ON cr.leadId = l.id
						        LEFT JOIN
						    campaigns c ON c.id = cr.campaignId
						        LEFT JOIN
						    clients cl ON cl.id = cr.clientId
						        LEFT JOIN
						    campaignstages cs ON cs.id = cr.campaignstageid
						WHERE
						    (cs.name = 'Collections'
						        OR cs.name = 'Bad Credit Cards')
						        AND cr.leadid IS NOT NULL
						        AND cr.wrapup = 'Standard Refusal'
						        AND {{whereClause}}`
		}])
	})

	Models.Report.create({
		name: 'All Leads Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}],
			rawQuery: `SELECT 
				    l.id AS 'KAOS ID',
				    l.clientRef AS 'Client Ref',
				    tfs.name AS 'TF Reporting Group',
				    tfss.name AS 'TF Lead Type',
				    tms.name AS 'TM Reporting Group',
				    tmss.name AS 'TM Lead Type',
				    cs.name AS 'Current Stage',
					l.salutation AS 'Salutation',
					l.first_name AS 'First Name',
					l.last_name AS 'Last Name',
					l.address1 AS 'Address1',
					l.address2 AS 'Address2',
					l.address3 AS 'Address3',
					l.city AS 'City',
					l.state AS 'State',
					l.zip AS 'Zip',
					l.phone_home AS 'Phone1',
					l.phone_mobile AS 'Phone2',
					l.phone_work AS 'Phone3',
					l.phone_workmobile AS 'Phone4',
					l.email AS 'Email',
					l.division AS 'Division',
				    l.clientSourceCode as 'Source Code',
				    l.spouse_name as 'Spouse Name',
				    l.company_name as 'Company Name',
				    l.tix1 as 'Ticket Info 1',
				    l.tix2 as 'Ticket Info 2',
				    l.tix3 as 'Ticket Info 3',
				    l.tix4 as 'Ticket Info 4',
				    l.tix5 as 'Ticket Info 5',
				    l.tix6 as 'Ticket Info 6',
				    l.tix7 as 'Ticket Info 7',
				    l.tix8 as 'Ticket Info 8',
				    l.lastGiftAmount as 'Last Gift Amount',
				    l.lastGiftDate as 'Last Gift Date',
				    l.lyAmount as 'LY Amount',
				    l.lap1Amount as 'Lap 1 Amount',
				    l.lap2Amount as 'Lap 2 Amount',
				    l.lap3Amount as 'Lap 3 Amount',
				    l.lap4Amount as 'Lap 4 Amount'
				FROM
				    campaignleads cl
				        LEFT JOIN
				    campaigns cmp ON cmp.id = cl.campaignid
				        LEFT JOIN
				    campaignstages cs ON cs.id = cl.currentCampaignStageId
				        LEFT JOIN
				    leads l ON cl.leadid = l.id
				        LEFT JOIN
				    skills tfs ON tfs.id = l.tfSkillId
				        LEFT JOIN
				    subskills tfss ON tfss.id = l.tfSubSkillId
				        LEFT JOIN
				    skills tms ON tms.id = l.tmSkillId
				        LEFT JOIN
				    subskills tmss ON tmss.id = l.tmSubSkillId
				WHERE
					{{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'UPAF Daily Gift Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'ca',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeSingleLineDBChanges: true,
			rawQuery: `SELECT 
			    l.clientRef AS 'Lead Client Ref',
			    cr.giftAmount AS 'Gift Amount',
			    cr.decisionMaker AS 'Decision Maker',
			    l.salutation AS 'Lead Salutation',
			    l.first_name AS 'Lead First Name',
			    l.last_name AS 'Lead Last Name',
			    l.suffix AS 'Suffix',
			    l.address1 AS 'Lead Address1',
			    l.address2 AS 'Lead Address2',
			    l.address3 AS 'Lead Address3',
			    l.city AS 'Lead City',
			    l.state AS 'Lead State',
			    l.zip AS 'Lead ZIP',
			    l.division AS 'Lead Division',
			    IF(cr.paymentType = 'Invoice', 'PB', IF(cr.numberOfInstallments > 0,
			        'MCC',
			        'CC')) AS 'Class Code',
			    cr.creditCardNumber AS 'Credit Card Number',
			    cr.creditCardExpDate AS 'Credit Card Exp Date',
			    IF(cr.numberOfInstallments > 0,
			        'M',
			        'O') AS 'Frequencytype',
			    COALESCE(cr.numberOfInstallments, 1) AS 'Number of installments',
			    cr.payDate AS 'Pay Date',
			    IF(cr.numberOfInstallments > 0,
			        cr.giftAmount / cr.numberOfInstallments,
			        cr.giftAmount) AS 'userText1',
			    IF(cr.numberOfInstallments > 0,
			        cr.giftAmount / cr.numberOfInstallments,
			        0) AS 'Installment Amount',
			    cr.newMembershipCard AS 'New Membership Card',
			    '' AS 'Database Changes',
			    '' AS 'DBC Email',
			    IF(cr.notes <> '',
			        CONCAT_WS(' - ',
			                cr.decisionMaker,
			                cr.notes),
			        cr.decisionMaker) AS 'Remarks',
			    l.phone_home AS 'Lead Phone1',
			    l.phone_mobile AS 'Lead Phone2',
			    cr.giftMatchingCompany AS 'Gift Matching Company',
			    cr.createdAt AS 'Pledge Date',
			    l.clientSourceCode AS 'Lead Source Code',
			    IF(cr.declineBenefits = 1, 'Y', 'N') AS 'Decline Benefits',
			    l.company_name AS 'Company Name',
			    l.phone_work AS 'Lead Phone3',
			    l.phone_workmobile AS 'Lead Phone4',
			    IF(cr.requiresFollowUp = 1, 'Y', 'N')  AS 'Requires Follow-up',
			    cr.freeTickets AS 'Free Tickets',
			    cr.skill AS 'Reporting Group',
			    cr.subSkill AS 'Lead Type',
			    l.id AS 'Kaos ID',
			    l.lyAmount AS 'Lead LY Amount',
			    cr.installmentNotes AS 'Installment Notes',
			    cr.creditCardType AS 'Credit Card Type',
			    cr.creditCardSecurityCode AS 'Credit Card Security Code',
			    IF(cr.useExistingCreditCard = 1, 'Y', 'N') AS 'Use Existing Credit Card',
			    l.email AS 'Lead Email',
			    ca.name AS 'Campaign Name',
			    cs.name as 'Campaign Stage',
			    cl.name AS 'Client Name',
			    IF(cr.paymentType = 'Credit Card', 'Y', 'N') as 'UPAF Acknowledge'
			FROM
			    callresults cr
			        LEFT JOIN
			    leads l ON cr.leadid = l.id
			        LEFT JOIN
			    agents a ON cr.agentid = a.id
			        LEFT JOIN
			    campaigns ca ON cr.campaignid = ca.id
			        LEFT JOIN
			    clients cl ON cr.clientid = cl.id
			    	LEFT JOIN
			    campaignstages cs on cs.id = cr.campaignstageid
			WHERE
			    cr.giftAmount > 0 and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'Bad Numbers Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'clients',
				queryTableName: 'c',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    l.id AS 'Kaos Id',
				    l.clientRef AS 'Client Id',
				    l.clientSourceCode AS 'Client Source Code',
				    tfSkill.name 'Telefunding Reporting Group',
				    tfSubSkill.name 'Telefunding Lead Type',
				    tmSkill.name 'Telesales Reporting Group',
				    tmSubSkill.name 'Telesales Lead Type',
				    l.salutation AS 'Lead Salutation',
				    l.first_name AS 'Lead First Name',
				    l.last_name AS 'Lead Last Name',
				    l.spouse_name AS 'Lead Spouse Name',
				    l.address1 AS 'Lead Address1',
				    l.address2 AS 'Lead Address2',
				    l.city AS 'Lead City',
				    l.state AS 'Lead State',
				    l.zip AS 'Lead Zip',
				    l.phone_home AS 'Lead Phone1',
				    l.phone_mobile AS 'Lead Phone2',
				    l.phone_work AS 'Lead Phone3',
				    l.phone_workmobile AS 'Lead Phone4',
				    l.company_name AS 'Lead Company',
				    l.email AS 'Lead Email',
				    c.name AS 'Client Name'
				FROM
				    leads l
				        LEFT JOIN
				    clients c ON c.id = l.clientId
				        LEFT JOIN
				    skills tfSkill ON tfSkill.id = l.tfSkillId
				        LEFT JOIN
				    skills tmSkill ON tmSkill.id = l.tmSkillId
				        LEFT JOIN
				    subskills tfSubSkill ON tfSubSkill.id = l.tfSubSkillId
				        LEFT JOIN
				    subskills tmSubSkill ON tmSubSkill.id = l.tmSubSkillId
				WHERE
				    NULLIF(l.phone_home, '') IS NULL
				        AND NULLIF(l.phone_mobile, '') IS NULL
				        AND NULLIF(l.phone_work, '') IS NULL
				        AND NULLIF(l.phone_workmobile, '') IS NULL
				        AND l.reimported <> 1
				        AND {{whereClause}}
			`
		})
	})

	Models.Report.create({
		name: 'Daily Sales Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'ca',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cl',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
					    cr.skill AS 'Reporting Group',
					    cr.subSkill AS 'Lead Type',
					    s.createdAt AS 'Sale Date/Time',
					    l.id AS 'Kaos ID',
					    l.clientRef AS 'Lead Client Id',
					    l.clientSourceCode AS 'Lead Source',
					    l.division AS 'Lead Division',
					    l.salutation AS 'Lead Salutation',
					    l.first_name AS 'Lead First Name',
					    l.last_name AS 'Lead Last Name',
					    cr.decisionMaker AS 'Decision Maker',
					    s.series AS 'Series',
					    s.seats AS 'Seats',
					    s.dayOfWeek AS 'Day Of Week',
					    s.seatCount AS 'Seat Count',
					    s.costEach AS 'Cost Each',
					    s.feePerTicket AS 'Fee Per Ticket',
					    s.salesTax AS 'Sales Tax',
					    s.subtotal AS 'Subtotal',
					    cl.orderFee AS 'Client Order Fee',
					    cr.giftAmount AS 'Gift Amount',
					    cr.grandTotal AS 'Grand Total',
					    cr.useExistingCreditCard AS 'Use Existing Credit Card',
					    cr.creditCardType AS 'Credit Card Type',
					    cr.creditCardNumber AS 'Credit Card Number',
					    cr.creditCardExpDate AS 'Credit Card Exp Date',
					    cr.creditCardSecurityCode AS 'Credit Card Security Code',
					    cr.notes AS 'Notes',
					    cr.newMembershipCard AS 'New Membership Card',
					    cr.freeTickets AS 'Free Tickets',
					    l.address1 AS 'Lead Address1',
					    l.address2 AS 'Lead Address2',
					    l.city AS 'Lead City',
					    l.state AS 'Lead State',
					    l.zip AS 'Lead Zip',
					    l.phone_home AS 'Lead Phone1',
					    l.phone_mobile AS 'Lead Phone2',
					    l.phone_work AS 'Lead Phone3',
					    l.phone_workmobile AS 'Lead Phone4',
					    l.email AS 'Lead Email',
					    cl.name AS 'Client Name',
					    ca.name AS 'Campaign Name',
					    cs.name as 'Campaign Stage'
					FROM
					    sales s
					        LEFT JOIN
					    callresults cr ON cr.id = s.callresultid
					        LEFT JOIN
					    leads l ON s.leadid = l.id
					        LEFT JOIN
					    clients cl ON s.clientId = cl.id
					        LEFT JOIN
					    campaigns ca ON s.campaignid = ca.id
					    	LEFT JOIN
					    campaignstages cs ON cs.id = cr.campaignstageid
					WHERE cr.wrapup = 'Sale' and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'Agent Payroll',
		folder: 'Agent Reports',
		isSystem: true,
		definition: JSON.stringify([{
			worksheetName: 'By Week',
			rollupField: 'week',
			availableFilters: [{
				tableName: 'agents',
				queryTableName: 'a',
				model: 'Agent'
			}, {
				tableName: 'payrolls',
				queryTableName: 'p',
				field: 'date',
				type: 'datetime'
			}],
			rawQuery: `
				SELECT
					WEEK(p.date) AS Week,
					TRUNCATE(SUM(p.totalSeconds / 60 / 60), 2) AS 'Hours',
					a.name as Name,
					a.hourlyRate AS Rate,
					TRUNCATE(a.hourlyRate * SUM(p.totalSeconds / 60 / 60), 2) AS Cost
				FROM
					payrolls p
						LEFT JOIN
					agents a ON p.agentId = a.id
				WHERE {{whereClause}}
				GROUP BY WEEK(p.date), a.name WITH ROLLUP
			`
		}, {
			worksheetName: 'By Day',
			availableFilters: [{
				tableName: 'agents',
				queryTableName: 'a',
				model: 'Agent'
			}, {
				tableName: 'payrolls',
				queryTableName: 'p',
				field: 'date',
				type: 'datetime'
			}],
			rawQuery: `
				SELECT
					DATE_FORMAT(p.date, '%Y-%m-%d') AS Date,
					TRUNCATE(SUM(p.totalSeconds / 60 / 60), 2) AS 'Hours',
					a.name as Name,
					a.hourlyRate AS Rate,
					TRUNCATE(a.hourlyRate * SUM(p.totalSeconds / 60 / 60), 2) AS Cost,
				    (SELECT 
				            createdAt
				        FROM
				            agentevents ae
				        WHERE
				            ae.agentId = a.id
				                AND createdAt >= p.date
				                AND createdAt <= DATE_ADD(p.date, INTERVAL 1 DAY)
				                AND eventName = 'Logged In'
				        order by createdAt LIMIT 1) AS 'Login',
				    (SELECT 
				            createdAt
				        FROM
				            agentevents ae
				        WHERE
				            ae.agentId = a.id
				                AND createdAt >= p.date
				                AND createdAt <= DATE_ADD(p.date, INTERVAL 1 DAY)
				                AND eventName = 'Logged Out'
				        order by createdAt DESC LIMIT 1) AS 'Logout',
				    (SELECT 
				            createdAt
				        FROM
				            callresults cr
				        WHERE
				            cr.agentId = a.id
				                AND createdAt >= p.date
				                AND createdAt <= DATE_ADD(p.date, INTERVAL 1 DAY)
				        order by createdAt LIMIT 1) AS 'First Call',
				    (SELECT 
				            createdAt
				        FROM
				            callresults cr
				        WHERE
				            cr.agentId = a.id
				                AND createdAt >= p.date
				                AND createdAt <= DATE_ADD(p.date, INTERVAL 1 DAY)
				        order by createdAt DESC LIMIT 1) AS 'Last Call'
				FROM
					payrolls p
						LEFT JOIN
					agents a ON p.agentId = a.id
				WHERE {{whereClause}}
				GROUP BY agentId, date
			`
		}])
	})

	Models.Report.create({
		name: 'Exception Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'ca',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'c',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
			    cr.createdAt as 'Refusal Date',
			    l.id as 'Kaos ID',
			    l.clientRef as 'Client ID',
			    l.clientSourceCode as 'Lead Source Code',
			    l.division as 'Lead Division',
			    cr.refusalReason as 'Refusal Reason',
			    cr.notes as 'Notes',
			    cr.decisionMaker as 'Decision Maker',
			    l.salutation as 'Lead Salutation',
			    l.first_name as 'Lead First Name',
			    l.last_name as 'Lead Last name',
			    l.spouse_name as 'Lead Spouse Name',
			    cr.skill as 'Reporting Group',
			    cr.subskill as 'Lead Type',
			    l.address1 as 'Lead Address1',
			    l.address2 as 'Lead Address2',
			    l.city as 'Lead City',
			    l.state as 'Lead State',
			    l.zip as 'Lead Zip',
			    l.phone_home as 'Phone1',
			    l.phone_mobile as 'Phone2',
			    l.phone_work as 'Phone3',
			    l.phone_workmobile as 'Phone4',
			    ca.name as 'Campaign Name',
			    cs.name as 'Campaign Stage',
			    c.name as 'Client Name'
			FROM
			    callresults cr
			left join leads l on cr.leadId = l.id
			left join campaigns ca on ca.id = cr.campaignId
			left join clients c on c.id = cr.clientId
			left join campaignstages cs on cs.id = cr.campaignstageid
			WHERE
    			cr.wrapup = 'Exception Refusal' and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'Refusal Report',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'ca',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'c',
				model: 'Client'
			}, {
				tableName: 'callresults',
				queryTableName: 'cr',
				field: 'createdAt',
				type: 'datetime'
			}],
			includeDBChanges: true,
			rawQuery: `SELECT 
			    cr.createdAt as 'Refusal Date',
			    l.id as 'Kaos ID',
			    l.clientRef as 'Client ID',
			    l.clientSourceCode as 'Lead Source Code',
			    l.division as 'Lead Division',
			    cr.refusalReason as 'Refusal Reason',
			    cr.notes as 'Notes',
			    cr.decisionMaker as 'Decision Maker',
			    l.salutation as 'Lead Salutation',
			    l.first_name as 'Lead First Name',
			    l.last_name as 'Lead Last name',
			    l.spouse_name as 'Lead Spouse Name',
			    cr.skill as 'Reporting Group',
			    cr.subskill as 'Lead Type',
			    l.address1 as 'Lead Address1',
			    l.address2 as 'Lead Address2',
			    l.city as 'Lead City',
			    l.state as 'Lead State',
			    l.zip as 'Lead Zip',
			    l.phone_home as 'Phone1',
			    l.phone_mobile as 'Phone2',
			    l.phone_work as 'Phone3',
			    l.phone_workmobile as 'Phone4',
			    ca.name as 'Campaign Name',
			    cs.name as 'Campaign Stage',
			    c.name as 'Client Name'
			FROM
			    callresults cr
			left join leads l on cr.leadId = l.id
			left join campaigns ca on ca.id = cr.campaignId
			left join clients c on c.id = cr.clientId
			left join campaignstages cs on cs.id = cr.campaignstageid
			WHERE
    			cr.wrapup = 'Standard Refusal' and {{whereClause}}`
		})
	})

	Models.Report.create({
		name: 'Weekly Goals Report',
		folder: 'Agent Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				placeholder: '{{startDate}}',
				type: 'date',
				name: 'Start Date'
			}, {
				placeholder: '{{endDate}}',
				type: 'date',
				name: 'End Date'
			}],
			rawQuery: `SELECT 
						    u.name AS 'Supervisor',
						    c.name AS 'Client Name',
						    a.name AS 'Agent Name',
						    (SELECT 
						            ROUND(cat.scheduledHours)
						        FROM
						            campaignagenttargets cat
						        WHERE
						            cat.agentId = a.id
						                AND cat.campaignid = ca.id
						                AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						        LIMIT 1) AS 'Sch Hrs',
						    ROUND((SELECT 
						                    (SUM(p.totalSeconds) / 60 / 60)
						                FROM
						                    payrolls p
						                WHERE
						                    p.agentid = a.id
						                        AND p.campaignId = ca.id
						                        AND p.date >= '{{startDate}}'
						                        AND p.date <= '{{endDate}}')) AS 'Act Hours',
						    ROUND((SELECT 
				                    ROUND(cat.level)
				                FROM
				                    campaignagenttargets cat
				                WHERE
				                    cat.agentId = a.id
				                        AND cat.campaignid = ca.id
				                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
				                LIMIT 1) * (SELECT 
				                    ROUND(cat.scheduledHours)
				                FROM
				                    campaignagenttargets cat
				                WHERE
				                    cat.agentId = a.id
				                        AND cat.campaignid = ca.id
				                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
				                LIMIT 1)) AS 'Threshold',
						    ROUND((COUNT(cr.id) / (SELECT 
						                    (SUM(p.totalSeconds) / 60 / 60)
						                FROM
						                    payrolls p
						                WHERE
						                    p.agentid = a.id
						                        AND p.campaignId = ca.id
						                        AND p.date >= '{{startDate}}'
						                        AND p.date <= '{{endDate}}'))) AS 'Calls / Hr',
						    ROUND((SELECT 
						                    cat.goal
						                FROM
						                    campaignagenttargets cat
						                WHERE
						                    cat.agentId = a.id
						                        AND cat.campaignid = ca.id
						                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                		AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) limit 1)) AS 'Goal',
						    ROUND(SUM(cr.grandTotal)) AS 'Actual Amt',
						    (IF(a.hourlyRate = 11,
						        60,
						        IF(a.hourlyRate = 13, 80, 100))) AS 'Thresh/hr',
						    ROUND((SELECT 
						                    cat.goal
						                FROM
						                    campaignagenttargets cat
						                WHERE
						                    cat.agentId = a.id
						                        AND cat.campaignId = ca.id
						                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                		AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) limit 1) / (SELECT 
						                    cat.scheduledHours
						                FROM
						                    campaignagenttargets cat
						                WHERE
						                    cat.agentId = a.id
						                        AND cat.campaignid = ca.id
						                        AND cat.start <= DATE_ADD('{{startDate}}', INTERVAL 3 DAY)
						                		AND cat.end >= DATE_ADD('{{startDate}}', INTERVAL 3 DAY) limit 1)) AS 'Goal/hr',
						    ROUND((SUM(cr.grandTotal) / (SELECT 
						                    (SUM(p.totalSeconds) / 60 / 60)
						                FROM
						                    payrolls p
						                WHERE
						                    p.agentId = a.id AND p.campaignId = ca.id
						                        AND p.date >= '{{startDate}}'
						                        AND p.date <= '{{endDate}}'))) AS 'Actual/hr',
						    ROUND((100 / (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND grandTotal > 0
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) * (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND grandTotal > 0
						                        AND skill = 'acquisition'
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) AS 'Acq %',
						    ROUND(SUM((CASE
						                WHEN 
						                	DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE('{{startDate}}') THEN cr.grandTotal
						                ELSE 0
						            END))) AS 'Monday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 1 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Tuesday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 2 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Wednesday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 3 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Thursday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 4 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Friday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 5 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Saturday Total',
						    ROUND(SUM((CASE
						                WHEN
						                    DATE(DATE_SUB(cr.createdAt, INTERVAL 5 HOUR)) = DATE_ADD(DATE('{{startDate}}'),
						                        INTERVAL 6 DAY)
						                THEN
						                    cr.grandTotal
						                ELSE 0
						            END))) AS 'Sunday Total',
						    ROUND((100 / (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND grandTotal > 0
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) * (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND grandTotal > 0
						                        AND paymentType = 'Credit Card'
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) AS 'CC Rate',
						    ROUND((SUM((SELECT 
						                    SUM(callresults.giftAmount)
						                FROM
						                    callresults
						                WHERE
						                    leadId = cr.leadid
						                        AND campaignId = ca.id
						                        AND giftAmount > 0
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) / SUM(CAST(l.lyAmount AS UNSIGNED))) * 100) AS 'Renew Inc',
						    ROUND((100 / (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND saleAmount > 0
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) * (SELECT 
						                    COUNT(*)
						                FROM
						                    callresults
						                WHERE
						                    agentId = a.id AND campaignid = ca.id
						                        AND saleAmount > 0
						                        AND giftAmount > 0
						                        AND createdAt >= '{{startDate}}'
						                        AND createdAt <= '{{endDate}}')) AS 'Addon %',
						    (SELECT 
						            COUNT(*)
						        FROM
						            callresults
						        WHERE
						            agentId = a.id AND campaignid = ca.id
						                AND grandTotal > 0
						                AND createdAt >= '{{startDate}}'
						                AND createdAt <= '{{endDate}}') AS 'Total #',
						    SUM((SELECT 
						            GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
						                                CAST(leads.lap1Amount AS UNSIGNED),
						                                0)),
						                        0)
						        FROM
						            callresults
						                LEFT JOIN
						            leads ON leads.id = callresults.leadId
						        WHERE
						            agentId = a.id AND campaignid = ca.id
						                AND grandTotal > 0
						                AND callresults.createdAt >= '{{startDate}}'
						                AND callresults.createdAt <= '{{endDate}}'
						                AND callresults.leadId = l.id)) AS 'New $',
						    ROUND((SUM(CASE
						                WHEN
						                    ((SELECT 
						                            GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
						                                                CAST(leads.lap1Amount AS UNSIGNED),
						                                                0)),
						                                        0)
						                        FROM
						                            callresults
						                                LEFT JOIN
						                            leads ON leads.id = callresults.leadId
						                        WHERE
						                            callresults.leadId = l.id
						                                AND agentId = a.id
						                                AND campaignid = ca.id
						                                AND callresults.createdAt >= '{{startDate}}'
						                                AND callresults.createdAt <= '{{endDate}}'
						                                AND callresults.paymentType = 'Credit Card') > 0)
						                THEN
						                    1
						                ELSE 0
						            END) / SUM(CASE
						                WHEN
						                    ((SELECT 
						                            GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
						                                                CAST(leads.lap1Amount AS UNSIGNED),
						                                                0)),
						                                        0)
						                        FROM
						                            callresults
						                                LEFT JOIN
						                            leads ON leads.id = callresults.leadId
						                        WHERE
						                            callresults.agentId = a.id
						                                AND campaignid = ca.id
						                                AND callresults.createdAt >= '{{startDate}}'
						                                AND callresults.createdAt <= '{{endDate}}'
						                                AND callresults.leadId = l.id) > 0)
						                THEN
						                    1
						                ELSE 0
						            END)) * 100) AS 'New $ CC Rate'
						FROM
						    callresults cr
						        LEFT JOIN
						    agents a ON a.id = cr.agentid
						        LEFT JOIN
						    campaigns ca ON ca.id = cr.campaignId
						        LEFT JOIN
						    users u ON u.id = ca.owninguserid
						        LEFT JOIN
						    clients c ON c.id = cr.clientid
						        LEFT JOIN
						    leads l ON l.id = cr.leadid
						WHERE
						    cr.createdAt >= '{{startDate}}'
						        AND cr.createdAt <= '{{endDate}}'
						        AND cr.agentId IS NOT NULL
						        AND cr.clientId <> 3
						GROUP BY a.id , u.id , ca.id`
		})
	})

	Models.Report.create({
		name: 'Telefunding Cumulative',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify([{
			worksheetName: 'Campaign Summary',
			rollupField: 'Reporting Group',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
			    	sks.name AS 'Reporting Group',
					sbsks.name AS 'Lead Type',
					CONCAT('$', ROUND(SUM(
						COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)))) AS 'Gross Leads $',
					COUNT(lds.id) AS 'Gross Leads #',
					CONCAT('$',
							ROUND(SUM(CASE
										WHEN
											(NULLIF(lds.phone_home, '') IS NULL
												AND NULLIF(lds.phone_work, '') IS NULL
												AND NULLIF(lds.phone_mobile, '') IS NULL
												AND NULLIF(lds.phone_workmobile, '') IS NULL)
										THEN
												COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
												0)
										ELSE 0
									END))) AS 'Bad Numbers $',
					SUM(CASE
						WHEN
							(NULLIF(lds.phone_home, '') IS NULL
								AND NULLIF(lds.phone_work, '') IS NULL
								AND NULLIF(lds.phone_mobile, '') IS NULL
								AND NULLIF(lds.phone_workmobile, '') IS NULL)
						THEN
							1
						ELSE 0
					END) AS 'Bad Numbers #',
					CONCAT('$',
							ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									so_cstg.name = '1st Appeal'
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Unresolved $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									so_cstg.name = '1st Appeal'
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							1
						ELSE 0
					END) AS 'Unresolved #',
					COALESCE(CONCAT(ROUND((COUNT(lds.id) - SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															campaignleads cmp_lds
																LEFT JOIN
															campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
														WHERE
															so_cstg.name = '1st Appeal'
																AND cmp_lds.leadId = lds.id) > 0)
												THEN
													1
												ELSE 0
											END)) / (SUM(CASE
												WHEN
													(NULLIF(lds.phone_home, '') IS NULL
														AND NULLIF(lds.phone_work, '') IS NULL
														AND NULLIF(lds.phone_mobile, '') IS NULL
														AND NULLIF(lds.phone_workmobile, '') IS NULL)
												THEN
													0
												ELSE 1
											END)) * 100),
									'%'),
							'0%') AS 'Resolution Rate',
					CONCAT(ROUND(SUM(CASE
										WHEN
											((SELECT 
													COUNT(*)
												FROM
													callresults
												WHERE
													callresults.leadId = lds.id
														AND callresults.campaignId = cmp.id
														AND callresults.grandTotal > 0
												LIMIT 1) > 0)
										THEN
											1
										ELSE 0
									END) / (COUNT(lds.id)) * 100),
							'%') AS 'Response Rate on Gross',
					COALESCE(CONCAT(ROUND(SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															callresults
														WHERE
															callresults.leadId = lds.id
																AND callresults.campaignId = cmp.id
																AND callresults.grandTotal > 0
														LIMIT 1) > 0)
												THEN
													1
												ELSE 0
											END) / (COUNT(lds.id) - SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															campaignleads cmp_lds
																LEFT JOIN
															campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
														WHERE
															so_cstg.name = '1st Appeal'
																AND cmp_lds.leadId = lds.id) > 0)
												THEN
													1
												ELSE 0
											END)) * 100),
									'%'),
							'0%') AS 'Response Rate on Closed',
					COALESCE(SUM(CASE WHEN (SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)
								> 0 THEN 1 ELSE 0 END),
							0) AS 'Donors',
					COALESCE(SUM((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0) AS 'Gifts',
					CONCAT('$', ROUND(COALESCE(AVG((SELECT 
									AVG(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0))) AS 'Avg Gift',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0))) AS 'Total Pledged',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.createdAt > CONCAT(DATE_SUB(DATE(NOW()),
																		INTERVAL DAYOFWEEK(NOW()) + 5 DAY), ' 04:00:00')
										AND callresults.createdAt <= CONCAT(DATE_SUB(DATE(NOW()),
																		INTERVAL DAYOFWEEK(NOW()) - 2 DAY), ' 03:59:59'))),
							0))) AS 'Last Week Pledged',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            (SELECT 
				                                    SUM(callresults.giftAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadid = lds.id
				                                        AND callresults.campaignId = cmp.id) > 0
				                        THEN
				                            lds.lyAmount
				                        ELSE 0
				                    END))) AS 'LY Amount',
				    COALESCE(CONCAT((ROUND((SUM((SELECT 
				                                    SUM(callresults.giftAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.giftAmount > 0)) / ROUND(SUM(CASE
				                                        WHEN
				                                            (SELECT 
				                                                    SUM(callresults.giftAmount)
				                                                FROM
				                                                    callresults
				                                                WHERE
				                                                    callresults.leadid = lds.id
				                                                        AND callresults.campaignId = cmp.id) > 0
				                                        THEN
				                                            lds.lyAmount
				                                        ELSE 0
				                                    END))) * 100)),
				                    '%'),
				            '0%') AS 'Ren Inc',
					COALESCE(SUM((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0
										AND paymentType = 'Credit Card')),
							0) AS 'Credit Card Pledges',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0
										AND paymentType = 'Credit Card')),
							0))) AS 'Credit Card Total Pledged',
					COALESCE(
						CONCAT(
							ROUND(
								(COALESCE(
									SUM((SELECT 
										SUM(callresults.giftAmount)
									FROM
										callresults
									WHERE
										callresults.leadId = lds.id
											AND callresults.campaignId = cmp.id
											AND callresults.giftAmount > 0
											AND paymentType = 'Credit Card')),
								0)
				                /
				                COALESCE(	
									SUM((SELECT 
												SUM(callresults.giftAmount)
											FROM
												callresults
											WHERE
												callresults.leadId = lds.id
													AND callresults.campaignId = cmp.id
													AND callresults.giftAmount > 0)),
										0)
									) * 100),
								'%'),
							'0%') AS 'Credit Card Pledge %',
					CONCAT('$', ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.wrapup = 'Exception Refusal') > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Refusal Exceptions $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.wrapup = 'Exception Refusal') > 0)
						THEN
							1
						ELSE 0
					END) AS 'Refusal Exceptions #',
					CONCAT('$', ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									(so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Refusal - 1st No $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									(so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							1
						ELSE 0
					END) AS 'Refusal - 1st No #',
					CONCAT('$',
							ROUND(COALESCE(SUM((SELECT 
									IF(callresults.subskill = '2nd Appeal Non-Telefunding', 
										GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
														CAST(leads.lap1Amount AS UNSIGNED),
														0)),
												0),
				                        GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
														CAST(leads.lap1Amount AS UNSIGNED),
														0)),
												0)
									)
								FROM
									callresults
										LEFT JOIN
									leads ON leads.id = callresults.leadId
								WHERE
									callresults.leadId = lds.id)),
							0))) AS 'New $'
				FROM
					leads lds
						LEFT JOIN
					skills sks ON lds.tfSkillId = sks.id
						LEFT JOIN
					subskills sbsks ON lds.tfSubSkillId = sbsks.id
						LEFT JOIN
					campaignleads clds ON clds.leadId = lds.id
						LEFT JOIN
					campaigns cmp ON cmp.id = clds.campaignId
						LEFT JOIN
					clients cli ON cli.id = cmp.clientId
			    WHERE {{whereClause}}
				GROUP BY sks.name , sbsks.name WITH ROLLUP
        	`
		}, {
			worksheetName: 'By Refusal Code',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    skills.name as 'Reporting Group',
				    callresults.refusalReason AS 'Refusal Code',
				    COUNT(*) AS '#'
				FROM
				    callresults
				        LEFT JOIN
				    campaigns cmp ON cmp.id = callresults.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = callresults.clientId
				        LEFT JOIN
				    leads ON leads.id = callresults.leadId
				        LEFT JOIN
				    skills ON leads.tfSkillId = skills.id
				WHERE
				    callresults.refusalReason IS NOT NULL
				AND 
				{{whereClause}}
				GROUP BY callresults.refusalReason , skills.name
				ORDER BY skills.name
			`
		}, {
			worksheetName: 'Campaign Summary - Goals',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$', COALESCE(cmp.goal, 0)) AS 'Goal',
				    CONCAT('$',
				            ROUND(COALESCE((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition'),
				                    0))) AS 'Acquistion Goal',
				    CONCAT('$',
				            ROUND(COALESCE((cmp.goal - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)),
				                    0))) AS '$ to Goal',
				    CONCAT('$',
				            ROUND(COALESCE(((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition')),
				                    0))) AS '$ to Acq Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id) / cmp.goal) * 100),
				            '%') AS '% to Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition') / (SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition')) * 100),
				            '%') AS '% to Acq Goal',
				    CONCAT(ROUND(COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0))),
				            0), ' / ', ROUND((SELECT 
				            SUM(cmpproj.projectedQuantity)
				        FROM
				            campaignprojections cmpproj
				        WHERE
				            cmpproj.campaignId = cmp.id))) AS 'Volume Goal',
				    cmp.endDate AS 'End Date',
				    ROUND(DATEDIFF(cmp.endDate, NOW()) / 7) AS 'Weeks Remaining',
				    CONCAT('$',
				            ROUND((cmp.goal - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed',
				    CONCAT('$',
				            ROUND((((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition'))) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed Acq'
				FROM
				    leads lds
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
			`
		}, {
			worksheetName: 'Collections & Costs',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$',
				            ROUND((SELECT 
				                            SUM(inv.grandTotal - inv.amountRemaining)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id))) AS 'Total Collected',
				    CONCAT(ROUND(((SELECT 
				                            SUM(inv.grandTotal - inv.amountRemaining)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id) / (SELECT 
				                            SUM(inv.grandTotal)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id)) * 100),
				            '%') AS 'Collection Rate',
				    COALESCE((SELECT 
				                    SUM(cc.hours)
				                FROM
				                    clientcostings cc
				                WHERE
				                    cc.campaignId = cmp.id),
				            0) AS 'Total Hours',
				    CONCAT('$',
				            ROUND(((SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id) / COALESCE((SELECT 
				                            SUM(cc.hours)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    1)))) AS 'Dollars per Hour',
				    CONCAT('$',
				            COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0)) AS 'Total Cost',
				    CONCAT(ROUND((COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0) / (SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id)) * 100), '%') AS 'Cost per Dollar',
				    CONCAT('$',
				            ((SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id) - COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0))) AS 'Net Gain'
				FROM
				    campaigns cmp
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
			`
		}, {
			worksheetName: '2nd Appeal Summary',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$', COALESCE(SUM(cr.giftAmount), 0)) AS 'Total 2nd Appeal $',
				    CONCAT('$',
				            COALESCE(SUM(CASE
				                        WHEN cr.subSkill = '2nd Appeal Non-Telefunding' THEN cr.giftAmount
				                        ELSE 0
				                    END),
				                    0)) AS 'Non TF 2nd Appeal $',
				    CONCAT('$',
				            COALESCE(SUM(CASE
				                        WHEN cr.subSkill <> '2nd Appeal Non-Telefunding' THEN cr.giftAmount
				                        ELSE 0
				                    END),
				                    0)) AS 'TF 2nd Appeal $',
				    COALESCE(SUM((CASE
				                WHEN cr.giftAmount > 0 THEN 1
				                ELSE 0
				            END)),
				            0) AS '2nd Appeal Total # Gifts',
				    CONCAT('$',
				            COALESCE(ROUND(AVG(NULLIF(cr.giftAmount, 0))), 0)) AS '2nd Appeal Avg Gift',
				    CONCAT(ROUND(COALESCE((SUM(CASE
				                                WHEN
				                                    cr.giftAmount > 0
				                                        AND cr.subSkill <> '2nd Appeal Non-Telefunding'
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / SUM(IF(cr.subSkill <> '2nd Appeal Non-Telefunding', 1, 0))) * 100,
				                            0)),
				            '%') AS 'Donor Reponse Rate: TF 2nd Appeal',
				    CONCAT(ROUND(COALESCE((SUM(CASE
				                                WHEN
				                                    cr.giftAmount > 0
				                                        AND cr.subSkill = '2nd Appeal Non-Telefunding'
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / SUM(IF(cr.subSkill = '2nd Appeal Non-Telefunding', 1, 0))) * 100,
				                            0)),
				            '%') AS 'Donor Reponse Rate: Non-TF 2nd Appeal'
				FROM
				    callresults cr
				        LEFT JOIN
				    campaigns cmp ON cmp.id = cr.campaignId
				        LEFT JOIN
				    campaignstages cs ON cs.id = cr.campaignstageId
				        LEFT JOIN
				    clients cli ON cli.id = cr.clientId
				WHERE
				    (cs.name = '2nd Appeal' OR cr.subSkill = '2nd Appeal Non-Telefunding') AND
				    {{whereClause}}
			`
		}, {
			worksheetName: 'Executive Summary',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    sks.name AS 'Reporting Group',
				    COALESCE(
						(SELECT SUM(campaigngoals.projectedQuantity) FROM campaigngoals WHERE
							campaigngoals.reportingGroup = sks.name AND
				            campaigngoals.campaignId = cmp.id)
				            , 0) -
						COUNT(lds.id) AS 'Qty Variance',
				    CONCAT('$',
						ROUND(
							COALESCE(
								(SELECT SUM(goal) FROM campaigngoals WHERE
									campaigngoals.reportingGroup = sks.name AND
									campaigngoals.campaignId = cmp.id)
							, 0)
							-
							COALESCE(
								NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0)
							,0)
						)
					) AS 'Value Variance',
				    COALESCE(CONCAT(ROUND(SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            callresults
				                                        WHERE
				                                            callresults.leadId = lds.id
				                                                AND callresults.campaignId = cmp.id
				                                                AND callresults.grandTotal > 0
				                                        LIMIT 1) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / (COUNT(lds.id) - SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            campaignleads cmp_lds
				                                                LEFT JOIN
				                                            campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                        WHERE
				                                            so_cstg.name = '1st Appeal'
				                                                AND cmp_lds.leadId = lds.id) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END)) * 100),
				                    '%'),
				            '0%') AS 'Response Rate on Closed',
				    CONCAT('$', ROUND(COALESCE(AVG((SELECT 
				                    AVG(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0)),
				            0))) AS 'Average Gift $',
				    COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0
				                LIMIT 1)),
				            0) AS 'Donor Count',
				    CONCAT('$', ROUND(COALESCE(SUM((SELECT 
				                    SUM(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0)),
				            0))) AS 'Total $',
				    COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount >= 500)),
				            0) AS 'Large Gifts ($500+) #',
				    CONCAT('$', ROUND(COALESCE(SUM((SELECT 
				                    SUM(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount >= 500)),
				            0))) AS 'Large Gifts ($500+) $'
				FROM
				    leads lds
				        LEFT JOIN
				    skills sks ON lds.tfSkillId = sks.id
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
				GROUP BY sks.name
				WITH ROLLUP
			`
		}])
	})

	Models.Report.create({
		name: 'UPAF Telefunding Cumulative',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify([{
			worksheetName: 'Campaign Summary',
			rollupField: 'Division',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
					lds.division as 'Division',
			    	sks.name AS 'Reporting Group',
					sbsks.name AS 'Lead Type',
					CONCAT('$', ROUND(SUM(
						COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)))) AS 'Gross Leads $',
					COUNT(lds.id) AS 'Gross Leads #',
					CONCAT('$',
							ROUND(SUM(CASE
										WHEN
											(NULLIF(lds.phone_home, '') IS NULL
												AND NULLIF(lds.phone_work, '') IS NULL
												AND NULLIF(lds.phone_mobile, '') IS NULL
												AND NULLIF(lds.phone_workmobile, '') IS NULL)
										THEN
												COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
												NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
												0)
										ELSE 0
									END))) AS 'Bad Numbers $',
					SUM(CASE
						WHEN
							(NULLIF(lds.phone_home, '') IS NULL
								AND NULLIF(lds.phone_work, '') IS NULL
								AND NULLIF(lds.phone_mobile, '') IS NULL
								AND NULLIF(lds.phone_workmobile, '') IS NULL)
						THEN
							1
						ELSE 0
					END) AS 'Bad Numbers #',
					CONCAT('$',
							ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									so_cstg.name = '1st Appeal'
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Unresolved $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									so_cstg.name = '1st Appeal'
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							1
						ELSE 0
					END) AS 'Unresolved #',
					COALESCE(CONCAT(ROUND((COUNT(lds.id) - SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															campaignleads cmp_lds
																LEFT JOIN
															campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
														WHERE
															so_cstg.name = '1st Appeal'
																AND cmp_lds.leadId = lds.id) > 0)
												THEN
													1
												ELSE 0
											END)) / (SUM(CASE
												WHEN
													(NULLIF(lds.phone_home, '') IS NULL
														AND NULLIF(lds.phone_work, '') IS NULL
														AND NULLIF(lds.phone_mobile, '') IS NULL
														AND NULLIF(lds.phone_workmobile, '') IS NULL)
												THEN
													0
												ELSE 1
											END)) * 100),
									'%'),
							'0%') AS 'Resolution Rate',
					CONCAT(ROUND(SUM(CASE
										WHEN
											((SELECT 
													COUNT(*)
												FROM
													callresults
												WHERE
													callresults.leadId = lds.id
														AND callresults.campaignId = cmp.id
														AND callresults.grandTotal > 0
												LIMIT 1) > 0)
										THEN
											1
										ELSE 0
									END) / (COUNT(lds.id)) * 100),
							'%') AS 'Response Rate on Gross',
					COALESCE(CONCAT(ROUND(SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															callresults
														WHERE
															callresults.leadId = lds.id
																AND callresults.campaignId = cmp.id
																AND callresults.grandTotal > 0
														LIMIT 1) > 0)
												THEN
													1
												ELSE 0
											END) / (COUNT(lds.id) - SUM(CASE
												WHEN
													((SELECT 
															COUNT(*)
														FROM
															campaignleads cmp_lds
																LEFT JOIN
															campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
														WHERE
															so_cstg.name = '1st Appeal'
																AND cmp_lds.leadId = lds.id) > 0)
												THEN
													1
												ELSE 0
											END)) * 100),
									'%'),
							'0%') AS 'Response Rate on Closed',
					COALESCE(SUM(CASE WHEN (SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)
								> 0 THEN 1 ELSE 0 END),
							0) AS 'Donors',
					COALESCE(SUM((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0) AS 'Gifts',
					CONCAT('$', ROUND(COALESCE(AVG((SELECT 
									AVG(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0))) AS 'Avg Gift',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0)),
							0))) AS 'Total Pledged',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.createdAt > CONCAT(DATE_SUB(DATE(NOW()),
																		INTERVAL DAYOFWEEK(NOW()) + 5 DAY), ' 04:00:00')
										AND callresults.createdAt <= CONCAT(DATE_SUB(DATE(NOW()),
																		INTERVAL DAYOFWEEK(NOW()) - 2 DAY), ' 03:59:59'))),
							0))) AS 'Last Week Pledged',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            (SELECT 
				                                    SUM(callresults.giftAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadid = lds.id
				                                        AND callresults.campaignId = cmp.id) > 0
				                        THEN
				                            lds.lyAmount
				                        ELSE 0
				                    END))) AS 'LY Amount',
				    COALESCE(CONCAT((ROUND((SUM((SELECT 
				                                    SUM(callresults.giftAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.giftAmount > 0)) / ROUND(SUM(CASE
				                                        WHEN
				                                            (SELECT 
				                                                    SUM(callresults.giftAmount)
				                                                FROM
				                                                    callresults
				                                                WHERE
				                                                    callresults.leadid = lds.id
				                                                        AND callresults.campaignId = cmp.id) > 0
				                                        THEN
				                                            lds.lyAmount
				                                        ELSE 0
				                                    END))) * 100)),
				                    '%'),
				            '0%') AS 'Ren Inc',
					COALESCE(SUM((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0
										AND paymentType = 'Credit Card')),
							0) AS 'Credit Card Pledges',
					CONCAT('$', ROUND(COALESCE(SUM((SELECT 
									SUM(callresults.giftAmount)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.giftAmount > 0
										AND paymentType = 'Credit Card')),
							0))) AS 'Credit Card Total Pledged',
					COALESCE(
						CONCAT(
							ROUND(
								(COALESCE(
									SUM((SELECT 
										SUM(callresults.giftAmount)
									FROM
										callresults
									WHERE
										callresults.leadId = lds.id
											AND callresults.campaignId = cmp.id
											AND callresults.giftAmount > 0
											AND paymentType = 'Credit Card')),
								0)
				                /
				                COALESCE(	
									SUM((SELECT 
												SUM(callresults.giftAmount)
											FROM
												callresults
											WHERE
												callresults.leadId = lds.id
													AND callresults.campaignId = cmp.id
													AND callresults.giftAmount > 0)),
										0)
									) * 100),
								'%'),
							'0%') AS 'Credit Card Pledge %',
					CONCAT('$', ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.wrapup = 'Exception Refusal') > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Refusal Exceptions $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									callresults
								WHERE
									callresults.leadId = lds.id
										AND callresults.campaignId = cmp.id
										AND callresults.wrapup = 'Exception Refusal') > 0)
						THEN
							1
						ELSE 0
					END) AS 'Refusal Exceptions #',
					CONCAT('$', ROUND(SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									(so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
							NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
							0)
						ELSE 0
					END))) AS 'Refusal - 1st No $',
					SUM(CASE
						WHEN
							((SELECT 
									COUNT(*)
								FROM
									campaignleads cmp_lds
										LEFT JOIN
									campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
								WHERE
									(so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
										AND cmp_lds.leadId = lds.id) > 0)
						THEN
							1
						ELSE 0
					END) AS 'Refusal - 1st No #',
					CONCAT('$',
							COALESCE(SUM((SELECT 
									IF(callresults.subskill = '2nd Appeal Non-Telefunding', 
										GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
														CAST(leads.lap1Amount AS UNSIGNED),
														0)),
												0),
				                        GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
														CAST(leads.lap1Amount AS UNSIGNED),
														0)),
												0)
									)
								FROM
									callresults
										LEFT JOIN
									leads ON leads.id = callresults.leadId
								WHERE
									callresults.leadId = lds.id)),
							0)) AS 'New $'
				FROM
					leads lds
						LEFT JOIN
					skills sks ON lds.tfSkillId = sks.id
						LEFT JOIN
					subskills sbsks ON lds.tfSubSkillId = sbsks.id
						LEFT JOIN
					campaignleads clds ON clds.leadId = lds.id
						LEFT JOIN
					campaigns cmp ON cmp.id = clds.campaignId
						LEFT JOIN
					clients cli ON cli.id = cmp.clientId
			    WHERE {{whereClause}}
				GROUP BY lds.division, sks.name , sbsks.name WITH ROLLUP
        	`
		}, {
			worksheetName: 'By Refusal Code',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    skills.name as 'Reporting Group',
				    callresults.refusalReason AS 'Refusal Code',
				    COUNT(*) AS '#'
				FROM
				    callresults
				        LEFT JOIN
				    campaigns cmp ON cmp.id = callresults.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = callresults.clientId
				        LEFT JOIN
				    leads ON leads.id = callresults.leadId
				        LEFT JOIN
				    skills ON leads.tfSkillId = skills.id
				WHERE
				    callresults.refusalReason IS NOT NULL
				AND 
				{{whereClause}}
				GROUP BY callresults.refusalReason , skills.name
				ORDER BY skills.name
			`
		}, {
			worksheetName: 'Campaign Summary - Goals',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$', COALESCE(cmp.goal, 0)) AS 'Goal',
				    CONCAT('$',
				            COALESCE((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition'),
				                    0)) AS 'Acquistion Goal',
				    CONCAT('$',
				            COALESCE((cmp.goal - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)),
				                    0)) AS '$ to Goal',
				    CONCAT('$',
				            COALESCE(((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition')),
				                    0)) AS '$ to Acq Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id) / cmp.goal) * 100),
				            '%') AS '% to Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition') / (SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition')) * 100),
				            '%') AS '% to Acq Goal',
				    CONCAT(COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0)),
				            0), ' / ', ROUND((SELECT 
				            SUM(cmpproj.projectedQuantity)
				        FROM
				            campaignprojections cmpproj
				        WHERE
				            cmpproj.campaignId = cmp.id))) AS 'Volume Goal',
				    cmp.endDate AS 'End Date',
				    ROUND(DATEDIFF(cmp.endDate, NOW()) / 7) AS 'Weeks Remaining',
				    CONCAT('$',
				            ROUND((cmp.goal - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed',
				    CONCAT('$',
				            ROUND((((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.giftAmount)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition'))) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed Acq'
				FROM
				    leads lds
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
			`
		}, {
			worksheetName: 'Collections & Costs',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$',
				            ROUND((SELECT 
				                            SUM(inv.grandTotal - inv.amountRemaining)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id))) AS 'Total Collected',
				    CONCAT(ROUND(((SELECT 
				                            SUM(inv.grandTotal - inv.amountRemaining)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id) / (SELECT 
				                            SUM(inv.grandTotal)
				                        FROM
				                            invoices inv
				                        WHERE
				                            inv.campaignId = cmp.id)) * 100),
				            '%') AS 'Collection Rate',
				    COALESCE((SELECT 
				                    SUM(cc.hours)
				                FROM
				                    clientcostings cc
				                WHERE
				                    cc.campaignId = cmp.id),
				            0) AS 'Total Hours',
				    CONCAT('$',
				            ROUND(((SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id) / COALESCE((SELECT 
				                            SUM(cc.hours)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    1)))) AS 'Dollars per Hour',
				    CONCAT('$',
				            COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0)) AS 'Total Cost',
				    CONCAT(ROUND((COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0) / (SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id)) * 100), '%') AS 'Cost per Dollar',
				    CONCAT('$',
				            ((SELECT 
				                    SUM(inv.grandTotal)
				                FROM
				                    invoices inv
				                WHERE
				                    inv.campaignId = cmp.id) - COALESCE((SELECT 
				                            SUM(cc.cost)
				                        FROM
				                            clientcostings cc
				                        WHERE
				                            cc.campaignId = cmp.id),
				                    0))) AS 'Net Gain'
				FROM
				    campaigns cmp
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
			`
		}, {
			worksheetName: '2nd Appeal Summary',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$', COALESCE(SUM(cr.giftAmount), 0)) AS 'Total 2nd Appeal $',
				    CONCAT('$',
				            COALESCE(SUM(CASE
				                        WHEN cr.subSkill = '2nd Appeal Non-Telefunding' THEN cr.giftAmount
				                        ELSE 0
				                    END),
				                    0)) AS 'Non TF 2nd Appeal $',
				    CONCAT('$',
				            COALESCE(SUM(CASE
				                        WHEN cr.subSkill <> '2nd Appeal Non-Telefunding' THEN cr.giftAmount
				                        ELSE 0
				                    END),
				                    0)) AS 'TF 2nd Appeal $',
				    COALESCE(SUM((CASE
				                WHEN cr.giftAmount > 0 THEN 1
				                ELSE 0
				            END)),
				            0) AS '2nd Appeal Total # Gifts',
				    CONCAT('$',
				            COALESCE(ROUND(AVG(NULLIF(cr.giftAmount, 0))), 0)) AS '2nd Appeal Avg Gift',
				    CONCAT(ROUND(COALESCE((SUM(CASE
				                                WHEN
				                                    cr.giftAmount > 0
				                                        AND cr.subSkill <> '2nd Appeal Non-Telefunding'
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / COUNT(*)) * 100,
				                            0)),
				            '%') AS 'Donor Reponse Rate: TF 2nd Appeal',
				    CONCAT(ROUND(COALESCE((SUM(CASE
				                                WHEN
				                                    cr.giftAmount > 0
				                                        AND cr.subSkill = '2nd Appeal Non-Telefunding'
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / COUNT(*)) * 100,
				                            0)),
				            '%') AS 'Donor Reponse Rate: Non-TF 2nd Appeal'
				FROM
				    callresults cr
				        LEFT JOIN
				    campaigns cmp ON cmp.id = cr.campaignId
				        LEFT JOIN
				    campaignstages cs ON cs.id = cr.campaignstageId
				        LEFT JOIN
				    clients cli ON cli.id = cr.clientId
				WHERE
				    (cs.name = '2nd Appeal' OR cr.subSkill = '2nd Appeal Non-Telefunding') AND
				    {{whereClause}}
			`
		}, {
			worksheetName: 'Executive Summary',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    sks.name AS 'Reporting Group',
				    COALESCE(
						(SELECT SUM(campaigngoals.projectedQuantity) FROM campaigngoals WHERE
							campaigngoals.reportingGroup = sks.name AND
				            campaigngoals.campaignId = cmp.id)
				            , 0) -
						COUNT(lds.id) AS 'Qty Variance',
				    CONCAT('$',
						ROUND(
							COALESCE(
								(SELECT SUM(goal) FROM campaigngoals WHERE
									campaigngoals.reportingGroup = sks.name AND
									campaigngoals.campaignId = cmp.id)
							, 0)
							-
							COALESCE(
								NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
								NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0)
							,0)
						)
					) AS 'Value Variance',
				    COALESCE(CONCAT(ROUND(SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            callresults
				                                        WHERE
				                                            callresults.leadId = lds.id
				                                                AND callresults.campaignId = cmp.id
				                                                AND callresults.grandTotal > 0
				                                        LIMIT 1) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / (COUNT(lds.id) - SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            campaignleads cmp_lds
				                                                LEFT JOIN
				                                            campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                        WHERE
				                                            so_cstg.name = '1st Appeal'
				                                                AND cmp_lds.leadId = lds.id) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END)) * 100),
				                    '%'),
				            '0%') AS 'Response Rate on Closed',
				    CONCAT('$', ROUND(COALESCE(AVG((SELECT 
				                    AVG(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0)),
				            0))) AS 'Average Gift $',
				    COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0
				                LIMIT 1)),
				            0) AS 'Donor Count',
				    CONCAT('$', ROUND(COALESCE(SUM((SELECT 
				                    SUM(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0)),
				            0))) AS 'Total $',
				    COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount >= 500)),
				            0) AS 'Large Gifts ($500+) #',
				    CONCAT('$', ROUND(COALESCE(SUM((SELECT 
				                    SUM(callresults.giftAmount)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount >= 500)),
				            0))) AS 'Large Gifts ($500+) $'
				FROM
				    leads lds
				        LEFT JOIN
				    skills sks ON lds.tfSkillId = sks.id
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
				GROUP BY sks.name
				WITH ROLLUP
			`
		}])
	})

	Models.Report.create({
		name: 'Telesales Cumulative',
		folder: 'Campaign Reports',
		isSystem: true,
		definition: JSON.stringify([{
			worksheetName: 'Campaign Summary',
			rollupField: 'Reporting Group',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    sks.name AS 'Reporting Group',
				    sbsks.name AS 'Lead Type',
				    CONCAT('$',
				            ROUND(SUM(COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
				                            NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
				                            NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
				                            NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
				                            NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
				                            0)))) AS 'Gross Leads $',
				    COUNT(lds.id) AS 'Gross Leads #',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            (NULLIF(lds.phone_home, '') IS NULL
				                                AND NULLIF(lds.phone_work, '') IS NULL
				                                AND NULLIF(lds.phone_mobile, '') IS NULL
				                                AND NULLIF(lds.phone_workmobile, '') IS NULL)
				                        THEN
				                            COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
				                                    0)
				                        ELSE 0
				                    END))) AS 'Bad Numbers $',
				    SUM(CASE
				        WHEN
				            (NULLIF(lds.phone_home, '') IS NULL
				                AND NULLIF(lds.phone_work, '') IS NULL
				                AND NULLIF(lds.phone_mobile, '') IS NULL
				                AND NULLIF(lds.phone_workmobile, '') IS NULL)
				        THEN
				            1
				        ELSE 0
				    END) AS 'Bad Numbers #',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            ((SELECT 
				                                    COUNT(*)
				                                FROM
				                                    campaignleads cmp_lds
				                                        LEFT JOIN
				                                    campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                WHERE
				                                    so_cstg.name = '1st Appeal'
				                                        AND cmp_lds.leadId = lds.id) > 0)
				                        THEN
				                            COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
				                                    0)
				                        ELSE 0
				                    END))) AS 'Unresolved $',
				    SUM(CASE
				        WHEN
				            ((SELECT 
				                    COUNT(*)
				                FROM
				                    campaignleads cmp_lds
				                        LEFT JOIN
				                    campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                WHERE
				                    so_cstg.name = '1st Appeal'
				                        AND cmp_lds.leadId = lds.id) > 0)
				        THEN
				            1
				        ELSE 0
				    END) AS 'Unresolved #',
				    COALESCE(CONCAT(ROUND((COUNT(lds.id) - SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            campaignleads cmp_lds
				                                                LEFT JOIN
				                                            campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                        WHERE
				                                            so_cstg.name = '1st Appeal'
				                                                AND cmp_lds.leadId = lds.id) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END)) / (SUM(CASE
				                                WHEN
				                                    (NULLIF(lds.phone_home, '') IS NULL
				                                        AND NULLIF(lds.phone_work, '') IS NULL
				                                        AND NULLIF(lds.phone_mobile, '') IS NULL
				                                        AND NULLIF(lds.phone_workmobile, '') IS NULL)
				                                THEN
				                                    0
				                                ELSE 1
				                            END)) * 100),
				                    '%'),
				            '0%') AS 'Resolution Rate',
				    CONCAT(ROUND(SUM(CASE
				                        WHEN
				                            ((SELECT 
				                                    COUNT(*)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.grandTotal > 0
				                                LIMIT 1) > 0)
				                        THEN
				                            1
				                        ELSE 0
				                    END) / (COUNT(lds.id)) * 100),
				            '%') AS 'Response Rate on Gross',
				    COALESCE(CONCAT(ROUND(SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            callresults
				                                        WHERE
				                                            callresults.leadId = lds.id
				                                                AND callresults.campaignId = cmp.id
				                                                AND callresults.grandTotal > 0
				                                        LIMIT 1) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END) / (COUNT(lds.id) - SUM(CASE
				                                WHEN
				                                    ((SELECT 
				                                            COUNT(*)
				                                        FROM
				                                            campaignleads cmp_lds
				                                                LEFT JOIN
				                                            campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                        WHERE
				                                            so_cstg.name = '1st Appeal'
				                                                AND cmp_lds.leadId = lds.id) > 0)
				                                THEN
				                                    1
				                                ELSE 0
				                            END)) * 100),
				                    '%'),
				            '0%') AS 'Response Rate on Closed',
				    COALESCE(SUM((SELECT 
				                    1
				                FROM
				                    sales
				                WHERE
				                    sales.leadId = lds.id
				                        AND sales.campaignId = cmp.id
				                LIMIT 1)),
				            0) AS 'Sales',
				    COALESCE(SUM((SELECT 
				                    SUM(seatCount)
				                FROM
				                    sales
				                WHERE
				                    sales.leadId = lds.id
				                        AND sales.campaignId = cmp.id)),
				            0) AS 'Seats',
				    CONCAT('$',
				            ROUND(COALESCE(AVG((SELECT 
				                                    AVG(callresults.saleAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
														AND callresults.wrapup = 'Sale'
                                                        AND callresults.saleAmount > 0
				                                        AND callresults.campaignId = cmp.id)),
				                            0))) AS 'Avg Sale',
				    CONCAT('$',
				            ROUND(COALESCE(SUM((SELECT 
				                                    SUM(callresults.saleAmount)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id)),
				                            0))) AS 'Total Sales',
				    CONCAT('$',
				            ROUND(COALESCE(SUM((SELECT 
				                                    SUM(sales.subTotal)
				                                FROM
				                                    sales
				                                WHERE
				                                    sales.leadId = lds.id
				                                        AND sales.campaignId = cmp.id
				                                        AND sales.createdAt > CONCAT(DATE_SUB(DATE(NOW()),
				                                                INTERVAL DAYOFWEEK(NOW()) + 5 DAY),
				                                            ' 04:00:00')
				                                        AND sales.createdAt <= CONCAT(DATE_SUB(DATE(NOW()),
				                                                INTERVAL DAYOFWEEK(NOW()) - 2 DAY),
				                                            ' 03:59:59'))),
				                            0))) AS 'Last Week Sales',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            (SELECT 
				                                    SUM(callresults.grandTotal)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadid = lds.id
				                                        AND callresults.campaignId = cmp.id) > 0
				                        THEN
				                            lds.lyAmount
				                        ELSE 0
				                    END))) AS 'LY Amount',
				    COALESCE(CONCAT((ROUND((SUM((SELECT 
				                                    SUM(callresults.grandTotal)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.grandTotal > 0)) / ROUND(SUM(CASE
				                                        WHEN
				                                            (SELECT 
				                                                    SUM(callresults.grandTotal)
				                                                FROM
				                                                    callresults
				                                                WHERE
				                                                    callresults.leadid = lds.id
				                                                        AND callresults.campaignId = cmp.id) > 0
				                                        THEN
				                                            lds.lyAmount
				                                        ELSE 0
				                                    END))) * 100)),
				                    '%'),
				            '0%') AS 'Ren Sub Inc',
				    COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.giftAmount > 0
				                LIMIT 1)),
				            0) AS 'Donors',
				    CONCAT(COALESCE(ROUND(((COALESCE(SUM((SELECT 
				                                    COUNT(*)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.giftAmount > 0
				                                LIMIT 1)),
				                            0) / (COALESCE(SUM((SELECT 
				                                    1
				                                FROM
				                                    sales
				                                WHERE
				                                    sales.leadId = lds.id
				                                        AND sales.campaignId = cmp.id
				                                LIMIT 1)),
				                            0))) * 100)), 0),
				            '%') AS 'Add-on Gift %',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            ((SELECT 
				                                    COUNT(*)
				                                FROM
				                                    callresults
				                                WHERE
				                                    callresults.leadId = lds.id
				                                        AND callresults.campaignId = cmp.id
				                                        AND callresults.wrapup = 'Exception Refusal') > 0)
				                        THEN
				                            COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
				                                    0)
				                        ELSE 0
				                    END))) AS 'Refusal Exceptions $',
				    SUM(CASE
				        WHEN
				            ((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.wrapup = 'Exception Refusal') > 0)
				        THEN
				            1
				        ELSE 0
				    END) AS 'Refusal Exceptions #',
				    CONCAT('$',
				            ROUND(SUM(CASE
				                        WHEN
				                            ((SELECT 
				                                    COUNT(*)
				                                FROM
				                                    campaignleads cmp_lds
				                                        LEFT JOIN
				                                    campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                                WHERE
				                                    (so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
				                                        AND cmp_lds.leadId = lds.id) > 0)
				                        THEN
				                            COALESCE(NULLIF(CAST(lds.lyAmount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap1Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap2Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap3Amount AS UNSIGNED), 0),
				                                    NULLIF(CAST(lds.lap4Amount AS UNSIGNED), 0),
				                                    0)
				                        ELSE 0
				                    END))) AS 'Refusal - 1st No $',
				    SUM(CASE
				        WHEN
				            ((SELECT 
				                    COUNT(*)
				                FROM
				                    campaignleads cmp_lds
				                        LEFT JOIN
				                    campaignstages so_cstg ON cmp_lds.currentCampaignStageId = so_cstg.id
				                WHERE
				                    (so_cstg.name = '2nd Ask' OR so_cstg.name = 'Reapproach')
				                        AND cmp_lds.leadId = lds.id) > 0)
				        THEN
				            1
				        ELSE 0
				    END) AS 'Refusal - 1st No #',
				    CONCAT('$',
				            ROUND(COALESCE(SUM((SELECT 
		                            (GREATEST(SUM(callresults.giftAmount - COALESCE(NULLIF(CAST(leads.lyAmount AS UNSIGNED), 0),
		                                                CAST(leads.lap1Amount AS UNSIGNED),
		                                                0)),
		                                        0))
		                        FROM
		                            callresults
		                                LEFT JOIN
		                            leads ON leads.id = callresults.leadId
		                        WHERE
		                            callresults.leadId = lds.id)),
		                    0))) AS 'New $'
				FROM
				    leads lds
				        LEFT JOIN
				    skills sks ON lds.tmSkillId = sks.id
				        LEFT JOIN
				    subskills sbsks ON lds.tmSubSkillId = sbsks.id
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE {{whereClause}}
				GROUP BY sks.name , sbsks.name WITH ROLLUP
	        `
		}, {
			worksheetName: 'By Series',
			rollupField: 'Reporting Group',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    skills.name AS 'Reporting Group',
				    sales.series AS 'Series Name',
				    COALESCE(ROUND(SUM(sales.seatCount)), 0) AS '# Seats',
				    CONCAT('$',
				            COALESCE(ROUND(SUM(sales.subtotal)), 0)) AS 'Sales $',
				    CONCAT('$',
				            COALESCE(ROUND(AVG((sales.subtotal / sales.seatCount))),
				                    0)) AS 'Avg $ / Seat'
				FROM
				    sales
				        LEFT JOIN
				    leads ON sales.leadId = leads.id
				        LEFT JOIN
				    skills ON skills.id = leads.tmSkillId
				        LEFT JOIN
				    campaigns cmp ON cmp.id = sales.campaignid
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
				GROUP BY skills.name , sales.series WITH ROLLUP
			`
		}, {
			worksheetName: 'By Refusal',
			rollupField: 'Reporting Group',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
					skills.name as 'Reporting Group',
					callresults.refusalReason AS 'Refusal Code',
					COUNT(*) AS '#'
				FROM
					callresults
						LEFT JOIN
					campaigns cmp ON cmp.id = callresults.campaignId
						LEFT JOIN
					clients cli ON cli.id = callresults.clientId
						LEFT JOIN
					leads ON leads.id = callresults.leadId
						LEFT JOIN
					skills ON leads.tmSkillId = skills.id
				WHERE
					callresults.refusalReason IS NOT NULL
				AND 
					{{whereClause}}
				GROUP BY skills.name, callresults.refusalReason
				WITH ROLLUP
			`
		}, {
			worksheetName: 'Campaign Summary - Goals',
			availableFilters: [{
				tableName: 'campaigns',
				queryTableName: 'cmp',
				model: 'Campaign'
			}, {
				tableName: 'clients',
				queryTableName: 'cli',
				model: 'Client'
			}],
			rawQuery: `
				SELECT 
				    CONCAT('$', COALESCE(cmp.goal, 0)) AS 'Goal',
				    CONCAT('$',
				            ROUND(COALESCE((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition'),
				                    0))) AS 'Acquistion Goal',
				    CONCAT('$',
				            ROUND(COALESCE((cmp.goal - (SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)),
				                    0))) AS '$ to Goal',
				    CONCAT('$',
				            ROUND(COALESCE(((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition')),
				                    0))) AS '$ to Acq Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id) / cmp.goal) * 100),
				            '%') AS '% to Goal',
				    CONCAT(ROUND(((SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition') / (SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition')) * 100),
				            '%') AS '% to Acq Goal',
				    CONCAT(ROUND(COALESCE(SUM((SELECT 
				                    COUNT(*)
				                FROM
				                    callresults
				                WHERE
				                    callresults.leadId = lds.id
				                        AND callresults.campaignId = cmp.id
				                        AND callresults.grandTotal > 0)),
				            0)), ' / ', ROUND((SELECT 
				            SUM(cmpproj.projectedQuantity)
				        FROM
				            campaignprojections cmpproj
				        WHERE
				            cmpproj.campaignId = cmp.id))) AS 'Volume Goal',
				    cmp.endDate AS 'End Date',
				    ROUND(DATEDIFF(cmp.endDate, NOW()) / 7) AS 'Weeks Remaining',
				    CONCAT('$',
				            ROUND((cmp.goal - (SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id)) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed',
				    CONCAT('$',
				            ROUND((((SELECT 
				                            cg.goal
				                        FROM
				                            campaigngoals cg
				                        WHERE
				                            cg.campaignId = cmp.id
				                                AND cg.reportingGroup = 'Acquisition') - (SELECT 
				                            SUM(cr.grandTotal)
				                        FROM
				                            callresults cr
				                        WHERE
				                            cr.campaignId = cmp.id
				                                AND skill = 'Acquisition'))) / (DATEDIFF(cmp.endDate, NOW()) / 7))) AS '$/Wk Needed Acq'
				FROM
				    leads lds
				        LEFT JOIN
				    campaignleads clds ON clds.leadId = lds.id
				        LEFT JOIN
				    campaigns cmp ON cmp.id = clds.campaignId
				        LEFT JOIN
				    clients cli ON cli.id = cmp.clientId
				WHERE
				    {{whereClause}}
			`
		}])
	})

	Models.Report.create({
		name: 'TF Commission Report',
		folder: 'Agent Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				placeholder: '{{startDate}}',
				type: 'date',
				name: 'Start Date'
			}, {
				placeholder: '{{endDate}}',
				type: 'date',
				name: 'End Date'
			}],
			rawQuery: `SELECT 
					    cl.name AS 'Client',
					    ca.name AS 'Campaign',
					    cr.createdAt AS 'Pledge Date/Time',
					    l.id AS 'Kaos Id',
					    l.first_name AS 'First Name',
					    l.last_name AS 'Last Name',
					    cr.skill AS 'Reporting Group',
					    cr.subskill AS 'Lead Type',
					    cs.name 'Stage Name',
					    CONCAT('$',
					            COALESCE(NULLIF(l.lyAmount, 0),
					                    NULLIF(l.lap1Amount, 0),
					                    0)) AS 'LAP1 or LYAmount',
					    CONCAT('$', cr.giftAmount) AS 'Pledge Amount',
					    cr.paymentType AS 'Pay Type',
					    COALESCE(a.name, 'UNKNOWN') AS 'Agent Name',
					    CONCAT('$',
					            COALESCE(ROUND(IF(a.hourlyRate = 11,
					                                60,
					                                IF(a.hourlyRate = 13, 80, 100)) * (SELECT 
					                                    SUM(ROUND(cat.scheduledHours))
					                                FROM
					                                    campaignagenttargets cat
					                                WHERE
					                                    cat.agentId = a.id
					                                        AND cat.start <= cr.createdAt
					                                        AND cat.end >= cr.createdAt)),
					                    0)) AS 'Threshold',
					    CONCAT('$', COALESCE(ROUND((SELECT 
					                    SUM(IF(cat.overrideGoal > 0,
					                            cat.overrideGoal,
					                            cat.goal))
					                FROM
					                    campaignagenttargets cat
					                WHERE
					                    cat.agentId = a.id
					                        AND cat.start <= cr.createdAt
					                        AND cat.end >= cr.createdAt)), 0)) AS 'Goal',
					    CONCAT('$',
					            ROUND(COALESCE((SELECT 
					                                    SUM(giftAmount)
					                                FROM
					                                    callresults
					                                WHERE
					                                    agentId = cr.agentId AND giftAmount > 0
					                                        AND createdAt >= '{{startDate}}'
					                                        AND createdAt <= cr.createdAt),
					                            0))) AS 'Sales',
					    CONCAT(ROUND(COALESCE(((SELECT 
					                                    COUNT(*)
					                                FROM
					                                    callresults
					                                WHERE
					                                    agentId = cr.agentId AND giftAmount > 0
					                                        AND paymentType = 'Credit Card'
					                                        AND createdAt >= '{{startDate}}'
					                                        AND createdAt <= '{{endDate}}') / (SELECT 
					                                    COUNT(*)
					                                FROM
					                                    callresults
					                                WHERE
					                                    agentId = cr.agentid AND giftAmount > 0
					                                        AND createdAt >= '{{startDate}}'
					                                        AND createdAt <= '{{endDate}}') * 100),
					                            0)),
					            '%') AS 'CC Conv Rate %'
					FROM
					    callresults cr
					        LEFT JOIN
					    agents a ON cr.agentId = a.id
					        LEFT JOIN
					    campaigns ca ON cr.campaignid = ca.id
					        LEFT JOIN
					    clients cl ON cr.clientid = cl.id
					        LEFT JOIN
					    leads l ON cr.leadid = l.id
					    	LEFT JOIN
    					campaignstages cs ON cs.id = cr.campaignstageid
					WHERE
					    cr.createdAt >= '{{startDate}}'
					        AND cr.createdAt <= '{{endDate}}'
					        AND cr.giftAmount > 0
					        and cr.agentId is not null
					ORDER BY a.name, cr.createdAt`
		})
	})

	Models.Report.create({
		name: 'TM Commission Report',
		folder: 'Agent Reports',
		isSystem: true,
		definition: JSON.stringify({
			availableFilters: [{
				placeholder: '{{startDate}}',
				type: 'date',
				name: 'Start Date'
			}, {
				placeholder: '{{endDate}}',
				type: 'date',
				name: 'End Date'
			}],
			rawQuery: `SELECT 
					    cl.name AS 'Client Name',
					    ca.name AS 'Campaign Name',
					    cr.createdAt AS 'Sale Date/Time',
					    l.id AS 'Kaos Id',
					    l.first_name AS 'First Name',
					    l.last_name AS 'Last Name',
					    cr.skill AS 'TM Reporting Group',
					    cr.subskill AS 'TM Lead Type',
					    sks.name AS 'TF Reporting Group',
					    sbsks.name AS 'TF Lead Type',
					    cs.name 'Stage Name',
					    CONCAT('$',
					            COALESCE(NULLIF(l.lyAmount, 0),
					                    NULLIF(l.lap1Amount, 0),
					                    0)) AS 'LAP1 or LYAmount',
					    a.name AS 'Agent Name',
					    CONCAT('$',
					            ROUND(COALESCE(cr.grandTotal, 0), 2)) AS 'Grand Total',
					    CONCAT('$',
					            ROUND(COALESCE(cr.giftAmount, 0), 2)) AS 'AddOn Gift',
					    cr.creditCardNumber AS 'Credit Card #',
					    CONCAT('$',
					            COALESCE(ROUND(IF(a.hourlyRate = 11,
					                                60,
					                                IF(a.hourlyRate = 13, 80, 100)) * (SELECT 
					                                    SUM(ROUND(cat.scheduledHours))
					                                FROM
					                                    campaignagenttargets cat
					                                WHERE
					                                    cat.agentId = a.id
					                                        AND cat.start <= cr.createdAt
					                                        AND cat.end >= cr.createdAt)),
					                    0)) AS 'Threshold',
					    CONCAT('$',
					            COALESCE(ROUND((SELECT 
					                                    SUM(IF(cat.overrideGoal > 0,
					                                            cat.overrideGoal,
					                                            cat.goal))
					                                FROM
					                                    campaignagenttargets cat
					                                WHERE
					                                    cat.agentId = a.id
					                                        AND cat.start <= cr.createdAt
					                                        AND cat.end >= cr.createdAt)),
					                    0)) AS 'Goal',
					    CONCAT('$',
					            ROUND(COALESCE((SELECT 
					                                    SUM(grandTotal)
					                                FROM
					                                    callresults
					                                WHERE
					                                    agentId = cr.agentId
					                                        AND (saleAmount > 0
					                                        OR grandTotal > giftAmount)
					                                        AND createdAt >= '{{startDate}}'
					                                        AND createdAt <= cr.createdAt),
					                            0),
					                    2)) AS 'Sales'
					FROM
					    callresults cr
					        LEFT JOIN
					    leads l ON l.id = cr.leadid
					        LEFT JOIN
					    skills sks ON l.tfSkillId = sks.id
					        LEFT JOIN
					    subskills sbsks ON l.tfSubSkillId = sbsks.id
					        LEFT JOIN
					    agents a ON a.id = cr.agentid
					        LEFT JOIN
					    clients cl ON cl.id = cr.clientId
					        LEFT JOIN
					    campaigns ca ON ca.id = cr.campaignid
					        LEFT JOIN
					    campaignstages cs ON cs.id = cr.campaignstageid
					WHERE
					    cr.createdAt >= '{{startDate}}'
					        AND cr.createdAt <= '{{endDate}}'
					        AND (cr.saleAmount > 0
					        OR cr.grandTotal > cr.giftAmount)
					        AND cr.agentId IS NOT NULL
					ORDER BY a.name , cr.createdAt`
		})
	})
}