// Args
var rebuildDB = process.argv.indexOf('rebuilddb') > -1
var hostedDB = process.argv.indexOf('hosteddb') > -1

// Require dependencies
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: (hostedDB ? 'zenozi.cem9amrtu9pp.us-west-2.rds.amazonaws.com' : 'localhost')
	})
var Models = require('../database/schema')(sequelize, Sequelize)

// Sync DB Model
sequelize.sync({
	force: rebuildDB
})
.then(function (err) {
	console.log('DB sync for the win!')

	var initialReports = require('../database/initial_reports')

	setTimeout(function () {
		Models.Report.destroy({
			where: {
				isSystem: true
			}
		}).then(function () {
			initialReports(Models)
		})
	}, 1000)
})