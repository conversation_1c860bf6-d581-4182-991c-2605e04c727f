module.exports = function (Models) {

	// AgentState
	Models.AgentState.create({
		name: 'Idle',
		color: '#ffffff',
		outbound: false,
		system: true,
		isChargeable: false
	})

	Models.AgentState.create({
		name: 'Active',
		color: '#1dad23',
		outbound: true,
		isChargeable: true
	})

	Models.AgentState.create({
		name: 'Training',
		color: '#131acf',
		outbound: false,
		isChargeable: true
	})
	

	// Disposition
	Models.Disposition.create({
		name: 'Callback',
		system: true,
		controller: 'CallbackDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.callback.modal.html'
	})

	Models.Disposition.create({
		name: 'Pledge Credit Card',
		system: true,
		exhaustLead: true,
		controller: 'PledgeDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.pledge.modal.html',
		size: 'lg'
	})

	Models.Disposition.create({
		name: 'Pledge Invoice',
		system: true,
		exhaustLead: true,
		controller: 'PledgeDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.pledge.modal.html',
		size: 'lg'
	})

	Models.Disposition.create({
		name: 'Standard Refusal',
		system: true,
		exhaustLead: true,
		controller: 'RefusalDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.refusal.modal.html'
	})

	Models.Disposition.create({
		name: 'Exception Refusal',
		system: true,
		exhaustLead: true,
		controller: 'RefusalDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.refusal.modal.html'
	})

	Models.Disposition.create({
		name: 'Collections Standard Refusal',
		system: true,
		exhaustLead: true,
		controller: 'CollectionRefusalDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.refusal.modal.html'
	})

	Models.Disposition.create({
		name: 'Collections Exception Refusal',
		system: true,
		exhaustLead: true,
		controller: 'CollectionRefusalDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.refusal.modal.html'
	})

	Models.Disposition.create({
		name: 'Sale',
		system: true,
		exhaustLead: true,
		controller: 'SaleDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.sale.modal.html',
		size: 'lg'
	})

	Models.Disposition.create({
		name: 'Invoice Requested',
		system: true,
		exhaustLead: false,
		controller: 'InvoiceRequestedDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.requestinvoice.modal.html'
	})

	Models.Disposition.create({
		name: 'Invoice Payment',
		system: true,
		exhaustLead: false,
		controller: 'InvoicePaymentDispositionModalCtrl',
		templateUrl: 'views/agent/dispositions/agent.disposition.payinvoice.modal.html',
		size: 'lg'
	})

	Models.Disposition.create({
		name: 'No Resolution',
		system: true
	})

	Models.Disposition.create({
		name: 'Completed Thank You',
		system: true
	})



	// DateTimeRuleSet
	Models.DateTimeRuleSet.create({
		name: 'Anytime',
		description: 'All day, every day',
		startTime: '00:00:00',
		endTime: '23:59:59',
		monday: true,
		tuesday: true,
		wednesday: true,
		thursday: true,
		friday: true,
		saturday: true,
		sunday: true
	})

	Models.DateTimeRuleSet.create({
		name: 'Mon-Thur Morning',
		description: 'Monday to Thursday Morning',
		startTime: '09:00:00',
		endTime: '12:00:00',
		monday: true,
		tuesday: true,
		wednesday: true,
		thursday: true,
		friday: false,
		saturday: false,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Mon-Thur Afternoon',
		description: 'Monday to Thursday Afternoon',
		startTime: '12:00:00',
		endTime: '17:00:00',
		monday: true,
		tuesday: true,
		wednesday: true,
		thursday: true,
		friday: false,
		saturday: false,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Mon-Thur Evening',
		description: 'Monday to Thursday Evening',
		startTime: '17:00:00',
		endTime: '21:00:00',
		monday: true,
		tuesday: true,
		wednesday: true,
		thursday: true,
		friday: false,
		saturday: false,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Fri Morning',
		description: 'Friday Morning',
		startTime: '09:00:00',
		endTime: '12:00:00',
		monday: false,
		tuesday: false,
		wednesday: false,
		thursday: false,
		friday: true,
		saturday: false,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Fri Afternoon',
		description: 'Friday Afternoon',
		startTime: '12:00:00',
		endTime: '17:00:00',
		monday: false,
		tuesday: false,
		wednesday: false,
		thursday: false,
		friday: true,
		saturday: false,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Sat Morning',
		description: 'Saturday Morning',
		startTime: '09:00:00',
		endTime: '13:00:00',
		monday: false,
		tuesday: false,
		wednesday: false,
		thursday: false,
		friday: false,
		saturday: true,
		sunday: false
	})

	Models.DateTimeRuleSet.create({
		name: 'Sun Evening',
		description: 'Sunday Evening',
		startTime: '15:00:00',
		endTime: '21:00:00',
		monday: false,
		tuesday: false,
		wednesday: false,
		thursday: false,
		friday: false,
		saturday: false,
		sunday: true
	})



	// CallResultFieldGroup
	Models.CallResultFieldGroup.create({
		name: 'General'
	})



	// CallResultFieldType
	Models.CallResultFieldType.create({
		name: 'Text - Short',
		templateUrl: 'views/callresultfieldtemplates/textinput.html',
		showRegex: true,
		showPlaceholder: true,
		multipleOptions: false,
		multipleResults: false
	})

	Models.CallResultFieldType.create({
		name: 'Text - Long',
		templateUrl: 'views/callresultfieldtemplates/textareainput.html',
		showRegex: true,
		showPlaceholder: true,
		multipleOptions: false,
		multipleResults: false
	})

	Models.CallResultFieldType.create({
		name: 'Number',
		templateUrl: 'views/callresultfieldtemplates/numberinput.html',
		showRegex: true,
		showPlaceholder: true,
		multipleOptions: false,
		multipleResults: false
	})

	Models.CallResultFieldType.create({
		name: 'Email',
		templateUrl: 'views/callresultfieldtemplates/emailinput.html',
		showRegex: true,
		showPlaceholder: true,
		multipleOptions: false,
		multipleResults: false
	})

	Models.CallResultFieldType.create({
		name: 'Select List',
		templateUrl: 'views/callresultfieldtemplates/selectinput.html',
		showRegex: false,
		showPlaceholder: false,
		multipleOptions: true,
		multipleResults: false
	})

	Models.CallResultFieldType.create({
		name: 'Checkbox List',
		templateUrl: 'views/callresultfieldtemplates/checkboxlist.html',
		showRegex: false,
		showPlaceholder: false,
		multipleOptions: true,
		multipleResults: true
	})

	Models.CallResultFieldType.create({
		name: 'Radio List',
		templateUrl: 'views/callresultfieldtemplates/radiolist.html',
		showRegex: false,
		showPlaceholder: false,
		multipleOptions: true,
		multipleResults: false
	})

	//Users
	Models.User.create({
		name: 'Administrator',
		password: '8e0539f0',
		username: 'admin',
		showStats: true,
		homeState: 'supervisor.campaigns',
		isAdmin: true,
		isSupervisor: true,
		isAgent: false,
		firstLogin: false,
		lastPasswordUpdate: new Date()
	})

	Models.User.create({
		name: 'Supervisor',
		password: '8e0539f0',
		username: 'supervisor',
		showStats: true,
		homeState: 'supervisor.campaigns',
		isAdmin: false,
		isSupervisor: true,
		isAgent: false,
		firstLogin: false,
		lastPasswordUpdate: new Date()
	})

	//Campaign Types
	Models.CampaignType.create({
		name: 'Telefunding'
	})

	Models.CampaignType.create({
		name: 'Telesales'
	})


	//Skills
	Models.Skill.create({
		name: 'Acquisition',
		description: 'Default skill'
	})

	Models.Skill.create({
		name: 'Lapsed',
		description: 'Default skill'
	})

	Models.Skill.create({
		name: 'Renewal',
		description: 'Default skill'
	})

	//Subskills
	Models.SubSkill.create({
		name: 'Lapsed 1',
		description: 'Default subskill',
		priority: 5
	})

	Models.SubSkill.create({
		name: 'Lapsed 2-5',
		description: 'Default subskill',
		priority: 7
	})

	Models.SubSkill.create({
		name: 'Multi STB',
		description: 'Default subskill',
		priority: 8
	})

	Models.SubSkill.create({
		name: 'Renewal 1',
		description: 'Default subskill',
		priority: 4
	})

	Models.SubSkill.create({
		name: 'Renewal 2',
		description: 'Default subskill',
		priority: 3
	})

	Models.SubSkill.create({
		name: 'SOTP 15',
		description: 'Default subskill',
		priority: 99
	})

	Models.SubSkill.create({
		name: 'STB 14',
		description: 'Default subskill',
		priority: 99
	})

	Models.SubSkill.create({
		name: 'STB 15',
		description: 'Default subskill',
		priority: 99
	})

	Models.SubSkill.create({
		name: 'STB 16',
		description: 'Default subskill',
		priority: 99
	})

	Models.SubSkill.create({
		name: 'Sub 13 14',
		description: 'Default subskill',
		priority: 99
	})

	Models.SubSkill.create({
		name: 'Sub 15',
		description: 'Default subskill',
		priority: 99
	})
	
	Models.SubSkill.create({
		name: 'Sub 16',
		description: 'Default subskill',
		priority: 99
	})

	//Devices
	Models.Device.create({
		type: 'MockPhone',
		extension: '100',
		name: 'E100',
		recordCalls: true
	})

	Models.Device.create({
		type: 'MockPhone',
		extension: '101',
		name: 'E101',
		recordCalls: true
	})

	Models.Device.create({
		type: 'MockPhone',
		extension: '102',
		name: 'E102',
		recordCalls: true
	})

	Models.Device.create({
		type: 'MockPhone',
		extension: '103',
		name: 'E103',
		recordCalls: true
	})

	Models.Device.create({
		type: 'MockPhone',
		extension: '104',
		name: 'E104',
		recordCalls: true
	})

	Models.RefusalReason.create({
		name: 'NO - Straight no',
		exception: false,
		telefunding: true,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'NO - Hang up',
		exception: false,
		telefunding: true,
		telemarketing: false
	})

	Models.RefusalReason.create({
		name: 'NO - wants to give later in the year',
		exception: false,
		telefunding: true,
		telemarketing: false
	})

	Models.RefusalReason.create({
		name: 'Deceased - all parties on record',
		exception: true,
		telefunding: true,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Do not call again',
		exception: true,
		telefunding: true,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Moved out of area',
		exception: true,
		telefunding: true,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'NO - Age/Health',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Art (Other Groups)',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'No Money',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Parking/Access',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Programming (Artistic)',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'Smaller Package Only',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'STB Only',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'NO - Time',
		exception: true,
		telefunding: false,
		telemarketing: true
	})

	Models.RefusalReason.create({
		name: 'NO - Venue',
		exception: true,
		telefunding: false,
		telemarketing: true
	})
}