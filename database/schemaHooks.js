var moment = require('moment')

module.exports = function (Models) {
    // Hooks

    Models.Lead.afterUpdate((lead, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = lead._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = lead.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.leadupdate.create({
                            leadId: lead.dataValues.id,
                            type: 'Field Change',
                            data: f,
                            from: from,
                            to: to
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignLead.afterUpdate(function (instance, options) {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignleadupdate.create({
                            leadId: instance.dataValues.leadId,
                            campaignId: instance.dataValues.campaignId,
                            data: f,
                            from: from,
                            to: to
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.Campaign.afterUpdate(function (instance, options) {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignupdate.create({
                            campaignId: instance.dataValues.id,
                            field: f,
                            from: from,
                            to: to
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignStage.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignupdate.create({
                            campaignId: instance.dataValues.campaignId,
                            field: f,
                            from: from,
                            to: to,
                            detail: JSON.stringify({
                                type: 'CampaignStage',
                                id: instance.dataValues.id,
                                name: instance.dataValues.name
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignStageDisposition.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignstage.findById(instance.dataValues.campaignstageId).then(stage => {
                            options.model.sequelize.models.disposition.findById(instance.dataValues.dispositionId).then(disposition => {
                                options.model.sequelize.models.campaignupdate.create({
                                    campaignId: stage.campaignId,
                                    field: f,
                                    from: from,
                                    to: to,
                                    detail: JSON.stringify({
                                        type: 'CampaignStageDisposition',
                                        id: stage.id,
                                        name: stage.name,
                                        wrapup: disposition.name
                                    })
                                })
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignStageAgent.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignstage.findById(instance.dataValues.campaignstageId).then(stage => {
                            options.model.sequelize.models.agent.findById(instance.dataValues.agentId).then(agent => {
                                options.model.sequelize.models.campaignupdate.create({
                                    campaignId: stage.campaignId,
                                    field: f,
                                    from: from,
                                    to: to,
                                    detail: JSON.stringify({
                                        type: 'CampaignStageAgent',
                                        id: stage.id,
                                        name: stage.name,
                                        agent: agent.name
                                    })
                                })
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignProjections.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.subskill.findById(instance.dataValues.subskillId).then(skill => {
                            options.model.sequelize.models.campaignupdate.create({
                                campaignId: instance.dataValues.campaignId,
                                field: f,
                                from: from,
                                to: to,
                                detail: JSON.stringify({
                                    type: 'CampaignProjection',
                                    leadType: skill.name
                                })
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignAgentTarget.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.agent.findById(instance.dataValues.agentId).then(agent => {
                            options.model.sequelize.models.campaignupdate.create({
                                campaignId: instance.dataValues.campaignId,
                                field: f,
                                from: from,
                                to: to,
                                detail: JSON.stringify({
                                    type: 'CampaignAgentTarget',
                                    agent: agent.name,
                                    week: moment(instance.dataValues.start).format('YYYY-MM-DD')
                                })
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })

    Models.CampaignStageDateTimeRule.afterUpdate((instance, options) => {
        try {
            options.fields.forEach(f => {
                if (f !== 'updatedAt') {
                    var from = instance._previousDataValues[f]
                    if (from && typeof from === 'object' && typeof from.getMonth === 'function') {
                        from = moment(from).format('YYYY-MM-DD HH:mm:ss')
                    }
                    var to = instance.dataValues[f]
                    if (to && typeof to === 'object' && typeof to.getMonth === 'function') {
                        to = moment(to).format('YYYY-MM-DD HH:mm:ss')
                    }
                    if (from != to) {
                        options.model.sequelize.models.campaignstage.findById(instance.dataValues.campaignstageId).then(stage => {
                            options.model.sequelize.models.subskill.findById(instance.dataValues.subskillId).then(skill => {
                                options.model.sequelize.models.datetimeruleset.findById(instance.dataValues.datetimerulesetId).then(dtRule => {
                                    options.model.sequelize.models.campaignupdate.create({
                                        campaignId: stage.campaignId,
                                        field: f,
                                        from: from,
                                        to: to,
                                        detail: JSON.stringify({
                                            type: 'CampaignStageDateTimeRule',
                                            leadType: skill.name,
                                            rule: dtRule.name,
                                            stage: stage.name
                                        })
                                    })
                                })
                            })
                        }).catch(() => { })
                    }
                }
            })
        } catch (err) { }
    })
}