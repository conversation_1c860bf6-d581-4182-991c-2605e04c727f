var Promise = require('bluebird')
var redis = require('redis')
var _ = require('underscore')
Promise.promisifyAll(redis.RedisClient.prototype)
Promise.promisifyAll(redis.Multi.prototype)
var APP_SETTINGS = require('../config/constants')
var client = redis.createClient(APP_SETTINGS.REDIS)
if (APP_SETTINGS.REDIS.auth) client.auth(APP_SETTINGS.REDIS.auth)

module.exports = function (type) {
	var api = {
		get: function (id, stat) {
			return new Promise(function (resolve, reject) {
				client.getAsync(type + '-' + id + (stat ? '-stat-' + stat : ''))
					.then(function (result) {
						if (result) {
							resolve(JSON.parse(result))
						} else {
							resolve()
						}
					})
					.catch(reject)
			})
		},
		createStat: function (id, stat) {
			return new Promise(function (resolve, reject) {
				client.setAsync(type + '-' + id + '-stat-' + stat, 0)
					.then(function (result) {
						if (result) {
							resolve(true)
						} else {
							resolve(false)
						}
					})
					.catch(reject)
			})
		},
		increment: function (id, stat) {
			return new Promise(function (resolve, reject) {
				client.incrAsync(type + '-' + id + '-stat-' + stat)
					.then(resolve)
					.catch(reject)
			})
		},
		incrementBy: function (id, stat, value) {
			return new Promise(function (resolve, reject) {
				if (value === undefined || value === null) return reject('value cannot be null or undefined')
				var val = parseInt(value)
				if (!val) return reject('value must be an integer')

				client.incrbyAsync(type + '-' + id + '-stat-' + stat, val)
					.then(resolve)
					.catch(reject)
			})
		},
		resetStat: function (id, stat) {
			return new Promise(function (resolve, reject) {
				client.setAsync(type + '-' + id + '-stat-' + stat, 0)
					.then(function (result) {
						if (result) {
							resolve(true)
						} else {
							resolve(false)
						}
					})
					.catch(reject)
			})
		},
		setStat: function (id, stat, value) {
			return new Promise(function (resolve, reject) {
				client.setAsync(type + '-' + id + '-stat-' + stat, value)
					.then(function (result) {
						if (result) {
							resolve(true)
						} else {
							resolve(false)
						}
					})
					.catch(reject)
			})
		},
		save: function (object, stat) {
			return new Promise(function (resolve, reject) {
				client.setAsync(type + '-' + object.id + (stat ? '-stat-' + stat : ''), JSON.stringify(object))
					.then(function (result) {
						if (result && result === "OK")
							resolve(true)
						else
							resolve(false)
					})
					.catch(reject)
			})
		},
		getAll: function () {
			return new Promise(function (resolve, reject) {
				//KEYS is dangerous on large datasets, should change the SCAN which allows paging of the results
				//SCAN will return a result which is the index of where you should next scan from and the results
				client.keysAsync(type + '-*')
					.then(function (replies, err) {
						if (replies && replies.length) {
							client.mgetAsync(replies)
								.then(function (result) {
									var objects = [];
									for (var i = 0; i < result.length; i++) {
										objects.push(JSON.parse(result[i]))
									};
									resolve(objects)
								})
						} else {
							resolve([])
						}
					})
					.catch(reject)
			})
		},
		getAllStatsForObject: function (id) {
			return new Promise(function (resolve, reject) {
				client.keysAsync(type + '-' + id + '-*')
					.then(function (replies, err) {
						if (replies && replies.length) {
							var obj = {};
							for (var i = 0; i < replies.length; i++) {
								obj[replies[i]] = ''
							}
							client.mgetAsync(replies)
								.then(function (result) {
									for (var i = 0; i < result.length; i++) {
										obj[replies[i]] = JSON.parse(result[i])
									};
									resolve(obj)
								})
						} else {
							resolve([])
						}
					})
					.catch(reject)
			})
		},
		extend: function (id, newObject, stat) {
			return new Promise(function (resolve, reject) {
				var key = type + '-' + id + (stat ? '-stat-' + stat : '')
				client.watch(key)
				client.get(key, function (err, data) {
					var multi = client.multi()
					data = _.extend(JSON.parse(data), newObject)
					multi.set(key, JSON.stringify(data))
					multi.exec(function (err, replies) {
						resolve(data)
					})
				})
			})
		},
		delete: function (id, stat) {
			return new Promise(function (resolve, reject) {
				client.delAsync(type + '-' + id + (stat ? '-stat-' + stat : ''))
					.then(function (result) {
						if (result) {
							resolve(true)
						} else {
							resolve(false)
						}
					})
					.catch(reject)
			})
		}
	}

	return api
}