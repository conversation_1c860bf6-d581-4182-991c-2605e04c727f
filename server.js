// App constants
var BASE_URL = '/api/v0'

// Args
var rebuildDB = process.argv.indexOf('rebuilddb') > -1
var rebuildReports = process.argv.indexOf('rebuildreports') > 1

// keymetrics.io logging - must go before any http modules
var pmx = require('pmx')
pmx.init({
    http: true, // HTTP routes logging (default: true)
    ignore_routes: [/socket\.io/, /notFound/], // Ignore http routes with this pattern (Default: [])
    errors: true, // Exceptions loggin (default: true)
    custom_probes: true, // Auto expose JS Loop Latency and HTTP req/s as custom metrics
    network: true, // Network monitoring at the application level
    ports: true // Shows which ports your app is listening on (default: false)
})

// Other dependencies
var blocked = require('blocked')
var express = require('express')
var hostValidation = require('./middleware/host-validation')
var compression = require('compression')
var multer = require('multer')
var logger = require('morgan')
var helmet = require('helmet')
var app = express()
var fs = require('fs')
var https = require('https')
var passport = require('passport')
var methodOverride = require('method-override')
var bodyParser = require('body-parser')
var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(sequelize, Sequelize)
var tsys = require('./utils/tsys')

var encryption = require('./utils/encryption')()
var hostValidationConfig = { hosts: ['127.0.0.1:3000',
                                 'localhost:3000',
                                 'localhost:9000',
                                 'localhost:8000',
                                 'kaos.blueprintadvancement.com'] }

// Configure Express
app.use(methodOverride('X-HTTP-Method-Override'))
app.use(multer())
app.use(helmet())
app.use(logger('dev'))
app.use(bodyParser.json({
    limit: '50mb'
}))
app.use(bodyParser.urlencoded({
    limit: '50mb',
    extended: true
}))
app.use(hostValidation(hostValidationConfig))

//* TODO: REMOVE FOR PRODUCTION *//
app.set('view cache', false)

app.all('/*', function(req, res, next) {
    res.header('Access-Control-Allow-Origin', '*')
    res.header('Access-Control-Allow-Methods', 'PUT, GET, POST, DELETE, OPTIONS')
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, X-Access-Token, X-Key')

    if (req.method == 'OPTIONS')
        res.status(200).end()
    else
        next()
})

// Login for authenticated routes
app.post(BASE_URL + '/login', require('./controllers/auth')(Models).login)
app.all('/api/*', [require('./middleware/validateRequest')(Models).validate])

app.use(compression())
app.use(express.static('public'))

// Sync DB Model
sequelize.sync({ force: rebuildDB })
    .then(function(err) {
        console.log('DB sync for the win!')

        if (rebuildDB) {
            require('./database/initial_data')(Models)
            require('./database/initial_reports')(Models)

            var redis = require('redis')
            var client = redis.createClient({ host: APP_SETTINGS.REDIS.host })
            if (APP_SETTINGS.REDIS.auth) client.auth(APP_SETTINGS.REDIS.auth)
            client.flushdb()
        } else if (rebuildReports) {
            Models.Report.destroy({ where: { isSystem: true } }).then(function() {
                require('./database/initial_reports')(Models)
            })
        }
    })

app.get(BASE_URL + '/manifest', (req, res) => {
    tsys.manifest(req.query.clientId, Models).then(result => {
        res.status(200).send(result)
    })

})

// ** RESTful routing for resources
require('./controllers/client')(app, Models, BASE_URL)
require('./controllers/campaign')(app, Models, BASE_URL, sequelize)
require('./controllers/campaignproduct')(app, Models, BASE_URL)
require('./controllers/campaignstage')(app, Models, BASE_URL, sequelize)
require('./controllers/campaignstagedatetimerule')(app, Models, BASE_URL)
require('./controllers/broadcastmessage')(app, Models, BASE_URL)
require('./controllers/campaigntrainingdoc')(app, Models, BASE_URL)
require('./controllers/agent')(app, Models, BASE_URL, sequelize)
require('./controllers/agentstate')(app, Models, BASE_URL)
require('./controllers/callresult')(app, Models, BASE_URL)
require('./controllers/callback')(app, Models, BASE_URL)
require('./controllers/lead')(app, Models, BASE_URL)
require('./controllers/skill')(app, Models, BASE_URL)
require('./controllers/subskill')(app, Models, BASE_URL)
require('./controllers/campaigntype')(app, Models, BASE_URL)
require('./controllers/datetimeruleset')(app, Models, BASE_URL)
require('./controllers/leadfield')(app, Models, BASE_URL)
require('./controllers/leadfieldvalue')(app, Models, BASE_URL)
require('./controllers/disposition')(app, Models, BASE_URL)
require('./controllers/callresultfield')(app, Models, BASE_URL)
require('./controllers/callresultfieldoption')(app, Models, BASE_URL)
require('./controllers/callresultfieldtype')(app, Models, BASE_URL)
require('./controllers/callresultfieldgroup')(app, Models, BASE_URL)
require('./controllers/callresultfieldvalue')(app, Models, BASE_URL)
require('./controllers/agentsession')(app, Models, BASE_URL, sequelize)
require('./controllers/callrecord')(app, Models, BASE_URL)
require('./controllers/leadaudit')(app, Models, BASE_URL)
require('./controllers/user')(app, Models, BASE_URL)
require('./controllers/agentevent')(app, Models, BASE_URL)
require('./controllers/sale')(app, Models, BASE_URL)
require('./controllers/report')(app, Models, BASE_URL)
require('./controllers/reportschedule')(app, Models, BASE_URL)
require('./controllers/reporthistory')(app, Models, BASE_URL, sequelize)
require('./controllers/reporthistoryaudit')(app, Models, BASE_URL)
require('./controllers/reportmodules')(app, Models, BASE_URL)
require('./controllers/campaignprojections')(app, Models, BASE_URL)
require('./controllers/system')(app, Models, BASE_URL)
require('./controllers/device')(app, Models, BASE_URL)
require('./controllers/invoice')(app, Models, BASE_URL)
require('./controllers/invoicehistory')(app, Models, BASE_URL)
require('./controllers/clientcosting')(app, Models, BASE_URL)
require('./controllers/campaignnote')(app, Models, BASE_URL)
require('./controllers/emailhistory')(app, Models, BASE_URL)
require('./controllers/panicreports')(app, Models, BASE_URL)
require('./controllers/refusalreason')(app, Models, BASE_URL)
require('./controllers/leadupdate')(app, Models, BASE_URL)
require('./controllers/paymentlog')(app, Models, BASE_URL)
require('./controllers/merchant')(app, Models, BASE_URL)
require('./controllers/suppression')(app, Models, BASE_URL)
require('./controllers/recurringpayment')(app, Models, BASE_URL)


app.use(pmx.expressErrorHandler())

// If no route is matched by now, it must be a 404
// app.use(function(req, res, next) {
// 	var err = new Error('Not Found')
// 	err.status = 404

// 	next(err)
// })


blocked(function(ms) {
    console.log('BLOCKED FOR %sms', ms || 0)
})


// Start server
//app.set('port', process.env.PORT || 443)

/*
var httpsServerOptions = {
    ca: fs.readFileSync('ssl_cert/__dualtone_io.ca-bundle'),
    key: fs.readFileSync('ssl_cert/dualtone.key'),
    cert: fs.readFileSync('ssl_cert/__dualtone_io.crt'),
    secureProtocol: 'TLSv1_2_method'
}

https.createServer(httpsServerOptions, app).listen(app.get('port'))

// set up plain http server to redirect to https
var http = express()

http.use(hostValidation(hostValidationConfig))

http.get('*', function(req, res) {
    res.redirect('https://' + req.hostname + req.path)
})

http.listen(80)
*/
app.listen(8000)