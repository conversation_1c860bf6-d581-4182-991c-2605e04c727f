var moment = require('moment')
var _ = require('underscore')
var pdf = require('../reporting/reportToPDF')()
var APP_CONFIG = require('../config/constants')
var Promise = require('sequelize').Promise
var path = require('path')
var fs = require('fs')

module.exports = function (Models) {
    var salesOrderBlankContext = {
        clientName: '',
        campaignName: '',
        patronId: '',
        kaosId: '',
        leadSalutation: '',
        fullName: '',
        decisionMaker: '',
        address: '',
        city: '',
        state: '',
        zip: '',
        email: '',
        telephone1: '',
        telephone2: '',
        telephone3: '',
        telephone4: '',
        agentName: '',
        orderDateTime: '',
        division: '',
        segment: '',
        seriesTableRowHTML: '',
        notes: '',
        cardType: '',
        cardNumber: '',
        cardExp: '',
        installmentCount: '',
        installmentNotes: '',
        subtotal: '',
        salesTax: '',
        clientOrderFee: '',
        giftAmount: '',
        grandTotal: '',
        useAmountOnCredit: '',
        chargeOnCreditCard: '',
        reportingGroup: '',
        leadType: '',
        campaignStage: '',
        leadHasDbChanges: '',
        custom1: ''
	}

    var generatePdfsForSalesOrders = function (arrayOfSalesOrders, template, destinationFilePath) {
        var generatePdfPromises = []

        var filePaths = [];

        // Loop the Sales order records and generate a PDF for each
        arrayOfSalesOrders.forEach(function (salesOrder) {
            var context = salesOrderToContext(salesOrder)

            console.log(context);

            // file name
            var name = cleanString(salesOrder['Client Name'] + '.' + salesOrder['Kaos ID'] + ' ' + moment().format('HH-mm-ss DDMMYY'))

            generatePdfPromises.push(pdf.exportToPDF('salesorders', template, context, name));

            filePaths.push('./salesorders/pdf/' + name + '.pdf');
        })

        return new Promise(function (resolve, reject) {
            Promise.all(generatePdfPromises).then(function (results) {
                resolve(zipMultipleFiles(filePaths, destinationFilePath, ''));
            }).catch(reject)
        })
    }

    /**
     * Generates HTML table rows for series data.
     *
     * @param {Array<Object>} seriesDataArray - An array of objects, where each object
     * represents a series and contains keys like 'series', 'section', 'dayOfWeek',
     * 'seatCount', 'feePerTicket', and 'amount'.
     * @returns {string} A string containing the generated HTML <tr> elements,
     * ready to be inserted into a <tbody>.
     */
    function generateContextSeriesTableRowHTML(seriesDataArray) {
        var htmlRows = ''; // Initialize an empty string to accumulate the HTML
    
        // Check if the input is actually an array
        if (!Array.isArray(seriesDataArray)) {
            console.error("Input must be an array.");
            return ''; // Return empty string on error
        }
    
        // Loop through each object in the array
        for (var i = 0; i < seriesDataArray.length; i++) {
            // Use template literal to create the HTML for one row
            // and append it to the htmlRows string.
            htmlRows += `
<tr>
    <td class="col-description">${seriesDataArray[i]['Series'] || ''}</td>
    <td class="col-section">${seriesDataArray[i]['Seats'] || ''}</td>
    <td class="col-day">${seriesDataArray[i]['Day Of Week'] || ''}</td>
    <td class="col-seats">${seriesDataArray[i]['Seat Count'] || 0}</td>
    <td class="col-fee">$${seriesDataArray[i]['Fee Per Ticket'] || '0.00'}</td>
    <td class="col-amount">$${seriesDataArray[i]['Cost Each'] || '0.00'}</td>
    <td class="col-subtotal">$${seriesDataArray[i]['Subtotal'] || '0.00'}</td>
</tr>
            `;
            // Added fallback values (e.g., || '') in case a property is missing/undefined
        }
    
        return htmlRows; // Return the complete HTML string of all rows
    }

    function addZeroes(num) {
        if (num === null || num === undefined || num === '') {
            return '0.00'; 
        }

        var numStr = String(num);
        var dec = numStr.split('.')[1];
        var len = dec && dec.length > 2 ? dec.length : 2;
        return Number(numStr).toFixed(len);
    }

    var parseUseAmountOnCredit = function (custom1) {
        if (!custom1) {
            return '';
        }

        // Remove any commas added to the dollar values
        custom1 = custom1.replace(/,/g, "");

        var matches = custom1.replace(/,/g, "").match(/\$(\d+(\.\d{1,2})?)/);

        if (matches) {
            // Remove the dollar sign
            match = matches[0].replace(/\$/g, "");

            return parseFloat(match);
        }

        return '';
    }

    var salesOrderToContext = function (salesOrder) {
        var context = _.clone(salesOrderBlankContext)
        
        context.pathToPublic = path.relative(__dirname, APP_CONFIG.PUBLIC_LOCATION)

        var useAmountOnCredit = parseUseAmountOnCredit(salesOrder['Custom1']);
      
        var chargeOnCreditCard = salesOrder['Grand Total'];

        if (typeof salesOrder['Grand Total'] == 'number' && typeof useAmountOnCredit == 'number') {
            chargeOnCreditCard = (salesOrder['Grand Total'] - useAmountOnCredit).toFixed(2);
        }

        context.clientName          = salesOrder['Client Name']
        context.campaignName        = salesOrder['Campaign Name']
        context.patronId            = salesOrder['Lead Client Id']
        context.kaosId              = salesOrder['Kaos ID']
        context.leadSalutation      = salesOrder['Lead Salutation'] || ''
        context.fullName            = salesOrder['Lead First Name'] + ' ' + salesOrder['Lead Last Name']
        context.decisionMaker       = salesOrder['Decision Maker'] || ''
        context.address             = salesOrder['Lead Address1'] + salesOrder['Lead Address2']
        context.city                = salesOrder['Lead City'] || ''
        context.state               = salesOrder['Lead State'] || ''
        context.zip                 = salesOrder['Lead Zip'] || ''
        context.email               = salesOrder['Lead Email'] || ''
        context.telephone1          = salesOrder['Lead Phone1'] || ''
        context.telephone2          = salesOrder['Lead Phone2'] || ''
        context.telephone3          = salesOrder['Lead Phone3'] || ''
        context.telephone4          = salesOrder['Lead Phone4'] || ''
        context.agentName           = salesOrder['Agent'] || ''
        context.orderDateTime       = moment(salesOrder['Sale Date/Time']).format('MMMM D, YYYY h:mm A') || ''
        context.division            = salesOrder['Lead Division'] || ''
        context.segment             = salesOrder['Lead Source'] || ''
        context.seriesTableRowHTML  = generateContextSeriesTableRowHTML(salesOrder['Series Itemization'])
        context.notes               = salesOrder['Notes'] || ''
        context.cardType            = salesOrder['Credit Card Type'] || ''
        context.cardNumber          = salesOrder['Credit Card Number'] || ''
        context.cardExp             = salesOrder['Credit Card Exp Date'] || ''
        context.installmentCount    = salesOrder['Number of Installments'] || ''
        context.installmentNotes    = salesOrder['Installment Notes'] || ''
        context.subtotal            = addZeroes(salesOrder['Subtotal']) || ''
        context.salesTax            = addZeroes(salesOrder['Sales Tax']) || ''
        context.clientOrderFee      = addZeroes(salesOrder['Client Order Fee']) || ''
        context.giftAmount          = addZeroes(salesOrder['Gift Amount']) || ''
        context.grandTotal          = addZeroes(salesOrder['Grand Total']) || ''
        context.useAmountOnCredit   = addZeroes(useAmountOnCredit)
        context.chargeOnCreditCard  = addZeroes(chargeOnCreditCard)
        context.reportingGroup      = salesOrder['Reporting Group'] || ''
        context.leadType            = salesOrder['Lead Type'] || ''
        context.campaignStage       = salesOrder['Campaign Stage'] || ''
        context.leadHasDbChanges    = salesOrder[''] || ''
        context.custom1             = salesOrder['Custom1'] || ''
        
        return context;
    }

    var groupSalesRecords = function (arrayOfSalesRecords) {
        var salesOrdersDictionary = {};

        arrayOfSalesRecords.forEach(function(record) {
            if (! (record['Kaos ID'] in salesOrdersDictionary)) {
                salesOrdersDictionary[record['Kaos ID']] = {};

                for (var key in record) {
                    if (record.hasOwnProperty(key)) {
                        // Exclude columns we want to group together
                        // Don't exclude Subtotal, as we want to concatenate all subtotals to a grand subtotal
                        if (['Series', 'Seats', 'Day Of Week', 'Seat Count', 'Fee Per Ticket', 'Cost Each'].indexOf(key) < 0) {
                            salesOrdersDictionary[record['Kaos ID']][key] = record[key];
                        }
                    }
                 }

                 salesOrdersDictionary[record['Kaos ID']]['Series Itemization'] = [
                    {
                        'Series': record['Series'],
                        'Seats': record['Seats'],
                        'Day Of Week': record['Day Of Week'],
                        'Seat Count': record['Seat Count'],
                        'Fee Per Ticket': record['Fee Per Ticket'],
                        'Cost Each': record['Cost Each'],
                        'Subtotal': record['Subtotal']
                    }
                 ];
            } else {
                // Append the series columns to existing array
                salesOrdersDictionary[record['Kaos ID']]['Series Itemization'].push(
                    {
                        'Series': record['Series'],
                        'Seats': record['Seats'],
                        'Day Of Week': record['Day Of Week'],
                        'Seat Count': record['Seat Count'],
                        'Fee Per Ticket': record['Fee Per Ticket'],
                        'Cost Each': record['Cost Each'],
                        'Subtotal': record['Subtotal']
                    }
                )

                // Add to the grand subtotal
                salesOrdersDictionary[record['Kaos ID']]['Subtotal'] += record['Subtotal'];
            }
        });

        var salesOrdersArray = [];

        // `Object.values` does not exist
        for (var key in salesOrdersDictionary) {
            if (salesOrdersDictionary.hasOwnProperty(key)) {
                salesOrdersArray.push(salesOrdersDictionary[key])
            }
        }

        return salesOrdersArray;
    }

    function cleanString(input) {
        return input.replace(/[|&;$%@"<>()/+,]/g, "")
    }

    function removeFiles(filePaths) {
        for (var i = 0; i < filePaths.length; i++) {
            console.log(filePaths[i]);
            fs.unlink(filePaths[i], function (err) {
                console.log(err ? (err.message || err) : {})
            });
        }
        return;
    }

    /**
     * Zips multiple specified files into a single password-protected archive.
     *
     * @param {string[]} inputFiles - An array of full paths to the files that need to be zipped.
     * @param {string} outputZipPath - The full desired path for the output .zip file (e.g., '/path/to/saved_reports/archive.zip').
     * @param {string} password - The password to protect the zip file.
     * @returns {Promise<string>} A promise that resolves with the full path to the created zip file, or rejects on error.
     */
    function zipMultipleFiles(inputFiles, outputZipPath, password) {
        var spawn = require('child_process').spawn;

        return new Promise((resolve, reject) => {

            // --- Input Validation ---
            if (!Array.isArray(inputFiles) || inputFiles.length === 0) {
                return reject(new Error("inputFiles must be a non-empty array of file paths."));
            }

            if (!outputZipPath || typeof outputZipPath !== 'string') {
                return reject(new Error("outputZipPath (string) must be provided."));
            }

            // Consider if password is truly required or optional
            // if (!password || typeof password !== 'string') {
            //     return reject(new Error("Password (string) must be provided."));
            // }

            // Ensure the output path is resolved (makes it absolute if not already)
            var resolvedOutputZipPath = path.resolve(outputZipPath);

            // --- Construct Arguments for zip command ---
            var zipArgs = [
                //'--password', password,
                '--junk-paths',        // Store files flat in the zip, removing directory structure
                resolvedOutputZipPath, // The path for the output zip archive
            ].concat(inputFiles);

            // --- Spawn the zip process ---
            try {
                var zipProcess = spawn('zip', zipArgs);

                var stderrOutput = ''; // Collect stderr for better error reporting

                // Optional: Log stdout/stderr for debugging
                zipProcess.stdout.on('data', (data) => {
                    console.log(`zip stdout: ${data}`);
                });
                zipProcess.stderr.on('data', (data) => {
                    stderrOutput += data.toString();
                    console.error(`zip stderr: ${data}`);
                });

                // --- Handle process exit ---
                zipProcess.on('exit', (code) => {
                    if (code === 0) {
                        // Success
                        console.log(`Zip process completed successfully for: ${resolvedOutputZipPath}`);

                        // Remove PDFs that now exist within the zip file
                        removeFiles(inputFiles)

                        resolve(resolvedOutputZipPath); // Resolve with the path to the created zip
                    } else {
                        // Failure
                        var errorMsg = `Zip process exited with code ${code}. Stderr: ${stderrOutput || 'N/A'}`;
                        console.error(errorMsg);
                        reject(new Error(errorMsg));
                    }
                });

                // --- Handle errors during spawning (e.g., 'zip' command not found) ---
                zipProcess.on('error', (err) => {
                    console.error('Failed to start zip process:', err);
                    reject(err); // Reject the promise if the process could not be spawned
                });

            } catch (error) {
                // Catch synchronous errors during setup/spawn if any
                console.error("Error setting up or spawning zip process:", error);
                reject(error);
            }
        });
    }

    return {
        generatePdfsForSalesOrders: generatePdfsForSalesOrders,
        salesOrderToContext: salesOrderToContext,
        groupSalesRecords: groupSalesRecords
    }
}