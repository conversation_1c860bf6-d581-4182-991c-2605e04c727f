var fs = require('fs')
var path = require('path')
var nodemailer = require('nodemailer')
var sendmailTransport = require('nodemailer-sendmail-transport')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var APP_CONFIG = require('../config/constants')
Promise.promisifyAll(fs);

module.exports = function (Models) {
	function email(emailSettings, mailOptions) {
		return new Promise(function (resolve, reject) {
			var transporter;
			if (!emailSettings || !emailSettings.value) {
				transporter = nodemailer.createTransport(sendmailTransport())
			} else {
				emailSettings.value = JSON.parse(emailSettings.value)
				if (emailSettings.value.port && (emailSettings.value.port == 587 || emailSettings.value.port == 25)) {
					emailSettings.value.secure = false;
				}
				transporter = nodemailer.createTransport(emailSettings.value)
			}
			transporter.sendMail(mailOptions)
				.then(function (result) {
					delete mailOptions.attachments
					Models.EmailHistory.create({
						success: true,
						emailSettings: JSON.stringify(emailSettings),
						mailOptions: JSON.stringify(mailOptions)
					}, {
						logging: false
					})
					resolve(result)
				})
				.catch(function (err) {
					delete mailOptions.attachments
					Models.EmailHistory.create({
						success: false,
						emailSettings: JSON.stringify(emailSettings),
						mailOptions: JSON.stringify(mailOptions),
						error: err.message || 'unknown error'
					}, {
						logging: false
					})
					reject(err)
				})
		})
	}

	var resend = function (emailSettings, mailOptions) {
		return email(emailSettings, mailOptions)
	}

	var emailWithoutAttachments = function (template, recipients, fromAddress, subject, emailContext) {
		return new Promise(function (resolve, reject) {
			Models.System.findOne({
				where: {
					key: 'email'
				}
			})
				.then(function (emailSettings) {
					fs.readFileAsync('./emailing/templates/' + template + '.html', 'utf-8')
						.then(function (template) {
							if (template) {
								var mailOptions = {
									from: fromAddress || '<EMAIL>',
									to: recipients,
									subject: subject,
									html: mergeTemplateWithContext(template, emailContext),
									attachments: []
								}

								email(emailSettings, mailOptions).then(resolve).catch(reject)
							} else {
								throw new Error('No Email Template Found')
							}
						})
						.catch(reject)
				})
		})
	}

	var plainEmail = function (fromAddress, toAddress, subject, text) {
		return Models.System.findOne({
			where: {
				key: 'email'
			}
		}).then(emailSettings => {
			return email(emailSettings, {
				from: fromAddress || '<EMAIL>',
				to: toAddress,
				subject,
				text
			})
		})
	}

	var emailWithAttachments = function (template, recipients, fromAddress, attachments, subject, emailContext) {
		return new Promise(function (resolve, reject) {
			Models.System.findOne({
				where: {
					key: 'email'
				}
			})
				.then(function (emailSettings) {
					fs.readFileAsync('./emailing/templates/' + template + '.html', 'utf-8')
						.then(function (template) {
							if (template) {
								var mailOptions = {
									from: fromAddress || '<EMAIL>',
									to: recipients,
									subject: subject,
									html: mergeTemplateWithContext(template, emailContext),
									attachments: []
								}
								if (attachments.length) {
									attachments.forEach(function (attachment) {
										mailOptions.attachments.push({
											filename: path.basename(attachment.filename),
											content: fs.createReadStream(attachment.path)
										})
									})
									email(emailSettings, mailOptions).then(resolve).catch(reject)
								} else {
									Models.EmailHistory.create({
										success: false,
										emailSettings: JSON.stringify(emailSettings),
										mailOptions: JSON.stringify(mailOptions),
										error: 'No Attachments Found'
									}).then(reject)
								}
							} else {
								throw new Error('No Email Template Found')
							}
						})
						.catch(reject)
				})
		})
	}

	var emailReportLinkWithTemplate = function (history, recipients, userId) {
		return new Promise(function (resolve, reject) {
			Models.System.findOne({
				where: {
					key: 'email'
				}
			})
				.then(function (emailSettings) {
					fs.readFileAsync('./emailing/templates/reportReady.html', 'utf-8')
						.then(function (template) {
							if (template) {
								var mailOptions = {
									from: '<EMAIL>',
									to: recipients,
									subject: 'KAOS Report Ready To Download',
									text: history.report.name + ' Ready to Download',
									html: template.replace('{{ reportName }}', history.report.name).replace('{{ webAddress }}', APP_CONFIG.EXTERNAL_URL)
								}
								email(emailSettings, mailOptions).then(function (result) {
									Models.ReportHistoryAudit.create({
										isEmailed: true,
										recipients: recipients,
										reporthistoryId: history.id,
										userId: userId || null
									})
									resolve(result)
								})
									.catch(function (err) {
										Models.ReportHistoryAudit.create({
											isEmailed: false,
											error: err.message,
											recipients: recipients,
											reporthistoryId: history.id,
											userId: userId || null
										})
										reject(err)
									})
							} else {
								throw new Error('No Email Template Found')
							}
						})
						.catch(reject)
				})
				.catch(reject)
		})

	}

	var emailPassword = function (password, recipient) {
		return new Promise(function (resolve, reject) {
			Models.System.findOne({
				where: {
					key: 'email'
				}
			})
				.then(function (emailSettings) {
					fs.readFileAsync('./emailing/templates/password.html', 'utf-8')
						.then(function (template) {
							if (template) {
								var mailOptions = {
									from: '<EMAIL>',
									to: recipient,
									subject: 'KAOS user password',
									html: template.replace('{{ password }}', password).replace('{{ webAddress }}', APP_CONFIG.EXTERNAL_URL)
								}
								email(emailSettings, mailOptions).then(resolve).catch(reject)
							} else {
								throw new Error('No Email Template Found')
							}
						})
						.catch(reject)
				})
				.catch(reject)
		})

	}

	function mergeTemplateWithContext(template, context) {
		for (var prop in context) {
			template = template.replaceAll('{{ ' + prop + ' }}', context[prop])
		}

		return template
	}

	String.prototype.replaceAll = function (search, replacement) {
		var target = this;
		return target.replace(new RegExp(search, 'g'), replacement);
	};

	return {
		emailReportLinkWithTemplate: emailReportLinkWithTemplate,
		emailWithAttachments: emailWithAttachments,
		emailPassword: emailPassword,
		resend: resend,
		emailWithoutAttachments: emailWithoutAttachments,
		plainEmail: plainEmail
	}
}