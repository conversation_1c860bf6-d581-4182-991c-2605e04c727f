var _ = require('underscore')
var phantom = require('phantom')
var fs = require('fs')
var Promise = require('sequelize').Promise
Promise.promisifyAll(fs);

module.exports = function () {
	var exportToPDF = function (folder, template, context, name) {
		return new Promise(function (resolve, reject) {			
			phantom.create().then(ph => {
				ph.createPage().then(page => {
					fs.readFileAsync('./reporting/htmlTemplates/' + template, 'utf-8').then(templateHtml => {
						templateHtml = mergeContext(templateHtml, context)

						fs.writeFileAsync('./' + folder + '/html/' + name + '.html', templateHtml)
							.then(() => {
								page.viewportSize = {
									width: 1920,
									height: 1080
								}
								page.open('./' + folder + '/html/' + name + '.html').then(() => {
									page.property('paperSize', {
										format: 'Letter',
										width: '8.5in',
										height: '11in'
									}).then(() => {
										page.render('./' + folder + '/pdf/' + name + '.pdf').then(result => {
											ph.exit()
											resolve(result)
											//should delete the html here
											fs.unlink('./' + folder + '/html/' + name + '.html')
										}).catch(err => {
											reject(err)
											ph.exit()
										})
									}).catch(err => {
										reject(err)
										ph.exit()
									})
								}).catch(err => {
									reject(err)
									ph.exit()
								})
							}).catch(err => {
								reject(err)
								ph.exit()
							})
					}).catch(err => {
						reject(err)
						ph.exit()
					})
				}).catch(err => {
					reject(err)
					ph.exit()
				})
			}).catch(reject)
		})
	}

	function mergeContext(template, context) {
		for (var prop in context) {
			template = template.replaceAll('{{ ' + prop + ' }}', context[prop])
		}
		return template
	}

	String.prototype.replaceAll = function (search, replacement) {
		var target = this;
		return target.replace(new RegExp(search, 'g'), replacement);
	};

	return {
		exportToPDF: exportToPDF
	}
}