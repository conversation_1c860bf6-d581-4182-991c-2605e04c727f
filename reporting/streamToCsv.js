'use strict'

var _ = require('underscore')
var Promise = require('sequelize').Promise
var moment = require('moment')
var fs = require('fs')
var mysql = require('mysql')
var APP_SETTINGS = require('../config/constants')
var csv = require('csv')

module.exports = {
	run: function (definition, outputLocation) {
		return new Promise((resolve, reject) => {
			var connection = mysql.createConnection({
				host: APP_SETTINGS.DB.config.host,
				user: APP_SETTINGS.DB.user,
				password: APP_SETTINGS.DB.pass,
				database: APP_SETTINGS.DB.schema
			})
			if (definition && definition.rawQuery) {
				definition.rawQuery = definition.rawQuery.replace('{{whereClause}}', (definition.whereClause || '1'))
			}

			var stream = fs.createWriteStream(outputLocation)
			var stringifier = csv.stringify({
				header: true
			});

			connection.connect()
			connection.query(definition.rawQuery)
				.stream({
					highWaterMark: 5
				})
				.pipe(stringifier).pipe(stream).on('error', reject).on('finish', () => {
					connection.end()
					stream.end()
					resolve()
				})
		})

	}
}