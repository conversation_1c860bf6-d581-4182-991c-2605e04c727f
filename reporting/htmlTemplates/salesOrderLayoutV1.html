<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="../{{ pathToPublic }}/styles/bootstrap.css" >
    <title>Subscription Order</title>
    <style type="text/css">
        body {
            height: 11in;
            width: 8.5in;
            zoom: 0.75;
            font-family: <PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
            font-size: 11px;
        }
        .invoice-box {
            height: 100%;
            width: 100%;
            padding: 0.3in 0.625in 0 0.625in
        }

        .top-section {
            border-bottom: 1px solid #eee;
            margin: 0 0 5px 0; /* Reduced margin */
        }

        /* --- Header --- */
        .header-section {
            width: 100%;
            overflow: auto; /* To contain floated children */
        }
        .header-left {
            float: left;
            width: 50%;
            text-align: left;
            box-sizing: border-box; /* Include padding and border in width */
            padding-right: 10px; /* Optional spacing */
        }
        .header-right {
            float: right;
            width: 50%;
            text-align: right;
            box-sizing: border-box;
            padding-left: 10px; /* Optional spacing */
        }
        .header-right h4 {
            margin: 0 0 15px 0; /* Reduced margin */
        }

        /* --- Details Table (Dynamic Rows) --- */

        .details-section {
            width: 100%;
            border-top: 1px solid #eee;
            margin-top: 20px;  /* Reduced margin */
        }

        .details-table {
            width: 100%;
            border-collapse: collapse;
        }
        .details-table th, .details-table td {
            border-bottom: 1px solid #eee;
            padding: 5px 4px; /* Significantly reduced padding */
            text-align: left;
        }
        .details-table th {
            background-color: #f9f9f9;
            font-weight: bold;
            text-align: center;
        }
        .details-table td {
            text-align: center;
        }
        /* Adjust alignment for specific columns if needed */
        .details-table .col-description { text-align: left; }
        .details-table .col-section { text-align: center; width: 15%; }
        .details-table .col-day { text-align: center; width: 15%; }
        .details-table .col-seats { text-align: center; width: 10%; }
        .details-table .col-fee { text-align: center; width: 12%; }
        .details-table .col-amount { text-align: center; width: 10%; }
        .details-table .col-subtotal { text-align: right; width: 10%; }

        /* --- Notes Section --- */
        .notes-section {
            border-top: 1px solid #eee;
            padding-bottom: 15px; /* Reduced padding */
            margin-top: 20px;  /* Reduced margin */
        }
        .notes-section h5 {
            background-color: #f9f9f9;
            font-weight: bold;
            margin-top: 0;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
            padding-top: 4px;
            padding-left: 4px;
        }
        .notes-section p {
            margin-top: 0;
            margin-bottom: 8px; /* Reduce paragraph spacing */
        }

        /* --- Payment Info --- */
        .payment-section {
            border-top: 1px solid #eee;
            margin-bottom: 15px; /* Reduced margin */
        }
        .payment-section h5 {
            background-color: #f9f9f9;
            font-weight: bold;
            margin-top: 0;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
            padding-top: 4px;
            padding-left: 4px;
        }
        .payment-section .header-section { /* Re-using the header-section styles for layout */
            margin-bottom: 10px; /* Add some spacing below the payment info header */
        }
        .payment-section .header-left {
            width: 50%; /* Adjust width as needed */
        }
        .payment-section .header-right {
            width: 50%; /* Adjust width as needed */
        }

        /* --- Totals Section --- */
        .totals-section {
            width: 100%;
            overflow: auto; /* To contain floated children */
            margin-bottom: 20px; /* Reduced margin */
        }
        .totals-table {
            float: right; /* Align totals to the right */
            border-collapse: collapse;
        }
        .totals-table td {
        }
        .totals-table td:first-child { text-align: right; font-weight: bold; width: 60%; }
        .totals-table td:last-child { text-align: right; width: 40%; }
        .totals-table tr.grand-total td {
            border-top: 2px solid #333;
            font-weight: bold;
        }
        .totals-table tr.last-item {
            font-weight: bold;
        }

        .footer-header h5 {
            border-top: 1px solid #eee;
            background-color: #f9f9f9;
            font-weight: bold;
            margin-top: 0;
            margin-bottom: 8px;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
            padding-top: 4px;
            padding-left: 4px;
        }

        .footer-section {
            width: 100%;
            overflow: auto; /* To contain floated children */
        }
        .footer-left {
            float: left;
            width: 50%;
            text-align: left;
            box-sizing: border-box;
            padding-right: 10px;
        }
        .footer-right {
            float: right;
            width: 50%;
            text-align: right;
            box-sizing: border-box;
            padding-left: 10px;
        }

    </style>
</head>
<body>
    <div class="invoice-box">

        <div class="top-section">
            <h4>{{ clientName }} | {{ campaignName }}</h4>
        </div>

        <div class="header-section">
            <div class="header-left">
                <strong>Patron ID:</strong> {{ patronId }}<br>
                <strong>KAOS ID:</strong> {{ kaosId }}<br>
                <strong>Lead Salutation:</strong> {{ leadSalutation }}<br>
                <strong>Name:</strong> {{ fullName }}<br>
                <strong>Decision Maker:</strong> {{ decisionMaker }}<br>
                <strong>Address:</strong> {{ address }}<br>
                {{ city }}, {{ state }} {{ zip }}<br>
                <strong>Email:</strong> {{ email }}<br>

            </div>
            <div class="header-right">
                <h4>Subscription Order</h4>
                <strong>Agent Name:</strong> {{ agentName }}<br>
                <strong>Order Date:</strong> {{ orderDateTime }} <br>
                <strong>Lead Description:</strong> {{ division }}<br>
                <strong>Source Code:</strong> {{ segment }}<br>

            </div>
        </div>

        <strong>Tel1:</strong> {{ telephone1 }}
        <strong>Tel2:</strong> {{ telephone1 }}
        <strong>Tel3:</strong> {{ telephone1 }}
        <strong>Tel4:</strong> {{ telephone1 }}<br>

        <div class="details-section">
            <table class="details-table">
                <thead>
                    <tr>
                        <th class="col-description">Series</th>
                        <th class="col-section">Section</th>
                        <th class="col-day">Day</th>
                        <th class="col-seats">Seat Count</th>
                        <th class="col-fee">Fee Per Ticket</th>
                        <th class="col-amount">Amount</th>
                        <th class="col-subtotal">Subtotal</th>
                    </tr>
                </thead>
                <tbody>
                    {{ seriesTableRowHTML }}
                </tbody>
            </table>
        </div>

        <div class="notes-section">
            <h5>Notes</h5>
                <p>{{ notes }}</p>
        </div><br><br>

        <div class="payment-section">
            <h5>Payment Information</h5>
            <div class="header-section">
                <div class="header-left">
                    <div><strong>Name on Card: </strong> {{ leadSalutation }} </div>
                    <div><strong>Method:</strong> {{ cardType }}</div>
                    <div><strong>Card Number:</strong> {{ cardNumber }} </div>
                    <div><strong>Expiry:</strong> {{ cardExp }}</div>
                    <div><strong># of Installments:</strong> {{ installmentCount }}</div>
                    <div><strong>Installment Notes:</strong> {{ installmentNotes }}</div>
                </div>
                <div class="header-right">
                        <table class="totals-table">
                            <tr><td>Subtotal:</td><td>${{ subtotal }}</td></tr>
                            <tr><td>Sales Tax:</td><td>${{ salesTax }}</td></tr>
                            <tr><td>Order Fee:</td><td>${{ clientOrderFee }}</td></tr>
                            <tr><td>Gift Amount:</td><td>${{ giftAmount }}</td></tr>
                            <tr class="grand-total"><td>Grand Total:</td><td>${{ grandTotal }}</td> </tr>
                            <tr><td>Amount Applied from Credit:</td><td>${{ useAmountOnCredit }}</td></tr>
                            <tr class="last-item"><td>Amount Charged to Card:</td><td>${{ chargeOnCreditCard }}</td></tr>
                        </table>
                </div>
            </div>
        </div>

        <div class="footer-header"><h5>Misc</h5></div>
        <div class="footer-section">
            <div class="footer-left">
                <strong>Reporting Group: </strong>{{ reportingGroup }}<br>
                <strong>Lead Type: </strong>{{ leadType }}<br>
                <strong>Campaign Stage: </strong>{{ campaignStage }}<br>
            </div>
            <div class="footer-right">
                <!--<strong>Lead Has DB Changes: </strong>{{ leadHasDbChanges }}<br>-->
                <strong>Custom 1: </strong>{{ custom1 }}<br>
            </div>
        </div>
    </div>
</body>
</html>