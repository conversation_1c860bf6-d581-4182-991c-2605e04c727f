<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<link rel="stylesheet" href="../{{ pathToPublic }}/styles/bootstrap.css" >
		<!-- <link href='https://fonts.googleapis.com/css?family=Open+Sans:400,700,600' rel='stylesheet' type='text/css'> -->
		<style type="text/css">
			@media print {
			@page {
			size: letter;
			}
			}
			body {
					height: 11in;
					width: 8.5in;
					zoom: 0.75;
					font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
					font-size: 13px;
				}
			.layoutBox {
				border-style: solid;
				border-width: 1px;
				}
			span.box {
				font-size: 15px;
			}
			#perfLine {
				position: fixed;
				border-top: 3px dashed;
				width: 100%;
				top: 7.3in;
				border-color: orange;
			}
			.addressBox {
				position: fixed;
				border: 3px solid;
				border-color: red;
				border-radius: 22px;
			}
			#toAddressBox {
				top: 1.89in;
				left: 0.72in;
				width: 3.43in;
				height: 1.17in;
			}
			#fromAddressBox {
				top: 0.16in;
				left: 0.72in;
				width: 3in;
				height: 1.01in;
			}
			#returnAddressBox {
				top: 9.36in;
				left: 4.65in;
				width: 3.42in;
				height: 0.97in;
				border-color: orange;
			}
			.showBorder {
				border: 2px solid blue;
			}
		</style>
		<title></title>
	</head>
	<body>
		<!-- <div id="perfLine"></div>
		<div id="fromAddressBox" class="addressBox"></div>
		<div id="toAddressBox" class="addressBox"></div>
		<div id="returnAddressBox" class="addressBox"></div> -->
		<div class="ibox-content" style="height: 100%; width: 100%; padding: 0.3in 0.625in 0 0.625in">
			<!-- <div class="" id="logoLarge" style="margin-left: 0.24in; height: 0.88in; width: 1.8in;">
				<img src="../{{ logo }}" style="max-height: 0.88in; max-width: 1.8in;"/>
			</div> -->
			<div class="" id="fromAddress" style="float:left;margin-left: 0.32in; height: 1.38in;width:4.5in;">
				<div class="spacingFix" id="returnCompany" style="height: 0.18in; width: 100%;">{{ clientCompany }}</div>
				<div class="spacingFix" id="returnAddress1" style="height: 0.18in; width: 100%;">{{ clientAddress1 }}</div>
				<div class="spacingFix" id="returnAddress2" style="height: 0.18in; width: 100%;">{{ clientAddress2 }}</div>
				<div class="spacingFix" id="returnAddress3" style="height: 0.18in; width: 100%;">{{ clientAddress3 }}</div>
				<div class="spacingFix" id="website" style="margin-top:0.18in;height: 0.18in; width: 100%;">{{ clientWebsite }}</div>
				<div class="spacingFix" id="phone" style="height: 0.18in; width: 100%;">{{ clientPhone }}</div>
			</div>
			<div class="" id="logoSmall" style="float:left;margin-left: 0.2in; width:1.94in;height:1.38in;text-align: left;">
				<img src="../{{ logo }}" style="max-height: 1.38in;max-width: 2in;"/>
			</div>
			<div class="" id="toAddress" style="float:left;margin-left: 0.32in; margin-top: 0.45in; height: 0.8in;width: 4.5in;">
				<div class="spacingFix" id="leadName" style="height: 0.18in; width: 100%;">{{ leadName }}</div>
				<div class="spacingFix" id="leadAddress1" style="height: 0.18in; width: 100%;">{{ leadAddress1 }}</div>
				<div class="spacingFix" id="leadAddress2" style="height: 0.18in; width: 100%;">{{ leadAddress2 }}</div>
				<div class="spacingFix" id="leadAddress3" style="height: 0.18in; width: 100%;">{{ leadAddress3 }}</div>
			</div>
			<div style="float:left;height: 4.41in; width: 100%;margin-top:0.1in;">
				<div class="spacingFix" id="salutation" style="height: 0.32in; width: 100%;">
					<div style="float:left;height: 0.32in;width:4.5in;">{{ salutation }}</div><div style="float:left;margin-left: 0.52in; width:1.94in;height: 0.32in;text-align: left;">{{ longDate }}</div>
				</div>
				<!-- <div class="" id="dateBody" style="float:left;margin-top: 1.17in;margin-left: 0.26in; width:2in;height:0.26in;text-align: left;"></div> -->
				<div class="spacingFix" id="text" style="max-height: 3.45in; width: 100%;">
					{{ invoiceText }}
				</div>
				<div class="" id="signature" style="max-height: 0.44in; max-width: 3in;">
					<img src="../{{ signature }}" style="max-height: 0.44in; max-width: 3in;"/>
				</div>
				<div class="spacingFix" id="name" style="max-height: 0.18in; width: 100%;">{{ clientContactName }}</div>
				<div class="spacingFix" id="title" style="max-height: 0.18in; width: 100%;">{{ clientContactTitle }}</div>
			</div>		
			<div class="" style="float:left; height: 0.5in;width:4.5in;">
				<strong>PLEASE RETURN THIS LOWER PORTION WITH YOUR PAYMENT.</strong>
			</div>
			<div class="" id="logoSmall" style="float:left;margin-left: 0.52in; width:2in;height:0.5in;text-align: left;">
				<img src="../{{ logo }}" style="max-height: 0.47in; max-width: 1.9in;"/>
			</div>
			<div class="" id="notes" style="float:left;height: 0.52in; width: 100%; padding-right: 40px;">{{ message }}</div>
			<div class="" style="float:left;height: 6cm; width:100%">
				<div class="" style="float: left; height: 100%; overflow: hidden; width: 4in;">
					<div class="" id="invoiceText" style="height: 2.04in; width: 100%;">
						<p style="font-size: 12px">
							<strong>Pledge Amount: </strong><span style="margin-right: 10px;" id="invoiceAmount">{{ grandTotal }}</span>
							<strong>Balance Due: </strong><span id="invoiceAmount">{{ invoiceAmount }}</span>
							<br>
							<strong>Pledge Date: </strong><span style="margin-right: 10px;" id="payByDate">{{ pledgeDate }}</span>
							<strong>Please pay within 30 days</strong>
							<!-- <strong>Tickets: </strong><strong id="payByDate">{{ ticketCount }}</strong> -->
							<br>
							Amount Enclosed: $_________ <small>To waive benefits, check here: </small><span class="box">&#9633;</span>
							<br>
							<br>
							<span style="margin-right: 12px;">If you would like to pay by credit card, please call us at {{ clientPhone }} or pay online at {{ clientWebsite }}.</span>
							<br>
							<br>
							My company, _________________________________, will match my gift according to the information I have enclosed.
						</p>
						<div class="spacingFix" id="returnFooter" style="font-size: 12px; margin-top: 10px; width: 100%;">
							<div style="margin-right: 27px;float:left;">Patron ID: {{ leadRef }}</div><div style="margin-left: 20px;float:left;">Print Date: {{ printDate }}</div>
						</div>
					</div>
				</div>
				<div class="" style="float: left; overflow: hidden; height: 100%; width: 3in; margin-left:0.2in;">
					<div class="spacingFix" id="leadName" style="height: 0.18in; width: 100%;">{{ leadName }}</div>
					<div class="spacingFix" id="leadAddress1" style="height: 0.18in; width: 100%;">{{ leadAddress1 }}</div>
					<div class="spacingFix" id="leadAddress2" style="height: 0.18in; width: 100%;">{{ leadAddress2 }}</div>
					<div class="spacingFix" id="leadAddress3" style="height: 0.18in; width: 100%;">{{ leadAddress3 }}</div>
					<div class="spacingFix" id="returnCompany" style="height: 0.18in; width: 100%;margin-top:0.27in;">{{ clientCompany }}</div>
					<div class="spacingFix" id="address1" style="height: 0.18in; width: 100%;">{{ clientAddress1 }}</div>
					<div class="spacingFix" id="address2" style="height: 0.18in; width: 100%;">{{ clientAddress2 }}</div>
					<div class="spacingFix" id="address3" style="height: 0.18in; width: 100%;">{{ clientAddress3 }}</div>
				</div>
			</div>
		</div>
	</body>
</html>