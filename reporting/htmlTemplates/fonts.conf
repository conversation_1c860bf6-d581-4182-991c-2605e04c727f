    <fontconfig>
        <dir>/var/task/fonts/</dir>
        <cachedir>/tmp/fonts-cache/</cachedir>
         <!--
         Load local system customization file
        <include ignore_missing="no">conf.d</include> -->
        <match target="font">
            <edit mode="assign" name="rgba">
                <const>rgb</const>
            </edit>
        </match>
        <match target="font">
            <edit mode="assign" name="hinting">
                <bool>true</bool>
            </edit>
        </match>
        <match target="font">
            <edit mode="assign" name="hintstyle">
                <const>hintslight</const>
            </edit>
        </match>
        <match target="font">
            <edit mode="assign" name="antialias">
                <bool>true</bool>
            </edit>
        </match>
        <match target="font">
            <edit mode="assign" name="lcdfilter">
                <const>lcddefault</const>
            </edit>
        </match>    
        <config></config>
    </fontconfig>