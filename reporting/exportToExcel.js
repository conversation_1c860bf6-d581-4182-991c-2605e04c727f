'use strict'

var Excel = require('../thirdparty/exceljs')
// var Excel = require('exceljs')
var _ = require('underscore')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var moment = require('moment')
var fs = require('fs')

module.exports = function (Models) {
    var dbChangesGoAtEnd = false
    var exportToExcel = function (results, filename, definition) {
        var promise = new Promise(function (resolve, reject) {
            //this removes all unwanted properties from the results
            var cleanResults = JSON.parse(JSON.stringify(results))

            // construct a streaming XLSX workbook writer with styles and shared strings
            var options = {
                filename: filename,
                useStyles: true,
                useSharedStrings: true
            }
            var workbook = new Excel.stream.xlsx.WorkbookWriter(options);

            // var workbook = new Excel.Workbook();

            formatFilters(definition).then(filters => {
                processResults(cleanResults, definition, workbook, filters)
                    .then(() => {
                        // workbook.xlsx.writeFile(filename)
                        workbook.commit()
                            .then(resolve)
                            .catch(err => {
                                reject(err)
                            })
                    })
                    .catch(err => {
                        reject(err)
                    })
            })


        })

        return promise
    }

    var exportMultipleDynamicToExcel = function (resultsArray, names, definition, filename) {
        var workbook = new Excel.Workbook()
        workbook.creator = 'Dualtone'
        return formatFilters(definition).then(filter => {
            return Promise.all(resultsArray.map((results, index) => {
                var cleanResults = JSON.parse(JSON.stringify(results))
                var def = JSON.parse(JSON.stringify(definition))
                def.worksheetName = names[index]
                return processResults(cleanResults, def, workbook, filter, false)
            }))
        }).then(() => {
            return workbook.xlsx.writeFile(filename)
        })
    }

    var exportMultipleToExcel = function (resultsArray, filename, definitions) {
        return new Promise(function (resolve, reject) {
            var workbook = new Excel.Workbook();

            workbook.creator = 'Dualtone'

            var filterPromises = []

            definitions.forEach(definition => {
                filterPromises.push(formatFilters(definition))
            })

            Promise.all(filterPromises)
                .then(filters => {
                    var promises = []

                    var arrayOfResults = {}
                    definitions.forEach((def, i) => {
                        var cleanResults = JSON.parse(JSON.stringify(resultsArray[i]))
                        if (arrayOfResults[def.worksheetName]) {
                            arrayOfResults[def.worksheetName].results.push(cleanResults)
                            arrayOfResults[def.worksheetName].definitions.push(def)
                        } else {
                            arrayOfResults[def.worksheetName] = {
                                definitions: [def],
                                results: [cleanResults]
                            }
                        }
                    })

                    for (var prop in arrayOfResults) {
                        var results = arrayOfResults[prop]
                        if (results.results.length > 1) {
                            promises.push(processResults(results.results, results.definitions[0], workbook, filters[0], true))
                        } else {
                            promises.push(processResults(results.results[0], results.definitions[0], workbook, filters[0], false))
                        }

                    }

                    Promise.all(promises).then(() => {
                        workbook.xlsx.writeFile(filename)
                            .then(resolve)
                            .catch(err => {
                                reject(err)
                            })
                    })
                        .catch(err => {
                            reject(err)
                        })
                })
        })
    }

    function processResults(results, definition, workbook, filters, multipleResults) {
        return new Promise(function (resolve, reject) {
            var promises = []
            if (!definition.rawQuery && doesDefinitionContainLeads(definition)) {
                for (var i = 0; i < results.length; i++) {
                    var result = results[i]
                    promises.push(getChangedLeads(result.lead || result))
                }
                Promise.all(promises)
                    .then(function (leads) {
                        processComplexRows(workbook, results, leads, definition, definition.worksheetName, filters)
                        resolve()

                    })
                    .catch(reject)
            } else if (definition.rawQuery && definition.includeDBChanges) {
                //figure out the chhanges to leads
                if (definition.dbChangesGoAtEnd)
                    dbChangesGoAtEnd = true
                else
                    dbChangesGoAtEnd = false

                if (!multipleResults) {
                    getLeadChangesForFlatResults(results, definition)
                        .then(function (resultsWithDBChanges) {
                            processFlatRows(workbook, resultsWithDBChanges, definition.rollupField, definition.worksheetName, filters, multipleResults)
                            resolve()
                        })
                        .catch(reject)
                } else {
                    resolve()
                }

            } else if (definition.rawQuery && definition.includeSingleLineDBChanges) {
                if (!multipleResults) {
                    getLeadChangesForFlatResults(results, definition)
                        .then(function (resultsWithDBChanges) {
                            resultsWithDBChanges = mergeChangesToSingleField(resultsWithDBChanges)
                            processFlatRows(workbook, resultsWithDBChanges, definition.rollupField, definition.worksheetName, filters, multipleResults)
                            resolve()
                        })
                        .catch(reject)
                } else {
                    resolve()
                }
            } else {
                if (definition.rawQuery) {
                    console.log(multipleResults)
                    try {
                        processFlatRows(workbook, results, definition.rollupField, definition.worksheetName, filters, multipleResults)
                    } catch (err) {
                        console.log(err)
                    }

                    resolve()

                } else {
                    processComplexRows(workbook, results, null, definition, definition.worksheetName, filters)
                    resolve()
                }
            }
        })
    }

    function mergeChangesToSingleField(results) {
        results.forEach(function (result) {
            result['Database Changes'] = ''
            for (var prop in result) {
                if (prop.indexOf('DBC ') == 0) {
                    if (result[prop]) {
                        var field = prop.substring(4)
                        result['Database Changes'] = result['Database Changes'] + field + ': ' + result[prop] + ', '
                    }
                    if (prop != 'DBC Email') {
                        delete result[prop]
                    }
                }
            }
        })

        return results
    }

    function getLeadChangesForFlatResults(results, definition) {
        //return a promise of all the changes on the results
        return new Promise(function (resolve, reject) {
            getCampaignDate(definition)
                .then(fromDate => {
                    var promises = []
                    results.forEach(result => {
                        result['Lead has DB Changes'] = 'No'
                        result['DBC First Name'] = ''
                        result['DBC Last Name'] = ''
                        result['DBC Salutation'] = ''
                        result['DBC Suffix'] = ''
                        result['DBC Spouse Name'] = ''
                        result['DBC Company Name'] = ''
                        result['DBC Address1'] = ''
                        result['DBC Address2'] = ''
                        result['DBC Address3'] = ''
                        result['DBC City'] = ''
                        result['DBC State'] = ''
                        result['DBC Zip'] = ''
                        result['DBC Phone1'] = ''
                        result['DBC Phone2'] = ''
                        result['DBC Phone3'] = ''
                        result['DBC Phone4'] = ''
                        result['DBC Email'] = ''
                        promises.push(Models.LeadAudit.findAll({
                            where: {
                                leadId: result['Kaos ID'],
                                createdAt: {
                                    $gt: fromDate
                                }
                            }
                        }))
                    })
                    Promise.all(promises)
                        .then(changes => {
                            changes.forEach(change => {
                                change = JSON.parse(JSON.stringify(change))
                                if (change.length) {
                                    var result = _.findWhere(results, {
                                        'Kaos ID': change[0].leadId
                                    })
                                    if (result) {
                                        result['Lead has DB Changes'] = 'Yes'
                                        for (var i = 0; i < change.length; i++) {
                                            var ch = change[i]
                                            var field = findFieldName(ch.field)
                                            if (field) {
                                                if (result[field]) {
                                                    var split = result[field].split(' -> ')
                                                    result[field] = split[0] + ' -> ' + (ch.newValue || '--BLANK--')
                                                } else {
                                                    result[field] = (ch.previousValue || '--BLANK--') + ' -> ' + (ch.newValue || '--BLANK--')
                                                }
                                            }
                                        }
                                    }
                                }
                            })
                            resolve(results)
                        })
                        .catch(reject)
                })
                .catch(reject)
        })
    }

    function getCampaignDate(definition) {
        var fromDate = moment().subtract(1, 'year').toDate()
        return new Promise((resolve, reject) => {
            if (definition.rawQuery && definition.whereClause) {
                var found = false
                definition.whereClause.split('and').forEach(filter => {
                    var split = filter.split('.')
                    if (split && split.length > 1) {
                        var table = _.findWhere(definition.availableFilters, {
                            queryTableName: split[0].trim()
                        })
                        if (table && table.model == 'Campaign') {
                            found = true
                            var querySplit = split[1].split('=')
                            Models.Campaign.findById(querySplit[1].trim())
                                .then(campaign => {
                                    if (campaign && campaign.startDate) {
                                        fromDate = moment(campaign.startDate).toDate()
                                    }
                                    resolve(fromDate)
                                })
                        }
                    }
                })
                if (!found) resolve(fromDate)
            } else {
                resolve(fromDate)
            }
        })
    }

    function findFieldName(input) {
        var field = '';
        switch (input) {
            case 'first_name':
                field = 'DBC First Name'
                break
            case 'last_name':
                field = 'DBC Last Name'
                break
            case 'salutation':
                field = 'DBC Salutation'
                break
            case 'suffix':
                field = 'DBC Suffix'
                break
            case 'spouse_name':
                field = 'DBC Spouse Name'
                break
            case 'company_name':
                field = 'DBC Company Name'
                break
            case 'address1':
                field = 'DBC Address1'
                break
            case 'address2':
                field = 'DBC Address2'
                break
            case 'address3':
                field = 'DBC Address3'
                break
            case 'city':
                field = 'DBC City'
                break
            case 'state':
                field = 'DBC State'
                break
            case 'zip':
                field = 'DBC Zip'
                break
            case 'phone_home':
                field = 'DBC Phone1'
                break
            case 'phone_mobile':
                field = 'DBC Phone2'
                break
            case 'phone_work':
                field = 'DBC Phone3'
                break
            case 'phone_workmobile':
                field = 'DBC Phone4'
                break
            case 'email':
                field = 'DBC Email'
                break
        }
        return field;
    }

    var processFlatRows = function (workbook, results, rollupField, worksheetName, filters, multipleResults) {
        var worksheet = workbook.addWorksheet(worksheetName || 'Report')

        if (!multipleResults) {
            results = [results];
        }

        results.forEach((cleanResults, index) => {
            if (cleanResults && cleanResults.length) {

                if (rollupField) {
                    var currentrow = cleanResults[0][rollupField]
                }
                var columns = []
                var header = Object.keys(cleanResults[0])
                var firstRow = []
                for (var i = 0; i < header.length; i++) {
                    columns.push({
                        width: 20
                    })
                    if (header[i] == 'Lead has DB Changes' && !dbChangesGoAtEnd) {
                        firstRow.unshift(header[i])
                    } else {
                        firstRow.push(header[i])
                    }
                }
                if (!worksheet.columns || worksheet.columns.length < columns.length) worksheet.columns = columns

                if (filters && index == 0) {
                    var row = worksheet.addRow(filters)
                    row.font = {
                        bold: true
                    }
                    row.alignment = {
                        horizontal: 'center'
                    }
                    row.font = {
                        bold: true,
                        name: 'Tahoma',
                        family: 4,
                        size: 8
                    }
                    row.commit()
                    worksheet.addRow([]).commit()
                }

                var headerRow = worksheet.addRow(firstRow)
                headerRow.font = {
                    bold: true,
                    name: 'Tahoma',
                    family: 4,
                    size: 8
                }
                headerRow.alignment = {
                    horizontal: 'center'
                }
                headerRow.commit()
                var lastRow
                for (var i = 0; i < cleanResults.length; i++) {
                    var result = cleanResults[i]
                    var newRow
                    if (i == cleanResults.length - 1 && cleanResults.length > 2 && rollupField) {
                        //this is the grandtotal row
                        lastRow.getCell(1).value = 'Total'
                        lastRow.font = {
                            bold: true
                        }
                        lastRow.alignment = {
                            horizontal: 'center'
                        }

                        newRow = worksheet.addRow(objectToArray(result))
                        newRow.getCell(1).value = 'Grand Total'
                        newRow.font = {
                            bold: true
                        }
                        newRow.alignment = {
                            horizontal: 'center'
                        }
                    } else if (rollupField && currentrow != result[rollupField] && cleanResults.length > 2) {
                        //this is a subtotal row
                        lastRow.getCell(1).value = 'Total'
                        lastRow.font = {
                            bold: true
                        }
                        lastRow.alignment = {
                            horizontal: 'center'
                        }
                        var arrayOfIt = objectToArray(result)
                        newRow = worksheet.addRow(arrayOfIt)
                    } else {
                        //this is a normal row
                        var arrayOfIt = objectToArray(result)
                        try {
                            newRow = worksheet.addRow(arrayOfIt)
                        } catch (err) {
                            console.log(arrayOfIt)
                        }
                    }

                    currentrow = result[rollupField]

                    if (lastRow) {
                        if (!lastRow.font) {
                            lastRow.font = {}
                        }
                        lastRow.font.name = 'Tahoma'
                        lastRow.font.family = 4
                        lastRow.font.size = 8
                        lastRow.height = 15
                        lastRow.commit()
                    }
                    lastRow = newRow
                }

                if (rollupField) {
                    lastRow.getCell(1).value = 'Grand Total'
                    lastRow.font = {
                        bold: true
                    }
                    lastRow.alignment = {
                        horizontal: 'center'
                    }
                }

                if (!lastRow.font) {
                    lastRow.font = {}
                }
                lastRow.font.name = 'Tahoma'
                lastRow.font.family = 4
                lastRow.font.size = 8
                lastRow.height = 15
                lastRow.commit()

                worksheet.addRow([]).commit()
            }
        })

        if (multipleResults)
            console.log('finished processing')


        if (worksheet.commit)
            worksheet.commit()
    }

    var objectToArray = function (obj) {
        var result = []
        for (var key in obj) {

            if (obj[key] && obj[key].length > 9 && obj[key].substring(0, 10).match(/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/)) {
                //this could be a date so try and parse it
                if (obj[key].length === 10)
                    obj[key] = obj[key].substring(5, 7) + '/' + obj[key].substring(8, 10) + '/' + obj[key].substring(0, 4)
                else {
                    var testDate = new Date(obj[key])
                    if (testDate != 'Invalid Date') {
                        obj[key] = moment(testDate).format('MM/DD/YYYY HH:mm:ss')
                    }
                }
            }
            var value = (obj[key] === undefined || obj[key] === null) ? '' : obj[key]

            if (value && typeof value === 'string' && JSON.stringify(value).indexOf('\\u001f') > -1) {
                value = JSON.parse(JSON.stringify(value).split('\\u001f').join(''))
            }

            if (key == 'Lead has DB Changes' && !dbChangesGoAtEnd) {
                result.unshift(value)
            } else {
                result.push(value)
            }
        }


        return result
    }

    var leadHasDbChanges = function (lead) {
        for (var prop in lead) {
            if (prop.indexOf('dbc') === 0 && lead[prop].indexOf('->') > -1) {
                return true
            }
        }
        return false
    }

    var processComplexRows = function (workbook, cleanResults, leads, definition, worksheetName, filters) {
        try {
            var worksheet = workbook.addWorksheet(worksheetName || 'Report')
            if (!cleanResults.length) return

            if (filters) {
                worksheet.addRow(filters)
                worksheet.lastRow.font = {
                    bold: true
                }
                worksheet.lastRow.alignment = {
                    horizontal: 'center'
                }
                worksheet.lastRow.font = {
                    bold: true,
                    name: 'Tahoma',
                    family: 4,
                    size: 8
                }
                worksheet.addRow([])
            }

            if (leads) {
                for (var i = 0; i < cleanResults.length; i++) {
                    cleanResults[i].lead = leads[i]
                    cleanResults[i].leadHasDbChanges = leadHasDbChanges(leads[i])
                }
            }

            //create the columns
            var flattened = flatten(cleanResults[0])
            var headers = Object.keys(flattened)
            worksheet.columns = []
            var totals = []
            var firstRow = []
            var totalRow

            for (var i = 0; i < headers.length; i++) {
                var header = headers[i]
                if ((header === 'id' && definition.primaryModule.name === 'Lead') || header === 'lead.id' || (header.indexOf('.id') === -1 && header !== 'id')) {
                    worksheet.columns.push({
                        width: 20
                    })
                    totals.push({
                        col: i + 1,
                        name: header,
                        total: 0,
                        count: 0
                    })
                    if (header == 'leadHasDbChanges') {
                        firstRow.unshift(header)
                    } else {
                        firstRow.push(header)
                    }
                }
            }

            var firstRow = formatHeaderRow(firstRow)

            //create the header
            worksheet.addRow(firstRow)
            var headerRow = worksheet.lastRow
            headerRow.font = {
                bold: true
            }
            headerRow.alignment = {
                horizontal: 'center'
            }
            headerRow.font = {
                bold: true,
                name: 'Tahoma',
                family: 4,
                size: 8
            }

            //create the rows
            for (var i = 0; i < cleanResults.length; i++) {
                var flattenedRow = flatten(cleanResults[i])
                var values = []
                var pos = 0
                var formats = []
                for (var value in flattenedRow) {
                    if ((value === 'id' && definition.primaryModule.name === 'Lead') || value === 'lead.id' || (value.indexOf('.id') === -1 && value !== 'id')) {
                        pos++
                        if (value.indexOf('creditCard') === -1 && value.indexOf('clientRef') < 0 && value.indexOf('id') !== 0 && value !== 'lead.id' && value.indexOf('Id') < 0 && value.indexOf('zip') === -1 && value.indexOf('phone') === -1 && value.indexOf('numberOfInstallments') === -1 && value.indexOf('clientSourceCode') === -1 && value.indexOf('division') === -1 && value.indexOf('spouse') === -1) {
                            if (!isNaN(flattenedRow[value])) {
                                if (flattenedRow[value] % 1 != 0) {
                                    //means its a decimal
                                    formats.push({
                                        cell: pos,
                                        fmt: '0.00'
                                    })
                                }

                                if (value.indexOf('avg_') > -1 && flattenedRow[value]) {
                                    totals[pos - 1].total += flattenedRow[value]
                                    totals[pos - 1].count++
                                } else if (value.indexOf('sum_') > -1 && flattenedRow[value]) {
                                    totals[pos - 1].total += flattenedRow[value]
                                    totals[pos - 1].count++
                                } else if (value.indexOf('min_') > -1) {
                                    if (flattenedRow[value] < totals[pos - 1].total)
                                        totals[pos - 1].total = flattenedRow[value]
                                    totals[pos - 1].count++
                                } else if (value.indexOf('max_') > -1) {
                                    if (flattenedRow[value] > totals[pos - 1].total)
                                        totals[pos - 1].total = flattenedRow[value]
                                    totals[pos - 1].count++
                                } else if (flattenedRow[value]) {
                                    totals[pos - 1].total += flattenedRow[value]
                                    totals[pos - 1].count++
                                }
                            }
                        }

                        if (flattenedRow[value] && flattenedRow[value].length == 24 && flattenedRow[value].indexOf(':') > -1) {
                            //this could be a date so try and parse it
                            var testDate = new Date(flattenedRow[value])
                            if (testDate != 'Invalid Date') {
                                flattenedRow[value] = moment(testDate).format('MM/DD/YY')
                            }
                        }


                        if (flattenedRow[value] === null || flattenedRow[value] === undefined)
                            flattenedRow[value] = ''

                        //check for true/false values and correct them
                        if (value === 'leadHasDbChanges') {
                            if (flattenedRow[value] === true) {
                                values.unshift("yes")
                            } else {
                                values.unshift("no")
                            }
                        } else if (flattenedRow[value] === true) {
                            values.push("yes")
                        } else if (flattenedRow[value] === false) {
                            values.push("no")
                        } else {
                            values.push(flattenedRow[value])
                        }
                    }
                }
                try {
                    worksheet.addRow(values)
                    worksheet.lastRow.font = {
                        name: 'Tahoma',
                        family: 4,
                        size: 8
                    }
                } catch (err) {
                    return
                }

                //format the cells based on the values
                if (formats.length) {
                    var row = worksheet.lastRow
                    for (var j = 0; j < formats.length; j++) {
                        var format = formats[j]
                        row.getCell(format.cell).numFmt = format.fmt
                    }
                }
            }

            values = []

            for (var i = 0; i < totals.length; i++) {
                var total = totals[i]
                if (total.count) {
                    if (total.name.indexOf('avg_') > -1 && total.total > 0) {
                        values.push((total.total / total.count).toFixed(2))
                    } else {
                        values.push(total.total)
                    }
                } else {
                    values.push('')
                }
            }

            // worksheet.addRow(values)
            // totalRow = worksheet.lastRow
            // totalRow.font = {
            // 	bold: true,
            // 	name: 'Tahoma',
            // 	family: 4,
            // 	size: 8
            // }
            // totalRow.alignment = {
            // 	horizontal: 'center'
            // }
        } catch (err) {
            console.log(err)
        }
    }

    var flatten = function (data) {
        var result = {}

        function recurse(cur, prop) {
            if (Object(cur) !== cur) {
                result[prop] = cur
            } else if (Array.isArray(cur)) {
                for (var i = 0, l = cur.length; i < l; i++)
                    recurse(cur[i], prop ? prop + "." + i : "" + i)
                if (l == 0)
                    result[prop] = []
            } else {
                var isEmpty = true
                for (var p in cur) {
                    isEmpty = false
                    recurse(cur[p], prop ? prop + "." + p : p)
                }
                if (isEmpty)
                    result[prop] = {}
            }
        }
        recurse(data, "")
        return result
    }

    function formatHeaderRow(fields) {
        var results = []
        for (var i = 0; i < fields.length; i++) {
            var value = fields[i]
                .split('.').join('_')
                .split('_').join(' ')
                .replace(/([A-Z])/g, ' $1')
                .replace(/^./, function (str) {
                    return str.toUpperCase()
                })

            if (value == 'Id' || value == 'Lead id') {
                value = 'KAOS Id'
            }
            results.push(toTitleCase(value))
        }
        return results
    }

    function toTitleCase(str) {
        return str.replace(/\w\S*/g, function (txt) {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        })
    }

    function getChangedLeads(lead) {
        return new Promise(function (resolve, reject) {
            var extend = {
                dbc_first_name: '',
                dbc_last_name: '',
                dbc_salutation: '',
                dbc_suffix: '',
                dbc_spouse_name: '',
                dbc_company_name: '',
                dbc_address1: '',
                dbc_address2: '',
                dbc_address3: '',
                dbc_city: '',
                dbc_state: '',
                dbc_zip: '',
                dbc_phone_home: '',
                dbc_phone_mobile: '',
                dbc_phone_work: '',
                dbc_phone_workmobile: '',
                dbc_email: ''
            }

            lead = _.extend(lead, extend)

            Models.LeadAudit.findAll({
                where: {
                    leadId: lead.id
                }
            })
                .then(function (changes) {
                    for (var j = 0; j < changes.length; j++) {
                        var change = changes[j]
                        if (lead['dbc_' + change.field]) {
                            var split = lead['dbc_' + change.field].split(' -> ')
                            lead['dbc_' + change.field] = split[0] + ' -> ' + change.newValue
                        } else {
                            lead['dbc_' + change.field] = (change.previousValue || '--BLANK--') + ' -> ' + change.newValue
                        }
                    }
                    resolve(lead)
                })
        })

    }

    function doesDefinitionContainLeads(definition) {
        if (definition.primaryModule.name == "Lead")
            return false

        var result = false
        var checkModule = function (module) {
            if (module.name == "Lead") {
                result = true
            }

            if (module.relatedModules && module.relatedModules.length) {
                for (var i = 0; i < module.relatedModules.length; i++) {
                    checkModule(module.relatedModules[i])
                }
            }
        }

        checkModule(definition.primaryModule)

        return result
    }

    function formatFilters(definition) {
        return new Promise((resolve, reject) => {
            var promises = []
            var nonPromiseResults = []
            var promiseResults = []
            if (definition.rawQuery) {
                if (definition.whereClause) {
                    var filters = definition.whereClause.split('and')
                    filters.forEach(filter => {
                        var split = filter.split('.')
                        if (split && split.length > 1) {
                            var table = _.findWhere(definition.availableFilters, {
                                queryTableName: split[0].trim()
                            })
                            if (table) {
                                if (['Agent', 'Campaign', 'Client'].indexOf(table.model) > -1) {
                                    promiseResults.push({
                                        table: table.model,
                                        operator: '=',
                                        value: ''
                                    })
                                    var filterObj = {
                                        where: {}
                                    }

                                    var querySplit = ''

                                    // handle 'in' clause
                                    if (split[1].indexOf(' in (') > -1) {
                                        querySplit = split[1].split(' in (')
                                        querySplit[1] = querySplit[1].split(',')[0]
                                    }
                                    else {
                                        querySplit = split[1].split('=')
                                    }

                                    filterObj.where[querySplit[0].trim()] = querySplit[1].trim()
                                    promises.push(Models[table.model].findOne(filterObj))
                                } else if (table.tableName === 'callresults') {
                                    var querySplit = split[1].split('=')
                                    if (split[1].indexOf('createdAt') > -1) {
                                        nonPromiseResults.push({
                                            table: 'DateTime',
                                            operator: querySplit[0][querySplit[0].length - 1],
                                            value: moment(querySplit[1].trim().replace("'", "")).format('MMM-DD-YYYY HH:mm:ss')
                                        })
                                    }
                                }
                            }
                        }
                    })
                }
            } else {

            }

            //hack hack hack
            if (_.where(nonPromiseResults, {
                table: 'DateTime'
            }).length === 2) {
                var index
                var foundFilter = false
                for (var i = 0; i < nonPromiseResults.length; i++) {
                    var filter = nonPromiseResults[i]
                    if (filter.table == 'DateTime') {
                        delete filter.operator
                        if (!foundFilter) {
                            foundFilter = filter
                            foundFilter.value = 'DateTime between ' + foundFilter.value + ' and '
                        } else {
                            foundFilter.value += filter.value
                            index = i
                        }
                    }
                }
                if (index !== undefined) {
                    nonPromiseResults.splice(index, 1)
                }
            }

            Promise.all(promises)
                .then(results => {
                    results.forEach((result, index) => {
                        promiseResults[index].value = result.name
                    })
                    var combined = promiseResults.concat(nonPromiseResults)
                    resolve(combined.map(filter => {
                        if (filter.operator === '=') {
                            return filter.table + ': ' + filter.value
                        } else if (filter.operator) {
                            return filter.table + ' ' + filter.operator + ' ' + filter.value
                        } else {
                            return filter.value
                        }
                    }))
                })
                .catch(reject)
        })

    }


    return {
        exportToExcel: exportToExcel,
        exportMultipleToExcel: exportMultipleToExcel,
        exportMultipleDynamicToExcel
    }
}