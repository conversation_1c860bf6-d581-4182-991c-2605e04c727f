var _ = require('underscore')
var sequelize = require('sequelize')

module.exports = function (Models, db) {

	var getResultFromDefinition = function (definition, limit) {
		if (definition.rawQuery) {
			return db.query(definition.rawQuery.replace(/{{whereClause}}/g, (definition.whereClause || '1')), { type: sequelize.QueryTypes.SELECT })
		}
		else {
			var query = generateQueryObj(definition.primaryModule)

			if (definition.groupBy && definition.groupBy.length)
				query.group = generateGroupByObj(definition.groupBy)


			query.limit = limit || 100000

			return Models[definition.primaryModule.name].findAll(query)
		}
	}

	var generateQueryObj = function (moduleDefinition) {
		var attrs = ['id'],
			groupByClause = [],
			whereClause = { $or: [] }

		if (moduleDefinition.fields) {
			moduleDefinition.fields.forEach(function (field) {
				attrs.push(field.operation ? ([sequelize.fn(field.operation, sequelize.col(field.fieldName)), (field.operation + '_' + field.fieldName)]) : field.fieldName)
			})
		}

		if (moduleDefinition.filters) {
			if (moduleDefinition.filters.and) {
				moduleDefinition.filters.and.forEach(function (filter) {
					if (!filter.operator || filter.operator === 'eq')
						whereClause[filter.fieldName] = filter.comparator
					else
						whereClause[filter.fieldName] = _.extend((whereClause[filter.fieldName] || {}), generateComparatorValue(filter))
				})
			}
			if (moduleDefinition.filters.or) {
				moduleDefinition.filters.or.forEach(function (filter) {
					var orClause = {}
					orClause[filter.fieldName] = generateComparatorValue(filter)
					whereClause.$or.push(orClause)
				})
			}
		}

		var includeObj = {
			attributes: attrs,
			where: whereClause,
			model: Models[moduleDefinition.name],
			include: (moduleDefinition.relatedModules ? moduleDefinition.relatedModules.map(generateQueryObj) : [])
		}

		if (moduleDefinition.as)
			includeObj.as = moduleDefinition.as

		return includeObj
	}

	var generateComparatorValue = function (filter) {
		var val = {}
		val[('$' + filter.operator)] = filter.comparator
		return val
	}

	var generateGroupByObj = function (groupByClauses) {
		return groupByClauses ? groupByClauses.map(function (grouping) {
			return [sequelize.col(grouping.moduleName.toLowerCase() + '.' + grouping.fieldName), grouping.sortOrder]
		}) : []
	}

	return {
		runReport: getResultFromDefinition
	}
}