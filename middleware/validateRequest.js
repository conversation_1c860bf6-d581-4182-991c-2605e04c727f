var jwt = require('jwt-simple')

module.exports = function (Models) {
	var validateUser = require('../controllers/auth')(Models).validateUser

	return {
		validate: function (req, res, next) {

			// When performing a cross domain request, you will recieve
			// a preflighted request first. This is to check if our the app
			// is safe. 

			// We skip the token outh for [OPTIONS] requests.
			// if (req.method == 'OPTIONS') next()

			// return next()

			var token = (req.body && req.body.access_token) || (req.query && req.query.access_token) || req.headers['x-access-token']
			var key = (req.body && req.body.x_key) || (req.query && req.query.x_key) || req.headers['x-key']

			if (token || key) {
				try {
					var decoded = jwt.decode(token, require('../config/secret.js')())

					if (decoded.exp <= Date.now()) {
						res.status(400)
						res.json({
							"status": 400,
							"message": "Token Expired"
						})
						return
					}

					// Override header `x-key` value with `username` stored in JWT token
					if (decoded.username) {
						key = decoded.username;
					}

					// Authorize the user to see if they can access our resources
					validateUser(key).then(function (dbUser) {
						if (dbUser) {
							if (dbUser && (dbUser.isAdmin || dbUser.isClientAdmin) || (req.url.indexOf('admin') < 0 && req.url.indexOf('/api/') >= 0)) {
								req.user = dbUser
								next()
							}
							else {
								res.status(403)
								res.json({
									"status": 403,
									"message": "Not Authorized"
								})
								return
							}
						}
						else {
							// No user with this name exists, respond back with a 401
							res.status(401)
							res.json({
								"status": 401,
								"message": "Invalid User"
							})
							return
						}
					}) // The key would be the logged in users username

				}
				catch (err) {
					res.status(500)
					res.json({
						"status": 500,
						"message": "Oops something went wrong",
						"error": err
					})
				}
			}
			else {
				res.status(401)
				res.json({
					"status": 401,
					"message": "Invalid Token or Key"
				})
				return
			}
		}
	}
}