# CSV-Only File Upload Implementation

## Overview
This implementation restricts the lead import process to only accept CSV files, preventing non-CSV files from causing the import process to get stuck.

## Changes Made

### 1. Frontend Validation (JavaScript)

#### Files Modified:
- `app/scripts/controllers/admin/campaign/admin.campaign.importleads.js`
- `app/scripts/controllers/admin/campaign/admin.campaign.wizard.js`

#### Implementation:
- Added `filters` array to FileUploader configuration
- Created `csvFilter` that validates both file extension and MIME type
- Added `onWhenAddingFileFailed` callback for user feedback

```javascript
filters: [
  {
    name: 'csvFilter',
    fn: function(item, options) {
      var type = '|' + item.type.slice(item.type.lastIndexOf('/') + 1) + '|';
      var name = '|' + item.name.slice(item.name.lastIndexOf('.') + 1).toLowerCase() + '|';
      return '|csv|'.indexOf(type) !== -1 || '|csv|'.indexOf(name) !== -1;
    }
  }
],
onWhenAddingFileFailed: function(item, filter, options) {
  if (filter.name === 'csvFilter') {
    alert('Only CSV files are allowed for lead import.');
  }
}
```

### 2. HTML Template Updates

#### Files Modified:
- `app/views/admin/campaign/admin.campaign.importleads.html`
- `app/views/admin/campaign/admin.campaign.wizard.html`

#### Implementation:
- Added `accept=".csv,text/csv"` attribute to file input elements
- Provides browser-level file type filtering

```html
<input type="file" id="leadsDb" name="leadsDb" accept=".csv,text/csv" nv-file-select uploader="uploader" />
```

### 3. Backend Validation (Node.js)

#### File Modified:
- `controllers/campaign.js`

#### Implementation:
- Added server-side validation in `/campaigns/:id/uploadleads` endpoint
- Validates file extension and MIME type
- Returns 400 error for non-CSV files

```javascript
// Validate file type - only allow CSV files
if (!req.files || !req.files.file) {
    return res.status(400).send({
        error: 'No file uploaded'
    })
}

var originalName = req.files.file.originalname || ''
var fileExtension = originalName.toLowerCase().split('.').pop()
var mimeType = req.files.file.mimetype || ''

// Check file extension and MIME type
if (fileExtension !== 'csv' && !mimeType.includes('csv') && mimeType !== 'text/csv' && mimeType !== 'application/csv') {
    return res.status(400).send({
        error: 'Only CSV files are allowed for lead import. Please upload a .csv file.'
    })
}
```

## Protection Levels

The implementation provides three layers of protection:

1. **Browser Level**: `accept` attribute guides users to select CSV files
2. **Client-Side**: JavaScript filters prevent non-CSV files from being queued
3. **Server-Side**: Backend validation ensures only CSV files are processed

## Testing

Use the provided test script to create sample files:

```bash
node test-csv-validation.js
```

This creates:
- `valid-leads.csv` (should be accepted)
- `invalid-leads.txt` (should be rejected)
- `invalid-leads.json` (should be rejected)

## Benefits

1. **Prevents Import Failures**: Non-CSV files can no longer cause the import process to get stuck
2. **User-Friendly**: Clear error messages guide users to upload correct file types
3. **Robust**: Multiple validation layers ensure comprehensive protection
4. **Maintainable**: Clean, well-documented code that's easy to understand and modify

## Future Considerations

- Consider adding support for other structured data formats if needed (e.g., TSV, Excel)
- Add file size validation if not already present
- Consider adding CSV structure validation (column headers, etc.)
