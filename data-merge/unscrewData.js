var moment = require('moment')
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zeno<PERSON>', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
			// ,logging: (() => {})
	})
	// sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87')
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

// function fixManualPaymentsStillInCollections () {
// 	sequelize.query(`SELECT 
// 		    invoices.leadId, invoices.campaignId
// 		FROM
// 		    invoices
// 		        LEFT JOIN
// 		    campaignleads ON campaignleads.leadid = invoices.leadid and campaignleads.campaignId = invoices.campaignId
// 		        LEFT JOIN
// 		    campaignstages ON campaignleads.currentCampaignStageId = campaignstages.id
// 		WHERE
// 		    invoices.amountRemaining <= 0
// 		    and campaignstages.name = 'Collections'
//     `).then(results => {
//     	results[0].forEach(result => {
//     		if (result.leadId && result.campaignId) {
//     			transitionLead(Models, result.leadId, null, result.campaignId)
//     		}
//     	})
//     	console.log(results[0].length)
//     })
// }

// var pledgeQuery = `SELECT 
// 					    cr.leadId
// 					FROM
// 					    callresults cr
// 					        LEFT JOIN
// 					    campaignstagedispositions csd ON csd.campaignstageid = cr.campaignstageid
// 					        LEFT JOIN
// 					    dispositions d ON d.id = csd.dispositionId
// 					WHERE
// 					    csd.transitioncutoffdate IS NOT NULL
// 					    	AND cr.callAttemptJson IS NOT NULL
// 					        AND cr.wrapup = d.name`;

// var refusalQuery = `SELECT 
// 						cr.id
// 					FROM
// 					    callresults cr
// 					        LEFT JOIN
// 					    campaignstagedispositions csd ON csd.campaignstageid = cr.campaignstageid
// 					        LEFT JOIN
// 					    campaignleads cl ON cr.leadid = cl.leadid
// 					        LEFT JOIN
// 					    dispositions d ON d.id = csd.dispositionId
// 					        LEFT JOIN
// 					    campaignstages cs ON cl.currentCampaignStageId = cs.id
// 					WHERE
// 					    csd.transitioncutoffdate IS NOT NULL
// 					        AND cr.callAttemptJson IS NOT NULL
// 					        AND cr.wrapup = 'Standard Refusal'
// 					        AND cr.wrapup = d.name
// 					        AND cr.leadId NOT IN (SELECT 
// 					            leadId
// 					        FROM
// 					            callresults
// 					        WHERE
// 					            grandTotal > 0)`

// var query = `SELECT 
//     cr.leadId, cr.campaignId
// FROM
//     callresults cr
//         LEFT JOIN
//     campaignleads cl ON cl.leadid = cr.leadid
// WHERE
//     cr.wrapup = 'Exception Refusal'
//         AND cl.currentCampaignStageId IS NOT NULL`

// function fixStandardRefusals(clientId, campaignId, secondAskStageId, fileLocation) {
// 	var clientRefs = []
// 	var blacklist = [2,8,12,27,66,68,69,70,71]
// 	var counter = 0
// 	var cutOffDate = '2016-07-31'

// 	function onNewRecord(record) {
// 		var refCode = record['REFUSAL CODE']
// 		var refDate = record['REFUSAL DATE']
// 		var clientRef = record['CLIENT DONOR/PATRON ID']

// 		if (refCode == 'NO - 1st No' || refCode == 'NO - Hang Up') {
// 			if (moment(refDate) < moment(cutOffDate)) {
// 				clientRefs.push({
// 					ref: clientRef,
// 					date: refDate
// 				})
// 			}
// 		}
// 	}

// 	function fixRefusals() {
// 		clientRefs.forEach(function(clientRef) {
// 			Models.Lead.findOne({
// 				where: {
// 					clientRef: clientRef.ref,
// 					clientId: clientId
// 				}
// 			}).then(function(lead) {
// 				if (lead) {
// 					Models.CallResult.findAll({
// 						where: {
// 							leadId: lead.id,
// 							campaignId: campaignId
// 						}
// 					}).then(function(callresults) {
// 						if (callresults && callresults.length) {
// 							var refusalCount = 0
// 							var hasExceptionOrPledge = false
// 							callresults.forEach(function(callres) {
// 								if (callres.wrapup === 'Exception Refusal' || callres.wrapup.indexOf('Pledge') === 0) {
// 									hasExceptionOrPledge = true
// 								} else if (callres.wrapup === 'Standard Refusal') {
// 									refusalCount++
// 									if (refusalCount == 1) {
// 										sequelize.query(`UPDATE callresults SET createdAt = '${clientRef.date} 12:00:00' WHERE id = ${callres.id}`)
// 										//update this callresults createdAt to be something else
// 									}
// 								}
// 							})

// 							if (refusalCount === 1 && !hasExceptionOrPledge && blacklist.indexOf(lead.tfSubSkillId)) {
// 								//lead has only 1 standard refusal and no exception or pledge so move to 2nd Ask
// 								transitionLead(Models, lead.id, secondAskStageId, campaignId)
// 								counter++
// 								console.log(counter)
// 							}
// 						}
// 					})
// 				}
// 			})
// 		})
// 	}


// 	parseCSVFile(fileLocation, true, onNewRecord, () => {}, fixRefusals)
// }

// fixStandardRefusals(9, 11, 63, 'C:\\users\\<USER>\\desktop\\STLS unfuck refusals.csv')

// function moveExceptionRefusals() {
// 	var count = 0
// 	sequelize.query(query, {
// 		type: Sequelize.QueryTypes.SELECT
// 	}).then(function (results) {
// 		results.forEach(function (result) {
// 			if (result && result.leadId && result.campaignId) {
// 				Models.CampaignLead.findOne({
// 					where: {
// 						leadId: result.leadId,
// 						campaignId: result.campaignId
// 					}
// 				}).then(function (campaignlead) {
// 					if (campaignlead.currentCampaignStageId) {
// 						//move this to null
// 						transitionLead(Models, campaignlead.leadId, null, campaignlead.campaignId)
// 					}
// 				})
// 			}
// 		})
// 	})
// }

// moveExceptionRefusals()

// function fixRefusalDates(clientId, fileLocation) {
// 	var clientRefs = []
// 	var count = 0

// 	function onNewRecord(record) {
// 		clientRefs.push({
// 			clientRef: record['CLIENT DONOR/PATRON ID'],
// 			refusalDate: record['REFUSAL DATE']
// 		})
// 	}

// 	function fixRefusals() {
// 		clientRefs.forEach(function (clientRef) {
// 			if (!clientRef.clientRef || !clientRef.refusalDate) return
// 			Models.Lead.findOne({
// 					where: {
// 						clientId: clientId,
// 						clientRef: clientRef.clientRef
// 					}
// 				})
// 				.then(function (lead) {
// 					if (lead) {
// 						if (!lead.dontContactUntil || moment(lead.dontContactUntil) < moment(clientRef.refusalDate).add(45, 'day')) {
// 							lead.updateAttributes({
// 								dontContactUntil: moment(clientRef.refusalDate).add(45, 'day').toDate()
// 							})
// 						}
// 					}
// 				})
// 		})
// 	}

// 	parseCSVFile(fileLocation, true, onNewRecord, () => {}, fixRefusals)
// }

// fixRefusalDates(8, 'D:\\Dualtone\\Resources\\Taylor\\Imports\\BCPA\\refusals.csv')


// function moveLeadsToCorrectStageBasedOnCutOffDates() {
// 	sequelize.query(refusalQuery, {
// 			type: Sequelize.QueryTypes.SELECT
// 		})
// 		.then(function (results) {
// 			var count = 0
// 			var crIds = _.pluck(results, 'id')

// 			Models.CallResult.findAll({
// 					where: {
// 						id: {
// 							$in: crIds
// 						}
// 					},
// 					include: [{
// 						model: Models.CampaignStage,
// 						include: [Models.Disposition]
// 					}]
// 				})
// 				.then(function (callresults) {
// 					callresults = JSON.parse(JSON.stringify(callresults))
// 					callresults.forEach(function (callres) {
// 						if (callres.campaignstage && callres.leadId) {
// 							var disposition = _.findWhere(callres.campaignstage.dispositions, {
// 								name: callres.wrapup
// 							});
// 							if (disposition && disposition.campaignstagedispositions && disposition.campaignstagedispositions.transitionCutOffDate) {
// 								var destinationStageId
// 								if (moment(callres.createdAt) > moment(disposition.campaignstagedispositions.transitionCutOffDate)) {
// 									destinationStageId = disposition.campaignstagedispositions.transitionCutOffDateDispositionId
// 								} else {
// 									destinationStageId = disposition.campaignstagedispositions.transitionToCampaignStageId
// 								}
// 								Models.CampaignLead.findOne({
// 									where: {
// 										leadId: callres.leadId
// 									}
// 								}).then(function (campaignLead) {
// 									campaignLead = JSON.parse(JSON.stringify(campaignLead))
// 									if (campaignLead.currentCampaignStageId !== destinationStageId) {
// 										// console.log(`moving lead ${callres.leadId} from stage ${campaignLead.currentCampaignStageId} to ${destinationStageId}`)
// 										transitionLead(Models, callres.leadId, destinationStageId, callres.campaignId)
// 										// count++
// 										// console.log(`${count} lead moved`)
// 									} else {
// 										// console.log(`lead ${callres.leadId} already in correcty stage - ${destinationStageId}`)
// 									}
// 								}).catch(function (err) {
// 									if (err && err.message) {
// 										console.log(err.message)
// 									} else {
// 										console.log('error')
// 									}
// 								})
// 							}
// 						}
// 					})
// 				})

// 		})
// 		.catch(function (err) {

// 		})
// }

// moveLeadsToCorrectStageBasedOnCutOffDates();

// function moveAllLeadsFromOneStageToADifferentStage(campaignId, sourceStageId, destinationStageId) {
// 	Models.CampaignLead.findAll({
// 		include: [Models.Lead],
// 		where: {
// 			currentCampaignStageId: sourceStageId
// 		}
// 	}).then(function (leads) {
// 		leads = _.pluck(leads, 'lead')
// 		leads = JSON.parse(JSON.stringify(leads))
// 		if (leads && leads.length) {
// 			leads.forEach(function (lead) {
// 				transitionLead(Models, lead.id, destinationStageId, campaignId)
// 			})
// 		}
// 	})
// }

// moveAllLeadsFromOneStageToADifferentStage(22, 133, null)

// function sortOutBlaklists(campaignId, currentStageId, blacklistedSkills) {
// 	Models.CampaignLead.findAll({
// 		include: [{
// 			model: Models.Lead,
// 			where: {
// 				tfSubSkillId: {
// 					$in: blacklistedSkills
// 				}
// 			},
// 			required: true
// 		}],
// 		where: {
// 			currentCampaignStageId: currentStageId
// 		}
// 	}).then(function (results) {
// 		if(results && results.length) {
// 			results.forEach(function (result) {
// 				transitionLead(Models, result.lead.id, null, campaignId)
// 			})
// 		}
// 	})
// }

// sortOutBlaklists(1, 1, [2,8,11,9,12,13,3,18])



// function fixBushnellThankyou () {
// 	Models.CampaignLead.findAll({
// 		where: {
// 			campaignId: 13,
// 			currentCampaignStageId: 77
// 		}
// 	})
// 	.then(function (campaignLeads) {
// 		if (campaignLeads && campaignLeads.length) {
// 			campaignLeads.forEach(function (campaignLead) {
// 				transitionLead(Models, campaignLead.leadId, 79, 13)
// 			})
// 		}
// 	})
// }

// fixBushnellThankyou()

// function fixBushnellRefusals () {
// 	var clientRefs = []

// 	function onNewRecord (record) {
// 		var clientRef = record['CLIENT DONOR/PATRON ID']
// 		if (clientRef) {
// 			var refusalDate = record['REFUSAL DATE']

// 			if (moment(refusalDate).isAfter('2016-03-31'))
// 				clientRefs.push(clientRef)
// 		}
// 	}

// 	function fixRefusals () {
// 		clientRefs.forEach(function (clientRef) {
// 			Models.Lead.findOne({
// 				where: {
// 					clientId: 8,
// 					clientRef: clientRef
// 				}
// 			})
// 			.then(function (lead) {
// 				transitionLead(Models, lead.id, null, 13)
// 				console.log(lead.id)
// 			})
// 		})
// 	}

// 	parseCSVFile('/usr/local/dualtone/data-merge-files/051816/BCPA/refusals.csv', true, onNewRecord, () => {}, fixRefusals)
// }

// fixBushnellRefusals()


// function fixInvoices () {
// 	sequelize.query("select invoices.id as 'invoiceId', invoices.leadId as 'wrongLeadId' from invoices left join leads on leads.id = invoices.leadId where leads.clientId <> invoices.clientId")
// 		.then(function (invoiceIds) {
// 			if (invoiceIds && invoiceIds[0] && invoiceIds[0].length) {
// 				invoiceIds[0].forEach(function (wrongInvoice) {
// 					Models.Invoice.findById(wrongInvoice.invoiceId).then(function (invoice) {
// 						Models.Lead.findById(wrongInvoice.wrongLeadId).then(function (wrongLead) {
// 							Models.Lead.findOne({
// 								where: {
// 									clientId: invoice.clientId,
// 									clientRef: wrongLead.clientRef
// 								}
// 							})
// 							.then(function (correctLead) {
// 								Models.CallResult.findById(invoice.callresultId).then(function (callResult) {
// 									callResult.leadId = correctLead.id
// 									callResult.save()
// 									console.log(`Updating callresult ID '${callResult.id}' to lead ID '${correctLead.id}'`)
// 								})

// 								invoice.leadId = correctLead.id
// 								invoice.save()
// 								console.log(`Updating invoice ID '${invoice.id}' to lead ID '${correctLead.id}'`)

// 								Models.CampaignStage.findOne({
// 									where: {
// 										campaignId: invoice.campaignId,
// 										name: (invoice.amountRemaining > 0 ? 'Collections' : '2nd Ask')
// 									}
// 								})
// 								.then(function (campaignStage) {
// 									console.log(`Transitioning lead ID '${correctLead.id}' to campaign stage '${campaignStage.name}'`)
// 									transitionLead(Models, correctLead.id, campaignStage.id, invoice.campaignId)
// 								})
// 							})
// 						})
// 					})
// 				})
// 			}
// 		})
// }

// function fixRefusals () {
// 	sequelize.query("select callresults.id as 'callResultId', callresults.wrapup as 'wrapup', callresults.leadId as 'wrongLeadId' from callresults left join leads on leads.id = callresults.leadId where leads.clientId <> callresults.clientId")
// 		.then(function (callResultIds) {
// 			if (callResultIds && callResultIds[0] && callResultIds[0].length) {
// 				callResultIds[0].forEach(function (wrongCallResult) {
// 					if (wrongCallResult.wrapup && wrongCallResult.wrapup.indexOf('Refusal') > -1) {
// 						Models.CallResult.findById(wrongCallResult.callResultId).then(function (callResult) {
// 							Models.Lead.findById(wrongCallResult.wrongLeadId).then(function (wrongLead) {
// 								Models.Lead.findOne({
// 									where: {
// 										clientId: callResult.clientId,
// 										clientRef: wrongLead.clientRef
// 									}
// 								})
// 								.then(function (correctLead) {
// 									callResult.leadId = correctLead.id
// 									callResult.save()
// 									console.log(`Updating callresult ID '${callResult.id}' to lead ID '${correctLead.id}'`)

// 									if (callResult.wrapup === 'Exception Refusal') {
// 										Models.CampaignStage.findOne({
// 											where: {
// 												campaignId: callResult.campaignId,
// 												name: '2nd Appeal'
// 											}
// 										})
// 										.then(function (campaignStage) {
// 											console.log(`Transitioning lead ID '${correctLead.id}' to campaign stage '${campaignStage.name}'`)
// 											transitionLead(Models, correctLead.id, campaignStage.id, callResult.campaignId)
// 										})
// 									}
// 									else {
// 										console.log(`Transitioning lead ID '${correctLead.id}' to campaign stage 'null'`)
// 										transitionLead(Models, correctLead.id, null, callResult.campaignId)
// 									}
// 								})
// 							})
// 						})
// 					}
// 				})
// 			}
// 		})
// }

// function fixCallbacks () {
// 	sequelize.query("select callbacks.id as 'callbackId', campaigns.clientId as 'clientId', callbacks.leadId as 'wrongLeadId' from callbacks left join leads on leads.id = callbacks.leadId left join campaigns on campaigns.id = callbacks.campaignId where leads.clientId <> campaigns.clientId")
// 		.then(function (callbackIds) {
// 			if (callbackIds && callbackIds[0] && callbackIds[0].length) {
// 				callbackIds[0].forEach(function (wrongCallback) {
// 					Models.Callback.findById(wrongCallback.callbackId).then(function (callback) {
// 						Models.Lead.findById(wrongCallback.wrongLeadId).then(function (wrongLead) {
// 							Models.Lead.findOne({
// 								where: {
// 									clientId: wrongCallback.clientId,
// 									clientRef: wrongLead.clientRef
// 								}
// 							})
// 							.then(function (correctLead) {
// 								callback.leadId = correctLead.id
// 								callback.save()
// 								console.log(`Updating callback ID '${callback.id}' to lead ID ${correctLead.id}`)
// 							})
// 						})
// 					})
// 				})
// 			}
// 		})
// }

// function undoLeadAudits () {
// 	Models.LeadAudit.findAll({
// 		where: {
// 			newValue: null,
// 			createdAt: {
// 				$gt: '2016-05-09 00:00:00'
// 			},
// 			userId: 1
// 		}
// 	}).then(function (leadAudits) {
// 		leadAudits.forEach(function (audit) {
// 			Models.Lead.findById(audit.leadId).then(function (wrongLead) {
// 				wrongLead[audit.field] = audit.previousValue
// 				wrongLead.save()
// 				audit.destroy()
// 			})
// 		})
// 	})
// }

// undoLeadAudits()