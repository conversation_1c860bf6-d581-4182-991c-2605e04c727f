var parseCsv = require('../utils/fileUtils').parseCSVFile
var Sequelize = require('sequelize'),
    sequelize = new Sequelize('kaos_backup2', 'zenozi', 'Lavabug87', {
        host: 'localhost',
        logging: false
    })
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var fs = require('fs')

var lines = []

function handleLine(line) {
    lines.push(line)
}

function processLines() {
    console.log(lines.length)

    var complete = 0
    console.log(`${complete}% complete`)

    fs.appendFile('../logs/unfuckDeletedLeads.csv', 'leadId,tfSkillId,tmSkillId,tfSubSkillId,tmSubSkillId\r\n', function(err) {
        if (err) console.log(err)

        var p = Promise.resolve()
        lines.forEach((line, index) => {
            (function() {
                p = p.then(() => {
                    var current = Math.floor((100 / lines.length) * index)
                    if (current > complete) {
                        complete = current
                        console.log(`${complete}% complete`)
                    }

                    return processLead(line)
                })
            }());
        })
    })
}

function processLead(line) {
    var arr = []
    return new Promise((resolve, reject) => {
        Models.Lead.findById(line.removedLead, { raw: true })
            .then(deletedLead => {
                if (!deletedLead) {
                    console.log('cannot find lead for ' + line.removedLead)
                    return resolve()
                }
                arr.push(deletedLead.tfSkillId)
                arr.push(deletedLead.tmSkillId)
                arr.push(deletedLead.tfSubSkillId)
                arr.push(deletedLead.tmSubSkillId)

                fs.appendFile('../logs/unfuckDeletedLeads.csv', `${line.legitLead},${arr.join(',')}\r\n`, function(err) {
                    if (err) console.log(err)
                    return resolve()
                })
            })
            .catch(err => {
                console.log(err)
                return resolve()
            })
    })
}

parseCsv('../logs/removeDupes-Tue Oct 31 2017.csv', true, handleLine, console.log, processLines)