var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

module.exports = function (filePath, campaignId) {
	var sales = []

	function onNewRecord(record) {
		var decisionMaker = record['DecisionMaker']
		var ccNumber = record['Creditcard#']
		var ccExpDate = record['ExpDate']
		var ccSecCode = record['3digit']
		var notes = record['Notes']
		var payDate = record['SaleDate']
		var createdAt = payDate + ' ' + record['SaleTime']
		var employeeCode = record['EmplCode']
		var grandTotal = record['GrandTotal']
		var saleAmount = record['Subtotal_sub'] || record['Subtotal_tix']

		sales.push({
			clientRef: record['CLIENT DONOR/PATRON ID'],
			wrapup: 'Sale',
			wrapupduration: 0,
			completed: true,
			notes: notes,
			campaignId: campaignId,
			paymentType: 'Credit Card',
			creditCardNumber: ccNumber,
			creditCardExpDate: ccExpDate,
			creditCardSecurityCode: ccSecCode,
			payDate: payDate,
			decisionMaker: decisionMaker,
			createdAt: createdAt,
			employeeCode: employeeCode,
			grandTotal: grandTotal,
			saleAmount: saleAmount
		})
	}

	function createPledges(linesRead) {
		Models.Agent.findAll().then(function (agents) {
			var getAgent = require('./getAgent')(agents).getAgent
			Models.Campaign.findById(campaignId, {
					include: [Models.CampaignType]
				})
				.then(function (campaign) {
					Models.CampaignStage.findOne({
						where: {
							campaignId: campaignId,
							name: '1st Appeal'
						},
						include: Models.Disposition
					}).then(function (firstAppealStage) {
						firstAppealStage = JSON.parse(JSON.stringify(firstAppealStage))

						Models.Skill.findAll().then(function (skills) {
							Models.SubSkill.findAll().then(function (subSkills) {
								sales.forEach(function (sale) {
									var skillTypePrefix = (campaign.campaigntype.name === 'Telefunding' ? 'tf' : 'tm')

									Models.Lead.findOne({
										where: {
											clientRef: pledge.clientRef,
											clientId: campaign.clientId
										}
									}).then(function (lead) {
										delete sale.clientRef
										sale.leadId = lead.id
										sale.skill = _.findWhere(skills, {
											id: lead[skillTypePrefix + 'SkillId']
										}).name
										sale.subSkill = _.findWhere(subSkills, {
											id: lead[skillTypePrefix + 'SubSkillId']
										}).name
										sale.campaignstageId = firstAppealStage.id
										sale.clientId = campaign.clientId

										var agent = getAgent(sale.employeeCode)
										if (agent) sale.agentId = agent.id

										Models.CallResult.create(sale).then(function (callResult) {
											var invoice = Models.Invoice.build()
											invoice.callresultId = callResult.id
											invoice.grandTotal = sale.grandTotal
											invoice.amountRemaining = 0
											invoice.requestCount = sale
											invoice.invoiceType = sale.paymentType
											invoice.clientId = sale.clientId
											invoice.campaignId = sale.campaignId
											invoice.leadId = lead.id
											invoice.sendInvoice = false
											invoice.deliveryMethod = sale.invoiceMethod
											invoice.dueDate = sale.payDate
											invoice.save()
										})

										var disposition = _.findWhere(firstAppealStage.dispositions, {
											name: 'Sale'
										})

										if (disposition && disposition.campaignstagedispositions) {
											if (disposition.campaignstagedispositions.transitionCutOffDate && moment(sale.createdAt) > moment(disposition.campaignstagedispositions.transitionCutOffDate)) {
												transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionCutOffDateDispositionId, campaignId)
											} else {
												transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionToCampaignStageId, campaignId)
											}
											lead.dontContactUntil = moment(sale.createdAt).add(disposition.campaignstagedispositions.dontContactLeadForHours, 'hours').toDate()
											lead.save()
										} else {
											console.log('WARNING: cannot find destination for leadId: ' + lead.id)
										}
									})
								})
							})
						})
					})
				})
		})

	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createPledges)
}