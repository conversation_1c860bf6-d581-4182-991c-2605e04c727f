// var mergeBadNumbers = require('./badNumbersImport')
// var mergeRefusals = require('./refusalsImport')
var mergePledges = require('./pledgesImport')
// var mergePayments = require('./paymentsImport')
// var mergeCallbacks = require('./callbacksImport')
// var updateAgentPayments = require('./agentsImport')

var campaignId = 5

var rootFolder = 'C:\\Users\\<USER>\\Desktop\\'

// mergeBadNumbers(rootFolder + 'bad numbers.csv', clientId)
mergePledges(rootFolder + 'pledges.csv', campaignId)
// mergePayments(rootFolder + 'payments.csv', clientId, campaignId, campaignStageId, hasThankyou, has2ndAppeal)
// mergeRefusals(rootFolder + 'refusals.csv', clientId, campaignId, campaignStageId, has2ndAsk)
// mergeCallbacks(rootFolder + 'callbacks.csv', clientId, campaignId, campaignStageId)
// updateAgentPayments(rootFolder + 'pledges.csv', campaignId)