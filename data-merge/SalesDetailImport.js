var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

module.exports = function (filePath, campaignId) {
	var sales = []

	function onNewRecord(record) {
		var decisionMaker = record['DecisionMaker']
		var ccNumber = record['Creditcard#']
		var ccExpDate = record['ExpDate']
		var ccSecCode = record['3digit']
		var notes = record['Notes']
		var payDate = record['SaleDate']
		var createdAt = payDate + ' ' + record['SaleTime']
		var employeeCode = record['EmplCode']

		sales.push({
			clientRef: record['CLIENT DONOR/PATRON ID'],
			campaignId: campaignId,
			seatCount: record['NoSeats'],
			productCode: record['ProductCode'],
			series: record['Series'],
			dayOfWeek: record['DayofWeek'],
			seats: record['Seats'],
			costEach: record['CostEach'],
			feePerTicket: record['FeePerTicket'],
			subtotal: record['Subtotal'],
			tix_sub: record['Tix_Sub'],
			salesTax: record['SalesTax']
		})
	}

	function createPledges(linesRead) {
		Models.Campaign.findById(campaignId)
			.then(function (campaign) {
				Models.Skill.findAll().then(function (skills) {
					Models.SubSkill.findAll().then(function (subSkills) {
						sales.forEach(function (sale) {
							Models.Lead.findOne({
								where: {
									clientRef: sale.clientRef,
									clientId: campaign.clientId
								}
							}).then(function (lead) {
								Models.CallResult.findOne({
									leadId: lead.id,
									wrapup: 'Sale'
								}).then(function (callresult) {
									delete sale.clientRef
									sale.leadId = lead.id
									sale.campaignStageId = firstAppealStage.id
									sale.clientId = campaign.clientId
									sale.callresultId = callresult.id
									sale.createdAt = callresult.createdAt
									Models.Sale.create(sale)
								})
								
							})
						})
					})
				})
			})

	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createPledges)
}