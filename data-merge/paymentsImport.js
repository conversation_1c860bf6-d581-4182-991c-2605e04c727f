var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

module.exports = function (filePath, campaignId) {
	var payments = []

	function onNewRecord(record) {
		payments.push(record)
	}

	function createPayments(linesRead) {
		Models.Campaign.findById(campaignId, {
				include: [Models.CampaignType]
			})
			.then(function (campaign) {
				Models.CampaignStage.findOne({
						where: {
							campaignId: campaignId,
							name: 'Collections'
						},
						include: Models.Disposition
					})
					.then(function (collectionsStage) {
						collectionsStage = JSON.parse(JSON.stringify(collectionsStage))
						Models.Skill.findAll().then(function (skills) {
							Models.SubSkill.findAll().then(function (subSkills) {
								payments.forEach(function (payment) {
									var skillTypePrefix = (campaign.campaigntype.name === 'Telefunding' ? 'tf' : 'tm')

									var decisionMaker = payment['DecisionMaker']
									var payType = payment['PaymentType']
									var ccNumber = payment['CCNumber']
									var ccExpDate = payment['CCExpDate']
									var ccSecCode = payment['CC3digit']
									var notes = payment['Notes']
									var payAmount = payment['PaymentAmount']
									var clientRef = payment['CLIENT DONOR/PATRON ID']
									var createdAt = payment['Date']

									Models.Lead.findOne({
										where: {
											clientRef: clientRef,
											clientId: campaign.clientId
										},
										include: [Models.Invoice]
									}).then(function (lead) {
										if (lead.invoices && lead.invoices.length) {
											if (lead.invoices[0].amountRemaining > 0) {
												if (payment.payType !== 'CR') {
													Models.CallResult.create({
														clientRef: payment['CLIENT DONOR/PATRON ID'],
														wrapup: 'Invoice Payment',
														wrapupduration: 0,
														completed: true,
														notes: payment['comments'],
														campaignId: campaignId,
														clientId: campaign.clientId,
														campaignstageId: collectionsStage.id,
														payAmount: payAmount,
														paymentType: 'Credit Card',
														creditCardNumber: ccNumber,
														creditCardExpDate: ccExpDate,
														creditCardSecurityCode: ccSecCode,
														decisionMaker: decisionMaker,
														createdAt: createdAt
													})
												}
												var invoice = lead.invoices[0]

												invoice.amountRemaining -= payAmount
												if (invoice.amountRemaining <= 0) {
													var disposition = _.findWhere(collectionsStage.dispositions, {
														name: 'Invoice Payment'
													})

													if (disposition && disposition.campaignstagedispositions) {
														if (disposition.campaignstagedispositions.transitionCutOffDate && moment(createdAt) > moment(disposition.campaignstagedispositions.transitionCutOffDate)) {
															transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionCutOffDateDispositionId, campaignId)
														} else {
															transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionToCampaignStageId, campaignId)
														}
														lead.dontContactUntil = moment(createdAt).add(disposition.campaignstagedispositions.dontContactLeadForHours, 'hours').toDate()
														lead.save()
													} else {
														console.log('WARNING: cannot find destination for leadId: ' + lead.id)
													}
												}

												invoice.save()
											}
										}
									})
								})
							})
						})
					})
			})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createPayments)
}