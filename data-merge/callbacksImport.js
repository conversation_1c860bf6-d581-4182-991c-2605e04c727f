var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

module.exports = function (filePath, clientId, campaignId) {
	var callbacks = []

	function getHourFormatted(hour) {
		return (parseInt(hour) > 9) ? hour : ('0' + hour)
	}

	function onNewRecord(record) {
		callbacks.push({
			clientRef: record['CLIENT DONOR/PATRON ID'],
			callbackDate: record['CallbackDate'],
			employee: record['CallerAssignment'],
			hour: record['MilitaryTime']
		})
	}

	function createCallbacks(linesRead) {
		Models.Campaign.findById(campaignId).then(function (campaign) {
			callbacks.forEach(function (callback) {
				Models.Agent.findAll().then(function (agents) {
					var getAgent = require('./getAgent')(agents).getAgent
					Models.Lead.findOne({
							where: {
								clientRef: callback.clientRef,
								clientId: clientId
							}
						})
						.then(function (lead) {
							if (!lead) {
								console.log('LEAD DOES NOT EXIST')
								return
							}
							var callbackObj = Models.Callback.build()

							if (moment(callback.callbackDate).isBefore(moment())) {
								callbackObj.startDateTime = '2000-01-01 00:00:00'
								callbackObj.endDateTime = '2099-01-01 00:00:00'
							} else {
								callbackObj.startDateTime =
									moment(callback.callbackDate + ' ' + getHourFormatted(callback.hour) + ':00:00').toDate()

								callbackObj.endDateTime =
									moment(callback.callbackDate + ' ' + getHourFormatted(parseInt(callback.hour) + 2) + ':00:00').toDate()
							}

							var agent = getAgent(agents, callback.employee)

							if (agent)
								callbackObj.agentId = agent.id

							callbackObj.leadId = lead.id
							callbackObj.campaignId = campaignId

							callbackObj.callAttemptJson = JSON.stringify({
								startTime: '05:00:00',
								endTime: '04:59:59',
								monday: 1,
								tuesday: 1,
								wednesday: 1,
								thursday: 1,
								friday: 1,
								saturday: 1,
								sunday: 1,
								isCallback: true,
								randomSelector: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaaa',
								campaignstageId: campaign.initialCampaignStageId,
								campaignId: campaignId,
								leadId: lead.id
							})

							callbackObj.save()
						})
				})
			})
		})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createCallbacks)
}