var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zeno<PERSON>', 'Lavabug87')
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var statsAPI = require('../database/redisAPI')('campaign')

//get all callresults
Models.CallResult.findAll()
	.then(callresults => {
		for (var i = 0; i < callresults.length; i++) {
			var cr = JSON.parse(JSON.stringify(callresults[i]))
			if (cr.giftAmount || cr.saleAmount) {
				if (cr.giftAmount) {
					statsAPI.incrementBy(cr.campaignId, 'totalGiftAmount', cr.giftAmount)
					statsAPI.increment(cr.campaignId, 'totalGiftCount')
					if (cr.skill == "Renewal" || cr.skill == "Lapsed") {
						statsAPI.incrementBy(cr.campaignId, 'renewalGiftAmount', cr.giftAmount)
						statsAPI.increment(cr.campaignId, 'renewalGiftCount')
					} else {
						statsAPI.incrementBy(cr.campaignId, 'acquisitionGiftAmount', cr.giftAmount)
						statsAPI.increment(cr.campaignId, 'acquisitionGiftCount')
					}
				}
				if (cr.saleAmount) {
					statsAPI.incrementBy(cr.campaignId, 'totalSaleAmount', cr.saleAmount)
					statsAPI.increment(cr.campaignId, 'totalSaleCount')
					if (cr.skill == "Renewal" || cr.skill == "Lapsed") {
						statsAPI.incrementBy(cr.campaignId, 'renewalSaleAmount', cr.saleAmount)
						statsAPI.increment(cr.campaignId, 'renewalSaleCount')
					} else {
						statsAPI.incrementBy(cr.campaignId, 'acquisitionSaleAmount', cr.saleAmount)
						statsAPI.increment(cr.campaignId, 'acquisitionSaleCount')
					}
				}
				if (!cr.declineBenefits) statsAPI.increment(cr.campaignId, 'addonCount')
				if (cr.paymentType == 'Credit Card') statsAPI.increment(cr.campaignId, 'ccCount')
			}
			statsAPI.increment(cr.campaignId, 'callCount')
		}
	})