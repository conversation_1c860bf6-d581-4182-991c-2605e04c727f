var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

module.exports = function (filePath, clientId) {
	var badNumberClientRefs = []

	function onNewRecord(record) {
		var clientRef = record['CLIENT DONOR/PATRON ID']
		if (clientRef)
			badNumberClientRefs.push(clientRef)
	}

	function done(linesRead) {
		flagBadNumbers()
	}

	function createAudit(leadId, field, previousValue) {
		var audit = {}
		audit.field = field
		audit.previousValue = previousValue
		audit.newValue = null
		audit.leadId = leadId
		audit.userId = 1

		Models.LeadAudit.create(audit)
	}

	function flagBadNumbers() {
		badNumberClientRefs.forEach(function (clientRef) {
			Models.Lead.findOne({
					where: {
						clientRef: clientRef,
						clientId: clientId
					}
				})
				.then(function (lead) {
					if (lead) {
						if (lead.phone_home) {
							createAudit(lead.id, 'phone_home', lead.phone_home)
							lead.phone_home = null
						}
						if (lead.phone_mobile) {
							createAudit(lead.id, 'phone_mobile', lead.phone_mobile)
							lead.phone_mobile = null
						}
						if (lead.phone_work) {
							createAudit(lead.id, 'phone_work', lead.phone_work)
							lead.phone_work = null
						}
						if (lead.phone_workmobile) {
							createAudit(lead.id, 'phone_workmobile', lead.phone_workmobile)
							lead.phone_workmobile = null
						}
						lead.save()
					}
				})
		})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, done)
}