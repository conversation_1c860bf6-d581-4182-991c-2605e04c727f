var APP_SETTINGS = require('../config/constants');
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var fs = require('fs')

var campaignId = 90;
var clientId = 26;
var createdAt = '2020-06-23 00:00:00';
var lessThan = '2020-06-24 00:00:00';
var offset = 0;
var leadsIdsToDelete = [];
var campaignLeadMapping = []

// fs.writeFileSync('fixLeads.csv', 'oldClientRef,newClientRef,oldLeadId,newLeadId\r\n', {
//     encoding: 'utf8'
// })

function loop() {
    var campaignLeads, newLeads, oldLeads
    return Models.CampaignLead.findAll({
        where: {
            campaignId,
            createdAt: {
                $gt: createdAt,
                $lt: lessThan
            }
        },
        limit: 500,
        offset
    }).then(_campaignLeads => {
        campaignLeads = _campaignLeads
        if (!campaignLeads || !campaignLeads.length) return
        offset += campaignLeads.length
        var leadIds = campaignLeads.map(cl => cl.leadId)
        return Models.Lead.findAll({
            where: {
                id: {
                    $in: leadIds
                }
            },
            logging: false
        })
    }).then(leads => {
        newLeads = leads
        if (!newLeads || !newLeads.length) return
        var clientRefs = newLeads.map(l => l.clientRef)
        var newLeadIds = newLeads.map(l => l.id)
        return Models.Lead.findAll({
            where: {
                clientId,
                clientRef: {
                    $in: clientRefs
                },
                id: {
                    $notIn: newLeadIds
                }
            },
            logging: false
        })
    }).then(leads => {
        oldLeads = leads
        if (!oldLeads || !oldLeads.length) return
        var leadIndex = 0;
        function leadLoop() {
            var promise = Promise.resolve()
            var newLead = newLeads[leadIndex];
            if (!newLead) return promise;
            leadIndex++;
            var oldLead = oldLeads.find(l => l.clientRef == newLead.clientRef)
            if (oldLead) {
                promise = updateLeadResults(newLead, oldLead)
            }
            return promise.then(leadLoop)
        }
        return leadLoop()
    }).then(() => {
        if (campaignLeads && campaignLeads.length) {
            campaignLeads = null
            newLeads = null
            oldLeads = null
            return loop()
        }
    })
}

function updateLeadResults(newLead, oldLead) {
    console.log('updating', newLead.id, 'to', oldLead.id)

    leadsIdsToDelete.push(newLead.id)
    campaignLeadMapping.push({ oldLead: oldLead.id, newLead: newLead.id })

    // fs.appendFileSync('fixLeads.csv', `${oldLead.clientRef},${newLead.clientRef},${oldLead.id},${newLead.id}\r\n`, {
    //     encoding: 'utf8'
    // })

    // return Promise.resolve()

    var updateObj = { leadId: oldLead.id }
    var whereObj = { where: { leadId: newLead.id } }
    return Models.CallResult.update(updateObj, whereObj).then(() => {
        return Models.Invoice.update(updateObj, whereObj)
    }).then(() => {
        return Models.PaymentLog.update(updateObj, whereObj)
    }).then(() => {
        return Models.PaymentLogHistory.update(updateObj, whereObj)
    }).then(() => {
        return Models.Suppression.update(updateObj, whereObj)
    }).then(() => {
        return Models.CallAttempt.update(updateObj, whereObj)
    }).then(() => {
        return Models.Callback.update(updateObj, whereObj)
    }).then(() => {
        return Models.CallRecord.update(updateObj, whereObj)
    }).then(() => {
        return Models.CardToken.findOne({
            where: {
                leadId: newLead.id
            }
        }).then(token => {
            if (token) {
                return Models.CardToken.destroy({
                    where: {
                        leadId: oldLead.id
                    }
                }).then(() => {
                    return token.update({
                        leadId: oldLead.id
                    })
                })
            }
        })
    }).then(() => {
        var lead = newLead.toJSON()
        delete lead.id
        delete lead.clientRef
        if (lead.dontContactUntil === null) delete lead.dontContactUntil
        if (lead.lastAgent === null) delete lead.lastAgent
        if (lead.tfSkillId === null) delete lead.tfSkillId
        if (lead.tfSubSkillId === null) delete lead.tfSubSkillId
        if (lead.tmSkillId === null) delete lead.tmSkillId
        if (lead.tmSubSkillId === null) delete lead.tmSubSkillId
        return oldLead.update(lead)
    })
}

loop().then(() => {
    if (leadsIdsToDelete && leadsIdsToDelete.length) {
        // return console.log('deleting', leadsIdsToDelete.length)
        return loopCampaignLeads().then(() => {
            console.log('finished updating campaignleads')
            return loopDeleteLeads()
        }).then(() => {
            console.log('finished deleting')
        })
    }
    console.log('no leads found to delete')
}).catch(console.log)

function loopDeleteLeads() {
    var chunks = []
    var chunkSize = 1000
    while (leadsIdsToDelete.length) {
        chunks.push(leadsIdsToDelete.splice(0, chunkSize))
    }
    function theLoop() {
        if (!chunks.length) return Promise.resolve()
        var chunk = chunks.splice(0, 1)[0]
        console.log('deleting', chunk.length)
        return Models.Lead.destroy({
            where: {
                id: {
                    $in: chunk
                }
            }
        }).then(theLoop)
    }
    return theLoop()
}

function loopCampaignLeads() {
    var clIndex = 0;
    function theLoop() {
        var mapping = campaignLeadMapping[clIndex];
        if (!mapping) return Promise.resolve()
        clIndex++;
        return Models.CampaignLead.findOne({
            where: {
                leadId: mapping.oldLead,
                campaignId
            }
        }).then(existing => {
            if (existing) {
                // there is a correct campaign lead already so just update it to have the latest stage and history
                return Models.CampaignLead.findOne({
                    where: {
                        leadId: mapping.newLead,
                        campaignId
                    }
                }).then(newOne => {
                    if (newOne && newOne.currentCampaignStageId !== existing.currentCampaignStageId) {
                        // only update if the stage is a higher id (meaning not initial stage) or the current stage is null
                        if (existing.currentCampaignStageId && (!newOne.currentCampaignStageId || newOne.currentCampaignStageId > existing.currentCampaignStageId)) {
                            return existing.update({
                                currentCampaignStageId: newOne.currentCampaignStageId,
                                campaignStageHistory: newOne.campaignStageHistory
                            }).then(() => {
                                return newOne.destroy()
                            })
                        }
                    }

                    if (newOne) {
                        return newOne.destroy()
                    }
                })
            } else {
                return Models.CampaignLead.update({
                    leadId: mapping.oldLead
                }, {
                    where: {
                        campaignId,
                        leadId: mapping.newLead
                    }
                })
            }
        }).then(theLoop)
    }
    return theLoop()
}