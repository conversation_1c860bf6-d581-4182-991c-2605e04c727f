var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zeno<PERSON>', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

module.exports = function (filePath, campaignId) {
	var refusals = []
	var badNumberClientRefs = []

	var exceptionRefusals = [
		'AG - Already Gave',
		'Deceased',
		'DNC - Do Not Call',
		'DUPE - Duplicate Record Found',
		'Moved',
		'REM - Removed per Client',
		'ASUB - Already Subscribed',
		'REM - Removed per Client'
	]

	function onNewRecord(record) {
		var clientRef = record['CLIENT DONOR/PATRON ID']
		if (clientRef) {
			var refusalCode = record['REFUSAL CODE']
			var refusalNotes = record['RefusalComments']
			var refusalType = ((exceptionRefusals.indexOf(refusalCode) > -1) ? 'Exception Refusal' : 'Standard Refusal')
			var decisionMaker = record['RefusalDecisionMaker']
			var createdAt = record['REFUSAL DATE']

			refusals.push({
				clientRef: clientRef,
				refusalReason: refusalCode,
				decisionMaker: decisionMaker,
				notes: refusalNotes,
				type: refusalType
			})
		}
	}

	function createRefusals(linesRead) {
		Models.Campaign.findById(campaignId, {
				include: [Models.CampaignType]
			})
			.then(function (campaign) {
				Models.CampaignStage.findOne({
					where: {
						campaignId: campaignId,
						name: '1st Appeal'
					}
				}).then(function (firstAppealStage) {
					firstAppealStage = JSON.parse(JSON.stringify(firstAppealStage))
					Models.Skill.findAll().then(function (skills) {
						Models.SubSkill.findAll().then(function (subSkills) {
							var skillTypePrefix = (campaign.campaigntype.name === 'Telefunding' ? 'tf' : 'tm')

							refusals.forEach(function (refusal) {
								Models.Lead.findOne({
										where: {
											clientRef: refusal.clientRef,
											clientId: campaign.clientId
										},
										include: []
									})
									.then(function (lead) {
										if (lead) {
											Models.CallResult.create({
												campaignId: campaignId,
												clientId: campaign.clientId,
												campaignstageId: firstAppealStage.id,
												wrapup: refusal.type,
												wrapupduration: 0,
												completed: true,
												notes: refusal.notes,
												decisionMaker: refusal.decisionMaker,
												refusalReason: refusal.refusalReason,
												skill: _.findWhere(skills, {
													id: lead[skillTypePrefix + 'SkillId']
												}).name,
												subSkill: _.findWhere(subSkills, {
													id: lead[skillTypePrefix + 'SubSkillId']
												}).name,
												leadId: lead.id
											})

											var disposition = _.findWhere(firstAppealStage.dispositions, {
												name: refusal.type
											})

											if (disposition && disposition.campaignstagedispositions) {
												if (disposition.campaignstagedispositions.transitionCutOffDate && moment(refusal.createdAt) > moment(disposition.campaignstagedispositions.transitionCutOffDate)) {
													transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionCutOffDateDispositionId, campaignId)
												} else {
													transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionToCampaignStageId, campaignId)
												}
												lead.dontContactUntil = moment(refusal.createdAt).add(disposition.campaignstagedispositions.dontContactLeadForHours, 'hours').toDate()
												lead.save()
											} else {
												console.log('WARNING: cannot find destination for leadId: ' + lead.id)
											}
										}
									})
							})
						})
					})
				})
			})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createRefusals)
}