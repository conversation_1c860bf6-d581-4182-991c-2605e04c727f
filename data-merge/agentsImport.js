var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

module.exports = function (filePath, campaignId) {
	var updates = []

	function onNewRecord(record) {
		updates.push({
			agentCode: record['EmployeeCode'],
			giftAmount: record['PledgeAmount'],
			clientRef: record['CLIENT DONOR/PATRON ID']
		})
	}
	
	function updatePledges(linesRead) {
		Models.Agent.findAll().then((agents) => {
			var getAgent = require('./getAgent')(agents).getAgent
			updates.forEach(update => {
				var agent = getAgent(agents, update.agentCode)
				if (agent) {
					Models.CallResult.findOne({
						where: {
							campaignId: campaignId,
							giftAmount: update.giftAmount
						},
						include: [{
							model: Models.Lead,
							where: {
								clientRef: update.clientRef
							},
							required: true
						}]
					}).then(callresult => {
						if(callresult) {
							callresult.updateAttributes({
								agentId: agent.dataValues.id
							})
						}
					}).catch(err => {

					})
				}
			})
		})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, updatePledges)
}
