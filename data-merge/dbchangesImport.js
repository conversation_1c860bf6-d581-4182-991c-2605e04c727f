var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87')
	// sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87')
var Models = require('../database/schema')(sequelize, Sequelize)

module.exports = function (filePath, clientId) {
	var updates = []

	function onNewRecord(record) {
		var update = {
			clientRef: record['CLIENT DONOR/PATRON ID']
		}

		if (record['DBC COMPANY NAME']) update.companyName = record['DBC COMPANY NAME']
		if (record['DBC FIRST NAME']) update.first_name = record['DBC FIRST NAME']
		if (record['DBC LAST NAME']) update.last_name = record['DBC LAST NAME']
		if (record['DBC SPOUSE NAME']) update.spouse_name = record['DBC SPOUSE NAME']
		if (record['DBC ADDRESS1']) update.address1 = record['DBC ADDRESS1']
		if (record['DBC ADDRESS2']) update.address2 = record['DBC ADDRESS2']
		if (record['DBC ADDRESS3']) update.address3 = record['DBC ADDRESS3']
		if (record['DBC CITY']) update.city = record['DBC CITY']
		if (record['DBC STATE']) update.state = record['DBC STATE']
		if (record['DBC ZIP']) update.zip = record['DBC ZIP']
		if (record['DBC HOME PHONE1']) update.phone_home = record['DBC HOME PHONE1']
		if (record['DBC HOME PHONE2']) update.phone_mobile = record['DBC HOME PHONE2']
		if (record['DBC HOME PHONE1']) update.phone_work = record['DBC HOME PHONE1']
		if (record['DBC HOME PHONE2']) update.phone_workmobile = record['DBC HOME PHONE2']
		if (record['DBC EMAIL']) update.email = record['DBC EMAIL']

		updates.push(update)
	}

	function updatePledges(linesRead) {
		updates.forEach(update => {

			Models.Lead.findOne({
				where: {
					clientRef: update.clientRef,
					clientId: clientId
				}
			}).then(lead => {
				if (lead) {
					lead.updateAttributes(update)
				}
			}).catch(err => {
				console.log(err)
			})
		})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, updatePledges)
}