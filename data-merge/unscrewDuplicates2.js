var parseCsv = require('../utils/fileUtils').parseCSVFile
var Sequelize = require('sequelize'),
    sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
        host: 'kaos-db.dualtone.io',
        logging: false
    })
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var fs = require('fs')

var lines = []

function processLeads() {
    var complete = 0
    console.log(`${complete}% complete`)

    var p = Promise.resolve()
    lines.forEach((line, index) => {
        (function() {
            p = p.then(() => {
                var current = Math.floor((100 / lines.length) * index)
                if (current > complete) {
                    complete = current
                    console.log(`${complete}% complete`)
                }

                var updateObj = {}
                for (var prop in line) {
                    if (prop !== 'leadId' && line[prop]) {
                        updateObj[prop] = line[prop]
                    }
                }

                return Models.Lead.update(updateObj, {
                    where: {
                        id: line.leadId
                    }
                })
            })
        }());
    })
}

parseCsv('../logs/unfuckDeletedLeads.csv', true, lines.push, console.log, processLeads)