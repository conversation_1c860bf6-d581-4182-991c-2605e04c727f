var Sequelize = require('sequelize')
var sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
    host: 'kaos-db.dualtone.io',
    logging: console.log
})
var fileUtils = require('../utils/fileUtils')
var Models = require('../database/schema')(sequelize, Sequelize)
var DRY_RUN = true
var campaignId = parseInt(process.argv[2])
var filePath = process.argv[3]

if (!campaignId || !filePath) {
    console.log('Usage: node fixCampaignLeadTypes.js <campaignId> <filePath>')
    process.exit(1)
}

var parseCSVFile = fileUtils.parseCSVFile

parseCSVFile(filePath, true, onNewRecord, () => {}, onComplete)

var records = []

function onNewRecord (record) {
    records.push({
        leadId: record['KAOS ID'],
        leadType: record['TF Lead Type'],
        reportingGroup: record['TF Reporting Group']
    })
}

function onComplete () {
    var delay = 1

    function next (index) {
        var record = records[index]

        if (!record || !record.leadId) {
            return setTimeout(function () {
                next(index + 1)
            }, delay)
        }

        if (DRY_RUN) {
            console.log(index, 'Updating record with leadId', record.leadId, 'campaignId', campaignId, 'to skill', record.reportingGroup, 'and subSkill', record.leadType)
            return setTimeout(function () {
                next(index + 1)
            }, delay)
        }

        Models.CampaignLead.update({
            skill: record.reportingGroup,
            subSkill: record.leadType
        }, {
            where: {
                leadId: record.leadId,
                campaignId: campaignId
            }
        })
        .then(function () {
            setTimeout(function () {
                next(index + 1)
            }, delay)
        })
        .catch(function (err) {
            console.error(err)
            
            setTimeout(function () {
                next(index + 1)
            }, delay)
        })
    }

    next(0)
}