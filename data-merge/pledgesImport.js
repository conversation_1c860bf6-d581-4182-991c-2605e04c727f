var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var moment = require('moment')
var _ = require('underscore')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var transitionLead = require('./transitionLead')

module.exports = function (filePath, campaignId) {
	var pledges = []

	function onNewRecord(record) {
		var decisionMaker = record['DecisionMaker']
		var payType = record['PayType'] === 'Credit Card' ? 'Credit Card' : 'Invoice'
		var ccType = record['CCType']
		var ccNumber = record['CCNumber']
		var ccExpDate = record['CCExpDate']
		var ccSecCode = record['CC3digit']
		var installmentNotes = record['Installments']
		var requiresFollowUp = (record['ImmediateFollowUp'].toLowerCase() === 'yes')
		var giftMatchingCompany = record['MatchingGiftCompany']
		var declineBenefits = (record['PremiumDecline'] === 'Y')
		var invoiceMethod = 'Paper'
		var notes = record['Notes']
		var freeTickets = record['FreeTickets']
		var payDate = record['PledgeDate']
		var giftAmount = record['PledgeAmount']
		var createdAt = payDate + ' ' + record['PledgeTime']
		var employeeCode = record['EmployeeCode']

		if (payType === 'Invoice')
			payDate = moment(payDate).add(30, 'days').format('YYYY-MM-DD')

		pledges.push({
			clientRef: record['CLIENT DONOR/PATRON ID'],
			wrapup: 'Pledge ' + payType,
			wrapupduration: 0,
			completed: true,
			notes: notes,
			campaignId: campaignId,
			giftAmount: giftAmount,
			freeTickets: freeTickets,
			paymentType: payType,
			creditCardType: ccType,
			creditCardNumber: ccNumber,
			creditCardExpDate: ccExpDate,
			creditCardSecurityCode: ccSecCode,
			installmentNotes: installmentNotes,
			requiresFollowUp: requiresFollowUp,
			payDate: payDate,
			decisionMaker: decisionMaker,
			giftMatchingCompany: giftMatchingCompany,
			declineBenefits: declineBenefits,
			invoiceMethod: invoiceMethod,
			grandTotal: giftAmount,
			createdAt: createdAt,
			employeeCode: employeeCode
		})
	}

	function createPledges(linesRead) {
		Models.Agent.findAll().then(function (agents) {
			var getAgent = require('./getAgent')(agents).getAgent
			Models.Campaign.findById(campaignId, {
					include: [Models.CampaignType]
				})
				.then(function (campaign) {
					Models.CampaignStage.findOne({
						where: {
							campaignId: campaignId,
							name: '1st Appeal'
						},
						include: Models.Disposition
					}).then(function (firstAppealStage) {
						firstAppealStage = JSON.parse(JSON.stringify(firstAppealStage))

						Models.Skill.findAll().then(function (skills) {
							Models.SubSkill.findAll().then(function (subSkills) {
								pledges.forEach(function (pledge) {
									var skillTypePrefix = (campaign.campaigntype.name === 'Telefunding' ? 'tf' : 'tm')

									Models.Lead.findOne({
										where: {
											clientRef: pledge.clientRef,
											clientId: campaign.clientId
										}
									}).then(function (lead) {
										delete pledge.clientRef
										pledge.leadId = lead.id
										pledge.skill = _.findWhere(skills, {
											id: lead[skillTypePrefix + 'SkillId']
										}).name
										pledge.subSkill = _.findWhere(subSkills, {
											id: lead[skillTypePrefix + 'SubSkillId']
										}).name
										pledge.campaignStageId = firstAppealStage.id
										pledge.clientId = campaign.clientId

										var agent = getAgent(pledge.employeeCode)
										if (agent) pledge.agentId = agent.id

										Models.CallResult.create(pledge).then(function (callResult) {
											var invoice = Models.Invoice.build()
											invoice.callresultId = callResult.id
											invoice.grandTotal = pledge.grandTotal
											invoice.amountRemaining = pledge.paymentType === 'Credit Card' ? 0 : pledge.grandTotal
											invoice.requestCount = 0
											invoice.invoiceType = pledge.paymentType
											invoice.clientId = pledge.clientId
											invoice.campaignId = pledge.campaignId
											invoice.leadId = lead.id
											invoice.sendInvoice = false
											invoice.deliveryMethod = pledge.invoiceMethod
											invoice.dueDate = pledge.payDate
											invoice.save()
										})

										var disposition
										if (pledge.paymentType === 'Credit Card') {
											disposition = _.findWhere(firstAppealStage.dispositions, {
												name: 'Pledge Credit Card'
											})
										} else {
											disposition = _.findWhere(firstAppealStage.dispositions, {
												name: 'Pledge Invoice'
											})
										}

										if (disposition && disposition.campaignstagedispositions) {
											if (disposition.campaignstagedispositions.transitionCutOffDate && moment(pledge.createdAt) > moment(disposition.campaignstagedispositions.transitionCutOffDate)) {
												transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionCutOffDateDispositionId, campaignId)
											} else {
												transitionLead(Models, lead.id, disposition.campaignstagedispositions.transitionToCampaignStageId, campaignId)
											}
											lead.dontContactUntil = moment(pledge.createdAt).add(disposition.campaignstagedispositions.dontContactLeadForHours, 'hours').toDate()
											lead.save()
										} else {
											console.log('WARNING: cannot find destination for leadId: ' + lead.id)
										}
									})
								})
							})
						})
					})
				})
		})

	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, createPledges)
}