var _ = require('underscore')

module.exports = function (agents) {
	return {
		getAgent: function getAgent(agents, employeeCode) {
			switch (employeeCode) {
				case 'AAE':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'ABW':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'ADS':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'AJM':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'ALE':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'AMH':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'BJV':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'BRJ':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'CLV':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'BXL':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'BXM':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'CMO':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'CLV':
					return _.findWhere(agents, {
						name: "<PERSON>"
					})
				case 'DJY':
					return _.find<PERSON>here(agents, {
						name: "<PERSON><PERSON>"
					})
				case '<PERSON>F':
					return _.find<PERSON>here(agents, {
						name: "<PERSON> Frusciano"
					})
				case 'DVA':
					return _.findWhere(agents, {
						name: "<PERSON> <PERSON>"
					})
				case 'E<PERSON>E':
					return _.findWhere(agents, {
						name: "<PERSON>ide"
					})
				case 'EMC':
					return _.findWhere(agents, {
						name: "<PERSON> <PERSON>"
					})
				case 'EXL':
					return _.findWhere(agents, {
						name: "Elias Leon"
					})
				case 'JCW':
					return _.findWhere(agents, {
						name: "James White"
					})
				case 'JDS':
					return _.findWhere(agents, {
						name: "Jared Shelton"
					})
				case 'JDZ':
					return _.findWhere(agents, {
						name: "Jorden Ziebell"
					})
				case 'JGO':
					return _.findWhere(agents, {
						name: "Jim Orcholski"
					})
				case 'JHJ':
					return _.findWhere(agents, {
						name: "Jafari Jackson"
					})
				case 'JHB':
					return _.findWhere(agents, {
						name: "Juanita Byers"
					})
				case 'JHM':
					return _.findWhere(agents, {
						name: "Jaimie Mutza"
					})
				case 'JKK':
					return _.findWhere(agents, {
						name: "Jahaya Kahmanne"
					})
				case 'JMB':
					return _.findWhere(agents, {
						name: "Jim Borgelt"
					})
				case 'JMF':
					return _.findWhere(agents, {
						name: "Jason Farr"
					})
				case 'JWP':
					return _.findWhere(agents, {
						name: "John Patterson"
					})
				case 'KJT':
					return _.findWhere(agents, {
						name: "Kudzai Tsoka"
					})
				case 'KKS':
					return _.findWhere(agents, {
						name: "Kartier Scott"
					})
				case 'LAB':
					return _.findWhere(agents, {
						name: "Lisbeth Bass"
					})
				case 'LBC':
					return _.findWhere(agents, {
						name: "Louis Christopher"
					})
				case 'MCD':
					return _.findWhere(agents, {
						name: "Marlene Davis"
					})
				case 'MML':
					return _.findWhere(agents, {
						name: "Michelle LaFleur"
					})
				case 'MJW':
					return _.findWhere(agents, {
						name: "Monique White"
					})
				case 'MKY':
					return _.findWhere(agents, {
						name: "Mark Yarbrough"
					})
				case 'MRZ':
					return _.findWhere(agents, {
						name: "Michael Zerzycki"
					})
				case 'MTG':
					return _.findWhere(agents, {
						name: "Marcus Goodrich"
					})
				case 'PJC':
					return _.findWhere(agents, {
						name: "Parker Cristan"
					})
				case 'PMD':
					return _.findWhere(agents, {
						name: "Peter Dalton"
					})
				case 'PMO':
					return _.findWhere(agents, {
						name: "Pat O'Leary"
					})
				case 'RMB':
					return _.findWhere(agents, {
						name: "Ronald Burch"
					})
				case 'RSW':
					return _.findWhere(agents, {
						name: "Robert Wilson"
					})
				case 'SFL':
					return _.findWhere(agents, {
						name: "Shae Lappen"
					})
				case 'TCP':
					return _.findWhere(agents, {
						name: "Timothy Pierce"
					})
				case 'TER':
					return _.findWhere(agents, {
						name: "Thomas Realm"
					})
				case 'TJD':
					return _.findWhere(agents, {
						name: "Tomi Davis"
					})
				case 'TLB':
					return _.findWhere(agents, {
						name: "Tracy Brown"
					})
				case 'ZLR':
					return _.findWhere(agents, {
						name: "Zak Rau"
					})

				default:
					return null
			}
		}
	}
}