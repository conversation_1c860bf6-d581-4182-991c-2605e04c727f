var uuid = require('node-uuid')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise

module.exports = function (Models, leadId, newCampaignStageId, campaignId) {
	// delete any existing call attempts for this lead in this campaign stage
	return Models.CallAttempt.destroy({
		where: {
			leadId: leadId,
			campaignId: campaignId
		}
	})
	.then(function () {
		// get the campaignlead object so we can update the current stage and insert campaign stage history
		return Models.CampaignLead.findOne({
			where: {
				leadId: leadId,
				campaignId: campaignId
			}
		})
	})
	.then(function (campaignLead) {
		if (!campaignLead) return
		if (newCampaignStageId) {
			var lead, campaignStage
			return Models.Lead.findById(leadId, {
				attributes: ['id', 'tfSkillId', 'tmSkillId', 'tfSubSkillId', 'tmSubSkillId', 'phone_home', 'phone_mobile', 'phone_work', 'phone_workmobile']
			})
			.then(function (_lead) {
				lead = _lead
				return Models.CampaignStage.findById(newCampaignStageId, {
					include: [{
						model: Models.Campaign,
						include: [Models.CampaignType]
					}]
				})
			})
			.then(campaignstage => {
				if (campaignstage && campaignstage.name === 'Return to Previous Stage') {
					if (campaignLead && campaignLead.campaignStageHistory) {
						var history = JSON.parse(campaignLead.campaignStageHistory)

						if (history.length) {
							newCampaignStageId = history[history.length - 1]
							return Models.CampaignStage.findById(req.body.newCampaignStageId, {
								include: [{
									model: Models.Campaign,
									include: [Models.CampaignType]
								}]
							})
						}
					}
				}
				
				return campaignstage
			})
			.then(function (_campaignStage) {
				campaignStage = _campaignStage
				var skillType = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')

				var csSkillsBlacklist = []
				if (campaignStage.blacklistedSkills) {
					try {
						csSkillsBlacklist = JSON.parse(campaignStage.blacklistedSkills)
					} catch (e) {
						csSkillsBlacklist = []
					}
				}

				if (csSkillsBlacklist.indexOf(lead[skillType]) > -1)
					newCampaignStageId = campaignStage.blacklistCampaignstageId || null

				var history = []
				if (campaignLead.campaignStageHistory) {
					try {
						history = JSON.parse(campaignLead.campaignStageHistory)
					} catch (e) {
						history = []
					}
				}

				if (campaignLead.currentCampaignStageId) {
					history.push(campaignLead.currentCampaignStageId)
				}

				campaignLead.updateAttributes({
					transitionDate: new Date(),
					currentCampaignStageId: newCampaignStageId,
					campaignStageHistory: JSON.stringify(history)
				}).catch(() => { })

				if (lead.phone_home || lead.phone_mobile || lead.phone_work || lead.phone_workmobile) {
					return Models.CampaignStageDateTimeRule.findAll({
						include: [Models.DateTimeRuleSet],
						where: {
							subskillId: lead[skillType],
							campaignstageId: campaignStage.id
						}
					})
					.then(function (campaignStageDTRules) {
						var inserts = []
						campaignStageDTRules.forEach(function (campaignDTRule) {
							for (var i = 0; i < campaignDTRule.quantity; i++) {
								var dtr = campaignDTRule.datetimeruleset
								inserts.push({
									startTime: dtr.startTime,
									endTime: dtr.endTime,
									startDate: (campaignDTRule.startDate || null),
									endDate: (campaignDTRule.endDate || null),
									monday: dtr.monday,
									tuesday: dtr.tuesday,
									wednesday: dtr.wednesday,
									thursday: dtr.thursday,
									friday: dtr.friday,
									saturday: dtr.saturday,
									sunday: dtr.sunday,
									campaignId: campaignStage.campaign.id,
									campaignstageId: campaignStage.id,
									createdFromDTUuid: campaignDTRule.uuid,
									leadId: lead.id,
									randomSelector: uuid.v4()
								})
							}
						})
						return Models.CallAttempt.bulkCreate(inserts)
					})
				}
			})
		}
		else {
			//just update the CampaignLead to set currentcampstage to null
			// we also want to put the history on this
			var history = []
			if (campaignLead.campaignStageHistory) {
				try {
					history = JSON.parse(campaignLead.campaignStageHistory)
				} catch (e) {
					history = []
				}
			}
			history.push(campaignLead.currentCampaignStageId)

			return campaignLead.updateAttributes({
				campaignStageHistory: JSON.stringify(history),
				currentCampaignStageId: null
			})
		}
	})
}