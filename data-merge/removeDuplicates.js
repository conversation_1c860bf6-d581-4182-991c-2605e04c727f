var moment = require('moment')
var Sequelize = require('sequelize'),
    sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
        host: 'localhost',
        logging: console.log
    })
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var fs = require('fs')

var letsDoThis = false

function writeToFile(data, sync) {
    var file = '../logs/removeDupes-' + new Date().toDateString() + '.txt'
    if (typeof data == 'object') data = JSON.stringify(data, null, 2)

    if (sync) {
        fs.appendFileSync(file, data + '\r\n', { encoding: 'utf8' })
        return
    } else {
        return new Promise((resolve, reject) => {
            fs.appendFile(file, data + '\r\n', { encoding: 'utf8' }, (err) => {
                if (err) return reject(err)
                resolve()
            })
        })
    }
}

Models.Lead.findAll({
    attributes: ['id', 'clientRef', 'updatedAt', 'dontContactUntil', 'tfSkillId', 'tfSubSkillId', 'tmSkillId', 'tmSubSkillId'],
    where: {
        clientId: 9
    },
    include: [{
        model: Models.CampaignLead,
        where: {
            campaignId: {
                $in: [52, 46]
            }
        },
        required: true
    }]
})
.then(leads => {
    leads = leads.map(l => l.toJSON())
    var groups = {}
    leads.forEach(l => {
        if (groups[l.clientRef]) {
            groups[l.clientRef].push(l)
        } else {
            groups[l.clientRef] = [l]
        }
    })

    var duplicates = {}
    for (var prop in groups) {
        if (groups[prop] && groups[prop].length && groups[prop].length > 1) {
            duplicates[prop] = groups[prop]
        }
    }

    var keys = Object.keys(duplicates)

    console.log(`found ${keys.length} clientRefs with dupes`)

    // REMOVE FOR GO LIVE
    // keys = keys.slice(0, 10)

    var p = Promise.resolve()
    var complete = 0
    console.log(`${complete}% complete`)
    keys.forEach((key, index) => {
        (function() {
            p = p.then(() => {
                var current = Math.floor((100 / keys.length) * index)
                if (current > complete) {
                    complete = current
                    console.log(`${complete}% complete`)
                }

                return processLead(duplicates[key])
            })
        }());
    })
    p.then(() => {
        console.log('finished')
    })
})
.catch(err => {
    console.log(err)
})


function processLead(dupes) {
    return new Promise((resolve, reject) => {
        var legitLead

        if (dupes.length > 1) {
            var dontContactUntil
            dupes.forEach(l => {
                if (!dontContactUntil) {
                    dontContactUntil = l.dontContactUntil
                } else if (l.dontContactUntil) {
                    if (moment(l.dontContactUntil) > moment(dontContactUntil))
                        dontContactUntil = l.dontContactUntil
                }

                if (!legitLead) {
                    legitLead = l
                    return
                }

                // if its gotten this far then just use the most recently updated lead
                if (moment(l.updatedAt) > moment(legitLead.updatedAt)) {
                    legitLead = l
                }
            })

            if (!legitLead) {
                return resolve()
            }

            var leadsToClean = dupes.filter(d => d.id !== legitLead.id)

            if (!letsDoThis) {
                var csvLine = `${legitLead.clientRef},${legitLead.id},${leadsToClean.map(l => l.id).join(',')}`
                writeToFile(csvLine)
                    .then(() => {
                        resolve()
                    }).catch(() => {
                        resolve()
                    })
            } else {
                Models.CampaignLeads.findAll({
                        where: {
                            leadId: {
                                $in: dupes.map(d => d.id)
                            }
                        }
                    }).then(campaignleads => {
                        var genuine = campaignleads.filter(cl => cl.leadId == legitLead.id)
                        var notGenuine = campaignleads.filter(cl => cl.leadId !== legitLead.id)
                        var promises = []
                        notGenuine.forEach(ng => {
                            var found = genuine.find(g => g.campaignId == ng.campaignId)
                            if (found) {
                                // delete this mutha otherwise the update later on will except
                                promises.push(ng.destroy())
                            }
                        })
                        return Promise.all(promises)
                    })
                    .then(() => {
                        var updatepromises = []
                        leadsToClean.forEach(l => {

                            var updateObj = {}
                            if (l.tfSkillId) updateObj.tfSkillId = l.tfSkillId
                            if (l.tfSubSkillId) updateObj.tfSubSkillId = l.tfSubSkillId
                            if (l.tmSkillId) updateObj.tmSkillId = l.tmSkillId
                            if (l.tmSubSkillId) updateObj.tmSubSkillId = l.tmSubSkillId

                            updatepromises.push(Models.Lead.update(updateObj, {
                                where: {
                                    id: legitLead.id
                                }
                            }))

                            updatepromises.push(Models.CampaignLead.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id,
                                }
                            }).then(() => {
                                return
                            }).catch(() => {
                                return
                            }))

                            // move the callattempts to the correct lead
                            updatepromises.push(Models.CallAttempt.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))

                            // move callresults to legit lead
                            updatepromises.push(Models.CallResult.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))

                            // move sales to legit lead
                            updatepromises.push(Models.Sale.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))

                            // move invoices to legit lead
                            updatepromises.push(Models.Invoice.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))

                            // move callrecords to legit lead
                            updatepromises.push(Models.CallRecord.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))

                            // move callbacks to legit lead
                            updatepromises.push(Models.Callback.update({
                                leadId: legitLead.id
                            }, {
                                where: {
                                    leadId: l.id
                                }
                            }))
                        })


                        Promise.all(updatepromises).then(() => {
                            // now delete the non legit leads
                            var deletePromises = []
                            leadsToClean.forEach(l => {
                                deletePromises.push(Models.Lead.destroy({
                                    where: {
                                        id: l.id
                                    }
                                }))
                            })
                            Promise.all(deletePromises).then(() => {
                                if (dontContactUntil) {
                                    Models.Lead.update({
                                        dontContactUntil
                                    }, {
                                        where: {
                                            id: legitLead.id
                                        }
                                    }).then(() => {
                                        resolve()
                                    }).catch(err => {
                                        console.log(err)
                                        resolve()
                                    })
                                } else {
                                    return resolve()
                                }
                            }).catch(err => {
                                console.log(err)
                                resolve()
                            })
                        }).catch(err => {
                            console.log(err)
                            resolve()
                        })
                    })
            }
        } else {
            return resolve()
        }
    })
}