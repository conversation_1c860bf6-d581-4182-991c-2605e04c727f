var APP_SETTINGS = require('./config/constants')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('./database/schema')(sequelize, Sequelize)

require('./tasks/agentPayroll')(Models)
require('./tasks/cleanCallbacks')(Models)
require('./tasks/staleSession')(Models)
require('./tasks/invoiceDelivery')(Models)
require('./tasks/scheduledReports')(Models, sequelize)
require('./tasks/rebuildStats')(sequelize, Models)
require('./tasks/cleanCallAttempts')(Models, sequelize)
require('./tasks/payments')(Models)
require('./tasks/suppression')(Models)
require('./tasks/renewalsCheck')(Models)
console.log('Tasks Started')