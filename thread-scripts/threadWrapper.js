var threads = require('threads')
var uuid = require('node-uuid')
var _ = require('underscore')
var taskProgressAPI = require('../database/redisAPI')('threadtasks')

threads.config.set({
	basepath: {
		node: __dirname + '/../thread-scripts'
	}
})

module.exports = {
	start: function (task, data) {
		var thread
		var taskId = uuid.v4()
		var self = this

		taskProgressAPI.save({
			id: taskId
		}, 'progress')

		var setProgress = function (taskId, status, percentComplete, data, error) {
			if (data && error) {
				return taskProgressAPI.extend(taskId, {
					status: status,
					percentComplete: (percentComplete || 0),
					data: data,
					error: error
				}, 'progress')
			} else if (data) {
				return taskProgressAPI.extend(taskId, {
					status: status,
					percentComplete: (percentComplete || 0),
					data: data
				}, 'progress')
			} else if (error) {
				return taskProgressAPI.extend(taskId, {
					status: status,
					percentComplete: (percentComplete || 0),
					error: error
				}, 'progress')
			} else {
				return taskProgressAPI.extend(taskId, {
					status: status,
					percentComplete: (percentComplete || 0)
				}, 'progress')
			}
		}		

		var killThread = function () {
			try {
				if (thread)
					thread.kill()
			} catch (e) {}
		}

		if (typeof task === 'function') {
			thread = threads.spawn(task)
		} else if (typeof task === 'string') {
			thread = threads.spawn(task + '.js')
		} else {
			throw new Error('Invalid task type')
		}

		thread.send(_.extend((data || {}), {
				taskId: taskId
			}))
			.on('message', function (message) {
				if (message) {
					if (message.error) {
						console.error('Task ID "' + taskId + '" errored: ', message.error)
						setProgress(taskId, self.states.FAULTED, 100, null, message.error)
					} else {
						console.log('Task ID "' + taskId + '" has been completed')
						setProgress(taskId, self.states.COMPLETED, 100, message)
					}
					setTimeout(killThread, 5000)
				}
			})
			.on('progress', function (progress) {
				if (progress > 0) {
					console.log('Task ID "' + taskId + '" progress: ' + progress + '%')
				} else {
					console.log('Task ID "' + taskId + '" in progress')
				}
				setProgress(taskId, self.states.INPROGRESS, progress)
			})
			.on('error', function (error) {
				console.error('Task ID "' + taskId + '" errored: ', error)
				setProgress(taskId, self.states.FAULTED, 100, null, error)
				setTimeout(killThread, 5000)
			})
			.on('exit', function () {
				console.log('Task ID "' + taskId + '" has exited')
				setProgress(taskId, self.states.COMPLETED, 100)
				setTimeout(killThread, 5000)
			})

		setProgress(taskId, self.states.PROCESSING)

		return taskId
	},
	getProgress: function (taskId) {
		return taskProgressAPI.get(taskId, 'progress')
	},
	states: {
		PROCESSING: 'Processing',
		INPROGRESS: 'In progress',
		COMPLETED: 'Completed',
		FAULTED: 'Completed with errors'
	}
}