var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
    sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var moment = require('moment')
var parseCSVFile = require('../utils/fileUtils.js').parseCSVFile

module.exports = function (imports, onComplete, progress) {
    var campaignId = imports.campaignId
    var campaignstageId = imports.campaignstageId
    var clientId = imports.clientId
    var historyId = imports.historyId
    var startDate = imports.startDate ? moment(imports.startDate) : null
    var endDate = imports.endDate ? moment(imports.endDate) : null
    var filePath = imports.filePath
    var updateOnly = imports.updateOnly
    var completed = 0
    var ignored = 0
    var processed = 0
    var created = 0
    var updated = 0
    var noOfRecords = 0
    var invalidRecords = 0
    var records = []
    var history

    function onNewRecord(record) {
        noOfRecords++
        if (record['CLIENT DONOR/PATRON ID']) {
            records.push({
                clientRef: record['CLIENT DONOR/PATRON ID'],
                stage: record.STAGE,
                startDate: record.STARTDATE ? moment(record.STARTDATE, 'YYYY-MM-DD') : startDate || new Date(),
                endDate: record.ENDDATE ? moment(record.ENDDATE, 'YYYY-MM-DD') : endDate || null
            })
        } else {
            invalidRecords++
        }
    }

    var updateProgress = function () {
        var progressPercent = Math.round((processed / noOfRecords) * 100)
        progress(progressPercent)
    }

    function done() {
        var index = 0
        updateHistory({
            total: noOfRecords
        })
        function loop() {
            var record = records[index]
            if (!record) return Promise.resolve()
            index++;
            processed++;
            return Models.Lead.findOne({
                attributes: ['id'],
                where: {
                    clientId,
                    clientRef: record.clientRef
                },
                include: [{
                    model: Models.CampaignLead,
                    where: {
                        campaignId
                    },
                    required: true
                }]
            }).then(lead => {
                if (lead && lead.campaignleads && lead.campaignleads.length) {
                    var promise = Promise.resolve()

                    if (record.stage) {
                        promise = Models.CampaignStage.findOne({
                            where: {
                                campaignId,
                                name: record.stage
                            }
                        })
                    }
                    return promise.then(stage => {
                        return Models.Suppression.findOne({
                            where: {
                                campaignId,
                                leadId: lead.id,
                                finished: false
                            }
                        }).then(existing => {
                            if (existing) {
                                updated++
                                var updateObj = {
                                    startDate: record.startDate,
                                    endDate: record.endDate,
                                    campaignstageId: stage ? stage.id : (campaignstageId === -1 ? null : campaignstageId),
                                    suppressionhistoryId: historyId,
                                    currentStage: campaignstageId === -1
                                }
                                if (existing.actualStartDate && existing.currentStage && !updateObj.campaignstageId) {
                                    // dont update it when its Current Stage of an already suppressed lead
                                    delete updateObj.campaignstageId
                                }
                                return existing.update(updateObj)
                            } else if (!updateOnly) {
                                created++
                                return Models.Suppression.create({
                                    leadId: lead.id,
                                    campaignstageId: stage ? stage.id : campaignstageId === -1 ? null : campaignstageId,
                                    campaignId,
                                    startDate: record.startDate,
                                    endDate: record.endDate,
                                    finished: false,
                                    suppressionhistoryId: historyId,
                                    currentStage: campaignstageId === -1
                                })
                            } else {
                                ignored++;
                            }
                        })
                    }).then(() => {
                        completed++
                        updateProgress()
                    })
                } else {
                    ignored++;
                    completed++;
                    console.log('lead not found')
                    updateProgress()
                }
            }).then(loop)
        }

        loop().then(() => {
            updateHistory({
                created,
                updated,
                ignored,
                invalid: invalidRecords
            })
            onComplete({
                linesRead: noOfRecords,
                leadsMoved: completed,
                leadNotFound: ignored,
                invalidRecords: invalidRecords
            })
        })
    }

    Models.SuppressionHistory.findById(historyId).then(_history => {
        history = _history
        parseCSVFile(filePath, true, onNewRecord, () => { }, done)
    })

    function updateHistory(obj) {
        if (history && history.update) {
            history.update(obj).catch(() => { })
        }
    }
}