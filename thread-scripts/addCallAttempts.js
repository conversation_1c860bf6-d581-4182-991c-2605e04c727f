var uuid = require('node-uuid')
var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

module.exports = function (imports, onComplete, progress) {
	var dtr = imports.dateTimeRule
	var dtRuleUuid = imports.uuid
	var campaignStage = imports.campaignStage
	var quantity = imports.quantity
	var startDate = (imports.startDate || null)
	var endDate = (imports.endDate || null)
	var leadIdObjs = imports.leadIdObjs
	var inserts = []

	if (!dtRuleUuid)
		return onComplete({ error: 'No uuid specified' })

	if (!dtr)
		return onComplete({ error: 'No date/time rule specified' })

	if (!quantity)
		return onComplete({ error: 'No quantity specified' })

	if (!leadIdObjs)
		return onComplete({ error: 'No leads specified' })

	if (!campaignStage || !campaignStage.campaign)
		return onComplete({ error: 'No campaign specified' })

	leadIdObjs.forEach(function (leadIdObj) {
		for (var i = 0; i < quantity; i++) {
			if (leadIdObj && leadIdObj.leadId) {
				inserts.push({
					startTime: dtr.startTime,
					endTime: dtr.endTime,
					startDate: startDate,
					endDate: endDate,
					monday: dtr.monday,
					tuesday: dtr.tuesday,
					wednesday: dtr.wednesday,
					thursday: dtr.thursday,
					friday: dtr.friday,
					saturday: dtr.saturday,
					sunday: dtr.sunday,
					campaignId: campaignStage.campaign.id,
					campaignstageId: campaignStage.id,
					createdFromDTUuid: dtRuleUuid,
					leadId: leadIdObj.leadId,
					randomSelector: uuid.v4()
				})
			}
		}
	})

	console.log('Inserting ' + inserts.length + ' call attempts')

	var insertChunks = [], size = 1000;

	while (inserts.length > 0)
	    insertChunks.push(inserts.splice(0, size))

	var chunks = insertChunks.length

	console.log('Split call attempts into ' + chunks + ' arrays')

	var promises = []
	var completedChunks = 0

	// this is important to indicate that the task is 'in-progress', rather than 'processing'
	progress(0)

	var p = Promise.resolve();
    insertChunks.forEach((chunk) => {
        (function () {
			p = p.then(() => {
				return new Promise(function (resolve, reject) {
					console.log(`Running bulk create for ${chunk.length} call attempts`)
					Models.CallAttempt.bulkCreate(chunk)
						.then(function () {
							completedChunks++
							console.log('Completed chunk')
							progress(Math.round((completedChunks / chunks) * 100))
							resolve()
						})
						.catch(function (err) {
							console.log(JSON.stringify(err))
							resolve()
						})
				})
			})
        }());
    })

	p.then(function () {
		onComplete({
			callAttemptsCreated: (leadIdObjs * quantity)
		})
	})
	.catch(function (err) {
		onComplete({ error: err })
	})
}