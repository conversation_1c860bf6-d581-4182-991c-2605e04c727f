var _ = require('underscore')
var csv = require('csv')
var async = require('async')
var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize('dialer', 'zenozi', 'Lavabug87', {
		host: 'kaos-db.dualtone.io'
	})
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile

module.exports = function (imports, onComplete, progress) {
	var products = []
	var successful = 0
	var campaign = imports.campaign
	var filePath = imports.filePath

	if (!campaign)
		return onComplete({ error: 'No campaign specified' })

	if (!filePath)
		return onComplete({ error: 'No upload file specified' })

	function onNewRecord(record) {
		products.push(record)
	}

	function done(linesRead) {
		if (products.length > 0)
			createProducts(products, linesRead)
		else
			return onComplete({ error: 'No records found' })
	}

	function createProducts(productsToInsert, linesRead) {
		var campaignProducts = productsToInsert.map(function (record) {
			var product = {}

			for (var field in record) {
				var dbFieldMap = APP_SETTINGS.EXPECTED_PRODUCT_IMPORT_FIELDS[field]

				if (dbFieldMap) {
					switch(dbFieldMap.type) {
						case "STRING":
							product[dbFieldMap.field] = record[field]
							break;
						case "BOOLEAN":
							if (record[field] === "TRUE" || record[field] === "FALSE") {
								product[dbFieldMap.field] = record[field] === "TRUE"
							}
							break;
						case "DOUBLE":
							product[dbFieldMap.field] = record[field].replace(/[^0-9$.,-]/g, '')
							break;
						default:
							product[dbFieldMap.field] = record[field]

					}
				}
			}

			product.campaignId = campaign.id

			return product
		})

		console.log(campaignProducts)

		var noOfProducts = campaignProducts.length
		console.log('Inserting ' + noOfProducts + ' products')

		var insertChunks = [], size = 1000;

		while (campaignProducts.length > 0)
		    insertChunks.push(campaignProducts.splice(0, size))

		var chunks = insertChunks.length

		console.log('Split products into ' + chunks + ' arrays')

		var promises = []
		var completedChunks = 0

		// this is important to indicate that the task is 'in-progress', rather than 'processing'
		progress(0)

		// for (var i = 0; i < chunks; i++) {
		// 	promises.push(
		// 		Models.CampaignProduct.bulkCreate(insertChunks[i])
		// 			.then(function () {
		// 				completedChunks++
		// 				progress(Math.round((completedChunks / chunks) * 100))
		// 				return Promise.resolve()
		// 			})
		// 	)
		// }

		var p = Promise.resolve()
	    insertChunks.forEach((chunk) => {
	        (function () {
				p = p.then(() => {
					return new Promise(function (resolve, reject) {
						Models.CampaignProduct.bulkCreate(chunk, { ignoreDuplicates: true })
							.then(function () {
								completedChunks++
								progress(Math.round((completedChunks / chunks) * 100))
								resolve()
							})
							.catch(function (err) {
								console.log(JSON.stringify(err))
								resolve()
							})
					})
				})
	        }());
	    })

		p.then(() => {
			return onComplete({
				productsCreated: noOfProducts
			})
		})
		.catch(function (err) {
			onComplete({ error: err })
		})

		// Promise.all(promises)
		// 	.then(function () {
		// 		onComplete({
		// 			productsCreated: noOfProducts
		// 		})
		// 	})
		// 	.catch(function (err) {
		// 		onComplete({ error: err })
		// 	})
	}

	parseCSVFile(filePath, true, onNewRecord, () => {}, done)
}
