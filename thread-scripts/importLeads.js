var _ = require('underscore')
var fs = require('fs')
var csv = require('csv')
var path = require('path')
var async = require('async')
var uuid = require('node-uuid')
var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var ensureDirectoryExists = fileUtils.ensureDirectoryExists
var copyFile = fileUtils.copyFile

module.exports = function (imports, onComplete, progress) {
	var records = []
	var skills = []
	var subSkills = []
	var skillObjs = []
	var subSkillObjs = []
	var campaign = imports.campaign
	var filePath = imports.filePath
	var updateOnly = imports.updateOnly
	var taskId = imports.taskId
	var importId = imports.importId
	var linesRead = 0
	
	function throwError(error) {
		if (importId && error && typeof error === 'string') {
			Models.LeadImportHistory.update({
				error
			}, {
				where: {
					id: importId
				}
			}).catch(() => { })
		}
		
		onComplete({ error })
	}
	
	if (!campaign) return throwError('No campaign specified')
	
	if (!filePath) return throwError('No upload file specified')
	
	function cleanString(str) {
		return (str && str.trim) ? str.trim() : str
	}
	
	// Function to execute on each CSV line read - we can't have any async code in here or it may overlap the 'done' callback at the end
	function onNewRecord(record) {
		try {
			if (!record[APP_SETTINGS.MANDATORY_LEAD_FIELD])
			return;
			
			// set campaign id against the lead object
			record.clientId = campaign.clientId
			
			// add to list to insert later (this method is executed async so we need to store these for now and lookup/add skills before inserting)
			records.push(record)
			
			var tfSkill = record[APP_SETTINGS.TF_LEAD_TYPE_FIELD]
			var tfSubSkill = record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD]
			var tmSkill = record[APP_SETTINGS.TM_LEAD_TYPE_FIELD]
			var tmSubSkill = record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD]
			
			// maintain a distinct list of skill names to reference later
			if (tfSkill && _.indexOf(skills, tfSkill) === -1)
			skills.push(tfSkill)
			if (tmSkill && _.indexOf(skills, tmSkill) === -1)
			skills.push(tmSkill)
			
			// maintain a distinct list of subskill names to reference later
			if (tfSubSkill && _.indexOf(subSkills, tfSubSkill) === -1)
			subSkills.push(tfSubSkill)
			if (tmSubSkill && _.indexOf(subSkills, tmSubSkill) === -1)
			subSkills.push(tmSubSkill)
		}
		catch (e) {
			// shouldn't ever get here but maybe need to handle better to report back errored records
			console.log(e)
			console.log(record)
		}
	}
	
	function done(noOfLines) {
		linesRead = noOfLines
		if (records && records.length > 0) {
			// if there's no skills to lookup/create then just go ahead and insert leads
			if (skills.length === 0 && subSkills.length === 0)
			createRecords(records)
			// else lookup / create skill and add to list for later reference
			else {
				createSkills(skills, function (err) {
					createSubSkills(subSkills, function (err) {
						// once all skills have been looked up / created then we can go ahead and insert the leads
						createRecords(records)
					})
				})
			}
		}
		else {
			return throwError('No records found')
		}
	}
	
	function createSkills(skills, cb) {
		async.each(skills, function (skill, callback) {
			Models.Skill
			.findOrCreate({
				where: {
					name: skill
				},
				defaults: {
					description: 'Automatically created by import process'
				}
			})
			.then(function (result) {
				if (result && result.length) {
					result[0].name = result[0].name.toLowerCase()
					skillObjs.push(result[0])
				}
				callback()
			})
		}, cb)
	}
	
	function createSubSkills(skills, cb) {
		async.each(skills, function (skill, callback) {
			Models.SubSkill
			.findOrCreate({
				where: {
					name: skill
				},
				defaults: {
					description: 'Automatically created by import process'
				}
			})
			.then(function (result) {
				if (result && result.length) {
					result[0].name = result[0].name.toLowerCase()
					subSkillObjs.push(result[0])
				}
				callback()
			})
		}, cb)
	}
	
	function createRecords(recordsToInsert) {
		var noOfRecords = recordsToInsert.length
		var linesIgnored = linesRead - recordsToInsert.length
		var successfulLeadInserts = 0
		var successfulLeadUpdates = 0
		var failedLeadImports = 0
		var recordsProcessed = 0
		var recordsPreProcessed = 0
		
		var updateProgress = function () {
			var progressPercent = Math.round(((recordsProcessed + recordsPreProcessed) / (noOfRecords * 2)) * 90)
			progress(progressPercent)
		}
		
		// this is important to indicate that the task is 'in-progress', rather than 'processing'
		progress(0)
		
		Models.CampaignStageDateTimeRule.findAll({
			include: [Models.DateTimeRuleSet],
			where: {
				campaignstageId: campaign.initialCampaignStageId
			}
		})
		.then(function (campaignStageDTRules) {
			var callAttemptInserts = []
			var campaignLeadInserts = []
			
			var promises = recordsToInsert.map(function (record) {
				var tfSkill = record[APP_SETTINGS.TF_LEAD_TYPE_FIELD]
				var tfSubSkill = record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD]
				var tmSkill = record[APP_SETTINGS.TM_LEAD_TYPE_FIELD]
				var tmSubSkill = record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD]
				
				if (tfSkill) {
					var skillObj = _.findWhere(skillObjs, {
						name: tfSkill.toLowerCase()
					})
					if (skillObj)
					record.tfSkillId = skillObj.id
				}
				
				if (tfSubSkill) {
					var subSkillObj = _.findWhere(subSkillObjs, {
						name: tfSubSkill.toLowerCase()
					})
					if (subSkillObj)
					record.tfSubSkillId = subSkillObj.id
				}
				
				if (tmSkill) {
					var skillObj = _.findWhere(skillObjs, {
						name: tmSkill.toLowerCase()
					})
					if (skillObj)
					record.tmSkillId = skillObj.id
				}
				
				if (tmSubSkill) {
					var subSkillObj = _.findWhere(subSkillObjs, {
						name: tmSubSkill.toLowerCase()
					})
					if (subSkillObj)
					record.tmSubSkillId = subSkillObj.id
				}
				
				var lead = {}
				var customFields = {}
				
				for (var field in record) {
					var dbFieldMap = APP_SETTINGS.EXPECTED_IMPORT_FIELDS[field]
					
					if (dbFieldMap) {
						try {
							if (field.indexOf('PHONE') > -1) {
								lead[dbFieldMap] = record[field].replace(/\D/g, '')
							}
							else if (['LY AMOUNT', 'LAP1 AMOUNT', 'LAP2 AMOUNT', 'LAP3 AMOUNT', 'LAP4 AMOUNT'].indexOf(field) > -1) {
								lead[dbFieldMap] = record[field].replace(/[^0-9.]/g, '')
							}
							else {
								lead[dbFieldMap] = cleanString(record[field])
							}
						}
						catch (e) {
							lead[dbFieldMap] = null
						}
					}
					else if (
						[APP_SETTINGS.TF_LEAD_TYPE_FIELD,
							APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD,
							APP_SETTINGS.TM_LEAD_TYPE_FIELD,
							APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD
						].indexOf(field) === -1
						)
						customFields[field] = cleanString(record[field])
					}
					
					lead.customFields = JSON.stringify(customFields)
					lead.reimported = false
					
					//should check here if the lead already exists
					return new Promise(function (resolve, reject) {
						Models.Lead.findOne({
							where: {
								clientId: lead.clientId,
								$and: [{
									clientRef: {
										$not: null
									}
								}, {
									clientRef: (lead.clientRef || null)
								}]
							},
							include: [{
								model: Models.CampaignLead,
								where: {
									campaignId: campaign.id
								},
								required: false
							}],
							
						})
						.then(function (result) {
							recordsPreProcessed++
							updateProgress()
							
							if (!result) {
								if (updateOnly) {
									linesIgnored++;
									resolve({ lead: result, createCallAttempts: false, isUpdate: false, stageToCreate: null })
								} else {
									Models.Lead.create(lead)
									.then(function (createdLead) {
										successfulLeadInserts++
										resolve({ lead: createdLead, createCallAttempts: true, isUpdate: false, stageToCreate: campaign.initialCampaignStageId })
									})
								}
							}
							else {
								var updates = JSON.parse(JSON.stringify(lead))
								var createCallAttempts = false
								var stageToCreate = campaign.initialCampaignStageId
								
								// remove fields that shouldn't be updated - these shouldn't exist on the imported data anyway, but just to be sure
								delete updates.id
								delete updates.clientId
								delete updates.clientRef
								delete updates.dontContactUntil
								delete updates.notes
								
								if ((!result.phone_home && !result.phone_work && !result.phone_mobile && !result.phone_workmobile) &&
										(updates.phone_home || updates.phone_work || updates.phone_mobile || updates.phone_workmobile)) {
									updates.reimported = true
								}
								
								if (result.campaignleads && result.campaignleads.length) {
									if (result.campaignleads[0].currentCampaignStageId && updates.reimported) {
										createCallAttempts = true
										stageToCreate = result.campaignleads[0].currentCampaignStageId
									}
								} else {
									createCallAttempts = true
								}
								
								result.updateAttributes(updates)
								.then(function (updatedLead) {
									if (updatedLead.campaignleads && updatedLead.campaignleads.length) {
										// only count the update if it was already on the campaign, otherwise its a create
										successfulLeadUpdates++
									}
									resolve({ lead: updatedLead, createCallAttempts: createCallAttempts, isUpdate: true, stageToCreate: stageToCreate })
								})
							}
						})
					})
					.then(function (result) {
						var lead = result.lead
						
						if (!lead) {
							recordsProcessed++
							return updateProgress()
						}
						var skill = (campaign.campaigntype.name === 'Telefunding' ? record[APP_SETTINGS.TF_LEAD_TYPE_FIELD] : record[APP_SETTINGS.TM_LEAD_TYPE_FIELD])
						var subSkill = (campaign.campaigntype.name === 'Telefunding' ? record[APP_SETTINGS.TF_LEAD_SUB_TYPE_FIELD] : record[APP_SETTINGS.TM_LEAD_SUB_TYPE_FIELD])
						
						if (lead.campaignleads && lead.campaignleads.length) {
							var cl = lead.campaignleads[0]
							if (skill && subSkill && cl.skill !== skill || cl.subSkill !== subSkill) {
								// update the skill and subskill for reporting purposes
								Models.CampaignLead.update({
									skill, subSkill
								}, {
									where: {
										leadId: lead.id,
										campaignId: campaign.id
									}
								}).catch(() => { })
							}
						} else if (!updateOnly) {
							campaignLeadInserts.push({
								campaignId: campaign.id,
								leadId: lead.id,
								currentCampaignStageId: campaign.initialCampaignStageId,
								skill,
								subSkill
							})
						}
						
						if (result.createCallAttempts && result.stageToCreate) {
							var leadType = (campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
							
							var skillDTRules = _.where(campaignStageDTRules, {
								subskillId: lead[leadType]
							})
							
							if (skillDTRules && skillDTRules.length) {
								skillDTRules.forEach((campaignDTRule) => {
									for (var i = 0; i < campaignDTRule.quantity; i++) {
										var dtr = campaignDTRule.datetimeruleset
										callAttemptInserts.push({
											startTime: dtr.startTime,
											endTime: dtr.endTime,
											startDate: (campaignDTRule.startDate || null),
											endDate: (campaignDTRule.endDate || null),
											monday: dtr.monday,
											tuesday: dtr.tuesday,
											wednesday: dtr.wednesday,
											thursday: dtr.thursday,
											friday: dtr.friday,
											saturday: dtr.saturday,
											sunday: dtr.sunday,
											campaignId: campaign.id,
											campaignstageId: result.stageToCreate || campaign.initialCampaignStageId,
											createdFromDTUuid: campaignDTRule.uuid,
											leadId: lead.id,
											randomSelector: uuid.v4()
										})
									}
								})
							}
						}
						
						recordsProcessed++
						
						updateProgress()
					})
					.catch(function (err) {
						console.log(err)
						failedLeadImports++
					})
				})
				
				Promise.all(promises).then(function () {
					promises = null
					return onFinishedProcessing(callAttemptInserts, campaignLeadInserts, successfulLeadInserts, successfulLeadUpdates, linesIgnored)
				})
			})
			.catch(function (err) {
				return throwError(err.message)
			})
		}
		
		function onFinishedProcessing(callAttemptInserts, campaignLeadInserts, successfulLeadInserts, successfulLeadUpdates, linesIgnored) {
			var size = 1000
			var promises = []
			var caInsertChunks = []
			var clInsertChunks = []
			var successfulBulkInserts = 0
			var caInserts = callAttemptInserts.length
			var clInserts = campaignLeadInserts ? campaignLeadInserts.length : 0
			
			if (callAttemptInserts) {
				console.log('Inserting ' + caInserts + ' call attempts')
				
				while (callAttemptInserts.length > 0)
				caInsertChunks.push(callAttemptInserts.splice(0, size))
				
				console.log('Split call attempts into ' + caInsertChunks.length + ' arrays')
			}
			
			if (campaignLeadInserts) {
				console.log('Inserting ' + campaignLeadInserts.length + ' campaign leads')
				
				while (campaignLeadInserts.length > 0)
				clInsertChunks.push(campaignLeadInserts.splice(0, size))
				
				console.log('Split campaign leads into ' + clInsertChunks.length + ' arrays')
			}
			
			var totalBulkInserts = clInsertChunks.length + caInsertChunks.length
			
			var p = Promise.resolve()
			clInsertChunks.forEach((chunk) => {
				(function () {
					p = p.then(() => {
						return new Promise(function (resolve, reject) {
							Models.CampaignLead.bulkCreate(chunk, { ignoreDuplicates: true })
							.then(function () {
								successfulBulkInserts++
								
								var progressPercent = Math.round(((successfulBulkInserts / totalBulkInserts) * 10) + 90)
								progress(progressPercent)
								
								resolve()
							})
							.catch(function (err) {
								console.log(JSON.stringify(err))
								resolve()
							})
						})
					})
				}());
			})
			
			caInsertChunks.forEach((chunk) => {
				(function () {
					p = p.then(() => {
						return new Promise(function (resolve, reject) {
							Models.CallAttempt.bulkCreate(chunk)
							.then(function () {
								successfulBulkInserts++
								
								var progressPercent = Math.round(((successfulBulkInserts / totalBulkInserts) * 10) + 90)
								progress(progressPercent)
								
								resolve()
							})
							.catch(function (err) {
								console.log(JSON.stringify(err))
								resolve()
							})
						})
					})
				}());
			})
			
			p.then(() => {
				clearResources()
				
				return onComplete({
					linesRead: linesRead,
					leadsCreated: clInserts,
					leadsUpdated: successfulLeadUpdates,
					callAttemptInserts: caInserts,
					linesIgnored: linesIgnored
				})
			})
			.catch(function (err) {
				clearResources()
				
				// if error was just that there's primary key constraints on the campaignleads then thats all good
				if (err && err.errors && err.errors.length && err.errors[0].type === 'unique violation')
				return onComplete({
					linesRead: linesRead,
					leadsCreated: successfulLeadInserts,
					leadsUpdated: successfulLeadUpdates,
					callAttemptInserts: caInserts,
					linesIgnored: linesIgnored
				})
				else {
					return throwError(err.message)
				}
			})
			
			function clearResources() {
				callAttemptInserts = null
				campaignLeadInserts = null
				records = null
			}
		}
		
		parseCSVFile(filePath, true, onNewRecord, () => { }, done)
	}