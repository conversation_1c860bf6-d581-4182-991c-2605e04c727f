var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var _ = require('underscore')
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile
var invoiceGenerator = require('../invoices/invoiceGenerator')(Models)
var moment = require('moment')
var _ = require('underscore')
var path = require('path')
var fs = require('fs')
var archiver = require('archiver')
var email = require('../emailing/email')(Models)

module.exports = function (imports, onComplete, progress) {
	var campaign = imports.campaign
	var filePath = imports.filePath
	var historyId = imports.historyId
	var completed = 0
	var ignored = 0
	var noOfRecords = 0
	var invalidRecords = 0
	var records = []
	var history
    var invoicesToSend = []

	var updateProgress = function () {
		var progressPercent = Math.round(((completed + ignored) / (noOfRecords - invalidRecords)) * 100)
		console.log(progressPercent)
		progress(progressPercent)
	}

	function updateHistory(obj) {
		if (!history) return
		history.update(obj).catch(() => { })
	}

	function onNewRecord(record) {
		noOfRecords++
		if (record['CLIENT DONOR/PATRON ID']) {
			records.push({
				clientRef: record['CLIENT DONOR/PATRON ID']
			})
		} else {
			invalidRecords++
            console.log('Invalid record, skipping', record)
		}
	}

	function done() {
		var index = 0
		function loop() {
			var record = records[index]
			if (!record) return Promise.resolve()
			index++;
			return Models.Lead.findOne({
				where: {
					clientId: campaign.clientId,
					clientRef: record.clientRef
				},
				attributes: ['id', 'clientRef', 'clientId']
			})
            .then(function (lead) {
                if (lead) {
                    return new Promise(resolve => {
                        Models.Invoice.findAll({
                            where: {
                                leadId: lead.id,
                                campaignId: campaign.id
                            },
                            include: [{
                                model: Models.Lead
                            }, {
                                model: Models.Client
                            }, {
                                model: Models.Campaign
                            }, {
                                model: Models.CallResult,
                                include: [Models.CampaignStage]
                            }]
                        })
                        .then(resolve)
                        .catch(err => {
                            console.error(err)
                            resolve([])
                        })
                    })
                }
                else
                    return null
            })
            .then(function (invoices) {
				if (invoices && invoices.length) {
					completed++
					updateHistory({ updated: completed })
                    invoicesToSend.push(invoices.reverse()[0])
				} else {
					ignored++
                    console.log('Ignoring record', record)
					updateHistory({ ignored: ignored })
				}
			})
            .then(() => {
				updateProgress()
				return loop()
			})
            .catch(err => {
				console.log(err)
				return loop()
			})
		}
		Models.BatchChangeHistory.findById(historyId).then(_history => {
			history = _history
			updateHistory({ total: noOfRecords, invalid: invalidRecords, ignored: ignored })
			loop().then(() => {
				if ((completed + ignored) == (noOfRecords - invalidRecords)) {
                    sendInvoices(invoicesToSend).catch(console.log).finally(() => {
                        onComplete({
                            linesRead: noOfRecords,
                            invoicesExported: completed,
                            invalidRecords: invalidRecords
                        })
                    })
				}
			})
		})

	}

	parseCSVFile(filePath, true, onNewRecord, () => { }, done)
}

function sendInvoices(invoices) {
    console.log('creating paper invoices')

    if (invoices && invoices.length) {
        var p = Promise.resolve()
        var names = []
        invoices.forEach(invoice => {
            (function () {
                p = p.then(() => {
                    return new Promise((resolve, reject) => {
                        invoiceGenerator.generatePdfForInvoice(invoice)
                            .then(history => {
                                names.push(history.invoiceHtml)
                                resolve()
                            })
                            .catch(err => {
                                console.log(err)
                                resolve()
                            })
                    })
                })
            }())
        })

        return p.then(() => {
            var name = 'invoices_' + moment().format('DDMMYY_HHmmss') + '.zip'
            var output = fs.createWriteStream(APP_SETTINGS.BULK_INVOICE_LOCATION + name)
            var archive = archiver('zip')

            output.on('close', function () {
                Models.System.findOne({
                    where: {
                        key: 'invoiceBulkRecipient'
                    }
                })
                    .then(function (address) {
                        if (address && address.value) {
                            var emailContext = {
                                client: 'Kaos',
                                download: APP_SETTINGS.EXTERNAL_URL + '/bulkinvoices/' + name
                            }
                            email.emailWithoutAttachments('invoiceLink', address.value, null, 'Paper Invoices', emailContext)
                                .then(function () {
                                    invoices.forEach(function (inv) {
                                        inv.updateAttributes({
                                            requestCount: inv.requestCount + 1,
                                            dueDate: moment().add(30, 'days').toDate(),
                                            sendInvoice: false
                                        })
                                    })
                                })
                                .catch(function (err) {
                                    console.log(err)
                                })
                        } else {
                            //default email address?
                        }
                    })
            })

            archive.on('error', function (err) {
                console.log(err)
                throw err
            })

            archive.pipe(output)
            for (var i = 0; i < names.length; i++) {
                archive.append(fs.createReadStream('./invoices/pdf/' + names[i] + '.pdf'), {
                    name: names[i] + '.pdf'
                })
            }

            archive.finalize()
        })
    }
}