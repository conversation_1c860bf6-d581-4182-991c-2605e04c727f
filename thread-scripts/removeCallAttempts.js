var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise

module.exports = function (imports, onComplete, progress) {
	var uuid = imports.uuid
	var leadIdObjs = imports.leadIdObjs
	var quantity = imports.quantity
	var completed = 0

	if (!quantity)
		return onComplete({ error: 'No quantity specified' })

	if (!leadIdObjs)
		return onComplete({ error: 'No leads specified' })

	leadIdObjs.forEach(function (leadIdObj) {
		var promises = []
		
		progress(0)

		if (leadIdObj.leadId) {
			promises.push(
				new Promise(function (resolve, reject) {
					Models.CallAttempt.destroy({
						where: {
							createdFromDTUuid: uuid,
							leadId: leadIdObj.leadId
						},
						limit: quantity
					})
					.then(function () {
						completed++
						resolve()
					})
					.catch(function (err) {
						console.error(err)
						resolve()
					})
				})
			)
		}

		Promise.all(promises)
			.then(function () {
				progress(100)
				onComplete()
			})
			.catch(function (err) {
				progress(100)
				onComplete({ error: err })
			})
	})
}