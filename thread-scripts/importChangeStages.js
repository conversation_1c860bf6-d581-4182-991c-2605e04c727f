var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var _ = require('underscore')
var transitionLead = require('../data-merge/transitionLead')
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile

module.exports = function (imports, onComplete, progress) {
	var campaign = imports.campaign
	var filePath = imports.filePath
	var stageId = imports.stageId
	var historyId = imports.historyId
	var completed = 0
	var ignored = 0
	var processed = 0
	var noOfRecords = 0
	var invalidRecords = 0
	var records = []
	var history

	var updateProgress = function () {
		var progressPercent = Math.round(((completed + ignored) / (noOfRecords - invalidRecords)) * 100)
		console.log(progressPercent)
		progress(progressPercent)
	}

	function updateHistory(obj) {
		if (!history) return
		history.update(obj).catch(() => { })
	}

	function onNewRecord(record) {
		noOfRecords++
		if (record['CLIENT DONOR/PATRON ID']) {
			records.push({
				clientRef: record['CLIENT DONOR/PATRON ID']
			})
		} else {
			invalidRecords++
		}
	}

	function done() {
		var index = 0
		function loop() {
			var record = records[index]
			if (!record) return Promise.resolve()
			index++;
			return Models.Lead.findOne({
				where: {
					clientId: campaign.clientId,
					clientRef: record.clientRef
				},
				attributes: ['id', 'clientRef', 'clientId'],
				include: [{
					model: Models.CampaignLead,
					where: {
						campaignId: campaign.id
					},
					required: true
				}]
			}).then(function (lead) {
				if (lead && lead.campaignleads && lead.campaignleads.length) {
					completed++
					updateHistory({ updated: completed })
					if (stageId === 0) {
						return transitionLead(Models, lead.id, null, campaign.id)
					} else {
						return transitionLead(Models, lead.id, stageId, campaign.id)
					}
				} else {
					ignored++
					updateHistory({ ignored: ignored })
				}
			}).then(() => {
				updateProgress()
				return loop()
			}).catch(err => {
				console.log(err)
				return loop()
			})
		}
		Models.BatchChangeHistory.findById(historyId).then(_history => {
			history = _history
			updateHistory({ total: noOfRecords, invalid: invalidRecords, ignored: ignored })
			loop().then(() => {
				if ((completed + ignored) == (noOfRecords - invalidRecords)) {
					onComplete({
						linesRead: noOfRecords,
						leadsMoved: completed,
						leadNotFound: ignored,
						invalidRecords: invalidRecords
					})
				}
			})
		})

	}

	parseCSVFile(filePath, true, onNewRecord, () => { }, done)
}