var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var _ = require('underscore')
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile

module.exports = function (imports, onComplete, progress) {
	var campaign = imports.campaign
	var filePath = imports.filePath
	var completed = 0
	var ignored = 0
	var processed = 0
	var noOfRecords = 0
	var invalidRecords = 0
	var records = []

	var updateProgress = function () {
		var progressPercent = Math.round(((processed + invalidRecords) / noOfRecords) * 100)
		progress(progressPercent)
	}

	function onNewRecord(record) {
		noOfRecords++
		if (record['CLIENT DONOR/PATRON ID']) {
			records.push(record['CLIENT DONOR/PATRON ID'])
		} else {
			invalidRecords++
		}
	}

	function done() {
		var promises = []
		records.forEach(function (clientRef) {
			promises.push(Models.Lead.findOne({
				where: {
					clientId: campaign.clientId,
					clientRef: clientRef
				},
				attributes: ['id', 'clientRef'],
				raw: true,
			}))
		})
		Promise.all(promises)
			.then(function (leads) {
				leads.forEach(function (lead) {
					if (lead && lead.clientRef && records.indexOf(lead.clientRef) > -1) {
						var deletePromises = []
						deletePromises.push(Models.LeadAudit.destroy({
							where: {
								leadId: lead.id
							}
						}))
						deletePromises.push(Models.Sale.destroy({
							where: {
								leadId: lead.id
							}
						}))
						deletePromises.push(Models.Invoice.destroy({
							where: {
								leadId: lead.id
							}
						}))
						deletePromises.push(Models.CallResult.destroy({
							where: {
								leadId: lead.id
							}
						}))
						deletePromises.push(Models.CallAttempt.destroy({
							where: {
								leadId: lead.id
							}
						}))
						deletePromises.push(Models.Callback.destroy({
							where: {
								leadId: lead.id
							}
						}))

						Promise.all(deletePromises).then(function () {
							Models.CampaignLead.destroy({
								where: {
									leadId: lead.id
								}
							}).then(function () {
								Models.Lead.destroy({
									where: {
										id: lead.id
									}
								}).then(function () {
									completed++

									if ((completed + ignored) == (noOfRecords - invalidRecords)) {
										onComplete({
											linesRead: noOfRecords,
											leadsDeleted: completed,
											leadsNotFound: ignored,
											invalidRecords: invalidRecords
										})
									}
								})
							})
						})
					} else {
						ignored++
					}

					processed++
					updateProgress()
				})
			})
	}

	parseCSVFile(filePath, true, onNewRecord, () => { }, done)
}