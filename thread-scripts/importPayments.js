var APP_SETTINGS = require('../config/constants.js')
var Sequelize = require('sequelize'),
	sequelize = new Sequelize(APP_SETTINGS.DB.schema, APP_SETTINGS.DB.user, APP_SETTINGS.DB.pass, APP_SETTINGS.DB.config)
var Models = require('../database/schema')(sequelize, Sequelize)
var Promise = Sequelize.Promise
var _ = require('underscore')
var transitionLead = require('../data-merge/transitionLead')
var fileUtils = require('../utils/fileUtils.js')
var parseCSVFile = fileUtils.parseCSVFile

module.exports = function (imports, onComplete, progress) {
	var campaign = imports.campaign
	var filePath = imports.filePath
	var userId = imports.userId
	var historyId = imports.historyId
	var completed = 0
	var ignored = 0
	var processed = 0
	var noOfRecords = 0
	var invalidRecords = 0
	var records = []
	var history

	var updateProgress = function () {
		var progressPercent = Math.round(((processed + invalidRecords) / noOfRecords) * 100)
		progress(progressPercent)
	}

	function updateHistory(obj) {
		if (!history) return
		history.update(obj).catch(() => { })
	}

	function onNewRecord(record) {
		noOfRecords++
		if (record['CLIENT DONOR/PATRON ID'] && record['PAY AMOUNT']) {
			var payAmount = parseFloat(record['PAY AMOUNT'].replace('$', '').replace(',', ''))
			if (payAmount) {
				console.log('adding ' + payAmount + ' for ' + record['CLIENT DONOR/PATRON ID'])
				records.push({
					clientRef: record['CLIENT DONOR/PATRON ID'],
					payAmount: payAmount
				})
			} else {
				console.log('not adding ' + record['CLIENT DONOT/PATRON ID'] + ' as there is no payamount')
				invalidRecords++
			}
		} else {
			console.log('not adding as there is no payamount or client ref')
			invalidRecords++
		}
	}

	function done() {
		Models.BatchChangeHistory.findById(historyId).then(_history => {
			history = _history
			updateHistory({ total: noOfRecords, invalid: invalidRecords, updated: 0, ignored: 0 })
			if (!records.length) {
				onComplete({
					linesRead: noOfRecords,
					invoicesUpdated: completed,
					invoicesIgnored: ignored,
					invalidRecords: invalidRecords
				})
			}

			var promises = []
			records.forEach(function (record) {
				promises.push(new Promise((resolve, reject) => {
					Models.Lead.findOne({
						where: {
							clientId: campaign.clientId,
							clientRef: record.clientRef
						},
						include: [{
							model: Models.Invoice,
							where: {
								campaignId: campaign.id
							}
						}, {
							model: Models.Campaign,
							where: {
								id: campaign.id
							},
							include: [{
								model: Models.CampaignStage,
								include: [Models.Disposition]
							}]
						}, {
							model: Models.Suppression,
							where: {
								campaignId: campaign.id,
								actualStartDate: {
									$lte: new Date()
								},
								actualEndDate: {
									$eq: null
								},
								finished: false,
								skipped: false
							},
							required: false
						}]
					}).then(function (lead) {
						if (lead && lead.invoices && lead.invoices.length) {
							var invoice
							lead.invoices.forEach(function (inv) {
								//check the invoice has an amount remaining that is the same or greater than the pay amount
								if (inv.amountRemaining || inv.writtenOff) {
									invoice = inv
								}
							})
							if (invoice) {
								var remaining = invoice.amountRemaining - record.payAmount
								var writtenOffAmount = invoice.writtenOffAmount
								if (remaining < 0) {
									// might be from a partial write off so take the rest from there if possible
									if (writtenOffAmount) {
										writtenOffAmount += remaining
										if (writtenOffAmount < 0) writtenOffAmount = 0
									}
									remaining = 0
								}

								// create payment log
								Models.PaymentLog.create({
									amount: record.payAmount,
									paymentDate: new Date(),
									isPaid: true,
									actualPaymentDate: new Date(),
									status: 'paid',
									leadId: lead.id,
									userId: userId,
									clientId: lead.clientId,
									invoiceId: invoice.id,
									campaignId: invoice.campaignId,
									callresultId: invoice.callresultId,
									source: filePath
								}).catch(() => { })

								if (invoice.writtenOffAmount && writtenOffAmount !== invoice.writtenOffAmount) {
									Models.InvoiceEvent.create({
										invoiceId: invoice.id,
										userId: userId,
										changeType: 'Written Off Amount',
										field: 'writtenOffAmount',
										fromValue: invoice.writtenOffAmount,
										toValue: writtenOffAmount
									})
								}

								if (invoice.writtenOff) {
									Models.InvoiceEvent.create({
										invoiceId: invoice.id,
										userId: userId,
										changeType: 'Written Off',
										field: 'writtenOff',
										fromValue: true,
										toValue: false
									})
								}


								if (remaining === 0) {
									Models.Invoice.update({
										amountRemaining: remaining,
										writtenOffAmount: writtenOffAmount,
										sendInvoice: false,
										writtenOff: false
									}, {
										where: {
											id: invoice.id
										},
										limit: 1
									})
								} else {
									Models.Invoice.update({
										amountRemaining: remaining
									}, {
										where: {
											id: invoice.id
										},
										limit: 1
									})
								}

								Models.InvoiceEvent.create({
									invoiceId: invoice.id,
									userId: userId,
									changeType: 'Amount Remaining',
									field: 'amountRemaining',
									fromValue: invoice.amountRemaining,
									toValue: remaining
								})

								if (remaining === 0) {
									//transition lead using the workflow for current stage
									var currentStageId = null
									var campaign = null
									var suppression
									if (lead.campaigns && lead.campaigns.length)
										campaign = lead.campaigns[0]

									if (campaign && campaign.campaignleads) {
										currentStageId = campaign.campaignleads.currentCampaignStageId
									}

									// if they are suppressed then use the stage from that instead and update the suppression rather than transitioning
									if (lead.suppressions && lead.suppressions.length) {
										suppression = lead.suppressions[0]
										currentStageId = suppression.campaignstageId
									}

									if (currentStageId) {
										var stage = _.findWhere(campaign.campaignstages, {
											id: currentStageId
										})

										if (stage && (stage.name == 'Collections' || stage.name == 'Bad Credit Cards')) {
											var disposition = stage.dispositions.find(d => d.name.indexOf('Invoice Payment') > -1)


											if (disposition && disposition.campaignstagedispositions) {
												var dis = disposition.campaignstagedispositions;
												var destination = null;
												if (dis.transitionCutOffDate && moment() > moment(dis.transitionCutOffDate)) {
													destination = dis.transitionCutOffDateDispositionId
												} else {
													destination = dis.transitionToCampaignStageId
												}
												if (suppression) {
													suppression.update({
														campaignstageId: destination
													}).catch(() => { })
												} else {
													transitionLead(Models, lead.id, destination, campaign.id)
												}

												completed++
												processed++
												updateProgress()
												resolve()
											} else {
												completed++
												processed++
												updateProgress()
												resolve()
											}
										} else {
											completed++
											processed++
											updateProgress()
											resolve()
											//not currently in collections so dont know where to send them
										}
									} else {
										completed++
										processed++
										updateProgress()
										resolve()
										//not in a stage so ignore
									}
								} else {
									//not a full payment so not transitioning
									completed++
									processed++
									updateProgress()
									resolve()
								}
							} else {
								//no valid invoice found
								ignored++
								processed++
								updateProgress()
								resolve()
							}
						} else {
							//no lead or invoices
							ignored++
							processed++
							updateProgress()
							resolve()
						}
					})
				}))

				Promise.all(promises).then(() => {
					updateHistory({ updated: completed, ignored: ignored })
					onComplete({
						linesRead: noOfRecords,
						invoicesUpdated: completed,
						invoicesIgnored: ignored,
						invalidRecords: invalidRecords
					})
				}).catch(err => {
					onComplete({
						err: err.message || err
					})
				})
			})
		})
	}

	parseCSVFile(filePath, true, onNewRecord, () => { }, done)
}