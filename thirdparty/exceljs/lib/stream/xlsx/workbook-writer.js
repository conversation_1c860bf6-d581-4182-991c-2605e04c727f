/**
 * Copyright (c) 2015 <PERSON><PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 * 
 */
"use strict";

var fs = require("fs");
var Promise = require("bluebird");
var _ = require("underscore");
var Archiver = require("archiver");

var utils = require("../../utils/utils");
var Enums = require("../../enums");
var StreamBuf = require("../../utils/stream-buf");

var RelType = require("../../xlsx/rel-type");
var StyleManager = require("../../xlsx/stylemanager");
var SharedStrings = require("../../utils/shared-strings");

var WorksheetWriter = require("./worksheet-writer");

var WorkbookWriter = module.exports = function(options) {
    options = options || {};
    //console.log(JSON.stringify(options, null, "    "))
    
    this.created = options.created || new Date();
    this.modified = options.modified || this.created;
    this.creator = options.creator || "ExcelJS";
    this.lastModifiedBy = options.lastModifiedBy || "ExcelJS";
    
    // using shared strings creates a smaller xlsx file but may use more memory
    this.useSharedStrings = options.useSharedStrings || false;
    this.sharedStrings = new SharedStrings();
    
    // style manager
    this.styles = options.useStyles ? new StyleManager() : new StyleManager.Mock();
    
    this._worksheets = [];
    
    this.zip = Archiver("zip");
    if (options.stream) {
        this.stream = options.stream;
    } else if (options.filename) {
        this.stream = fs.createWriteStream(options.filename);
    } else {
        this.stream = new StreamBuf();
    }
    this.zip.pipe(this.stream);
    
    // these bits can be added right now
    this.promise = Promise.all([
        this.addThemes(),
        this.addOfficeRels()
    ]);
}
WorkbookWriter.prototype = {
    _openStream: function(path) {
        var self = this;
        var stream = new StreamBuf({bufSize: 65536, batch: true});
        self.zip.append(stream, { name: path });
        stream.on('end', function() {
            stream.emit('zipped');
        });
        return stream;
    },
    _commitWorksheets: function() {
        var commitWorksheet = function(worksheet) {
            if (!worksheet.committed) {
                var deferred = Promise.defer();
                worksheet.stream.on('zipped', function() {
                    deferred.resolve();
                });
                worksheet.commit();    
                return deferred.promise;
            } else {
                return Promise.resolve();
            }
        };
        // if there are any uncommitted worksheets, commit them now and wait
        var promises = this._worksheets.map(commitWorksheet);
        if (promises.length) {
            return Promise.all(promises);
        } else {
            return Promise.resolve();
        }
    },
    commit: function() {
        var self = this;
        
        // commit all worksheets, then add suplimentary files
        return this.promise.then(function() {
            return self._commitWorksheets();
        })
        .then(function() {
            return Promise.all([
                self.addContentTypes(),
                self.addApp(),
                self.addCore(),
                self.addSharedStrings(),
                self.addStyles(),
                self.addWorkbookRels()
            ]);
        })
        .then(function() {
            return self.addWorkbook();
        })
        .then(function(){
            return self._finalize();
        });
    },
    get nextId() {
        // find the next unique spot to add worksheet
        var i;
        for (i = 1; i < this._worksheets.length; i++) {
            if (!this._worksheets[i]) {
                return i;
            }
        }
        return this._worksheets.length || 1;
    },
    addWorksheet: function(name, options) {
        // it's possible to add a worksheet with different than default
        // shared string handling
        // in fact, it's even possible to switch it mid-sheet
        options = options || {};
        var useSharedStrings = options.useSharedStrings !== undefined ?
            options.useSharedStrings :
            this.useSharedStrings;
        
        var id = this.nextId;
        name = name || "sheet" + id;
        
        var worksheet = new WorksheetWriter({
            id: id,
            name: name,
            workbook: this,
            useSharedStrings: useSharedStrings
        });
        
        this._worksheets[id] = worksheet;
        return worksheet;
    },
    getWorksheet: function(id) {
        if (id === undefined) {
            return _.find(this._worksheets, function(worksheet) { return true; });
        } else if (typeof(id) === "number") {
            return this._worksheets[id];
        } else if (typeof id === "string") {
            return _.find(this._worksheets, function(worksheet) {
                return worksheet.name == id;
            });
        } else {
            return undefined;
        }
    },
    addStyles: function() {
        return this.styles.addToZip(this.zip);
    },
    addThemes: function() {
        var self = this;
        return utils.readModuleFile(require.resolve("../../xlsx/theme1.xml"))
            .then(function(data){
                self.zip.append(data, { name: "xl/theme/theme1.xml" });
            });
    },
    addOfficeRels: function() {
        var self = this;
        var rels = {
            relationships: [
                { rId: "rId1", type: RelType.OfficeDocument, target: "xl/workbook.xml" }
            ]
        };
        return utils.fetchTemplate(require.resolve("../../xlsx/.rels"))
            .then(function(template) {
                return template(rels);
            })
            .then(function(data) {
                self.zip.append(data, { name: "/_rels/.rels" });
            });
    },
    
    addContentTypes: function() {
        var self = this;
        return utils.fetchTemplate(require.resolve("../../xlsx/content-types.xml"))
            .then(function(template){
                return template({
                    worksheets: self._worksheets
                });
            })
            .then(function(data) {
                self.zip.append(data, { name: "[Content_Types].xml" });
            });
    },
    addApp: function() {
        var self = this;
        return utils.fetchTemplate(require.resolve("../../xlsx/app.xml"))
            .then(function(template){
                return template({
                    worksheets: self._worksheets
                });
            })
            .then(function(data) {
                self.zip.append(data, { name: "docProps/app.xml" });
            });
    },
    addCore: function() {
        var self = this;
        return utils.fetchTemplate(require.resolve("../../xlsx/core.xml"))
            .then(function(template){
                return template(self);
            })
            .then(function(data) {
                self.zip.append(data, { name: "docProps/core.xml" });
            });
    },
    addSharedStrings: function() {
        var self = this;
        if (this.sharedStrings.count) {
            return utils.fetchTemplate(require.resolve("../../xlsx/sharedStrings.xml"))
                .then(function(template) {
                    return template(self.sharedStrings);
                })
                .then(function(data) {
                    self.zip.append(data, { name: "/xl/sharedStrings.xml" });
                });
        } else {
            return Promise.resolve();
        }
    },
    addWorkbookRels: function() {
        var self = this;
        var count = 1;
        var workbookRels = {
            relationships: [
                { rId: "rId" + (count++), type: RelType.Styles, target: "styles.xml" },
                { rId: "rId" + (count++), type: RelType.Theme, target: "theme/theme1.xml" }
            ]
        };
        if (this.sharedStrings.count) {
            workbookRels.relationships.push(
                { rId: "rId" + (count++), type: RelType.SharedStrings, target: "sharedStrings.xml" }
            );
        }
        _.each(this._worksheets, function(worksheet) {
            worksheet.rId = "rId" + (count++);
            workbookRels.relationships.push(
                { rId: worksheet.rId, type: RelType.Worksheet, target: "worksheets/sheet" + worksheet.id + ".xml" }
            );
        });
        return utils.fetchTemplate(require.resolve("../../xlsx/.rels"))
            .then(function(template) {
                return template(workbookRels);
            })
            .then(function(data) {
                self.zip.append(data, { name: "/xl/_rels/workbook.xml.rels" });
            });
    },
    addWorkbook: function() {
        var self = this;
        return utils.fetchTemplate(require.resolve("../../xlsx/workbook.xml"))
            .then(function(template){
                return template({
                    worksheets: self._worksheets
                });
            })
            .then(function(data) {
                self.zip.append(data, { name: "/xl/workbook.xml" });
            });
    },
    _finalize: function() {
        var self = this;
        var deferred = Promise.defer();
        
        this.stream.on('error', function(error){
            deferred.reject(error);
        });
        this.stream.on('finish', function(){
            deferred.resolve(self);
        });
        this.zip.on('error', function(error){
            deferred.reject(error);
        });
        
        this.zip.finalize();
        
        return deferred.promise;
    }
};

