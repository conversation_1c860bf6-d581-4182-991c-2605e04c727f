<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<worksheet
    xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main"
    xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="x14ac"
    xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac">

  <% if (typeof tabColor !== 'undefined') { %>
  <sheetPr>
    <tabColor rgb="<%=tabColor%>"/>
  </sheetPr>
  <% } %>

  <dimension ref="<%=dimensions%>"/>
  <sheetViews>
    <sheetView workbookViewId="0"/>
  </sheetViews>
  <sheetFormatPr defaultRowHeight="15" x14ac:dyDescent="0.25"/>
  
  <% if (cols) { %>
  <cols>
    <% _.each(cols, function(col) { %>
      <col min="<%=col.min%>" 
         max="<%=col.max%>" 
         width="<%=col.width%>"
         <% if (col.styleId) { %> style="<%=col.styleId%>" <% } %>
         <% if (col.hidden) { %> hidden="1" <%  } %>
         customWidth="1"/><% }); %>
  </cols>
  <% } %>
  
  <sheetData>
    <% _.each(rows, function(row) { %>
      <row r="<%=row.number%>"
           <% if(row.height) {%> ht="<%=row.height%>" customHeight="1"<% } %>
           <% if(row.hidden) {%> hidden="1"<% } %>
           spans="<%=row.min%>:<%=row.max%>"
           <% if (row.styleId) { %> s="<%=row.styleId%>" customFormat="1" <% } %>
           x14ac:dyDescent="0.25">
      <% _.each(row.cells, function(cell){ %>
        <%=cell.xml%><% }); %>
      </row>
    <% }); %>
  </sheetData>

  <% if (merges) { %>
  <mergeCells count="2">
    <% _.each(merges, function(dimensions) { %>
    <mergeCell ref="<%=dimensions%>"/>
    <% }); %>
  </mergeCells>
  <% } %>
  
  
  <% if (hyperlinks) { %>
  <hyperlinks>
    <% _.each(hyperlinks, function(hyperlink) { %>
    <hyperlink ref="<%=hyperlink.address%>" r:id="<%=hyperlink.rId%>"/>    
    <% }); %>
  </hyperlinks>
  <% } %>
  
  <pageMargins left="0.7" right="0.7" top="0.75" bottom="0.75" header="0.3" footer="0.3"/>
</worksheet>