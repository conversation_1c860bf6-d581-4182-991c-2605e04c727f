/**
 * Copyright (c) 2015 <PERSON><PERSON> Roche
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 * 
 */
"use strict";

var utils = require("./utils");

var SharedStrings = module.exports = function() {
    this._values = [];
    this._totalRefs = 0;
    this._hash = {}
};

SharedStrings.prototype = {
    get count() {
        return this._values.length;
    },
    get values() {
        return this._values;
    },
    get totalRefs() {
        return this._totalRefs;
    },
    
    getString: function(index) {
        return this._values[index];
    },
    
    add: function(value) {
        value = utils.xmlEncode(value);
        var index = this._hash[value];
        if (index === undefined) {
            index = this._hash[value] = this._values.length;
            this._values.push(value);
        }
        this._totalRefs++;
        return index;
    }
};

