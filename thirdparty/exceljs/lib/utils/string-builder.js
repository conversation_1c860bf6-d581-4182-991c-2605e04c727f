/**
 * Copyright (c) 2015 <PERSON><PERSON>
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 * 
 */
"use strict";

var utils = require("./utils");

var nop = function() {};

// StringBuf - a way to keep string memory operations to a minimum
// while building the strings for the xml files
var StringBuf = module.exports = function(options) {
    this.reset();
}

StringBuf.prototype = {
    get length() {
        return this._buf.length;
    },
    toString: function() {
        return this._buf.join('');
    },
    
    reset: function(position) {
        if (position) {
            while (this._buf.length > position) {
                this._buf.pop();
            }
        } else {
            this._buf = [];
        }
    },
    addText: function(text) {
        this._buf.push(text);
    },
    
    //addText: function() {
    //    for (var i = 0; i < arguments.length; i++) {
    //        this._addText(arguments[i]);
    //    }
    //},
    
    addStringBuf: function(inBuf) {
        this._buf.push(inBuf.toString());
    }
};
