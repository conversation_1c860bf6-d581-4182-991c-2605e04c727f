{"name": "exceljs", "version": "0.2.4", "description": "Excel Workbook Manager", "private": false, "license": "MIT", "author": {"name": "<PERSON><PERSON> Roche", "email": "<EMAIL>", "url": "https://github.com/guyonroche/exceljs"}, "repository": {"type": "git", "url": "git+https://github.com/guyonroche/exceljs.git"}, "keywords": ["xlsx", "json", "csv", "excel", "font", "border", "fill", "number", "format", "number format", "alignment", "office", "spreadsheet", "workbook"], "dependencies": {"archiver": "0.14.3", "bluebird": "*", "fast-csv": "0.5.7", "moment": "~2.9.0", "sax": "^0.6.0", "underscore": "1.4.4", "unzip": ">= 0.1.9"}, "devDependencies": {"jasmine-node": "~1.14", "memorystream": "*"}, "scripts": {"test": "jasmine-node spec"}, "main": "./excel.js", "files": ["excel.js", "lib", "LICENSE", "README.md", "MODEL.md"], "gitHead": "6bb901cad78c4560dd5f321385424b09b19dd47b", "bugs": {"url": "https://github.com/guyonroche/exceljs/issues"}, "homepage": "https://github.com/guyonroche/exceljs#readme", "_id": "exceljs@0.2.4", "_shasum": "5f781fecefcd521cdec1972067fd00f640883244", "_from": "exceljs@>=0.2.3 <0.3.0", "_npmVersion": "3.3.6", "_nodeVersion": "5.0.0", "_npmUser": {"name": "guyonroche", "email": "<EMAIL>"}, "maintainers": [{"name": "guyonroche", "email": "<EMAIL>"}], "dist": {"shasum": "5f781fecefcd521cdec1972067fd00f640883244", "tarball": "http://registry.npmjs.org/exceljs/-/exceljs-0.2.4.tgz"}, "_npmOperationalInternal": {"host": "packages-6-west.internal.npmjs.com", "tmp": "tmp/exceljs-0.2.4.tgz_1454439647731_0.2561695547774434"}, "directories": {}, "_resolved": "https://registry.npmjs.org/exceljs/-/exceljs-0.2.4.tgz"}