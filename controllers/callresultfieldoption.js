module.exports = function (app, Models, BASE_URL) {
	// Get callresultfieldoptions list
	app.get(BASE_URL + '/callresultfieldoptions', function (req, res) {
		Models.CallResultFieldOption.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get callresultfieldoption by id
	app.get(BASE_URL + '/callresultfieldoptions/:id', function (req, res) {
		Models.CallResultFieldOption.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create callresultfieldoption
	app.post(BASE_URL + '/callresultfieldoptions', function (req, res) {
		Models.CallResultFieldOption.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update callresultfieldoption
	app.put(BASE_URL + '/callresultfieldoptions/:id', function (req, res) {
		Models.CallResultFieldOption.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete callresultfieldoption
	app.delete(BASE_URL + '/callresultfieldoptions/:id', function (req, res) {
		Models.CallResultFieldOption.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}