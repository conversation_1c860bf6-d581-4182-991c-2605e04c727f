module.exports = function (app, Models, BASE_URL) {
	var email = require('../emailing/email')(Models)

	// Get skill list
	app.get(BASE_URL + '/emailhistory', function (req, res) {
		Models.EmailHistory.findAll()
			.then(function (items) {
				items.forEach(function (item) {
					item.emailSettings = JSON.parse(item.emailSettings)
					item.mailOptions = JSON.parse(item.mailOptions)
				})
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get skill by id
	app.get(BASE_URL + '/emailhistory/:id', function (req, res) {
		Models.EmailHistory.findById(req.params.id)
			.then(function (result) {
				result.emailSettings = JSON.parse(result.emailSettings)
				result.mailOptions = JSON.parse(result.mailOptions)
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Create skill
	app.post(BASE_URL + '/emailhistory', function (req, res) {
		if (req.body.emailSettings) {
			req.body.emailSettings = JSON.stringify(req.body.emailSettings)
		}
		if (req.body.mailOptions) {
			req.body.mailOptions = JSON.stringify(req.body.mailOptions)
		}
		Models.EmailHistory.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update skill
	app.put(BASE_URL + '/emailhistory/:id', function (req, res) {
		Models.EmailHistory.findById(req.params.id)
			.then(function (result) {
				if (req.body.emailSettings) {
					req.body.emailSettings = JSON.stringify(req.body.emailSettings)
				}
				if (req.body.mailOptions) {
					req.body.mailOptions = JSON.stringify(req.body.mailOptions)
				}
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.put(BASE_URL + '/emailhistory/:id/resend', function (req, res) {
		Models.EmailHistory.findById(req.params.id)
			.then(function (result) {
				var emailSettings = {}
				var mailOptions = {}
				if (req.body.emailSettings) {
					emailSettings = JSON.stringify(req.body.emailSettings)
				}
				if (req.body.mailOptions) {
					mailOptions = JSON.stringify(req.body.mailOptions)
				}
				email.resend(emailSettings, mailOptions)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete skill
	app.delete(BASE_URL + '/emailhistory/:id', function (req, res) {
		Models.EmailHistory.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}