module.exports = function (app, Models, BASE_URL) {
	// Get callresultfieldtypes list
	app.get(BASE_URL + '/callresultfieldtypes', function (req, res) {
		Models.CallResultFieldType.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get callresultfieldtype by id
	app.get(BASE_URL + '/callresultfieldtypes/:id', function (req, res) {
		Models.CallResultFieldType.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create callresultfieldtype
	app.post(BASE_URL + '/callresultfieldtypes', function (req, res) {
		Models.CallResultFieldType.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update callresultfieldtype
	app.put(BASE_URL + '/callresultfieldtypes/:id', function (req, res) {
		Models.CallResultFieldType.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete callresultfieldtype
	app.delete(BASE_URL + '/callresultfieldtypes/:id', function (req, res) {
		Models.CallResultFieldType.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}