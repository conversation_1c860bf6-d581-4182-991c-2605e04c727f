module.exports = function (app, Models, BASE_URL) {
	// Get agentstate list
	app.get(BASE_URL + '/agentstates', function (req, res) {
		Models.AgentState.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get agentstate by id
	app.get(BASE_URL + '/agentstates/:id', function (req, res) {
		Models.AgentState.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create agentstate
	app.post(BASE_URL + '/agentstates', function (req, res) {
		Models.AgentState.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update agentstate
	app.put(BASE_URL + '/agentstates/:id', function (req, res) {
		Models.AgentState.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete agentstate
	app.delete(BASE_URL + '/agentstates/:id', function (req, res) {
		Models.AgentState.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}