module.exports = function (app, Models, BASE_URL) {
	// Get client products list
	app.get(BASE_URL + '/campaignproducts', function (req, res) {
		Models.CampaignProduct.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get client product by id
	app.get(BASE_URL + '/campaignproducts/:id', function (req, res) {
		Models.CampaignProduct.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create client product
	app.post(BASE_URL + '/campaignproducts', function (req, res) {
		Models.CampaignProduct.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update client product
	app.put(BASE_URL + '/campaignproducts/:id', function (req, res) {
		Models.CampaignProduct.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete client product
	app.delete(BASE_URL + '/campaignproducts/:id', function (req, res) {
		Models.CampaignProduct.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.delete(BASE_URL + '/campaignproducts/campaigns/:id', function (req, res) {
		Models.CampaignProduct.destroy({
			where: {
				campaignId: req.params.id
			}
		})
		.then(() => {
			res.status(200).send({ success: true })
		})
		.catch(err => {
			return res.status(500).send({ error: err.message })
		})
	})
}