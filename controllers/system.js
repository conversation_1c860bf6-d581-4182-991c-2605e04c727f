var taskProgressAPI = require('../database/redisAPI')('threadtasks')
var jobQueue = require('../database/queueWrapper')

module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/system', function (req, res) {
		Models.System.findAll()
			.then(function (items) {
				items.forEach(function (item) {
					if (item.value) {
						item.value = JSON.parse(item.value)
					}
				})
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/system/:key', function (req, res) {
		Models.System.findOne({
				where: {
					key: req.params.key
				}
			})
			.then(function (result) {
				if (!result) {
					res.status(200).send({})
				} else {
					if (result.value) {
						result.value = JSON.parse(result.value)
					}
					res.status(200).send(result)
				}

			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/system/jobstatus/:jobId', (req, res) => {
		queueWrapper.getState(req.params.jobId)
			.then(result => {
				return res.status(200).send(result)
			})
			.catch(err => {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/system/threadtaskprogress/:taskId', function (req, res) {
		taskProgressAPI.get(req.params.taskId, 'progress').then(function (result) {
			return res.status(200).send(result)
		})
	})

	app.post(BASE_URL + '/system', function (req, res) {
		Models.System.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.put(BASE_URL + '/system/:key', function (req, res) {
		Models.System.findOne({
				where: {
					key: req.params.key
				}
			})
			.then(function (result) {
				if (req.body.value) {
					req.body.value = JSON.stringify(req.body.value)
				}
				if (result) {
					result.updateAttributes(req.body)
						.then(function (result) {
							res.status(200).send(result)
						})
						.catch(function (err) {
							return res.status(500).send({
								error: err.message
							})
						})
				} else {
					Models.System.create({
							key: req.params.key,
							value: req.body.value
						})
						.then(function (result) {
							res.status(200).send(result)
						})
						.catch(function (err) {
							return res.status(500).send({
								error: err.message
							})
						})
				}
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.delete(BASE_URL + '/system/:key', function (req, res) {
		Models.System.destroy({
				where: {
					key: req.params.key
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}