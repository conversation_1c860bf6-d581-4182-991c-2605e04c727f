module.exports = function (app, Models, BASE_URL) {
	// Get leadfield list
	app.get(BASE_URL + '/leadfields', function (req, res) {
		Models.LeadField.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get leadfield by id
	app.get(BASE_URL + '/leadfields/:id', function (req, res) {
		Models.LeadField.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create leadfield
	app.post(BASE_URL + '/leadfields', function (req, res) {
		Models.LeadField.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update leadfield
	app.put(BASE_URL + '/leadfields/:id', function (req, res) {
		Models.LeadField.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete leadfield
	app.delete(BASE_URL + '/leadfields/:id', function (req, res) {
		Models.LeadField.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}