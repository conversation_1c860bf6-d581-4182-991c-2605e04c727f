var statsAPI = require('../database/redisAPI')('campaign')
var fs = require('fs')
var moment = require('moment')
var tsys = require('../utils/tsys.js')

module.exports = function (app, Models, BASE_URL) {
	// Get call result list
	app.get(BASE_URL + '/callresults', function (req, res) {
		Models.CallResult.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get call result by id
	app.get(BASE_URL + '/callresults/:id', function (req, res) {
		Models.CallResult.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Create call result
	app.post(BASE_URL + '/callresults', function (req, res) {
		Models.CallResult.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/testpayment', function (req, res) {
		if (!req.body.amount) {
			return res.status(400).json({
				Success: false,
				message: 'Amount Required'
			})
		}
		if (!req.body.creditCardNumber) {
			return res.status(400).json({
				Success: false,
				message: 'CC Number Required'
			})
		}
		if (!req.body.creditCardDate) {
			return res.status(400).json({
				Success: false,
				message: 'CC Date Required'
			})
		}
		if (!req.body.creditCardPin) {
			return res.status(400).json({
				Success: false,
				message: 'CC Pin Required'
			})
		}
		var payment = {}
		return tsys.authTransaction(req.body.amount, req.body.creditCardNumber, req.body.creditCardPin, req.body.creditCardDate, req.body.address1, req.body.zip, req.body.clientId, Models)
			.then(function (response) {
				console.log(response)
				payment.authResponse = response
				return tsys.chargeTransaction(response.transactionID, null, null, Models)
			}).then(function (response) {
				console.log(response)
				payment.chargeResponse = response
				fs.appendFile('testpayment.txt', moment().format('YYYY-MM-DD HH:mm:ss') + ' - ' + JSON.stringify(payment, null, 2) + '\r\n\r\n', () => { })
			}).then(function () {
				res.status(200).json(payment)
			}).catch(function (err) {
				console.log(err)
				return res.status(500).send({
					error: err ? err.message || err : 'Error Taking Payment'
				})
			})
	})

	app.post(BASE_URL + '/callresults/:id/sale', function (req, res) {
		if (!req.body.amount) {
			return req.status(400).json({
				Success: false,
				message: 'Amount Required'
			})
		}

		var amount = req.body.amount
		var cvv2 = req.body.digits

		var callResult, paymentLog, cardToken

		Models.CallResult.findById(req.params.id, {
			include: [{
				model: Models.Lead,
			}]
		}).then(function (_callResult) {
			callResult = _callResult
			if (!callResult) return Promise.reject('Call Result Not Found')
			if (!callResult.lead) return Promise.reject('Lead Not Found')
			// go get the most recent card token (there are sometimes more than 1 for some reason)
			return Models.CardToken.findOne({
				where: {
					leadId: callResult.leadId
				},
				order: [['id', 'desc']]
			})
		}).then(_cardToken => {
			cardToken = _cardToken
			if (!cardToken) return Promise.reject('No Card On File')
			var date = moment(cardToken.expirationDate, 'MMYYYY').endOf('month')
			if (date < moment()) return Promise.reject('Card On File Expired')

			// if its a new attempt creat a new payment log, otherwise just use the existing one for this callresult

			return Models.PaymentLog.findOrCreate({
				where: {
					callresultId: callResult.id
				},
				defaults: {
					amount,
					isPaid: false,
					paymentDate: new Date(),
					campaignId: callResult.campaignId,
					clientId: req.body.clientId,
					leadId: callResult.leadId,
					userId: req.user.id,
					agentId: req.user.agentId,
					callresultId: callResult.id,
					invoiceId: req.body.invoiceId || null,
					inProgress: true,
					source: 'payment'
				}
			}).then(_paymentLog => {
				paymentLog = _paymentLog[0]
				if (paymentLog.isPaid) {
					return Promise.reject('Payment Already Taken')
				}
				if (!_paymentLog[1] && paymentLog.inProgress) {
					return Promise.reject('Payment Already In Progress')
				}
				paymentLog.update({ inProgress: true }).catch(() => { })
				try {
					fs.appendFile('tsyslog.txt', `${moment().format()}: Lead=${paymentLog.leadId} CallResult=${paymentLog.callresultId} PaymentLog=${paymentLog.id} Amount=${amount} Agent=${req.user.agentId}\r\n`, () => { })
				} catch (e) {

				}
				return tsys.makeSale(amount, cardToken.tsepToken, cardToken.expirationDate, callResult.lead.address1, callResult.lead.zip, 'PHONE', cardToken.cardType, cvv2, req.body.clientId, Models)
			})
		}).then(function (result) {
			var success = result.status === 'PASS';
			return paymentLog.update({
				amount,
				inProgress: false,
				isPaid: success,
				status: success ? 'paid' : 'error',
				actualPaymentDate: success ? new Date() : null,
				transactionID: result.transactionID,
				receipt: JSON.stringify(result, null, 2)
			}).then(() => {
				return Models.PaymentLogHistory.create({
					amount: paymentLog.amount,
					isPaid: success,
					receipt: JSON.stringify(result, null, 2),
					transactionID: result.transactionID,
					error: success ? '' : result.responseMessage,
					maskedCardNumber: cardToken.maskedCardNumber,
					cardType: cardToken.cardType,
					expirationDate: cardToken.expirationDate,
					paymentlogId: paymentLog.id,
					leadId: paymentLog.leadId,
					userId: paymentLog.userId,
					clientId: paymentLog.clientId,
					campaignId: paymentLog.campaignId,
					invoiceId: paymentLog.invoiceId,
					callresultId: paymentLog.callresultId
				})
			}).then(() => {
				if (success) return paymentLog
				else return Promise.reject("The card has been declined by the customer's card issuer. Please ask for another form of payment and recommend the customer contact their bank\n\n" + result.responseMessage)
			})
		}).then(function (payment) {
			res.status(200).json({
				Success: true,
				payment
			})
		}).catch(function (err) {
			if (paymentLog && paymentLog.update) {
				paymentLog.update({ inProgress: false }).catch(() => { })
			}
			return res.status(200).send({
				Success: false,
				error: err ? err.message || err : 'Error Taking Payment'
			})
		})
	})

	app.put(BASE_URL + '/callresults/:id/removePledge', function (req, res) {
		Models.CallResult.findById(req.params.id, {
			include: [Models.Invoice]
		}).then(function (callresult) {
			if (callresult) {
				if (callresult.invoice) {
					callresult.invoice.destroy()
				}
				callresult.updateAttributes({
					wrapup: 'No Resolution',
					giftAmount: null,
					freeTickets: null,
					paymentType: null,
					creditCardType: null,
					creditCardNumber: null,
					creditCardExpDate: null,
					creditCardSecurityCode: null,
					installmentNotes: null,
					numberOfInstallments: null,
					requiresFollowUp: null,
					payDate: null,
					giftMatchingCompany: null,
					declineBenefits: null,
					invoiceMethod: null,
					grandTotal: null,
					saleAmount: null,
					newMembershipCard: null,
					useExistingCreditCard: null
				})
					.then(function () {
						res.status(200).send({
							result: true
						})
					})
			}
		})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.put(BASE_URL + '/callresults/:id/removeSale', function (req, res) {
		Models.CallResult.findById(req.params.id, {
			include: [Models.Invoice, Models.Sale]
		}).then(function (callresult) {
			if (callresult) {
				if (callresult.invoice) {
					callresult.invoice.destroy()
				}
				if (callresult.sales && callresult.sales.length) {
					callresult.sales.forEach(function (s) {
						s.destroy()
					})
				}
				callresult.updateAttributes({
					wrapup: 'No Resolution',
					giftAmount: null,
					freeTickets: null,
					paymentType: null,
					creditCardType: null,
					creditCardNumber: null,
					creditCardExpDate: null,
					creditCardSecurityCode: null,
					installmentNotes: null,
					numberOfInstallments: null,
					requiresFollowUp: null,
					payDate: null,
					giftMatchingCompany: null,
					declineBenefits: null,
					invoiceMethod: null,
					grandTotal: null,
					saleAmount: null,
					newMembershipCard: null,
					useExistingCreditCard: null
				})
					.then(function () {
						res.status(200).send({
							result: true
						})
					})
			}
		})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update call result
	app.put(BASE_URL + '/callresults/:id', function (req, res, next) {
		try {
			//update redis stats
			var cr = req.body

			statsAPI.increment(cr.campaignId, 'callCount')
				.then(function () { })
				.catch(function (err) {
					console.log(`callCount for campaignId ${cr.campaignId}`)
					console.log(err)
				})
			if (cr.giftAmount || cr.saleAmount) {
				if (cr.giftAmount) {
					statsAPI.incrementBy(cr.campaignId, 'totalGiftAmount', cr.giftAmount)
						.then(function () { })
						.catch(function (err) {
							console.log(`totalGiftAmount for campaignId ${cr.campaignId}`)
							console.log(err)
						})

					statsAPI.increment(cr.campaignId, 'totalGiftCount')
						.then(function () { })
						.catch(err => {
							console.log(`totalGiftCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})


					statsAPI.incrementBy(cr.campaignId, cr.skill + 'GiftAmount', cr.giftAmount)
						.then(function () { })
						.catch(err => {
							console.log(`GiftAmount for campaignId ${cr.campaignId}`)
							console.log(err)
						})

					statsAPI.increment(cr.campaignId, cr.skill + 'GiftCount')
						.then(function () { })
						.catch(err => {
							console.log(`GiftCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})
				}
				if (cr.saleAmount) {
					statsAPI.incrementBy(cr.campaignId, 'totalSaleAmount', cr.saleAmount)
						.then(function () { })
						.catch(err => {
							console.log(`totalSaleAmount for campaignId ${cr.campaignId}`)
							console.log(err)
						})

					statsAPI.increment(cr.campaignId, 'totalSaleCount')
						.then(function () { })
						.catch(err => {
							console.log(`totalSaleCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})

					statsAPI.incrementBy(cr.campaignId, cr.skill + 'SaleAmount', cr.saleAmount)
						.then(function () { })
						.catch(err => {
							console.log(`SaleAmount for campaignId ${cr.campaignId}`)
							console.log(err)
						})

					statsAPI.increment(cr.campaignId, cr.skill + 'SaleCount')
						.then(function () { })
						.catch(err => {
							console.log(`SaleCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})
				}
				if (!cr.declineBenefits) {
					statsAPI.increment(cr.campaignId, 'addonCount')
						.then(() => { })
						.catch(err => {
							console.log(`addonCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})
				}
				if (cr.paymentType == 'Credit Card') {
					statsAPI.increment(cr.campaignId, 'ccCount')
						.then(() => { })
						.catch(err => {
							console.log(`ccCount for campaignId ${cr.campaignId}`)
							console.log(err)
						})
				}
			}

		} catch (err) {
			console.log(err.message)
		} finally {
			next()
		}
	})

	app.put(BASE_URL + '/callresults/:id', function (req, res, next) {
		Models.CallResult.findById(req.params.id)
			.then(function (result) {
				// delete callbacks if they are adding an expcetion refusal
				if (result.leadId && req.body.wrapup === 'Exception Refusal') {
					Models.Callback.update({ deleted: true }, {
						where: {
							leadId: result.leadId
						}
					}).then(() => {
						console.log('flagged callbacks as deleted for', result.leadId)
					}).catch(err => {
						console.log(err)
					})
				}
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete call result
	app.delete(BASE_URL + '/callresults/:id', function (req, res) {
		Models.CallResult.destroy({
			where: {
				id: req.params.id
			}
		})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}