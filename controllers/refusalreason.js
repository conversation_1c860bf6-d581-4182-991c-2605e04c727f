module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/refusalreasons', function (req, res) {
		Models.RefusalReason.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.get(BASE_URL + '/refusalreasons/:id', function (req, res) {
		Models.RefusalReason.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.post(BASE_URL + '/refusalreasons', function (req, res) {
		Models.RefusalReason.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.put(BASE_URL + '/refusalreasons/:id', function (req, res) {
		Models.RefusalReason.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.delete(BASE_URL + '/refusalreasons/:id', function (req, res) {
		Models.RefusalReason.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}