module.exports = function(app, Models, BASE_URL) {
    app.get(BASE_URL + '/leadupdates/:id', function(req, res) {
        Models.LeadUpdate.findAll({ leadId: req.params.id })
            .then(function(leadupdates) {
                Models.CampaignLeadUpdate.findAll({ leadId: req.params.id })
                    .then(campaignleadupdate => {
                        res.status(200).send({
                            leadUpdates: leadupdates,
                            campaignLeadUpdates: campaignleadupdate
                        })
                    })
                    .catch(function(err) {
                        return res.status(500).send({ error: err.message })
                    })
            })
            .catch(function(err) {
                return res.status(500).send({ error: err.message })
            })
    })

    app.delete(BASE_URL + '/leadupdates/:id', function(req, res) {
        Models.LeadUpdate.destroy({
                where: { id: req.params.id }
            })
            .then(function(result) {
                res.status(200).send({ success: true })
            })
            .catch(function(err) {
                return res.status(500).send({ error: err.message })
            })
    })
}