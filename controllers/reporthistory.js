var _ = require('underscore')
var moment = require('moment')
var path = require('path')
var fs = require('fs')
var nodemailer = require('nodemailer')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var fileUtils = require('../utils/fileUtils.js')
var ensureDirectoryExists = fileUtils.ensureDirectoryExists
var APP_CONFIG = require('../config/constants')
var streamToCsv = require('../reporting/streamToCsv')
var encryption = require('../utils/encryption')()

Promise.promisifyAll(fs);

module.exports = function (app, Models, BASE_URL, db) {
	var reportBuilder = require('../reporting/reportBuilder')(Models, db)
	var excel = require('../reporting/exportToExcel')(Models)
	var email = require('../emailing/email')(Models)
	var salesOrderGenerator = require('../salesorders/salesOrdersGenerator.js')(Models)

	app.get(BASE_URL + '/reporthistories', function (req, res) {
		Models.ReportHistory.findAll({
			include: [Models.ReportHistoryAudit]
		}).then(function (items) {
			res.status(200).send(items)
		}).catch(function (err) {
			return res.status(500).send({
				error: err ? (err.message || err) : {}
			})
		})
	})

	app.get(BASE_URL + '/reporthistories/:id', function (req, res) {
		Models.ReportHistory.find({
			where: {
				id: req.params.id
			},
			include: [Models.Report, Models.ReportHistoryAudit]
		}).then(function (result) {
			if (result.report.passwordProtected) {
				if (!req.user.isClientAdmin && req.user.client && req.user.client.reportPasswordRequired) {
					if (!req.user.client.reportPassword) {
						return res.status(400).json({
							error: 'Password Requires Updating'
						})
					}
					if (moment(req.user.client.reportPasswordLastChange) < moment().subtract(90, 'days')) {
						return res.status(400).json({
							error: 'Password Requires Updating'
						})
					}
				}
			}

			var filePath = result.path
			var fileName = result.name
			var promise = Promise.resolve()
			if (result.report.passwordProtected && req.user.client && req.user.client.reportPasswordRequired) {
				promise = zipUpReport(result, encryption.decrypt(req.user.client.reportPassword)).then(zipped => {
					filePath = zipped
					fileName = result.name.replace('.xlsx', '.zip')
				}).catch(err => {
					console.log('Error Zipping File', err)
				})
			}
			promise.then(() => {
				res.status(200).sendFile(filePath, {
					headers: {
						'content-disposition': 'attachment; filename=' + fileName
					}
				})
			})

		}).catch(function (err) {
			return res.status(500).send({
				error: err ? (err.message || err) : {}
			})
		})
	})

	app.get(BASE_URL + '/reporthistories/:id/audit', function (req, res) {
		Models.ReportHistoryAudit.findAll({
			where: {
				reporthistoryId: req.params.id
			},
			include: [Models.ReportHistoryAudit]
		}).then(function (result) {
			if (req.query && req.query.countOnly)
				res.status(200).send({
					count: result.length
				})
			else
				res.status(200).send(result)
		}).catch(function (err) {
			return res.status(500).send({
				error: err ? (err.message || err) : {}
			})
		})
	})

	app.post(BASE_URL + '/reporthistories', function (req, res) {
		Models.Report.find({
			where: {
				id: req.body.reportId
			}
		}).then(function (result) {
			var reportModel = JSON.parse(result.definition)
			var report = result
			if (reportModel.asCsv) {
				reportModel.whereClause = req.body.whereClause
				var targetDir = path.resolve('./saved_reports/')
				var name = report.name + '-' + moment().format('DD-MMM-YY h-mm-ss.SSS a') + '.csv'
				var destinationFilePath = path.join(targetDir, name)
				ensureDirectoryExists(targetDir, () => {
					var finished = false
					var emailInstead = false
					setTimeout(() => {
						//if it has not finished by now just email a link when finished
						if (!finished) {
							emailInstead = true
							res.status(200).send({
								email: true
							})
						}
					}, 10000)
					streamToCsv.run(reportModel, destinationFilePath).then(() => {
						var history = req.body
						history.path = destinationFilePath
						history.name = name
						history.filters = JSON.stringify(req.body.filters)
						Models.ReportHistory.create(history).then(function (history) {
							finished = true
							if (emailInstead) {
								//email a the user who created it (req.body.userId) a link to download the report
								Models.ReportHistory.findOne({
									where: {
										id: history.id
									},
									include: [Models.Report]
								}).then(reportHistory => {
									Models.User.findById(req.body.userId).then(user => {
										if (user.email) {
											email.emailReportLinkWithTemplate(reportHistory, user.email)
										}
									})
								})
							} else {
								res.status(200).send(history)
							}
						})
					}).catch(err => {
						console.log(err)
					})
				})
			}
			else {
				var finished = false
				var emailInstead = false
				setTimeout(() => {
					//if it has not finished by now just email a link when finished
					if (!finished) {
						emailInstead = true
						res.status(200).send({
							email: true
						})
					}
				}, 10000)
				var gererateResultsPromise = new Promise(function (resolve, reject) {
					if (reportModel instanceof Array) {
						//multiple reports to run and add to a single file
						var promises = []
						reportModel.forEach(model => {
							if (req.body.whereClause) {
								model.whereClause = req.body.whereClause
								model.unionWhereClause = req.body.unionWhereClause
							}

							if (req.body.placeholderReplacements) {
								req.body.placeholderReplacements.forEach(function (replac) {
									model.rawQuery = model.rawQuery.replaceAll(replac.placeholder, replac.value)
								})
							} else if (req.body.filters) {
								model = mergeFilters(model, req.body.filters)
								req.body.filters = _.map(req.body.filters, function (filter) {
									return {
										field: filter.model,
										value: filter.friendlyName
									}
								})
							}
							promises.push(reportBuilder.runReport(model))
						})
						resolve(Promise.all(promises))
					} else {
						if (req.body.whereClause) {
							reportModel.whereClause = req.body.whereClause
							reportModel.unionWhereClause = req.body.unionWhereClause
						}

						if (req.body.placeholderReplacements) {
							req.body.placeholderReplacements.forEach(function (replac) {
								reportModel.rawQuery = reportModel.rawQuery.replaceAll(replac.placeholder, replac.value)
							})
						} else if (req.body.filters) {
							reportModel = mergeFilters(reportModel, req.body.filters)
							req.body.filters = _.map(req.body.filters, function (filter) {
								return {
									field: filter.model,
									value: filter.friendlyName
								}
							})
						}
						resolve(reportBuilder.runReport(reportModel))
					}
				})

				gererateResultsPromise.then(function (results) {
					if (!results || !results.length) {
						finished = true
						return res.status(500).send({
							message: 'No data found'
						})
					} else {

						var hasData = false
						if (reportModel instanceof Array) {
							results.forEach(result => {
								if (hasData) return
								hasData = !!result.length
							})
						} else {
							hasData = true
						}
						if (!hasData) {
							finished = true
							return res.status(500).send({
								message: 'No data found'
							})
						}
						var targetDir = path.resolve('./saved_reports/')

						if (reportModel.format === 'PDF') {
							var name = report.name + '-' + moment().format('DD-MMM-YY h-mm-ss.SSS a') + '.zip'
							var destinationFilePath = path.join(targetDir, name)

							ensureDirectoryExists(targetDir, function () {

								var pdfPromise = new Promise(function(resolve, reject) {
									salesOrders = salesOrderGenerator.groupSalesRecords(results);

									console.log('executing sales order generator');

									resolve(salesOrderGenerator.generatePdfsForSalesOrders(salesOrders, reportModel.template, destinationFilePath))
								})

								pdfPromise.then(function (result) {
									var history = req.body
									history.path = destinationFilePath
									history.name = name
									history.filters = JSON.stringify(history.filters)

									Models.ReportHistory.create(history)
										.then(function (history) {
											finished = true
											if (emailInstead) {
												Models.ReportHistory.findOne({
													where: {
														id: history.id
													},
													include: [Models.Report]
												})
													.then(reportHistory => {
														Models.User.findById(req.body.userId)
															.then(user => {
																if (user.email) {
																	email.emailReportLinkWithTemplate(reportHistory, user.email)
																}
															})
													})
											} else {
												res.status(200).send(history)
											}
										})
								})
								.catch(function (err) {
									finished = true
									if (!emailInstead) {
										return res.status(500).send({
											error: err ? (err.message || err) : {}
										})
									} else {
										console.error(err)
									}
								})
							});
							
						} else {
							var name = report.name + '-' + moment().format('DD-MMM-YY h-mm-ss.SSS a') + '.xlsx'
							var destinationFilePath = path.join(targetDir, name)

							ensureDirectoryExists(targetDir, function () {

								var excelPromise = new Promise(function (resolve, reject) {
									if (reportModel.dynamicGrouping) {
										// split the results by the dynamic grouping and add each to its own sheet
										var names = []
										var arr = []
										results.forEach(row => {
											if (row[reportModel.dynamicGrouping]) {
												var name = row[reportModel.dynamicGrouping]
												// excel doesnt support /\?*[] in sheet names so replace them with spaces
												var cleanName = name.split('/').join(' ').split('\\').join(' ').split('*').join(' ').split('?').join(' ').split('[').join(' ').split(']').join(' ')
												// excel doesnt support sheet names over 31 characters so trim it down
												if (cleanName.length > 31) {
													cleanName = cleanName.substr(0, 31)
												}
												var index = names.indexOf(cleanName)
												if (index === -1) {
													names.push(cleanName)
													index = names.length - 1
													arr[index] = []
												}
												arr[index].push(row)
											}
										})
										resolve(excel.exportMultipleDynamicToExcel(arr, names, reportModel, destinationFilePath))
									} else if (reportModel instanceof Array) {
										resolve(excel.exportMultipleToExcel(results, destinationFilePath, reportModel))
									} else {
										resolve(excel.exportToExcel(results, destinationFilePath, reportModel))
									}
								})
								excelPromise.then(function () {
									var history = req.body
									history.path = destinationFilePath
									history.name = name
									history.filters = JSON.stringify(history.filters)

									Models.ReportHistory.create(history)
										.then(function (history) {
											finished = true
											if (emailInstead) {
												Models.ReportHistory.findOne({
													where: {
														id: history.id
													},
													include: [Models.Report]
												})
													.then(reportHistory => {
														Models.User.findById(req.body.userId)
															.then(user => {
																if (user.email) {
																	email.emailReportLinkWithTemplate(reportHistory, user.email)
																}
															})
													})
											} else {
												res.status(200).send(history)
											}
										})
								})
								.catch(function (err) {
									finished = true
									if (!emailInstead) {
										return res.status(500).send({
											error: err ? (err.message || err) : {}
										})
									} else {
										console.error(err)
									}
								})
							})
						}
					}
				})
					.catch(function (err) {
						finished = true
						if (!emailInstead) {
							return res.status(500).send({
								error: err ? (err.message || err) : {}
							})
						} else {
							console.error(err)
						}
					})

			}



		})
	})

	app.put(BASE_URL + '/reporthistories/:id', function (req, res) {
		Models.ReportHistory.findById(req.params.id)
			.then(function (result) {
				req.body.filters = JSON.stringify(req.body.filters)
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err ? (err.message || err) : {}
						})
					})
			})
	})

	app.delete(BASE_URL + '/reporthistories/:id', function (req, res) {
		Models.ReportHistory.find({
			where: {
				id: req.params.id
			}
		})
			.then(function (history) {
				fs.unlink(history.path, function (err) {
					console.log(err ? (err.message || err) : {})
				})

				Models.ReportHistory.destroy({
					where: {
						id: req.params.id
					}
				})
					.then(function (result) {
						res.status(200).send({
							success: true
						})
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err ? (err.message || err) : {}
						})
					})
			})
	})

	app.post(BASE_URL + '/reporthistories/:id/email', function (req, res) {
		Models.ReportHistory.findOne({
			where: {
				id: req.params.id
			},
			include: [Models.ReportHistoryAudit, Models.Report]
		})
			.then(function (history) {
				if (history) {
					email.emailReportLinkWithTemplate(history, req.body.to, req.body.userId).then(function (result) {
						res.status(200).send(result)
					})
						.catch(function (err) {
							return res.status(500).send({
								error: err ? (err.message || err) : {}
							})
						})
				} else {
					res.status(500).send({
						message: 'No Report Found'
					})
				}
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err ? (err.message || err) : {}
				})
			})
	})

	function mergeFilters(definition, filters) {
		var addFilter = function (module, filter) {
			if (!module.filters) {
				module.filters = {
					and: [],
					or: []
				}
			}
			if (filter.type == 'and') {
				if (!module.filters.and) {
					module.filters.and = []
				}
				module.filters.and.push({
					fieldName: filter.field,
					operator: filter.operator,
					comparator: filter.value
				})
			} else {
				if (!module.filters.or) {
					module.filters.or = []
				}
				module.filters.or.push({
					fieldName: filter.field,
					operator: filter.operator,
					comparator: filter.value
				})
			}
		}

		for (var i = 0; i < filters.length; i++) {
			var filter = filters[i]
			if (filter.model == definition.primaryModule.name) {
				addFilter(definition.primaryModule, filter)
			} else {
				var model = _.findWhere(definition.primaryModule.relatedModules, {
					name: filter.model
				})
				if (model) {
					addFilter(model, filter)
				}
			}
		}

		return definition
	}
};

String.prototype.replaceAll = function (search, replacement) {
	var target = this;
	return target.replace(new RegExp(search, 'g'), replacement);
}

function zipUpReport(reporthistory, password) {
	return new Promise((resolve, reject) => {
		var spawn = require('child_process').spawn;
		var resolvedPath = path.resolve('./saved_reports/')
		var fullPath = path.join(resolvedPath, reporthistory.name.replace('.xlsx', '') + '.zip')
		var zip = spawn('zip', ['--password', password, '--junk-paths', fullPath, reporthistory.path]);
		zip.on('exit', code => {
			resolve(fullPath)
		});
		zip.on('error', err => {
			console.log(err)
			reject(err)
		})
	})

}