module.exports = function (app, Models, BASE_URL, db) {
	app.get(BASE_URL + '/reporthistoryaudits', function (req, res) {

		Models.ReportHistoryAudit.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})

	})

	app.get(BASE_URL + '/reporthistoryaudits/:id', function (req, res) {
		Models.ReportHistoryAudit.find({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/reporthistoryaudits', function (req, res) {
		Models.ReportHistoryAudit.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.put(BASE_URL + '/reporthistoryaudits/:id', function (req, res) {
		Models.ReportHistoryAudit.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
	})

	app.delete(BASE_URL + '/reporthistoryaudits/:id', function (req, res) {
		Models.ReportHistoryAudit.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
};