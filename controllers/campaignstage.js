var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var _ = require('underscore')
var thread = require('../thread-scripts/threadWrapper')
var queueWrapper = require('../database/queueWrapper')
var moment = require('moment')

module.exports = function (app, Models, BASE_URL, db) {
    // Get campaignstages list
    app.get(BASE_URL + '/campaignstages', function (req, res) {
        if (req.query.campaignId) {
            Models.CampaignStage.findAll({
                where: {
                    campaignId: req.query.campaignId
                },
                include: [Models.Campaign]
            })
                .then(function (items) {
                    res.status(200).send(items)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.CampaignStage.findAll()
                .then(function (items) {
                    res.status(200).send(items)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // Get campaignstage by id
    app.get(BASE_URL + '/campaignstages/:id', function (req, res) {
        Models.CampaignStage.find({
            include: [Models.Agent, Models.Disposition, Models.Campaign],
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaignstages/:id/leads', function (req, res) {
        var promises = []
        if (req.query.filters) {
            req.query.filters = JSON.parse(req.query.filters)
        } else {
            req.query.filters = {}
        }

        promises.push(Models.CampaignLead.findAll({
            include: [{
                model: Models.Lead,
                where: req.query.filters,
                include: [{
                    model: Models.Skill,
                    as: 'tfSkill'
                }, {
                    model: Models.SubSkill,
                    as: 'tfSubSkill'
                }, {
                    model: Models.Skill,
                    as: 'tmSkill'
                }, {
                    model: Models.SubSkill,
                    as: 'tmSubSkill'
                }]
            }],
            where: {
                currentCampaignStageId: req.params.id
            },
            limit: 30,
            offset: 30 * req.query.page,
            order: req.query.orderby + ' ' + req.query.dir,
        }))

        promises.push(Models.CampaignLead.count({
            where: {
                currentCampaignStageId: req.params.id
            }
        }))

        Promise.all(promises)
            .then(function (result) {
                res.status(200).send({
                    data: {
                        leads: result[0],
                        count: result[1]
                    }
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.delete(BASE_URL + '/campaignstages/:id/leads/', function (req, res) {
        if (!parseInt(req.params.id)) {
            return res.status(400).send({
                error: 'Invalid Parameters'
            })
        }

        Models.CampaignLead.destroy({
            where: {
                currentCampaignStageId: parseInt(req.params.id)
            }
        })
        .then(function (result) {
            res.status(200).send({
                success: true
            })
        })
        .catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // Get campaignstage date time rules by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/datetimerules', function (req, res) {
        Models.CampaignStageDateTimeRule.findAll({
            include: [Models.DateTimeRuleSet, Models.SubSkill],
            where: {
                campaignstageId: req.params.id
            }
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage training docs by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/trainingdocs', function (req, res) {
        Models.CampaignTrainingDoc.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get dispositions and their associated campaignstage by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/dispositions', function (req, res) {
        Models.Disposition.findAll({
            where: {
                campaignstageId: req.params.id
            },
            include: [Models.CampaignStage]
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/campaignstages/:id/leadcount', function (req, res) {
        Models.CampaignLead.count({
            where: {
                currentCampaignStageId: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    count: result
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/campaignstages/:id/resetleads', function (req, res) {
        db.query(`UPDATE leads l
				        INNER JOIN
				    campaignleads cl ON l.id = cl.leadid 
				SET 
				    l.dontContactUntil = NULL
				WHERE
				    l.dontContactUntil IS NOT NULL
				        AND cl.currentcampaignstageid = ${req.params.id}`)
            .then(results => {
                res.status(200).send(results)
            })
            .catch(err => {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Put campaignAgentTargets
    app.put(BASE_URL + '/campaignstages/:id/agenttargets', function (req, res) {
        Models.CampaignStage.findById(req.params.id)
            .then(function (campaign) {
                _.each(req.body, (function (agent) {
                    Models.CampaignAgentTargets.findOrCreate({
                        where: {
                            agentId: agent.agentId,
                            campaignId: campaign.id
                        },
                        defaults: {
                            agentId: agent.agentId,
                            campaignId: campaign.id
                        }
                    })
                }))
            })
    })

    // Put campaignstagedispositions by id
    app.put(BASE_URL + '/campaignstages/:id/workflows', function (req, res) {
        Models.CampaignStage.findById(req.params.id)
            .then(function (result) {
                var promises = []

                req.body.forEach(workflow => {
                    promises.push(new Promise((resolve, reject) => {
                        Models.CampaignStageDisposition.findOne({
                            where: {
                                campaignstageId: workflow.stageId,
                                dispositionId: workflow.dispositionId
                            }
                        }).then(existing => {
                            if (existing) {
                                resolve(existing.updateAttributes({
                                    transitionToCampaignStageId: workflow.destination,
                                    transitionCutOffDate: workflow.transitionCutOffDate,
                                    transitionCutOffDateDispositionId: workflow.transitionCutOffDateDispositionId,
                                    dontContactLeadForHours: (workflow.dontContactLeadForHours === undefined || workflow.dontContactLeadForHours === null) ? 24 : workflow.dontContactLeadForHours
                                }))
                            } else {
                                resolve()
                            }
                        }).catch(reject)
                    }))
                })

                Promise.all(promises)
                    .then(function () {
                        res.status(200).send({
                            success: true
                        })
                    })
                    .catch(err => {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campiangstagedispositions by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/workflows', function (req, res) {
        Models.CampaignStageDisposition.findAll({
            where: {
                campaignstageId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage messages by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/messages', function (req, res) {
        Models.CampaignMessage.findAll({
            where: {
                campaignId: req.params.id
            }
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage agents by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/agents', function (req, res) {
        Models.CampaignStage.findById(req.params.id)
            .then(function (campaignStage) {
                if (req.query && req.query.countOnly) {
                    campaignStage.getAgents()
                        .then(function (result) {
                            res.status(200).send({
                                count: result.length
                            })
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                } else {
                    campaignStage.getAgents()
                        .then(function (result) {
                            res.status(200).send(result)
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                }
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Set agents by campaignstage id
    app.put(BASE_URL + '/campaignstages/:id/agents', function (req, res) {
        if (!req.body)
            return res.status(400).send({
                error: 'Campaign Stage Agents not specified'
            })

        Models.CampaignStage.findById(req.params.id)
            .then(function (campaignStage) {
                campaignStage.setAgents(_.pluck(req.body, 'id'))
                    .then(function (result) {
                        var promises = []
                        req.body.forEach(function (agent) {
                            promises.push(
                                new Promise((resolve, reject) => {
                                    Models.CampaignStageAgent.findOne({
                                        where: {
                                            campaignstageId: req.params.id,
                                            agentId: agent.id
                                        }
                                    }).then(stageAgent => {
                                        if (stageAgent) {
                                            resolve(stageAgent.updateAttributes({
                                                scheduledHours: agent.campaignstageagents.scheduledHours,
                                                goal: agent.campaignstageagents.goal,
                                                agentskills: agent.campaignstageagents.agentskills
                                            }))
                                        } else {
                                            resolve()
                                        }
                                    }).catch(reject)
                                })
                            )
                        })

                        Promise.all(promises).then(function () {
                            return res.status(200).send(result)
                        })
                            .catch(function (err) {
                                return res.status(500).send({
                                    error: err.message
                                })
                            })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage leads by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/leads', function (req, res) {
        if (req.query && req.query.countOnly) {
            Models.CampaignLead.count({
                where: {
                    currentCampaignStageId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send({
                        count: result
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.CampaignLead.findAll({
                where: {
                    currentCampaignStageId: req.params.id
                },
                include: [Models.Lead]
            })
                .then(function (campaignLeads) {
                    var results = _.map(campaignLeads, function (cl) {
                        return cl.lead
                    })
                    res.status(200).send(results)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // Get campaignstage lead skills by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/leads/types', function (req, res) {
        Models.CampaignStage.findById(req.params.id, {
            include: [{
                model: Models.Campaign,
                include: [Models.CampaignType]
            }]
        })
            .then(function (campaignStage) {
                if (campaignStage) {
                    var skillType = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tfSkillId' : 'tmSkillId')
                    db.query(`
						select * from skills where id in
							(select distinct(leads.${skillType}) from campaignleads
							left join leads on leads.id = campaignleads.leadId
							where campaignleads.currentCampaignStageId = :campaignStageId)
					`, {
                        replacements: {
                            campaignStageId: campaignStage.id
                        },
                        type: Sequelize.QueryTypes.SELECT
                    })
                        .then(function (skills) {
                            res.status(200).send(skills)
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                } else {
                    return res.status(404).send({
                        error: 'Not Found'
                    })
                }
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage lead skills by campaignstage id
    app.get(BASE_URL + '/campaignstages/:id/leads/subtypes', function (req, res) {
        Models.CampaignStage.findById(req.params.id, {
            include: [{
                model: Models.Campaign,
                include: [Models.CampaignType]
            }]
        })
            .then(function (campaignStage) {
                if (campaignStage) {
                    var skillType = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                    db.query(`
						select * from subskills where id in
							(select distinct(leads.${skillType}) from campaignleads
							left join leads on leads.id = campaignleads.leadId
							where campaignleads.currentCampaignStageId = :campaignStageId)
					`, {
                        replacements: {
                            campaignStageId: campaignStage.id
                        },
                        type: Sequelize.QueryTypes.SELECT
                    })
                        .then(function (skills) {
                            res.status(200).send(skills)
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                } else {
                    return res.status(404).send({
                        error: 'Not Found'
                    })
                }
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get campaignstage calls by campaign id
    app.get(BASE_URL + '/campaignstages/:id/callattempts', function (req, res) {
        if (req.query && req.query.countOnly) {
            Models.CallAttempt.count({
                where: {
                    campaignstageId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send({
                        count: result
                    })
                })
                .catch(function (err) {
                    res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.CallAttempt.findAll({
                include: [Models.Lead],
                where: {
                    campaignstageId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send(result)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // exhaust lead for campaign stage
    app.post(BASE_URL + '/campaignstages/:id/lead/:leadId/exhaust', function (req, res) {
        Models.CampaignStage.findById(req.params.id, {
            include: [Models.Campaign]
        })
            .then(function (campaignStage) {
                Models.CampaignLead.findOne({
                    where: {
                        campaignId: campaignStage.campaign.id,
                        leadId: req.params.leadId
                    }
                })
                    .then(function (campaignLead) {
                        var history = []
                        if (campaignLead.campaignStageHistory) {
                            try {
                                history = JSON.parse(campaignLead.campaignStageHistory)
                            } catch (e) {
                                history = []
                            }
                        }

                        if (campaignLead.currentCampaignStageId) {
                            history.push(campaignLead.currentCampaignStageId)
                        }

                        campaignLead.updateAttributes({
                            currentCampaignStageId: null,
                            campaignStageHistory: JSON.stringify(history)
                        }).then(function () {
                            Models.CallAttempt.destroy({
                                where: {
                                    leadId: req.params.leadId
                                }
                            })
                                .then(function () {
                                    res.status(200).send({
                                        success: true
                                    })
                                })
                                .catch(function (err) {
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        })
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Add call attempts for campaign stage
    app.post(BASE_URL + '/campaignstages/:id/callattempts', function (req, res) {
        if (!req.body || !req.body.quantity || !req.body.dateTimeRule || !req.body.subskillId || !req.body.uuid) {
            return res.status(400).send({
                error: 'Invalid Parameters'
            })
        } else {
            Models.CampaignStage.findById(req.params.id, {
                include: [{
                    model: Models.Campaign,
                    include: [Models.CampaignType]
                }]
            })
                .then(function (campaignStage) {
                    var skillType = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                    db.query(`
						SELECT campaignleads.leadId FROM campaignleads 
						left join leads on leads.id = campaignleads.leadId 
						where leads.${skillType} = :subskillId and
						not ((NULLIF(leads.phone_home,'') IS NULL) and
							(NULLIF(leads.phone_mobile,'') IS NULL) and
							(NULLIF(leads.phone_work,'') IS NULL) and
							(NULLIF(leads.phone_workmobile,'') IS NULL)) and
						campaignleads.currentCampaignStageId = :campaignStageId
					`, {
                        replacements: {
                            campaignStageId: campaignStage.id,
                            subskillId: req.body.subskillId
                        },
                        type: Sequelize.QueryTypes.SELECT
                    })
                        .then(function (leadIdObjs) {
                            if (leadIdObjs) {
                                var job = queueWrapper.addJob('amendcallattempts', {
                                    title: 'add',
                                    action: 'add',
                                    leadIdObjs: leadIdObjs.map(l => l.leadId),
                                    campaignStage: campaignStage,
                                    uuid: req.body.uuid,
                                    quantity: req.body.quantity,
                                    startDate: req.body.startDate,
                                    endDate: req.body.endDate,
                                    dateTimeRule: req.body.dateTimeRule,
                                    leadCount: leadIdObjs.length
                                })
                                // var taskId = thread.start('addCallAttempts', {
                                // 	leadIdObjs: leadIdObjs,
                                // 	campaignStage: campaignStage,
                                // 	uuid: req.body.uuid,
                                // 	quantity: req.body.quantity,
                                // 	startDate: req.body.startDate,
                                // 	endDate: req.body.endDate,
                                // 	dateTimeRule: req.body.dateTimeRule
                                // })

                                return res.status(200).send({
                                    success: true,
                                    job: job
                                })
                            } else {
                                return res.status(200).send({
                                    result: 'No leads found'
                                })
                            }
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // Remove call attempts for campaign stage
    app.put(BASE_URL + '/campaignstages/:id/callattempts', function (req, res) {
        if (!req.body || !req.body.quantity || !req.body.dateTimeRule || !req.body.subskillId || !req.body.uuid) {
            return res.status(400).send({
                error: 'Invalid Parameters'
            })
        } else {

            Models.CampaignStage.findById(req.params.id, {
                include: [{
                    model: Models.Campaign,
                    include: [Models.CampaignType]
                }]
            })
                .then(function (campaignStage) {
                    var skillType = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tfSubSkillId' : 'tmSubSkillId')
                    db.query(`
					SELECT campaignleads.leadId FROM campaignleads 
					left join leads on leads.id = campaignleads.leadId 
					where leads.${skillType} = :subskillId and
					 campaignleads.currentCampaignStageId = :campaignStageId
				`, {
                        replacements: {
                            campaignStageId: campaignStage.id,
                            subskillId: req.body.subskillId
                        },
                        type: Sequelize.QueryTypes.SELECT
                    })
                        .then(function (leadIdObjs) {
                            if (leadIdObjs) {
                                var job = queueWrapper.addJob('amendcallattempts', {
                                    title: 'remove',
                                    action: 'remove',
                                    leadIdObjs: leadIdObjs.map(l => l.leadId),
                                    uuid: req.body.uuid,
                                    quantity: req.body.quantity,
                                    leadCount: leadIdObjs.length
                                })
                                // var taskId = thread.start('removeCallAttempts', {
                                // 	uuid: req.body.uuid,
                                // 	quantity: req.body.quantity,
                                // 	leadIdObjs: leadIdObjs
                                // })

                                return res.status(200).send({
                                    success: true
                                })
                            } else {
                                return res.status(200).send({
                                    result: 'No leads found'
                                })
                            }
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    // Create campaign stage
    app.post(BASE_URL + '/campaignstages', function (req, res) {
        Models.CampaignStage.create(req.body)
            .then(function (result) {
                var created = result
                if (req.body.dispositions && req.body.dispositions.length > 0) {
                    result.setDispositions(req.body.dispositions.map(function (d) {
                        return d.id
                    }))
                        .then(function (result) {
                            created.getDispositions()
                                .then(function (result) {
                                    dispositionsReturned = true
                                    created.dataValues.dispositions = result;

                                    res.status(201).send(created)
                                })
                                .catch(function (err) {
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                } else
                    res.status(201).send(created)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    var cleanAgentTargets = function (campaignId) {
        var stageAgents = []
        return Models.CampaignStage.findAll({
            where: {
                campaignId: campaignId
            },
            include: [Models.Agent]
        })
            .then(function (stages) {
                _.each(stages, function (stage) {
                    stageAgents = stageAgents.concat(stage.agents)
                })
            })
            .then(function () {
                return new Promise(function (resolve, reject) {
                    Models.CampaignAgentTarget.findAll({
                        where: {
                            campaignId: campaignId
                        }
                    })
                        .then(function (currentAgentsTargets) {
                            var promises = []
                            _.each(currentAgentsTargets, function (currentAgent) {
                                var found = _.find(stageAgents, function (stageAgent) {
                                    return stageAgent.id == currentAgent.agentId
                                })

                                if (!found) {
                                    promises.push(currentAgent.destroy())
                                }
                            })

                            Promise.all(promises).then(function () {
                                resolve(true)
                            })
                        })
                        .catch(function (e) {
                            reject(e)
                        })
                })
            })
    }

    // Update campaign stage
    app.put(BASE_URL + '/campaignstages/:id', function (req, res) {
        Models.CampaignStage.findById(req.params.id, {
            include: Models.Disposition
        })
            .then(function (result) {
                result.updateAttributes(req.body)
                    .then(function (result) {
                        if (req.body.dispositions && req.body.dispositions.length > 0) {
                            result.setDispositions(req.body.dispositions.map(function (d) {
                                return d.id
                            }))
                                .then(function () {
                                    res.status(200).send(result)
                                })
                                .catch(function (err) {
                                    return res.status(500).send({
                                        error: err.message
                                    })
                                })
                        } else
                            return res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Delete campaign stage
    app.delete(BASE_URL + '/campaignstages/:id', function (req, res) {
        Models.CampaignStage.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })
}