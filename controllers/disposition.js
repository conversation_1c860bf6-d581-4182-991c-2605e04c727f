var _ = require('underscore')

module.exports = function (app, Models, BASE_URL) {
	// Get dispositions list
	app.get(BASE_URL + '/dispositions', function (req, res) {
		Models.Disposition.findAll({
				include: [
					{
						model: Models.CallResultField,
						include: [
							Models.CallResultFieldType,
							Models.CallResultFieldOption
						]
					}
				]
			})
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get disposition by id
	app.get(BASE_URL + '/dispositions/:id', function (req, res) {
		Models.Disposition.find({ where: { id: req.params.id },
				include: [
					{
						model: Models.CallResultField,
						include: [
							Models.CallResultFieldType,
							Models.CallResultFieldOption
						]
					}
				]
			})
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create disposition
	app.post(BASE_URL + '/dispositions', function (req, res) {
		Models.Disposition.create(req.body)
			.then(function (result) {
				var created = result;
				if (req.body.callresultfields && req.body.callresultfields.length > 0) {
					result.setCallresultfields(req.body.callresultfields.map(function (s) {
							return s.id
						}))
						.then(function (result) {
							created.getCallresultfields()
								.then(function (result) {
									created.dataValues.callresultfields = result;
									res.status(201).send(created)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						})
						.catch(function (err) {
							return res.status(500).send({ error: err.message })
						})
				}
				else
					res.status(201).send(created)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update disposition
	app.put(BASE_URL + '/dispositions/:id', function (req, res) {
		Models.Disposition.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						if (req.body.callresultfields && req.body.callresultfields.length > 0) {
							result.setCallresultfields(req.body.callresultfields.map(function (s) {
								return s.id
							})).then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
						else {
							result.setCallresultfields([])
								.then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete disposition
	app.delete(BASE_URL + '/dispositions/:id', function (req, res) {
		Models.Disposition.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}