module.exports = function (app, Models, BASE_URL) {
	// Get callrecords list
	app.get(BASE_URL + '/callrecords', function (req, res) {
		Models.CallRecord.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get callresultfieldtype by id
	app.get(BASE_URL + '/callrecords/:id', function (req, res) {
		Models.CallRecord.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/callrecords/page/:page', function (req, res) {
		Models.CallRecord.findAndCount({
				where: req.query.filters ? JSON.parse(req.query.filters) : {},
				include: [Models.Lead, Models.CallResult, Models.Agent, Models.CampaignStage, Models.Campaign],
				order: req.query.orderby + ' ' + req.query.dir,
				limit: req.query.limit,
				offset: req.query.limit * req.params.page				
			})
			.then(function (result) {
				res.status(200).send({
					data: {
						callRecords: result.rows,
						count: result.count
					}
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Create callrecords
	app.post(BASE_URL + '/callrecords', function (req, res) {
		Models.CallRecord.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update callrecords
	app.put(BASE_URL + '/callrecords/:id', function (req, res) {
		Models.CallRecord.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete callrecords
	app.delete(BASE_URL + '/callrecords/:id', function (req, res) {
		Models.CallRecord.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}