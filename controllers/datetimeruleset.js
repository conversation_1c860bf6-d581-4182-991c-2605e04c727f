var Sequelize = require('sequelize')

module.exports = function (app, Models, BASE_URL) {
	// Get datetimerulesets list
	app.get(BASE_URL + '/datetimerulesets', function (req, res) {
		Models.DateTimeRuleSet.findAll({
				include: [{
					model: Models.CampaignStageDateTimeRule,
					required: false
				}]
			})
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get datetimeruleset by id
	app.get(BASE_URL + '/datetimerulesets/:id', function (req, res) {
		Models.DateTimeRuleSet.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Create datetimeruleset
	app.post(BASE_URL + '/datetimerulesets', function (req, res) {
		Models.DateTimeRuleSet.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update datetimeruleset
	app.put(BASE_URL + '/datetimerulesets/:id', function (req, res) {
		Models.DateTimeRuleSet.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete datetimeruleset
	app.delete(BASE_URL + '/datetimerulesets/:id', function (req, res) {
		Models.DateTimeRuleSet.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}