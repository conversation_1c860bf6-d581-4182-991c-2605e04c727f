module.exports = function (app, Models, BASE_URL) {
	// Get callresultfieldgroups list
	app.get(BASE_URL + '/callresultfieldgroups', function (req, res) {
		Models.CallResultFieldGroup.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get callresultfieldgroup by id
	app.get(BASE_URL + '/callresultfieldgroups/:id', function (req, res) {
		Models.CallResultFieldGroup.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create callresultfieldgroup
	app.post(BASE_URL + '/callresultfieldgroups', function (req, res) {
		Models.CallResultFieldGroup.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update callresultfieldgroup
	app.put(BASE_URL + '/callresultfieldgroups/:id', function (req, res) {
		Models.CallResultFieldGroup.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete callresultfieldgroup
	app.delete(BASE_URL + '/callresultfieldgroups/:id', function (req, res) {
		Models.CallResultFieldGroup.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}