var _ = require('underscore')

module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/leadaudits', function (req, res) {
		Models.LeadAudit.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.post(BASE_URL + '/leadaudits', function (req, res) {
		Models.LeadAudit.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.put(BASE_URL + '/leadaudits/:id', function (req, res) {
		Models.LeadAudit.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
	})

}