var _ = require('underscore')
var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var sessionAPI = require('../database/redisAPI')('agentsession')
var usersessionsAPI = require('../database/redisAPI')('usersession')
var agentAPI = require('../database/redisAPI')('agent')
var campaignAPI = require('../database/redisAPI')('campaign')
var moment = require('moment-timezone')
var promiseUtils = require('../utils/promiseUtils.js')
var promiseWhile = promiseUtils.promiseWhile

module.exports = function (app, Models, BASE_URL, db) {
    // Get agent sessions list
    app.get(BASE_URL + '/agentsessions', function (req, res) {
        sessionAPI.getAll()
            .then(function (results) {
                res.status(200).send(results)
            })
    })

    // Get agent session by id
    app.get(BASE_URL + '/agentsessions/:id', function (req, res) {
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session)
                    res.status(200).send(session)
                else
                    res.status(200).send()
            })

    })

    // keep alive session by agent id
    app.post(BASE_URL + '/agentsessions/:id/keepalive', function (req, res) {
        var monitorTimeout = req.user.agentId && req.user.isAgent && !req.user.isAdmin && !req.user.isSupervisor
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session) {
                    var timeoutDuration = 0
                    if (monitorTimeout && session.agentStatus && session.agentStatus.timeout) timeoutDuration = session.agentStatus.timeout
                    var idleTimeout = 1000 * 60 * timeoutDuration
                    if (timeoutDuration) {
                        var highest = session.agentStatusSince < session.lastPosting ? session.lastPosting : session.agentStatusSince
                        session.timeoutIn = Math.round(((highest + idleTimeout) - Date.now()) / 1000)
                    } else {
                        session.timeoutIn = 0
                    }
                    if (timeoutDuration && Date.now() - session.agentStatusSince > idleTimeout && Date.now() - session.lastPosting > idleTimeout) {
                        console.log('boot em', session.agent.name, timeoutDuration)
                        var userId = session.userId
                        cleanUpAgentSession(session)
                        usersessionsAPI.delete(userId)
                    } else {
                        session.lastKeepAlivePing = Date.now()
                        sessionAPI.save(session)
                    }
                    res.status(200).send({
                        success: true,
                        session
                    })
                } else
                    res.sendStatus(404)
            })
    })

    // keep alive session by agent id
    app.post(BASE_URL + '/agentsessions/:id/rejectlead', function (req, res) {
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session.currentLead && session.currentCallResult) {
                    if (session.callback && session.callback.id) {
                        Models.Callback.findById(session.callback.id)
                            .then(function (callback) {
                                if (callback) {
                                    callback.updateAttributes({
                                        deleted: false
                                    })
                                }
                            })
                    } else {
                        if (session.currentCallResult.callAttemptJson)
                            recreateCallAttemptFromJson(session.currentCallResult.callAttemptJson)
                    }

                    Models.CallResult.destroy({
                        where: {
                            id: session.currentCallResult.id
                        }
                    })

                    Models.Lead.update({
                        dontContactUntil: new Date()
                    }, {
                        where: {
                            id: session.currentLead.id
                        },
                        limit: 1
                    })

                    session.callState = 'Idle'
                    session.currentLead = null
                    session.currentCallResult = null
                    session.inWrapUp = false

                    sessionAPI.save(session)

                    res.status(200).send(session)
                } else
                    res.sendStatus(404)
            })
    })

    app.post(BASE_URL + '/agentsessions/:id/clearlead', function (req, res) {
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session.currentLead && session.currentCallResult) {
                    Models.CallAttempt.destroy({
                        where: {
                            leadId: session.currentLead.id
                        }
                    })
                        .then(function () {
                            Models.CallResult.destroy({
                                where: {
                                    id: session.currentCallResult.id
                                }
                            })

                            session.currentLead = null
                            session.currentCallResult = null
                            session.inWrapUp = false
                            session.callState = 'Avalaible'
                            sessionAPI.save(session)

                            res.status(200).send(session)
                        })
                }
            })
    })

    // add events to session by agent id
    app.post(BASE_URL + '/agentsessions/:id/events', function (req, res) {
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session) {
                    session.events.push(req.body)
                    sessionAPI.save(session)
                    res.status(200).send({
                        success: true
                    })
                } else
                    res.sendStatus(404)
            })
    })

    // Create or update agent session
    app.post(BASE_URL + '/agentsessions/:id', function (req, res) {
        if (req.body && req.body.agentId) {
            sessionAPI.get(req.body.agentId)
                .then(function (session) {
                    new Promise((resolve, reject) => {
                        if (session) {
                            sessionAPI.extend(req.body.agentId, req.body)
                                .then(function (updatedSession) {
                                    if (updatedSession) {
                                        updatedSession.lastKeepAlivePing = Date.now()
                                        updatedSession.lastPosting = updatedSession.lastPosting || Date.now()

                                        if (!session.agentStatus || updatedSession.agentStatus.id != session.agentStatus.id) {
                                            try {
                                                var timeInState = 0;
                                                if (updatedSession.stateStartTime) timeInState = (Date.now() - new Date(updatedSession.stateStartTime)) / 1000
                                                var agentStateHistory = {
                                                    agentId: updatedSession.agentId,
                                                    timeInState,
                                                    state: session.agentStatus ? session.agentStatus.name : ''
                                                }
                                                Models.AgentStateHistory.create(agentStateHistory).catch(() => { })
                                            } catch (e) {
                                                console.log(e)
                                            }

                                            var newEvent = {}
                                            newEvent.eventType = "State Change"
                                            if (updatedSession.agentStatus)
                                                newEvent.eventName = updatedSession.agentStatus.name
                                            else
                                                newEvent.eventName = 'Unknown'
                                            newEvent.agentId = updatedSession.agentId
                                            newEvent.additionalInfo = JSON.stringify(updatedSession.agentStatus)
                                            Models.AgentEvent.create(newEvent);

                                            if (session.agentStatus && session.currentCampaignStage) {
                                                if (session.agentStatus.isChargeable) {
                                                    updatePayroll(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
                                                }

                                                updateLoginTime(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
                                            }


                                        }

                                        sessionAPI.save(updatedSession)
                                        resolve(updatedSession)
                                    } else {
                                        reject()
                                    }
                                }).catch(reject)
                        } else {
                            reject()
                        }
                    }).then(session => {
                        res.status(200).send(session)
                    }).catch(() => {
                        var session
                        Models.Agent.find({
                            include: [Models.Device],
                            where: {
                                id: req.body.agentId
                            }
                        }).then(function (agent) {
                            if (agent) {
                                session = req.body
                                session.stateStartTime = new Date()
                                session.agent = agent
                                session.id = agent.id
                                session.lastKeepAlivePing = Date.now() // avoid it getting cleared up in the stale session checking loop
                                session.campaignStageSwitchInterval = agent.campaignSwitchInterval * 60 * 1000 //converts from minutes to millseconds
                                session.events = [] // initialise the events array so we can safely check it on every session

                                return usersessionsAPI.get(req.user.id)
                            } else {
                                res.status(404).send({
                                    error: 'Agent not found'
                                })
                            }
                        }).then(userSession => {
                            if (session) {
                                console.log(userSession)
                                if (userSession && userSession.monitorTimeout && userSession.agentStatus) {
                                    session.agentStatus = userSession.agentStatus
                                    session.agentStatusSince = userSession.agentStatusSince
                                    session.lastPosting = userSession.lastPosting
                                }
                                sessionAPI.save(session)
                                res.status(201).send(session)

                                var newEvent = {}
                                newEvent.eventType = "Login"
                                newEvent.eventName = "Logged In"
                                newEvent.agentId = session.agentId
                                newEvent.additionalInfo = JSON.stringify(session.agentStatus)
                                Models.AgentEvent.create(newEvent);
                            } else {
                                res.status(404).send({
                                    error: 'Agent not found'
                                })
                            }
                        }).catch(function (e) {
                            return res.status(500).send({
                                error: e.message
                            })
                        })
                    })
                })
        } else {
            res.status(500).send({
                error: 'No agent ID specified'
            })
        }
    })

    // Update agent session ** DEPRECATED **
    app.put(BASE_URL + '/agentsessions/:id', function (req, res) {
        res.sendStatus(400)
    })

    // Delete agent session
    app.delete(BASE_URL + '/agentsessions/:id', function (req, res) {
        sessionAPI.get(req.params.id)
            .then(function (session) {
                if (session) {
                    updateLastAgentSessionEndTime(req.params.id)
                    cleanUpAgentSession(session)
                    res.status(200).send({
                        success: true
                    })
                } else
                    res.sendStatus(404)
            })
    })

    // Get next call for agent session by agent id
    app.get(BASE_URL + '/agentsessions/:id/nextcall', function (req, res) {
        var overrideLeadId = req.query.leadId
        try {
            sessionAPI.get(req.params.id).then(function (session) {
                if (session) {
                    var now = new Date()
                    Models.Callback.findOne({
                        where: {
                            agentId: req.params.id,
                            startDateTime: {
                                $lt: now
                            },
                            endDateTime: {
                                $gt: now
                            },
                            deleted: {
                                $or: {
                                    $ne: true,
                                    $eq: null
                                }
                            }
                        }
                    }).then(function (callback) {
                        if (callback) {
                            callback.updateAttributes({
                                deleted: true
                            })

                            if (callback && callback.callAttemptJson) {
                                var callAttempt = JSON.parse(callback.callAttemptJson)
                                if (callAttempt) {
                                    callAttempt.isCallback = true
                                    callAttempt.leadId = callback.leadId
                                    session.callback = callback
                                    Models.Lead.findById(callback.leadId, {
                                        include: [{
                                            model: Models.Skill,
                                            as: 'tfSkill'
                                        }, {
                                            model: Models.SubSkill,
                                            as: 'tfSubSkill'
                                        }, {
                                            model: Models.Skill,
                                            as: 'tmSkill'
                                        }, {
                                            model: Models.SubSkill,
                                            as: 'tmSubSkill'
                                        }, {
                                            model: Models.Invoice
                                        }, {
                                            model: Models.PaymentLog,
                                            include: [Models.RecurringPayment, {
                                                model: Models.Campaign,
                                                attributes: ['id', 'name']
                                            }]
                                        }]
                                    }).then(function (lead) {
                                        lead.updateAttributes({
                                            dontContactUntil: nowPlusHours(24)
                                        })
                                        session.currentLead = lead
                                        if (!session.previousCampaignStage) {
                                            session.previousCampaignStage = session.currentCampaignStage
                                        }

                                        if (session.currentCampaignStage && (session.currentCampaignStage.id == callAttempt.campaignstageId)) {
                                            createEmptyCallResultForCallAttempt(callAttempt)
                                                .then(function (callResult) {
                                                    session.currentCallResult = callResult
                                                    sessionAPI.save(session)

                                                    return res.status(200).send(session)
                                                })
                                        } else {
                                            // we want to change the stage back after this callback so dont reset the start time
                                            // session.currentCampaignStageStartTime = Date.now()
                                            sessionAPI.save(session)

                                            Models.CampaignStage.findOne({
                                                where: {
                                                    id: callAttempt.campaignstageId
                                                },
                                                include: [
                                                    Models.Agent, {
                                                        model: Models.Campaign,
                                                        include: [Models.Client, Models.CampaignType, Models.CampaignProjections]
                                                    },
                                                    { model: Models.Disposition, include: [{ model: Models.CallResultField, include: [Models.CallResultFieldType] }] }
                                                ]
                                            })
                                                .then(function (campaignStage) {
                                                    session.currentCampaignStage = campaignStage
                                                    createEmptyCallResultForCallAttempt(callAttempt)
                                                        .then(function (callResult) {
                                                            session.currentCallResult = callResult
                                                            sessionAPI.save(session)

                                                            return res.status(200).send(session)
                                                        })
                                                })
                                                .catch(function (e) {
                                                    throw e
                                                })
                                        }
                                    })
                                }
                            }
                        } else {
                            // cache agent stages
                            var agentCampaignStages, callbackLeadIds
                            delete session.callback

                            if (session.previousCampaignStage) {
                                session.currentCampaignStage = session.previousCampaignStage
                                delete session.previousCampaignStage
                            }

                            //no callbacks so just get the next lead
                            new Promise(function (resolve, reject) {
                                // See if its time to switch campaigns for this agent
                                Models.Callback.findAll({
                                    attributes: ['leadId'],
                                    where: Sequelize.or({
                                        deleted: null
                                    }, {
                                        deleted: false
                                    })
                                }).then(function (callbacks) {
                                    callbackLeadIds = _.pluck(callbacks, 'leadId')

                                    if (overrideLeadId || !session.currentCampaignStage || (Date.now() - session.currentCampaignStageStartTime) > session.campaignStageSwitchInterval)
                                        resolve(undefined)
                                    else
                                        resolve(session.currentCampaignStage)
                                })
                            }).then(function (existingCampaignStage) {
                                return new Promise(function (resolve, reject) {
                                    if (existingCampaignStage)
                                        resolve(getNextLeadForAgent(session, existingCampaignStage, callbackLeadIds, 0, overrideLeadId))
                                    else
                                        resolve(undefined)
                                })
                            }).then(function (nextCallForExistingStage) {
                                return new Promise(function (resolve, reject) {
                                    if (nextCallForExistingStage)
                                        resolve(nextCallForExistingStage)
                                    else {
                                        var nextStage, nextCall, stages, retriedCurrentStage = false,
                                            forceExit = false

                                        var predicate = function () {
                                            return !!nextCall || forceExit
                                        }

                                        var burnStage = function (list, stage) {
                                            return list.filter(function (cs) {
                                                return cs.id !== stage.id
                                            })
                                        }

                                        var action = function () {
                                            var getNextStage = function () {
                                                if (stages && stages.length) {
                                                    if (nextStage) {
                                                        return _.find(stages, function (stage) {
                                                            return stage.id > nextStage.id
                                                        }) || stages[0]
                                                    } else
                                                        return stages[0]
                                                } else {
                                                    if (!session.currentCampaignStage || retriedCurrentStage)
                                                        return null
                                                    else {
                                                        // if we've been around the loop with all other stages then
                                                        // see if there is any more calls for the stage they were already on
                                                        retriedCurrentStage = true
                                                        return session.currentCampaignStage
                                                    }
                                                }
                                            }

                                            if (nextStage)
                                                stages = burnStage(stages, nextStage)

                                            nextStage = getNextStage(stages)

                                            if (nextStage)
                                                return getNextLeadForAgent(session, nextStage, callbackLeadIds, 0, overrideLeadId).then(function (result) {
                                                    nextCall = result
                                                    return Promise.resolve()
                                                })
                                            else {
                                                forceExit = true
                                                return reject('No viable leads found.\n\nPlease contact your supervisor')
                                            }
                                        }

                                        var onComplete = function () {
                                            if (nextStage && nextCall) {
                                                if (session.currentCampaignStage && (session.currentCampaignStage.campaignId != nextStage.campaignId)) {
                                                    if (session.agentStatus && session.agentStatus.isChargeable) {
                                                        if (session.agentStatus.isChargeable) {
                                                            updatePayroll(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
                                                        }
                                                        updateLoginTime(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
                                                    }
                                                    session.stateStartTime = new Date()
                                                }

                                                // update the agent session with the new campaign now that we successfully have a call
                                                if (retriedCurrentStage || !session.currentCampaignStage || (session.currentCampaignStage.id !== nextStage.id)) {
                                                    session.currentCampaignStage = nextStage
                                                    session.currentCampaignStageStartTime = Date.now()
                                                }

                                                sessionAPI.save(session)
                                                resolve(nextCall)
                                            } else
                                                return reject('Unable to find call for agent')
                                        }

                                        getCampaignStagesForAgent(session).then(function (results) {
                                            if (results && results.length) {
                                                if (session.currentCampaignStage)
                                                    stages = burnStage(results, session.currentCampaignStage)
                                                else
                                                    stages = results

                                                promiseWhile(predicate, action, onComplete)
                                                    .catch(reject)
                                            } else
                                                return reject('No campaign stage available for agent')
                                        }).catch(function (err) {
                                            reject(err)
                                        })
                                    }
                                })
                            }).then(function (nextCall) {
                                if (nextCall)
                                    createEmptyCallResultForCallAttempt(nextCall).then(function (callResult) {
                                        if (nextCall.lead && nextCall.lead.dataValues && nextCall.lead.dataValues.customFields && typeof nextCall.lead.dataValues.customFields === 'string') {
                                            try {
                                                nextCall.lead.dataValues.customFields = JSON.parse(nextCall.lead.dataValues.customFields)
                                            } catch (e) {

                                            }
                                        }
                                        session.currentLead = nextCall.lead
                                        session.currentCallResult = callResult

                                        sessionAPI.save(session)

                                        res.status(200).send(session)
                                    })
                                        .catch(function (err) {
                                            return res.status(500).send({
                                                error: (err ? (err.message || err) : 'Unknown error occurred')
                                            })
                                        })
                                else
                                    throw new Error('No call found for agent')
                            }).catch(function (err) {
                                return res.status(500).send({
                                    error: (err ? (err.message || err) : 'Unknown error occurred')
                                })
                            })
                        }
                    })
                } else {
                    res.sendStatus(404)
                }
            })
        } catch (err) {
            return res.status(500).send({
                error: (err ? (err.message || err) : 'Unknown error occurred')
            })
        }
    })


    function getCampaignStagesForAgent(session) {
        return new Promise(function (resolve, reject) {
            if (session && session.agentId) {
                var now = moment().format('YYYY-MM-DD HH:mm:ss')
                // var now = new Date()

                Models.CampaignStage.findAll({
                    include: [{
                        model: Models.Agent,
                        where: {
                            id: session.agentId
                        }
                    }, {
                        model: Models.Campaign,
                        include: [Models.Client, Models.CampaignType, Models.CampaignProjections]
                        // ,where: {
                        // 	startDate: {
                        // 		$lt: now
                        // 	},
                        // 	endDate: {
                        // 		$gt: now
                        // 	}
                        // }
                    }, {
                        model: Models.Disposition,
                        include: [{ model: Models.CallResultField, include: [Models.CallResultFieldType] }]
                    }],
                    where: {
                        startDate: {
                            $lt: now
                        },
                        endDate: {
                            $gt: now
                        }
                    }
                }).then(resolve).catch(reject)
            } else
                return reject()
        })
    }

    function getNextLeadForAgent(session, campaignStage, callbackLeadIds, attempts, overrideLeadId) {
        if (!attempts) attempts = 0
        attempts++;
        if (attempts > 30) {
            return Promise.resolve()
        }
        if (session && campaignStage) {
            var now = new Date()
            var clientNow = moment().tz(campaignStage.campaign.client.timezone)
            var campaignTypePrefix = (campaignStage.campaign.campaigntype.name === 'Telefunding' ? 'tf' : 'tm')

            // TODO: best way to check for empty numbers... should it be done here ??????
            var callAttemptWhereQuery = {
                dontContactUntil: {
                    $or: {
                        $eq: null,
                        $lt: now
                    }
                },
                $or: [{
                    phone_home: {
                        $and: {
                            $not: null,
                            $ne: ''
                        }
                    }
                }, {
                    phone_mobile: {
                        $and: {
                            $not: null,
                            $ne: ''
                        }
                    },
                }, {
                    phone_work: {
                        $and: {
                            $not: null,
                            $ne: ''
                        }
                    },
                }, {
                    phone_workmobile: {
                        $and: {
                            $not: null,
                            $ne: ''
                        }
                    }
                }]
            }

            if (callbackLeadIds && callbackLeadIds.length) {
                callAttemptWhereQuery['id'] = {
                    $notIn: callbackLeadIds
                }
            }

            if (overrideLeadId) {
                callAttemptWhereQuery.id = overrideLeadId
            }

            var skills = []

            campaignStage.agents.forEach((agent) => {
                if (agent.id === session.agentId) {
                    skills = agent.campaignstageagents.agentskills ? JSON.parse(agent.campaignstageagents.agentskills) : []
                    return
                }
            })

            if (session.agentStatus.name === 'Active (Portfolio)') {
                callAttemptWhereQuery.agentPortfolioTag = session.agent.name
            }
            else {
                // Agent has no skills for this campaign stage so just return out of this and let it move on to the next stage
                if (!skills.length)
                    return Promise.resolve()

                callAttemptWhereQuery.agentPortfolioTag = {
                    $or: {
                        $in: [''],
                        $eq: null
                    }
                }
                
                callAttemptWhereQuery[(campaignTypePrefix + 'SubSkillId')] = {
                    $in: skills
                }
            }

            var result = null

            console.log('Lead selection process started for agent ID ' + session.agentId)

            return Promise.resolve(
                db.transaction({
                    autocommit: false
                },
                function (t) {
                    return Models.CallAttempt.findOne({
                        include: [{
                            model: Models.Lead,
                            where: callAttemptWhereQuery,
                            include: [{
                                model: Models.Skill,
                                as: 'tfSkill'
                            }, {
                                model: Models.SubSkill,
                                as: 'tfSubSkill'
                            }, {
                                model: Models.Skill,
                                as: 'tmSkill'
                            }, {
                                model: Models.SubSkill,
                                as: 'tmSubSkill'
                            }, {
                                model: Models.Invoice
                            }, {
                                model: Models.CampaignLead,
                                where: {
                                    currentCampaignStageId: campaignStage.id
                                }
                            }, {
                                model: Models.PaymentLog,
                                include: [Models.RecurringPayment, {
                                    model: Models.Campaign,
                                    attributes: ['id', 'name']
                                }]
                            }]
                        }],
                        where: Sequelize.and({
                            campaignstageId: campaignStage.id
                        }, [getDayOfWeekAsString(parseInt(clientNow.format('d'))), true], {
                            startTime: {
                                $lt: clientNow.format('HH:mm:ss')
                            }
                        }, {
                            endTime: {
                                $gt: clientNow.format('HH:mm:ss')
                            }
                        }, {
                            startDate: {
                                $or: {
                                    $lt: now,
                                    $eq: null
                                }
                            }
                        }, {
                            endDate: {
                                $or: {
                                    $gt: now,
                                    $eq: null
                                }
                            }
                        }),
                        order: [
                            ['lastDispositionDate'],
                            ['randomSelector']
                        ],
                        transaction: t,
                        lock: "UPDATE"
                    })
                    .then(function (callAttempt) {
                        if (callAttempt) {
                            return callAttempt.lead.updateAttributes({
                                dontContactUntil: nowPlusHours(24),
                                lastAgent: session.agentId
                            })
                            .then(() => {
                                return callAttempt
                            })
                            .catch(Promise.reject)
                        }
                        else
                            return Promise.resolve()
                    })
                    .then(function (_result) {
                        result = _result
                        if (result && result.lead) {
                            return result.lead.reload().then(l => console.log('Lead reload completed at ' + result.lead.updatedAt))
                        }
                    })
                    .then(_ => {
                        if (result && result.lead && result.lead.lastAgent !== session.agentId) {
                            console.log('Found clash of agents: ' + result.lead.lastAgent + ' versus ' + session.agentId)
                            throw new Error('try restarting transaction')
                        }
                        else if (result) {
                            return result.destroy()
                        }
                    })
                    .catch(function (e) {
                        if (e && e.message && e.message.indexOf('try restarting transaction') > -1)
                            return getNextLeadForAgent(session, campaignStage, callbackLeadIds, attempts, overrideLeadId)
                        else
                            return Promise.reject(e)
                    })
                })
            )
        }
    }


    function cleanUpAgentSession(session) {
        if (session.currentCallResult) {
            if (session.currentCallResult.callAttemptJson) {
                var temp = JSON.parse(session.currentCallResult.callAttemptJson)
                temp.leadId = session.currentCallResult.leadId
                session.currentCallResult.callAttemptJson = JSON.stringify(temp)
                recreateCallAttemptFromJson(session.currentCallResult.callAttemptJson)
            }

            Models.CallResult.destroy({
                where: {
                    id: session.currentCallResult.id
                }
            })
        }

        updateLastAgentSessionEndTime(session.agentId)
        sessionAPI.delete(session.agentId)

        //put logout events here
        var newEvent = {}
        newEvent.eventType = "Login"
        newEvent.eventName = "Logged Out"
        newEvent.agentId = session.agentId
        newEvent.additionalInfo = JSON.stringify(session.agentStatus)
        Models.AgentEvent.create(newEvent);

        if (session.agentStatus) {
            try {
                var timeInState = 0;
                if (session.stateStartTime) timeInState = (Date.now() - new Date(session.stateStartTime)) / 1000
                var agentStateHistory = {
                    agentId: session.agentId,
                    timeInState,
                    state: session.agentStatus ? session.agentStatus.name : ''
                }
                Models.AgentStateHistory.create(agentStateHistory).catch(() => { })
            } catch (e) {
                console.log(e)
            }
        }

        if (session.agentStatus && session.currentCampaignStage) {
            if (session.agentStatus.isChargeable) {
                updatePayroll(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
            }
            updateLoginTime(session.stateStartTime, session.currentCampaignStage.campaignId, session.agentId)
        }
    }

    function updatePayroll(stateStartTime, campaignId, agentId) {
        var date = moment().format('DDMMYY')
        var now = moment()
        var start = moment(stateStartTime)
        var timeInState = now.diff(start, 'seconds')

        campaignAPI.incrementBy(campaignId + '-' + agentId, 'payroll-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, agentId ${agentId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId + '-' + agentId, 'payroll', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, agentId ${agentId} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId, 'payroll-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId, 'payroll', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId} with a value of ${timeInState}`)
                console.log(err)
            })
        agentAPI.incrementBy(agentId, 'payroll', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for agentId ${agentId} with a value of ${timeInState}`)
                console.log(err)
            })
        agentAPI.incrementBy(agentId, 'payroll-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for agentId ${agentId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
    }

    function updateLoginTime(stateStartTime, campaignId, agentId) {
        var date = moment().format('DDMMYY')
        var now = moment()
        var start = moment(stateStartTime)
        var timeInState = now.diff(start, 'seconds')

        campaignAPI.incrementBy(campaignId + '-' + agentId, 'loginduration-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, agentId ${agentId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId + '-' + agentId, 'loginduration', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, agentId ${agentId} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId, 'loginduration-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
        campaignAPI.incrementBy(campaignId, 'loginduration', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for campaignId ${campaignId} with a value of ${timeInState}`)
                console.log(err)
            })
        agentAPI.incrementBy(agentId, 'loginduration', timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for agentId ${agentId} with a value of ${timeInState}`)
                console.log(err)
            })
        agentAPI.incrementBy(agentId, 'loginduration-' + date, timeInState)
            .then(() => { }).catch(err => {
                console.log(`error in incrementBy for agentId ${agentId}, date ${date} with a value of ${timeInState}`)
                console.log(err)
            })
    }


    function updateLastAgentSessionEndTime(agentId) {
        Models.Agent.findById(agentId)
            .then(function (agent) {
                if (agent)
                    agent.updateAttributes({
                        lastSessionEnd: new Date()
                    })
            })
    }

    function recreateCallAttemptFromJson(callAttemptJson) {
        var callAttemptObj = JSON.parse(callAttemptJson)
        Models.CallAttempt.create(callAttemptObj)
            .catch(function (err) {
                delete callAttemptObj.id
                Models.CallAttempt.create(callAttemptObj)
            })
    }

    function createEmptyCallResultForCallAttempt(callAttempt) {
        if (callAttempt.dataValues) {
            callAttempt.leadId = callAttempt.dataValues.leadId
            callAttempt.campaignstageId = callAttempt.dataValues.campaignstageId
            callAttempt.campaignId = callAttempt.dataValues.campaignId
        }

        return Promise.resolve(
            Models.CallResult.create({
                leadId: callAttempt.leadId,
                campaignstageId: callAttempt.campaignstageId,
                callAttemptJson: JSON.stringify(callAttempt),
                campaignId: callAttempt.campaignId
            })
                .then(function (callResult) {
                    if (callAttempt.isCallback) {
                        callResult.dataValues.isCallback = true;
                    }
                    return Promise.resolve(callResult)
                })
        )
    }


    function nowPlusHours(hours) {
        var d = new Date()

        if (hours)
            d.setHours(d.getHours() + hours)

        return d;
    }

    function getDayOfWeekAsString(day) {
        switch (day) {
            case 0:
                return 'sunday'
            case 1:
                return 'monday'
            case 2:
                return 'tuesday'
            case 3:
                return 'wednesday'
            case 4:
                return 'thursday'
            case 5:
                return 'friday'
            case 6:
                return 'saturday'
            default:
                return undefined
        }
    }
}