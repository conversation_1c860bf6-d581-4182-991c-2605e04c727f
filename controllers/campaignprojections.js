module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/campaignprojections', function (req, res) {
		Models.CampaignProjections.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/campaignprojections/:id', function (req, res) {
		Models.CampaignProjections.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/campaignprojections', function (req, res) {
		Models.CampaignProjections.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})		
	})

	app.post(BASE_URL + '/campaignprojections/:id', function (req, res) {
		//just update them
		Models.CampaignProjections.findById(req.body.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})

	})
}