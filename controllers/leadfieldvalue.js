module.exports = function (app, Models, BASE_URL) {
	// Get leadfieldvalues list
	app.get(BASE_URL + '/leadfieldvalues', function (req, res) {
		Models.LeadFieldValue.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get leadfieldvalue by id
	app.get(BASE_URL + '/leadfieldvalues/:id', function (req, res) {
		Models.LeadFieldValue.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create leadfieldvalues
	app.post(BASE_URL + '/leadfieldvalues', function (req, res) {
		Models.LeadFieldValue.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update leadfieldvalues
	app.put(BASE_URL + '/leadfieldvalues/:id', function (req, res) {
		Models.LeadFieldValue.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete leadfieldvalue
	app.delete(BASE_URL + '/leadfieldvalues/:id', function (req, res) {
		Models.LeadFieldValue.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}