var moment = require('moment')

module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/paymentlog', function (req, res) {
		var where = {}
		var leadWhere = {}
		var limit = parseInt(req.query.limit) || 100
		var offset = parseInt(req.query.offset) || 0
		var orderBy = req.query.orderBy || 'paymentDate'
		var orderDir = req.query.orderDir || 'DESC'

		if (req.query.startDate) where.paymentDate = { $gte: req.query.startDate }
		if (req.query.endDate) where.$and = { paymentDate: { $lte: req.query.endDate } }
		if (req.query.clientId) where.clientId = req.query.clientId
		if (req.query.campaignId) where.campaignId = req.query.campaignId
		if (req.query.leadId) where.leadId = req.query.leadId
		if (req.query.clientRef) leadWhere.clientRef = req.query.clientRef
		if (req.query.filterPaid) where.isPaid = false
		if (req.query.userId) where.userId = req.query.userId
		if (req.query.filterErrors) where.status = 'errored'

		if (req.user.isClientAgent || req.user.isClientAdmin) {
			where.clientId = req.user.clientId
		}

		Models.PaymentLog.findAndCount({
			where,
			limit,
			offset,
			include: [Models.Client, Models.Campaign, Models.User, {
				model: Models.Lead,
				where: leadWhere
			}, Models.Invoice, Models.CallResult],
			order: [[orderBy, orderDir]]
		}).then(function (result) {
			res.status(200).send(result)
		}).catch(function (err) {
			return res.status(500).send({
				error: err.message
			})
		})
	})

	app.get(BASE_URL + '/paymentlog/download', function (req, res) {
		res.download(req.query.file, 'PaymentImport.csv')
	})

	app.get(BASE_URL + '/paymentlog/:id', function (req, res) {
		Models.PaymentLog.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/paymentlog', function (req, res) {
		var promise
		if (Array.isArray(req.body)) {
			// first delete any pending ones for this callresult to stop duplicates
			promise = Models.PaymentLog.update({
				disabled: true
			}, {
				where: {
					invoiceId: req.body[0].invoiceId,
					status: 'pending',
					deleted: false,
					disabled: false,
					isPaid: false
				}
			}).then(() => {
				return Models.PaymentLog.bulkCreate(req.body)
			})
		} else {
			promise = Models.PaymentLog.create(req.body)
		}
		promise.then(function (result) {
			res.status(200).send({
				Success: true,
				result
			})
		}).catch(function (err) {
			return res.status(200).send({
				Success: false,
				error: err ? err.message || err : 'Unknown Error'
			})
		})
	})

	app.put(BASE_URL + '/paymentlog/callresult/:id', function (req, res) {
		Models.PaymentLog.findAll(
			{
				where: {
					callresultId: req.params.id
				}
			})
			.then(function (results) {
				return Promise.all(results.map(r => r.updateAttributes(req.body)))
			})
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update agentstate
	app.put(BASE_URL + '/paymentlog/:id', function (req, res) {
		var paymentlog
		Models.PaymentLog.findById(req.params.id, {
			include: [Models.RecurringPayment]
		}).then(function (result) {
			paymentlog = result
			if (!moment(req.body.paymentDate).isSame(moment(result.paymentDate), 'day') && result.recurringpaymentId) {
				return Models.InvoiceEvent.create({
					invoiceId: paymentlog.recurringpayment.invoiceId,
					userId: req.user.id,
					changeType: 'Next Payment Date',
					field: 'paymentDate',
					fromValue: moment(result.paymentDate).format(),
					toValue: moment(req.body.paymentDate).format()
				})
			}
		}).then(() => {
			return paymentlog.updateAttributes(req.body)
		}).then(function (result) {
			res.status(200).send(result)
		}).catch(function (err) {
			console.log(err)
			return res.status(500).send({
				error: err.message
			})
		})
	})

	// Delete agentstate
	app.delete(BASE_URL + '/paymentlog/:id', function (req, res) {
		Models.PaymentLog.destroy({
			where: {
				id: req.params.id
			}
		})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}