module.exports = function (app, Models, BASE_URL) {
	// Get campaignstagedatetimerules list
	app.get(BASE_URL + '/campaignstagedatetimerules', function (req, res) {
		Models.CampaignStageDateTimeRule.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get campaignstagedatetimerule by id
	app.get(BASE_URL + '/campaignstagedatetimerules/:id', function (req, res) {
		Models.CampaignStageDateTimeRule.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create campaignstagedatetimerule
	app.post(BASE_URL + '/campaignstagedatetimerules', function (req, res) {
		Models.CampaignStageDateTimeRule.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update campaignstagedatetimerule
	app.put(BASE_URL + '/campaignstagedatetimerules/:id', function (req, res) {
		Models.CampaignStageDateTimeRule.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete campaignstagedatetimerule
	app.delete(BASE_URL + '/campaignstagedatetimerules/:id', function (req, res) {
		Models.CampaignStageDateTimeRule.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}