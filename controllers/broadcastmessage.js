var Sequelize = require('sequelize')
var Promise = Sequelize.Promise
var _ = require('underscore')

module.exports = function (app, Models, BASE_URL) {
	// Get campaign message list
	app.get(BASE_URL + '/broadcastmessages', function (req, res) {
		var where = {}
		if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        } else {
			where.clientId = {
				$eq: null
			}
		}
		Models.BroadcastMessage.findAll({ where })
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get campaign message by id
	app.get(BASE_URL + '/broadcastmessages/:id', function (req, res) {
		Models.BroadcastMessage.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/broadcastmessages/:id/getmymessages', function (req, res) {
		var campaignId = req.query.campaignId
		var stageId = req.query.stageId
		var agentId = req.params.id
		var skills = req.query.skills

		var result = {
			campaignMessages: [],
			skillMessages: [],
			agentMessages: []
		}

		var where = {
			campaignId: campaignId,
			$or: [{
				campaignstageId: stageId
			}, {
				campaignstageId: null
			}]
		}
		var subWhere = {
			agentId: agentId
		}

		if (req.user.isClientAgent || req.user.isClientAdmin) {
			where.clientId = req.user.clientId
			subWhere.clientId = req.user.clientId
		}
		

		Models.BroadcastMessage.findAll({
				where,
				limit: 10,
				order: [
					['createdAt', 'DESC']
				]
			})
			.then(function (results) {
				if (results && results.length) {
					result.campaignMessages = results
				}
				Models.BroadcastMessage.findAll({
						where: subWhere,
						limit: 10,
						order: [
							['createdAt', 'DESC']
						]
					})
					.then(function (results) {
						if (results && results.length) {
							result.agentMessages = results
						}

						var queryObj = {}

						if (skills) {
							if (skills instanceof Array) {
								queryObj['where'] = {
									subskillId: {
										$in: skills
									}
								}
							} else {
								queryObj['where'] = {
									subskillId: skills
								}
							}

							queryObj.limit = 10
							queryObj.group = ['uniqueId']
							queryObj.order = [
								['createdAt', 'DESC']
							]

							if (req.user.isClientAgent || req.user.isClientAdmin) {
								queryObj.where.clientId = req.user.clientId
							}

							Models.BroadcastMessage.findAll(queryObj)
								.then(function (results) {
									if (results && results.length) {
										result.skillMessages = results
									}
									
									res.status(200).send(result)
								})
								.catch(function (err) {
									res.status(500).send({
										error: err.message
									})
								})
						} else {
							res.status(200).send(result)
						}
					})
					.catch(function (err) {
						res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				res.status(500).send({
					error: err.message
				})
			})
	})

	// Create campaign message
	app.post(BASE_URL + '/broadcastmessages', function (req, res) {
		if (req.user.isClientAgent || req.user.isClientAdmin) {
			req.body.clientId = req.user.clientId || req.body.clientId
		}

		var resultSent = false;
		if (req.body.agents && req.body.agents.length > 0) {
			//add a message for each agent
			for (var i = 0; i < req.body.agents.length; i++) {
				var message = {
					agentId: req.body.agents[i],
					title: req.body.title,
					content: req.body.content,
					clientId: req.body.clientId,
					campaignId: req.body.campaignId,
					campaignstageId: req.body.campaignstageId,
				}
				Models.BroadcastMessage.create(message)
					.then(function (result) {
						if (!resultSent) {
							resultSent = true;
							res.status(201).send(result)
						}
					})
					.catch(function (err) {
						if (!resultSent) {
							resultSent = true;
							res.status(500).send({
								error: err.message
							})
						}
					})
			};

		} else if (req.body.skills && req.body.skills.length > 0) {
			//add a message for each skill
			for (var i = 0; i < req.body.skills.length; i++) {
				var message = {
					skillId: req.body.skills[i],
					title: req.body.title,
					content: req.body.content,
					clientId: req.body.clientId,
					campaignId: req.body.campaignId,
					campaignstageId: req.body.campaignstageId,
				}
				Models.BroadcastMessage.create(message)
					.then(function (result) {
						if (!resultSent) {
							resultSent = true;
							res.status(201).send(result)
						}
					})
					.catch(function (err) {
						if (!resultSent) {
							resultSent = true;
							res.status(500).send({
								error: err.message
							})
						}
					})
			};

		} else {
			Models.BroadcastMessage.create(req.body)
				.then(function (result) {
					res.status(201).send(result)
				})
				.catch(function (err) {
					res.status(500).send({
						error: err.message
					})
				})
		}
	})

	// Update campaign message
	app.put(BASE_URL + '/broadcastmessages/:id', function (req, res) {
		Models.BroadcastMessage.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete campaign message
	app.delete(BASE_URL + '/broadcastmessages/:id', function (req, res) {
		Models.BroadcastMessage.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}