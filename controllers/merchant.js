var tsys = require('../utils/tsys')

module.exports = function (app, Models, BASE_URL) {
    app.use(BASE_URL + '/merchant', (req, res, next) => {
        if (req.user.name !== 'superadmin') return res.status(401)
        next()
    })
    app.get(BASE_URL + '/merchant', function (req, res) {
        Models.Merchant.findAll({
            include: [Models.Client],
        }).then(marchants => {
            res.send(marchants.map(m => {
                m = m.toJSON()
                m.transactionKey = !!m.transactionKey
                return m
            }))
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })

    app.get(BASE_URL + '/merchant/:id', function (req, res) {
        Models.Merchant.findOne({
            where: { id: req.params.id },
            include: [Models.Client]
        }).then(result => {
            res.send(result)
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })

    app.put(BASE_URL + '/merchant/:id', (req, res) => {
        var merchant
        Models.Merchant.findOne({
            where: { id: req.params.id }
        }).then(_merchant => {
            merchant = _merchant
            if (!merchant) return Promise.reject()
            return merchant.update({
                deviceID: req.body.deviceID,
                merchantID: req.body.merchantID,
                developerID: req.body.developerID,
                clientId: req.body.clientId,
                transactionKey: null
            })
        }).then(result => {
            res.send(result)
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })

    app.put(BASE_URL + '/merchant/:id/refresh', (req, res) => {
        var merchant
        Models.Merchant.findOne({
            where: { id: req.params.id }
        }).then(_merchant => {
            merchant = _merchant
            if (!merchant) return Promise.reject()
            return tsys.createTransactionKey(merchant.merchantID, req.body.userID, req.body.password, merchant.apiDomain)
        }).then(result => {
            if (result.GenerateKeyResponse.status === 'PASS') {
                return merchant.update({
                    transactionKey: result.GenerateKeyResponse.transactionKey
                }).then(() => {
                    res.send({
                        success: true
                    })
                })
            } else {
                res.send({
                    success: false,
                    message: result.GenerateKeyResponse.responseMessage
                })
            }
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })

    app.post(BASE_URL + '/merchant', function (req, res) {
        Models.Merchant.create({
            deviceID: req.body.deviceID,
            merchantID: req.body.merchantID,
            developerID: req.body.developerID,
            clientId: req.body.clientId,
            uiDomain: req.body.uiDomain,
            apiDomain: req.body.apiDomain
        }).then(marchant => {
            res.send(marchant)
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })

    app.delete(BASE_URL + '/merchant/:id', function (req, res) {
        Models.Merchant.findOne({
            where: { id: req.params.id }
        }).then(result => {
            return result.destroy()
        }).then(() => {
            res.send({ success: true })
        }).catch(function (err) {
            return res.status(500).send({ error: err ? err.message || err : 'Unknown Error' })
        })
    })
}