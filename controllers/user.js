var _ = require('underscore')
var fs = require('fs')
var path = require('path')
var crypt = require('../utils/encryption')()
var constants = require('../config/constants')
var fileUtils = require('../utils/fileUtils.js')
var usersessionAPI = require('../database/redisAPI')('usersession')
var agentsessionAPI = require('../database/redisAPI')('agentsession')
var ensureDirectoryExists = fileUtils.ensureDirectoryExists
var copyFile = fileUtils.copyFile
var userUtils = require('../utils/user')

module.exports = function (app, Models, BASE_URL) {
	var email = require('../emailing/email')(Models)

	// keep alive session by user id
	app.post(BASE_URL + '/users/:id/keepalive', function (req, res, next) {
		var session
		usersessionAPI.get(req.params.id)
			.then(function (_session) {
				session = _session
				if (session && session.agentId && session.monitorTimeout && session.state === 'agent.dashboard' && req.body.state !== 'agent.dashboard') {
					// get the agentSession too and check the timeout
					return agentsessionAPI.get(session.agentId)
				}
			}).then(function (agentSession) {
				if (!session) {
					res.status(404).send({
						success: false
					})
				} else {
					if (agentSession) {
						session.agentStatus = agentSession.agentStatus
						session.agentStatusSince = agentSession.agentStatusSince
						session.lastPosting = agentSession.lastPosting
						if (!session.agentStatus) {
							return Models.AgentState.findOne({
								where: {
									name: 'Idle'
								},
								raw: true
							})
						}
					}
				}
			}).then(status => {
				if (!session) return
				if (status) {
					session.agentStatus = status
				}

				session.lastKeepAlivePing = Date.now()
				session.userAgent = req.headers["user-agent"]
				session.origin = req.headers["origin"]
				session.host = req.ip
				session.state = req.body.state

				if (session.monitorTimeout && session.state !== 'agent.dashboard') {
					// check the agent timeout 
					var timeoutDuration = 0
					if (session.agentStatus && session.agentStatus.timeout) timeoutDuration = session.agentStatus.timeout
					var idleTimeout = 1000 * 60 * timeoutDuration
					if (timeoutDuration) {
						var highest = session.agentStatusSince < session.lastPosting ? session.lastPosting : session.agentStatusSince
						session.timeoutIn = Math.round(((highest + idleTimeout) - Date.now()) / 1000)
					}

					if (timeoutDuration && Date.now() - session.agentStatusSince > idleTimeout && Date.now() - session.lastPosting > idleTimeout) {
						userUtils.logoutEvent(session.id, session.agentId, 'statusTimeout')
						usersessionAPI.delete(session.id)
						res.status(404).send({
							success: false
						})
					} else {
						usersessionAPI.save(session)
						res.status(200).send({
							success: true,
							session
						})
					}
				} else {
					usersessionAPI.save(session)
					res.status(200).send({
						success: true,
						session
					})
				}
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete user session
	app.delete(BASE_URL + '/users/:id/keepalive', function (req, res) {
		Models.User.findById(req.params.id)
			.then(user => {
				if (user && user.agentId) {
					var newEvent = {}
					newEvent.eventType = "Login"
					newEvent.eventName = "Logged Out"
					newEvent.agentId = user.agentId
					Models.AgentEvent.create(newEvent);
				}

				if (user) {
					userUtils.logoutEvent(user.id, user.agentId, 'logout')
				}

				usersessionAPI.delete(req.params.id)
					.then(function (session) {
						res.status(200).send({
							success: true
						})
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(err => {
				return res.status(500).send({
					error: err.message
				})
			})

	})

	app.get(BASE_URL + '/users/keepalive', function (req, res) {
		usersessionAPI.getAll()
			.then(function (sessions) {
				res.status(200).send(sessions)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/users', function (req, res) {
		var where = {}
		if (req.user.isClientAgent || req.user.isClientAdmin) {
			where.clientId = req.user.clientId
		}
		Models.User.findAll({
			where,
			include: [Models.Agent]
		})
			.then(function (items) {
				for (var i = 0; i < items.length; i++) {
					items[i].password = '--PLACEHOLDER--'
				}
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/user', function (req, res) {
		Models.User.find({
			where: {
				id: req.user.id
			},
			include: [{
				model: Models.Client
			}, {
				model: Models.Agent,
				include: [Models.Device]
			}]
		}).then(function (result) {
			if (result) {
				var roles = {
					'isSuperAdmin': result.username === 'dualtone_admin',
					'isAdmin': !!result.isAdmin,
					'isAgent': !!result.isAgent,
					'isClient': !!result.isClient,
					'isClientAdmin': !!result.isClientAdmin,
					'isClientAgent': !!result.isClientAgent,
					'isSuperManager': !!result.isSuperManager,
					'isSupervisor': !!result.isSupervisor,
				}

				result = Object.assign(result, roles);

				result.password = '--PLACEHOLDER--';

				res.status(200).send(result);
			}
		}).catch(function (err) {
			return res.status(500).send({
				error: err.message
			})
		});
	});


	app.get(BASE_URL + '/users/:id/checkpassword', function (req, res) {
		if (parseInt(req.params.id) !== req.user.id) {
			return res.status(200).send({
				result: false
			})
		}
		var pass = crypt.encrypt(req.query.password)
		if (pass === req.user.password) {
			res.status(200).send({
				result: true
			})
		} else {
			res.status(200).send({
				result: false
			})
		}
	})

	app.get(BASE_URL + '/users/:id/emailpassword', function (req, res) {
		Models.User.find({
			where: {
				id: req.params.id
			}
		}).then(function (result) {
			if (result) {
				var pass = crypt.decrypt(result.password)
				email.emailPassword(pass, result.email)
					.then(function (emailResult) {
						console.log(emailResult)
						res.status(200).send()
					})
					.catch(function (err) {
						console.log(err)
						return res.status(500).send({
							error: err.message
						})
					})
			}
		}).catch(function (err) {
			return res.status(500).send({
				error: err.message
			})
		})
	})

	//get all agents for this user based on user type and campaign ownership
	app.get(BASE_URL + '/users/:id/agents', function (req, res) {
		if (req.params.id == 0) {
			Models.Agent.findAll({
				include: [{
					model: Models.CampaignStage,
					include: [{
						model: Models.Campaign
					}]
				},
				Models.User,
				Models.Device
				]
			})
				.then(function (agents) {
					res.status(200).send(agents)
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		} else {
			Models.User.findOne({
				where: {
					id: req.params.id
				}
			})
				.then(function (user) {
					if (user.isAdmin || user.isSuperManager) {
						Models.Agent.findAll({
							include: [{
								model: Models.CampaignStage,
								include: [{
									model: Models.Campaign
								}]
							},
							Models.User,
							Models.Device
							]
						})
							.then(function (agents) {
								res.status(200).send(agents)
							})
							.catch(function (err) {
								return res.status(500).send({
									error: err.message
								})
							})
					} else if (user.isClientAdmin) {
						console.log('clientAdmin')
						Models.Agent.findAll({
							where: {
								clientId: user.clientId
							},
							include: [
								Models.Device
							],
							logging: console.log
						}).then(function (agents) {
							res.status(200).send(agents)
						}).catch(function (err) {
							return res.status(500).send({
								error: err.message
							})
						})
					} else {
						Models.Agent.findAll({
							include: [{
								model: Models.CampaignStage,
								include: [{
									model: Models.Campaign,
									where: {
										owningUserId: req.params.id
									},
									required: true
								}],
								required: true
							},
							Models.User,
							Models.Device
							]
						})
							.then(function (agents) {
								res.status(200).send(agents)
							})
							.catch(function (err) {
								return res.status(500).send({
									error: err.message
								})
							})
					}
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		}
	})

	app.get(BASE_URL + '/users/:id/sales', function (req, res) {
		if (!req.user.isClientAdmin && !req.user.isClientAgent && req.user.client && req.user.client.reportPasswordRequired) {
			return res.status(400).send({
				error: 'Password Protected'
			})
		}
		Models.User.findById(req.params.id)
			.then(user => {
				if (user) {
					var filterObj = {}
					if (req.query.startdate && req.query.enddate) {
						filterObj.createdAt = {
							$gte: req.query.startdate,
							$lte: req.query.enddate
						}
					} else if (req.query.enddate) {
						filterObj.createdAt = {
							$lte: req.query.enddate
						}
					} else if (req.query.startdate) {
						filterObj.createdAt = {
							$gte: req.query.startdate
						}
					}

					if (req.user.isClientAdmin) {
						filterObj.clientId = req.user.clientId
					}

					new Promise((resolve, reject) => {
						if (user.isAdmin || user.isSuperManager || user.isClientAdmin) {
							//return all sales
							Models.Sale.findAndCount({
								include: [Models.CallResult, Models.Lead, Models.Campaign, Models.CampaignStage, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir,
								where: filterObj
							}).then(resolve).catch(reject)
						} else if (user.isSupervisor) {
							//return sales for all the campaigns they are the manager of
							Models.Campaign.findAll({
								where: {
									owningUserId: user.id
								}
							}).then(campaigns => {
								filterObj.campaignId = {
									$in: campaigns.map(camp => {
										return camp.id
									})
								}
								Models.Sale.findAndCount({
									include: [{
										model: Models.CallResult,
										where: filterObj
									}, Models.Lead, Models.Campaign, Models.CampaignStage, Models.Client],
									offset: req.query.limit * req.query.page,
									limit: req.query.limit,
									order: req.query.orderby + ' ' + req.query.dir
								})
									.then(resolve).catch(reject)
							})

						} else if (user.isClient) {
							//return sales for this client
							filterObj.clientId = user.clientId
							Models.Sale.findAndCount({
								where: filterObj,
								include: [Models.CallResult, Models.Lead, Models.Campaign, Models.CampaignStage, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir
							})
								.then(resolve).catch(reject)
						} else if (user.isAgent || user.isClientAgent) {
							//return all this agents sales
							filterObj.agentId = user.agentId
							Models.Sale.findAndCount({
								include: [{
									model: Models.CallResult,
									where: filterObj
								}, Models.Lead, Models.Campaign, Models.CampaignStage, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir
							})
								.then(resolve).catch(reject)
						} else {
							//i dont know what you are so return NOTHING
							resolve({
								rows: [],
								count: 0
							})
						}
					})
						.then(results => {
							var promises = []
							results.rows.forEach(row => {
								promises.push(Models.LeadAudit.findAll({
									where: {
										leadId: row.leadId
									}
								}))
							})
							Promise.all(promises)
								.then(changes => {
									changes.forEach((change, i) => {
										if (change && change.length) {
											results.rows[i].dataValues.hasDbChanges = true;
											results.rows[i].dataValues.dbChanges = change;
										} else {
											results.rows[i].dataValues.hasDbChanges = false;
										}
									})
									res.status(200).send(results)
								})
						})
						.catch(err => {
							return res.status(400).send({
								error: err.message
							})
						})


				} else {
					return res.status(400).send({
						error: 'No User Found'
					})
				}
			}).catch(err => {
				return res.status(400).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/users/:id/pledges', function (req, res) {
		if (!req.user.isClientAdmin && !req.user.isClientAgent && req.user.client && req.user.client.reportPasswordRequired) {
			return res.status(400).send({
				error: 'Password Protected'
			})
		}
		Models.User.findById(req.params.id)
			.then(user => {
				if (user) {
					var filterObj = {
						giftAmount: {
							$gt: 0
						}
					}
					if (req.query.startdate && req.query.enddate) {
						filterObj.createdAt = {
							$gte: req.query.startdate,
							$lte: req.query.enddate
						}
					} else if (req.query.enddate) {
						filterObj.createdAt = {
							$lte: req.query.enddate
						}
					} else if (req.query.startdate) {
						filterObj.createdAt = {
							$gte: req.query.startdate
						}
					}

					if (req.user.isClientAdmin) {
						filterObj.clientId = req.user.clientId
					}

					new Promise((resolve, reject) => {
						if (user.isAdmin || user.isSuperManager || user.isClientAdmin) {
							//return all gifts
							Models.CallResult.findAndCount({
								where: filterObj,
								include: [Models.Lead, Models.CampaignStage, Models.CallRecord, Models.Campaign, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir
							}).then(resolve).catch(reject)
						} else if (user.isSupervisor) {
							//return gifts for all the campaigns they are the manager of
							Models.Campaign.findAll({
								where: {
									owningUserId: user.id
								}
							}).then(campaigns => {
								filterObj.campaignId = {
									$in: campaigns.map(camp => {
										return camp.id
									})
								}
								Models.CallResult.findAndCount({
									where: filterObj,
									include: [Models.Lead, Models.CampaignStage, Models.CallRecord, Models.Campaign, Models.Client],
									offset: req.query.limit * req.query.page,
									limit: req.query.limit,
									order: req.query.orderby + ' ' + req.query.dir
								}).then(resolve).catch(reject)
							})

						} else if (user.isClient) {
							//return gifts for this client
							filterObj.clientId = user.clientId
							Models.CallResult.findAndCount({
								where: filterObj,
								include: [Models.Lead, Models.CampaignStage, Models.CallRecord, Models.Campaign, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir
							}).then(resolve).catch(reject)
						} else if (user.isAgent || user.isClientAgent) {
							//return all this agents gifts
							filterObj.agentId = user.agentId
							Models.CallResult.findAndCount({
								where: filterObj,
								include: [Models.Lead, Models.CampaignStage, Models.CallRecord, Models.Campaign, Models.Client],
								offset: req.query.limit * req.query.page,
								limit: req.query.limit,
								order: req.query.orderby + ' ' + req.query.dir
							}).then(resolve).catch(reject)
						} else {
							//i dont know what you are so return NOTHING
							resolve({
								rows: [],
								count: 0
							})
						}
					}).then(results => {
						var promises = []
						results.rows.forEach(row => {
							promises.push(Models.LeadAudit.findAll({
								where: {
									leadId: row.leadId
								}
							}))
						})
						Promise.all(promises)
							.then(changes => {
								changes.forEach((change, i) => {
									if (change && change.length) {
										results.rows[i].dataValues.hasDbChanges = true;
										results.rows[i].dataValues.dbChanges = change;
									} else {
										results.rows[i].dataValues.hasDbChanges = false;
									}
								})
								res.status(200).send(results)
							})
					}).catch(err => {
						return res.status(400).send({
							error: err.message
						})
					})
				} else {
					return res.status(400).send({
						error: 'No User Found'
					})
				}
			}).catch(err => {
				return res.status(400).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/users/:id/collections', (req, res) => {
		if (!req.user.isClientAdmin && !req.user.isClientAgent && req.user.client && req.user.client.reportPasswordRequired) {
			return res.status(400).send({
				error: 'Password Protected'
			})
		}
		Models.User.findById(req.params.id)
			.then(user => {
				if (user) {
					var paymentsFilter = {
						paymentType: 'Credit Card',
						wrapup: 'Invoice Payment'
					}
					var exceptionFilter = {
						wrapup: 'Exception Refusal'
					}
					var refusalFilter = {
						wrapup: 'Standard Refusal'
					}
					var stageFilter = {
						$or: [{
							name: 'Collections'
						}, {
							name: 'Bad Credit Cards'
						}]
					}

					if (req.query.startdate && req.query.enddate) {
						paymentsFilter.createdAt = {
							$gte: req.query.startdate,
							$lte: req.query.enddate
						}
						exceptionFilter.createdAt = {
							$gte: req.query.startdate,
							$lte: req.query.enddate
						}
						refusalFilter.createdAt = {
							$gte: req.query.startdate,
							$lte: req.query.enddate
						}
					} else if (req.query.enddate) {
						paymentsFilter.createdAt = {
							$lte: req.query.enddate
						}
						exceptionFilter.createdAt = {
							$lte: req.query.enddate
						}
						refusalFilter.createdAt = {
							$lte: req.query.enddate
						}
					} else if (req.query.startdate) {
						paymentsFilter.createdAt = {
							$gte: req.query.startdate
						}
						exceptionFilter.createdAt = {
							$gte: req.query.startdate
						}
						refusalFilter.createdAt = {
							$gte: req.query.startdate
						}
					}

					if (req.query.dir) {
						req.query.dir = JSON.parse(req.query.dir)
					}
					if (req.query.orderby) {
						req.query.orderby = JSON.parse(req.query.orderby)
					}
					if (req.query.page) {
						req.query.page = JSON.parse(req.query.page)
					}

					if (req.user.isClientAdmin) {
						paymentsFilter.clientId = req.user.clientId
						exceptionFilter.clientId = req.user.clientId
						refusalFilter.clientId = req.user.clientId
					}

					new Promise((resolve, reject) => {
						if (user.isAdmin || user.isSuperManager || user.isClientAdmin) {
							resolve()
						} else if (user.isSupervisor) {
							Models.Campaign.findAll({
								where: {
									owningUserId: user.id
								}
							}).then(campaigns => {
								paymentsFilter.campaignId = {
									$in: campaigns.map(camp => {
										return camp.id
									})
								}
								exceptionFilter.campaignId = {
									$in: campaigns.map(camp => {
										return camp.id
									})
								}
								refusalFilter.campaignId = {
									$in: campaigns.map(camp => {
										return camp.id
									})
								}

								resolve()
							})
						} else if (user.isClient && user.clientId) {
							paymentsFilter.clientId = user.clientId
							exceptionFilter.clientId = user.clientId
							refusalFilter.clientId = user.clientId

							resolve()
						} else if ((user.isAgent || user.isClientAgent) && user.agentId) {
							paymentsFilter.agentId = user.agentId
							exceptionFilter.agentId = user.agentId
							refusalFilter.agentId = user.agentId

							resolve()
						} else {
							reject()
						}
					})
						.then(() => {
							var promises = []

							promises.push(Models.CallResult.findAndCount({
								where: paymentsFilter,
								include: [{
									model: Models.CampaignStage,
									where: stageFilter
								}, Models.Lead, Models.Campaign, Models.Client],
								offset: req.query.limit * (req.query.page.payments.current - 1),
								limit: req.query.limit,
								order: req.query.orderby.payments + ' ' + (req.query.dir.payments ? 'DESC' : 'ASC')
							}))

							//exception refusals
							promises.push(Models.CallResult.findAndCount({
								where: exceptionFilter,
								include: [{
									model: Models.CampaignStage,
									where: stageFilter
								}, Models.Lead, Models.Campaign, Models.Client],
								offset: req.query.limit * (req.query.page.exceptions.current - 1),
								limit: req.query.limit,
								order: req.query.orderby.exceptions + ' ' + (req.query.dir.exceptions ? 'DESC' : 'ASC')
							}))

							//standard refusals
							promises.push(Models.CallResult.findAndCount({
								where: refusalFilter,
								include: [{
									model: Models.CampaignStage,
									where: stageFilter
								}, Models.Lead, Models.Campaign, Models.Client],
								offset: req.query.limit * (req.query.page.refusals.current - 1),
								limit: req.query.limit,
								order: req.query.orderby.refusals + ' ' + (req.query.dir.refusals ? 'DESC' : 'ASC')
							}))

							Promise.all(promises)
								.then(results => {

									var paymentChangePromises = []
									var exceptionChangePromises = []
									var refusalChangePromises = []

									var payments = results[0]
									var exceptions = results[1]
									var refusals = results[2]

									payments.rows.forEach(payment => {
										paymentChangePromises.push(Models.LeadAudit.findAll({
											where: {
												leadId: payment.leadId
											}
										}))
									})

									exceptions.rows.forEach(exception => {
										exceptionChangePromises.push(Models.LeadAudit.findAll({
											where: {
												leadId: exception.leadId
											}
										}))
									})

									refusals.rows.forEach(refusal => {
										refusalChangePromises.push(Models.LeadAudit.findAll({
											where: {
												leadId: refusal.leadId
											}
										}))
									})

									Promise.all([Promise.all(paymentChangePromises), Promise.all(exceptionChangePromises), Promise.all(refusalChangePromises)])
										.then((changes) => {
											var payChanges = changes[0]
											var exceptChanges = changes[1]
											var refusalChanges = changes[2]

											payChanges.forEach((change, i) => {
												if (change && change.length) {
													payments.rows[i].dataValues.hasDbChanges = true;
													payments.rows[i].dataValues.dbChanges = change;
												} else {
													payments.rows[i].dataValues.hasDbChanges = false;
												}
											})

											exceptChanges.forEach((change, i) => {
												if (change && change.length) {
													exceptions.rows[i].dataValues.hasDbChanges = true;
													exceptions.rows[i].dataValues.dbChanges = change;
												} else {
													exceptions.rows[i].dataValues.hasDbChanges = false;
												}
											})

											refusalChanges.forEach((change, i) => {
												if (change && change.length) {
													refusals.rows[i].dataValues.hasDbChanges = true;
													refusals.rows[i].dataValues.dbChanges = change;
												} else {
													refusals.rows[i].dataValues.hasDbChanges = false;
												}
											})

											res.status(200).send({
												payments: payments,
												exceptions: exceptions,
												refusals: refusals
											})
										})
								})
								.catch(err => {
									return res.status(500).send({
										error: err.message
									})
								})
						})
						.catch(() => {
							//i dont know what you are so return NOTHING
							res.status(200).send([])
						})
				} else {
					return res.status(400).send({
						error: 'No User Found'
					})
				}
			})
			.catch(err => {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/users/:id', function (req, res) {
		if (req.params.id == 0) {
			return res.status(200).send({
				id: 0,
				username: 'dualtone_admin',
				name: 'superadmin',
				homeState: 'admin.clients',
				showStats: true,
				isAdmin: true,
				isSupervisor: true,
				isAgent: false
			})
		}

		Models.User.find({
			include: [Models.Agent],
			where: {
				id: req.params.id
			}
		})
			.then(function (result) {
				result.password = '--PLACEHOLDER--'
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/users/:id/uploadavatar', function (req, res) {
		var filePath = req.files.file.path

		var targetDir = path.resolve(constants.AVATAR_LOCATION)
		var destinationFilePath = path.join(targetDir, req.files.file.name)

		/*
		ensureDirectoryExists(targetDir, function () {
			sharp(filePath).resize(80).toFile(destinationFilePath, function (err) {
				Models.User.findById(req.params.id)
					.then(function (user) {
						if (user) {
							user.updateAttributes({
								avatar: req.files.file.name
							})
								.then(function (result) {
									res.status(201).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({
										error: err.message
									})
								})
						}
					})
			})
		})
		*/
	})

	app.post(BASE_URL + '/users', function (req, res) {
		if (req.body.password !== '--PLACEHOLDER--') {
			req.body.password = crypt.encrypt(req.body.password)
		}
		Models.User.find({
			where: {
				agentId: req.body.agentId
			}
		})
			.then(function (result) {
				if (!result || !req.body.isAgent) {
					req.body.lastPasswordUpdate = new Date()
					Models.User.create(req.body)
						.then(function (result) {
							res.status(201).send(result)
						})
						.catch(function (err) {
							return res.status(500).send({
								error: err.message
							})
						})
				} else {
					res.status(201).send(result)
				}
			})
	})

	app.put(BASE_URL + '/users/:id', function (req, res) {
		Models.User.findById(req.params.id)
			.then(function (result) {
				Models.User.find({
					where: {
						agentId: req.body.agentId,
						id: {
							$ne: result.id
						}
					}
				})
					.then(function (existingUser) {
						if (!existingUser || !req.body.isAgent) {
							if (req.body.password == '--PLACEHOLDER--') {
								delete req.body.password
							} else {
								req.body.passwordhistory = []
								if (result.passwordhistory) {
									req.body.passwordhistory = JSON.parse(result.passwordhistory)
								}
								req.body.passwordhistory.push(crypt.decrypt(result.password))
								if (req.body.passwordhistory.length > 3) {
									req.body.passwordhistory.shift()
								}
								req.body.passwordhistory = JSON.stringify(req.body.passwordhistory)

								req.body.password = crypt.encrypt(req.body.password)
								req.body.lastPasswordUpdate = new Date()
							}
							result.updateAttributes(req.body)
								.then(function (result) {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({
										error: err.message
									})
								})
						} else {
							res.status(200).send(existingUser)
						}
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.delete(BASE_URL + '/users/:id', function (req, res) {
		Models.User.destroy({
			where: {
				id: req.params.id
			}
		})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}