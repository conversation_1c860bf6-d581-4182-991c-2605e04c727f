module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/sales', function (req, res) {
		Models.Sale.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.get(BASE_URL + '/sales/:id', function (req, res) {
		Models.Sale.find({ where: { id: req.params.id } })
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.post(BASE_URL + '/sales', function (req, res) {
		if (!req.body.callresultId) {
			return res.status(500).send({ error: 'Invalid, please close Sale and try again' })
		}
		Models.Sale.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	app.put(BASE_URL + '/sales/:id', function (req, res) {
		Models.Sale.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
	})

	app.delete(BASE_URL + '/sales/:id', function (req, res) {
		Models.Sale.destroy({
			where: { id: req.params.id }
		})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}