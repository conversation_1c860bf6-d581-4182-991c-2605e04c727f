var _ = require('underscore')
var tsys = require('../utils/tsys')
var APP_SETTINGS = require('../config/constants')

module.exports = function (app, Models, BASE_URL) {


    // Get leads list
    app.get(BASE_URL + '/leads', function (req, res) {
        Models.Lead.findAll()
            .then(function (items) {
                res.status(200).send(items)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get leads entity field names
    app.get(BASE_URL + '/leads/dbfieldnames', function (req, res) {
        Models.LeadField.findAll()
            .then(function (items) {
                var fields = []

                _.each(Object.keys(Models.Lead.attributes), function (k) {
                    if (['id', 'campaignId', 'tfSkillId', 'tfSubSkillId', 'tmSkillId', 'tmSubSkillId', 'createdAt', 'updatedAt'].indexOf(k) === -1)
                        fields.push({
                            name: k
                        })
                })

                _.each(items, function (item) {
                    fields.push({
                        customField: true,
                        customFieldId: item.id,
                        name: item.name,
                        type: item.type,
                        regex: item.regex
                    })
                })

                res.send(fields)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get lead by id
    app.get(BASE_URL + '/leads/:id', function (req, res) {
        Models.Lead.findById(req.params.id,
            {
                include: [{ model: Models.CardToken, attributes: ['id', 'cardType', 'status', 'maskedCardNumber', 'expirationDate'] }]
            })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/leads/:id/cardtoken', (req, res) => {
        Models.CardToken.findOne({
            where: {
                leadId: req.params.id
            },
            order: [['id', 'desc']]
        }).then(result => {
            res.status(200).send(result)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })


    // get the most recent recurring payment for this lead
    app.get(BASE_URL + '/leads/:id/recurring', (req, res) => {
        Models.RecurringPayment.findOne({
            where: {
                leadId: req.params.id
            },
            include: [{
                model: Models.Lead,
                include: [Models.CardToken]
            }, Models.PaymentLog, Models.Invoice],
            order: [['id', 'desc']]
        }).then(result => {
            res.status(200).send(result)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/leads/:id/detail', function (req, res) {
        var leadId = req.params.id
        var lead = Models.Lead.findById(leadId, {
            include: [{
                model: Models.Client,
                attributes: ['id', 'name']
            }, {
                model: Models.Skill,
                as: 'tfSkill'
            }, {
                model: Models.SubSkill,
                as: 'tfSubSkill'
            }, {
                model: Models.Skill,
                as: 'tmSkill'
            }, {
                model: Models.SubSkill,
                as: 'tmSubSkill'
            }]
        })

        var invoices = Models.Invoice.findAll({
            where: { leadId },
            attributes: ['id', 'createdAt', 'grandTotal', 'amountRemaining', 'deliveryMethod', 'dueDate', 'requestCount'],
            raw: true
        })
        var callResults = Models.CallResult.findAll({
            where: { leadId, completed: 1 },
            attributes: ['id', 'wrapup', 'createdAt', 'notes', 'campaignId', 'campaignstageId', 'refusalReason', 'decisionMaker', 'giftAmount', 'grandTotal'],
            include: [{
                model: Models.CallRecord,
                attributes: ['id', 'callerId', 'callTo', 'startDateTime', 'totalDurationSecs', 'recordingServer', 'recordingLocation']
            }, {
                model: Models.Agent,
                attributes: ['id', 'name']
            }, {
                model: Models.PaymentLog,
                attributes: ['id', 'amount', 'paymentDate', 'isPaid', 'actualPaymentDate', 'status', 'error', 'deleted', 'disabled']
            }, Models.RecurringPayment]
        })
        var campaignLeads = Models.CampaignLead.findAll({
            where: { leadId },
            include: [{
                model: Models.Campaign,
                attributes: ['id', 'name'],
                include: [{
                    model: Models.CampaignStage,
                    attributes: ['id', 'name']
                }]
            }]
        })
        var leadAudits = Models.LeadAudit.findAll({
            where: { leadId },
            include: [{
                model: Models.User,
                attributes: ['id', 'name']
            }]
        })
        var callbacks = Models.Callback.findAll({
            where: { leadId },
            attributes: ['id', 'startDateTime', 'phone', 'expired', 'deleted', 'campaignId'],
            include: [{ model: Models.Agent, attributes: ['id', 'name'] }]
        })
        var leadUpdates = Models.LeadUpdate.findAll({
            where: { leadId },
            raw: true
        })
        var campaignLeadUpdates = Models.CampaignLeadUpdate.findAll({
            where: { leadId },
            raw: true
        })
        var suppressions = Models.Suppression.findAll({
            where: { leadId },
            raw: true
        })
        Promise.all([lead, invoices, callResults, campaignLeads, leadAudits, callbacks, leadUpdates, campaignLeadUpdates, suppressions]).then(results => {
            var lead = results[0].toJSON()
            if (lead && lead.customFields && typeof lead.customFields === 'string') {
                lead.customFields = JSON.parse(lead.customFields)
            }
            var campaignLeads = results[3].map(r => r.toJSON())
            lead.invoices = results[1]
            lead.callresults = results[2].map(r => r.toJSON())
            lead.callresults.forEach(cr => {
                var campaignLead = campaignLeads.find(cl => cl.campaignId === cr.campaignId)
                if (campaignLead && campaignLead.campaign) {
                    cr.campaign = JSON.parse(JSON.stringify(campaignLead.campaign))
                    if (cr.campaign.campaignstages) {
                        cr.campaignstage = cr.campaign.campaignstages.find(cs => cs.id === cr.campaignstageId)
                    }
                    delete cr.campaign.campaignstages
                }
                cr.installmentHistory = ''
                cr.installmentError = ''
                if (cr.paymentlogs && cr.paymentlogs.length && cr.paymentlogs.length > 1) {
                    cr.installmentHistory = cr.paymentlogs.filter(pl => !pl.deleted && !pl.disabled && pl.status !== 'error').length
                    cr.paymentlogs.reverse().forEach(row => {
                        if (cr.installmentError) return
                        if (row.deleted) return
                        if (row.error) cr.installmentError = row.error
                    })
                }
                delete cr.paymentlogs
            })
            lead.leadaudits = results[4].map(r => r.toJSON())
            lead.callbacks = results[5].map(r => r.toJSON())
            lead.callbacks.forEach(cb => {
                var campaignLead = campaignLeads.find(cl => cl.campaignId === cb.campaignId)
                if (campaignLead && campaignLead.campaign) {
                    cb.campaign = JSON.parse(JSON.stringify(campaignLead.campaign))
                    delete cb.campaign.campaignstages
                }
            })
            lead.leadupdates = results[6]
            lead.campaignleadupdates = results[7]
            lead.campaignleadupdates.forEach(clu => {
                var campaignLead = campaignLeads.find(cl => cl.campaignId === clu.campaignId)
                if (campaignLead && campaignLead.campaign) {
                    clu.campaign = JSON.parse(JSON.stringify(campaignLead.campaign))
                }
            })

            lead.suppressions = results[8]
            lead.suppressions.forEach(sup => {
                var campaignLead = campaignLeads.find(cl => cl.campaignId === sup.campaignId)
                if (campaignLead && campaignLead.campaign) {
                    sup.campaign = JSON.parse(JSON.stringify(campaignLead.campaign))
                }
            })
            
            lead.campaignLeads = campaignLeads

            res.status(200).send(lead)
        }).catch(err => {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/leads/:id/callattemptanalysis', function (req, res) {
        Models.Lead.findById(req.params.id)
            .then(lead => {
                Models.CallAttempt.findAll({
                    where: {
                        leadId: req.params.id
                    },
                    include: [{
                        model: Models.Campaign,
                        include: Models.CampaignType
                    }, {
                        model: Models.CampaignStage,
                        include: Models.Agent
                    }]
                }).then(callattempts => {
                    if (!callattempts || !callattempts.length) {
                        //just the campaign leads object for them to show they are not in a stage or dont have any call attempts left
                        Models.Campaign.findAll({
                            include: [{
                                model: Models.Lead,
                                where: {
                                    id: req.params.id
                                },
                                required: true
                            }]
                        }).then(campaigns => {
                            var promises = []
                            campaigns = JSON.parse(JSON.stringify(campaigns))
                            campaigns.forEach(campaign => {
                                if (campaign.leads && campaign.leads.length) {
                                    if (campaign.leads[0].campaignleads.currentCampaignStageId) {
                                        promises.push(Models.CampaignStage.findById(campaign.leads[0].campaignleads.currentCampaignStageId))
                                    } else {
                                        promises.push(Promise.resolve(null))
                                    }
                                } else {
                                    promises.push(Promise.resolve(null))
                                }
                            })
                            Promise.all(promises)
                                .then(stages => {
                                    stages.forEach((stage, i) => {
                                        campaigns[i].stages = stage ? [stage] : []
                                    })
                                    res.status(200).send({
                                        campaigns: campaigns
                                    })
                                })
                                .catch(err => {
                                    res.status(500).send({
                                        error: err.message
                                    })
                                })
                        })
                            .catch(err => {
                                res.status(500).send({
                                    error: err.message
                                })
                            })
                    } else {
                        //get the distinct dtrules and find the corresponding rule
                        var createdFromIds = []
                        callattempts.forEach(ca => {
                            if (createdFromIds.findIndex(id => ca.createdFromDTUuid == id) == -1) {
                                createdFromIds.push(ca.createdFromDTUuid)
                            }
                        })
                        var promises = []
                        createdFromIds.forEach(id => {
                            promises.push(Models.CampaignStageDateTimeRule.findOne({
                                where: {
                                    uuid: id
                                },
                                include: [Models.DateTimeRuleSet]
                            }))
                        })
                        Promise.all(promises)
                            .then(rules => {
                                res.status(200).send({
                                    callattempts: callattempts,
                                    rules: rules.filter(rule => !!rule)
                                })
                            })
                            .catch(err => {
                                res.status(500).send({
                                    error: err.message
                                })
                            })
                    }

                }).catch(err => {
                    return res.status(500).send({
                        error: err.message
                    })
                })
            })
            .catch(err => {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get call history by lead id
    app.get(BASE_URL + '/leads/:id/callhistory', function (req, res) {
        Models.CallResult.findAll({
            where: {
                leadId: req.params.id,
                completed: true
            },
            include: [{
                model: Models.Agent,
                include: [Models.Device]
            },
            Models.CallRecord,
            Models.Campaign,
            Models.CampaignStage,
            Models.Client,
            Models.RecurringPayment,
            Models.Sale,
            {
                model: Models.PaymentLog,
                attributes: ['id', 'amount', 'paymentDate', 'isPaid', 'actualPaymentDate', 'status', 'error', 'deleted', 'disabled']
            }],
            order: [
                ['createdAt', 'DESC']
            ]
        })
            .then(function (results) {
                res.status(200).send(results.map(result => {
                    result = result.toJSON()
                    result.installmentHistory = ''
                    result.installmentError = ''
                    if (result.paymentlogs && result.paymentlogs.length && result.paymentlogs.length > 1) {
                        result.installmentHistory = result.paymentlogs.filter(pl => !pl.deleted && !pl.disabled && pl.status !== 'error').length
                        result.paymentlogs.reverse().forEach(row => {
                            if (result.installmentError) return
                            if (row.deleted) return
                            if (row.error) result.installmentError = row.error
                        })
                    }
                    delete result.paymentlogs
                    return result
                }))
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get audit history by lead id
    app.get(BASE_URL + '/leads/:id/audithistory', function (req, res) {
        Models.LeadAudit.findAll({
            where: {
                leadId: req.params.id
            },
            include: [Models.User],
            order: ['createdAt']
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get lead calls by lead id
    app.get(BASE_URL + '/leads/:id/callattempts', function (req, res) {
        if (req.query && req.query.countOnly) {
            Models.CallAttempt.count({
                where: {
                    leadId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send({
                        count: result
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.CallAttempt.findAll({
                include: [Models.Lead],
                where: {
                    leadId: req.params.id
                }
            })
                .then(function (result) {
                    res.status(200).send(result)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    app.get(BASE_URL + '/leads/:id/callbacks', function (req, res) {
        Models.Callback.findAll({
            where: {
                leadId: req.params.id
            }
        }).then(callbacks => {
            res.status(200).send(callbacks)
        }).catch(err => {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/leads/:id/campaign/:campaignId', function (req, res) {
        Models.Lead.findOne({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.Campaign,
                where: {
                    id: req.params.campaignId
                }
            }]
        }).then(lead => {
            res.send(lead)
        }).catch(err => {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // Delete lead calls by lead id
    app.delete(BASE_URL + '/leads/:id/callattempts', function (req, res) {
        Models.CallAttempt.destroy({
            where: {
                leadId: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    rowsAffected: result
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Create lead
    app.post(BASE_URL + '/leads', function (req, res) {
        Models.Lead.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/leads/:id/cardtoken', (req, res) => {
        var error = ''
        if (!req.body.tsepToken) return res.status(500).send({
            error: 'tsepToken required'
        })

        var token = req.body
        token.leadId = req.params.id

        // delete previous tokens just in case
        Models.CardToken.destroy({
            where: {
                leadId: req.params.id
            }
        }).then(() => {
            return Models.CardToken.create(token)
        }).then(result => {
            res.status(200).send({
                Success: true,
                token: result
            })
        }).catch(function (err) {
            return res.status(200).send({
                Success: false,
                error: err ? err.message || err : 'Unknown Error'
            })
        })
    })

    app.delete(BASE_URL + '/leads/:id/cardtoken', (req, res) => {
        Models.CardToken.destroy({
            where: {
                leadId: req.params.id
            }
        }).then(() => {
            res.status(200).send({
                success: true
            })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    // Update lead
    app.put(BASE_URL + '/leads/:id', function (req, res) {
        Models.Lead.findById(req.params.id)
            .then(function (result) {

                //check for changes to create an audit event
                var auditPromises = []
                for (var i = 0; i < APP_SETTINGS.LEAD_AUDIT_FIELDS.length; i++) {
                    var field = APP_SETTINGS.LEAD_AUDIT_FIELDS[i].name;
                    if (result[field] != req.body[field] && req.body.hasOwnProperty(field)) {
                        var audit = {}
                        audit.field = field
                        audit.previousValue = result[field]
                        audit.newValue = req.body[field]
                        audit.leadId = req.params.id
                        audit.userId = req.user.id

                        auditPromises.push(Models.LeadAudit.create(audit))
                    }
                }

                return Promise.all(auditPromises).then(audits => {
                    try {
                        if (req.body.customFields && typeof req.body.customFields === 'object') req.body.customFields = JSON.stringify(req.body.customFields)
                    } catch (e) {
                        console.log(e)
                        delete req.body.customFields
                    }
                    return result.updateAttributes(req.body).then(function (result) {
                        if (!result.phone_home && !result.phone_mobile && !result.phone_work && !result.phone_workmobile) {
                            //no valid phone numbers so delete all call attempts
                            Models.CallAttempt.destroy({
                                where: {
                                    leadId: result.id
                                }
                            })
                        }
                        result.dataValues.audits = audits
                        res.status(200).send(result)
                    })
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Delete lead
    app.delete(BASE_URL + '/leads/:id', function (req, res) {
        Models.Lead.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })
}