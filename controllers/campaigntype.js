module.exports = function (app, Models, BASE_URL) {
	// Get campaign type list
	app.get(BASE_URL + '/campaigntypes', function (req, res) {
		Models.CampaignType.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get campaign type by id
	app.get(BASE_URL + '/campaigntypes/:id', function (req, res) {
		Models.CampaignType.find({ include: [ Models.Skill ], where: { id: req.params.id } })
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get campaign type skills by campaign type id
	app.get(BASE_URL + '/campaigntypes/:id/skills', function (req, res) {
		Models.CampaignType.findById(req.params.id)
			.then(function (campaignType) {
				if (req.query && req.query.countOnly) {
					campaignType.getSkills()
						.then(function (result) {
							res.status(200).send({ count: result.length })
						})
						.catch(function (err) {
							return res.status(500).send({ error: err.message })
						})
				}
				else {
					campaignType.getSkills()
						.then(function (result) {
							res.status(200).send(result)
						})
						.catch(function (err) {
							return res.status(500).send({ error: err.message })
						})
				}
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create campaign types
	app.post(BASE_URL + '/campaigntypes', function (req, res) {
		Models.CampaignType.create(req.body)
			.then(function (result) {
				var created = result;
				if (req.body.skills && req.body.skills.length > 0) {
					result.setSkills(req.body.skills.map(function (s) {
							return s.id
						}))
						.then(function (result) {
							created.getSkills()
								.then(function (result) {
									created.dataValues.skills = result;
									res.status(201).send(created)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						})
						.catch(function (err) {
							return res.status(500).send({ error: err.message })
						})
				}
				else
					res.status(201).send(created)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update campaign types
	app.put(BASE_URL + '/campaigntypes/:id', function (req, res) {
		Models.CampaignType.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						if (req.body.skills && req.body.skills.length > 0) {
							result.setSkills(req.body.skills.map(function (s) {
								return s.id
							})).then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
						else {
							result.setSkills([])
								.then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete campaign type
	app.delete(BASE_URL + '/campaigntypes/:id', function (req, res) {
		Models.CampaignType.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}