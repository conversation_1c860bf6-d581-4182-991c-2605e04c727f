module.exports = function (app, Models, BASE_URL) {
    app.get(BASE_URL + '/recurringpayment', function (req, res) {
        var limit = parseInt(req.query.limit) || 100
        var offset = parseInt(req.query.offset) || 0
        
        var where = {}
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }

        Models.RecurringPayment.findAll({
            limit,
            offset,
            where,
            include: [Models.PaymentLog, Models.Campaign, {
                model: Models.Lead,
                include: [Models.CardToken]
            }]
        }).then(function (result) {
            res.status(200).send(result.map(row => {
                var obj = row.toJSON()
                if (obj.data && typeof obj.data === 'string') {
                    try {
                        obj.data = JSON.parse(obj.data)
                    } catch (e) { }
                }
                return obj
            }))
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/recurringpayment/:id', function (req, res) {
        Models.RecurringPayment.findById(req.params.id, {
            include: [Models.PaymentLog, Models.Campaign, Models.Invoice, {
                model: Models.Lead,
                include: [Models.CardToken]
            }]
        }).then(function (result) {
            var obj = result.toJSON()
            if (obj.data && typeof obj.data === 'string') {
                try {
                    obj.data = JSON.parse(obj.data)
                } catch (e) { }
            }
            res.status(200).send(obj)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.post(BASE_URL + '/recurringpayment', function (req, res) {
        if (req.body.data && typeof req.body.data === 'object') req.body.data = JSON.stringify(req.body.data)
        Models.RecurringPayment.create(req.body).then(function (result) {
            res.status(200).send({
                Success: true,
                result
            })
        }).catch(function (err) {
            console.log(err)
            return res.status(200).send({
                Success: false,
                error: err ? err.message || err : 'Unknown Error'
            })
        })
    })

    app.put(BASE_URL + '/recurringpayment/:id', function (req, res) {
        if (req.body.data && typeof req.body.data === 'object') req.body.data = JSON.stringify(req.body.data)
        Models.RecurringPayment.findById(req.params.id)
            .then(function (recurring) {
                if (req.body.hasOwnProperty('amount') && recurring.amount !== req.body.amount) {
                    Models.InvoiceEvent.create({
                        invoiceId: recurring.invoiceId,
                        userId: req.user.id,
                        changeType: 'Recurring Amount',
                        field: 'amount',
                        fromValue: recurring.amount,
                        toValue: req.body.amount
                    })
                }
                if (req.body.hasOwnProperty('unit') && recurring.unit !== req.body.unit) {
                    Models.InvoiceEvent.create({
                        invoiceId: recurring.invoiceId,
                        userId: req.user.id,
                        changeType: 'Recurring Occurrence',
                        field: 'unit',
                        fromValue: recurring.unit,
                        toValue: req.body.unit
                    })
                }
                return recurring.updateAttributes(req.body)
            })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                console.log(err)
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Delete agentstate
    app.delete(BASE_URL + '/recurringpayment/:id', function (req, res) {
        Models.RecurringPayment.destroy({
            where: {
                id: req.params.id
            }
        }).then(function (result) {
            res.status(200).send({
                success: true
            })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })
}