module.exports = function (app, Models, BASE_URL) {
	// Get callresultfields list
	app.get(BASE_URL + '/callresultfields', function (req, res) {
		Models.CallResultField.findAll({ include: [ Models.CallResultFieldGroup, Models.CallResultFieldType, Models.CallResultFieldOption ] })
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get callresultfield by id
	app.get(BASE_URL + '/callresultfields/:id', function (req, res) {
		Models.CallResultField.find({ include: [ Models.CallResultFieldGroup, Models.CallResultFieldType, Models.CallResultFieldOption ], where: { id: req.params.id } })
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create callresultfield
	app.post(BASE_URL + '/callresultfields', function (req, res) {
		Models.CallResultField.create(req.body)
			.then(function (result) {
				var created = result
				if (req.body.callresultfieldoptions && req.body.callresultfieldoptions.length > 0) {
					result.setCallresultfieldoptions(req.body.callresultfieldoptions.map(function (a) {
							return a.id
						}))
						.then(function (result) {
							created.getCallresultfieldoptions()
								.then(function (result) {
									created.dataValues.callresultfieldoptions = result

									res.status(201).send(created)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						})
						.catch(function (err) {
							return res.status(500).send({ error: err.message })
						})
				}
				else
					res.status(201).send(created)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update callresultfield
	app.put(BASE_URL + '/callresultfields/:id', function (req, res) {
		Models.CallResultField.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						if (req.body.callresultfieldoptions && req.body.callresultfieldoptions.length > 0) {
							result.setCallresultfieldoptions(req.body.callresultfieldoptions.map(function (c) {
								return c.id
							})).then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
						else {
							result.setCallresultfieldoptions([])
								.then(function () {
									res.status(200).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({ error: err.message })
								})
						}
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete callresultfield
	app.delete(BASE_URL + '/callresultfields/:id', function (req, res) {
		Models.CallResultField.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}