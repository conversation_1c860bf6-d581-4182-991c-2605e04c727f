var _ = require('underscore')

module.exports = function (app, Models, BASE_URL, db) {
    app.get(BASE_URL + '/reports', function (req, res) {
        if (req.query.reportType) {
            Models.Report.findAll()
                .then(function (reports) {
                    reports = JSON.parse(JSON.stringify(reports))
                    var promises = []
                    reports.forEach(report => {
                        promises.push(Models.ReportHistory.findAll({
                            where: {
                                reportId: report.id
                            },
                            order: 'id DESC',
                            limit: 1
                        }))
                    })
                    Promise.all(promises).then(histories => {
                        histories.forEach((history, index) => {
                            reports[index].reporthistories = history
                        })
                        res.status(200).send(reports)
                    })

                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        } else {
            Models.Report.findAll()
                .then(function (reports) {
                    reports = JSON.parse(JSON.stringify(reports))
                    var promises = []
                    reports.forEach(report => {
                        promises.push(Models.ReportHistory.findAll({
                            where: {
                                reportId: report.id
                            },
                            order: 'id DESC',
                            limit: 1
                        }))
                    })
                    Promise.all(promises).then(histories => {
                        histories.forEach((history, index) => {
                            reports[index].reporthistories = history
                        })
                        res.status(200).send(reports)
                    })
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        }
    })

    app.get(BASE_URL + '/reports/client/:id', function (req, res) {
        Models.Client.findById(req.params.id).then(function (client) {
            Models.Report.findAll({
                include: [{
                    model: Models.ReportHistory,
                    where: {
                        isClientReady: true,
                    },
                    required: true
                }]
            })
                .then(function (reports) {
                    var clientReports = []
                    reports.forEach(report => {
                        report.reporthistories.forEach(history => {
                            if (!_.findWhere(clientReports, {
                                name: report.name
                            })) {
                                if (history.filters) {
                                    var filters = JSON.parse(history.filters)
                                    if (filters instanceof Array) {
                                        filters.forEach(filter => {
                                            if (filter.field == 'Client' && filter.value == client.name) {
                                                clientReports.push(report)
                                            }
                                        })
                                    } else if (filters) {
                                        if (filters.Client && filters.Client == client.id) {
                                            clientReports.push(report)
                                        }
                                    }
                                }
                            }
                        })
                    })
                    clientReports.forEach(report => {
                        delete report.reporthistories
                    })
                    res.status(200).send(clientReports)
                })
                .catch(function (err) {
                    return res.status(500).send({
                        error: err.message
                    })
                })
        })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/reports/:id', function (req, res) {
        Models.Report.find({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.ReportHistory,
                include: [Models.ReportHistoryAudit]
            }]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/reports/:id/schedules', function (req, res) {
        var schedules
        Models.ReportSchedule.findAll({
            where: {
                reportId: req.params.id
            },
            raw: true
        }).then(function (results) {
            schedules = results
            var stagesIds = []
            results.forEach(r => {
                if (r.filters) {
                    r.filters = JSON.parse(r.filters)
                    if (r.filters.CampaignStage) {
                        stagesIds.push(r.filters.CampaignStage)
                        return Models.CampaignStage.findById(r.filters.CampaignStage).then(stage => {
                            r.filters.CampaignStage = stage ? stage.name : r.filters.CampaignStage
                            return r
                        })
                    }
                }
            })
            if (stagesIds.length) return Models.CampaignStage.findAll({
                where: {
                    id: {
                        $in: stagesIds
                    }
                }
            })
        }).then(stages => {
            if (stages && stages.length) {
                schedules.forEach(r => {
                    if (r.filters) {
                        if (r.filters.CampaignStage) {
                            var found = stages.find(s => s.id == r.filters.CampaignStage)
                            r.filters.CampaignStage = found ? found.name : r.filters.CampaignStage
                        }
                    }
                })
            }
            res.status(200).send(schedules)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/reports/:id/history', function (req, res) {
        Models.ReportHistory.findAll({
            where: {
                reportId: req.params.id
            },
            include: [Models.ReportHistoryAudit]
        })
            .then(function (result) {
                if (req.query && req.query.countOnly)
                    res.status(200).send({
                        count: result.length
                    })
                else
                    res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/reports/:id/history/client/:clientid', function (req, res) {
        Models.Client.findById(req.params.clientid)
            .then(function (client) {
                if (client) {
                    Models.ReportHistory.findAll({
                        where: {
                            reportId: req.params.id,
                            isClientReady: true
                        },
                        include: [Models.ReportHistoryAudit]
                    })
                        .then(function (result) {
                            var clientHistories = []
                            result.forEach(history => {
                                if (history.filters) {
                                    var filters = JSON.parse(history.filters)
                                    if (filters instanceof Array) {
                                        filters.forEach(filter => {
                                            if (filter.field == 'Client' && filter.value == client.name) {
                                                clientHistories.push(history)
                                            }
                                        })
                                    } else if (filters) {
                                        if (filters.Client && filters.Client == client.id) {
                                            clientHistories.push(history)
                                        }
                                    }
                                }
                            })
                            res.status(200).send(clientHistories)
                        })
                        .catch(function (err) {
                            return res.status(500).send({
                                error: err.message
                            })
                        })
                } else {
                    res.status(200).send([])
                }
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })


    app.post(BASE_URL + '/reports', function (req, res) {
        Models.Report.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/reports/:id', function (req, res) {
        Models.Report.findById(req.params.id)
            .then(function (result) {
                result.updateAttributes(req.body)
                    .then(function (result) {
                        res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
    })

    app.delete(BASE_URL + '/reports/:id', function (req, res) {
        Models.Report.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })
};