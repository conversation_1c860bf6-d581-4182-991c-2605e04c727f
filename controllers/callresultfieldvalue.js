module.exports = function (app, Models, BASE_URL) {
	// Get callresultfieldvalues list
	app.get(BASE_URL + '/callresultfieldvalues', function (req, res) {
		Models.CallResultFieldValue.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get callresultfieldvalue by id
	app.get(BASE_URL + '/callresultfieldvalues/:id', function (req, res) {
		Models.CallResultFieldValue.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create callresultfieldvalue
	app.post(BASE_URL + '/callresultfieldvalues', function (req, res) {
		Models.CallResultFieldValue.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update callresultfieldvalue
	app.put(BASE_URL + '/callresultfieldvalues/:id', function (req, res) {
		Models.CallResultFieldValue.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete callresultfieldvalue
	app.delete(BASE_URL + '/callresultfieldvalues/:id', function (req, res) {
		Models.CallResultFieldValue.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}