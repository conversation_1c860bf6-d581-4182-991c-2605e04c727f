var pdf = require('../reporting/reportToPDF')()
var moment = require('moment')
var _ = require('underscore')
var APP_CONFIG = require('../config/constants')
var fs = require('fs')
var leadUtils = require('../utils/lead')
var transitionLead = require('../data-merge/transitionLead')
var Promise = require('sequelize').Promise
var writeOff = require('../invoices/utils/writeOff')

module.exports = function (app, Models, BASE_URL) {
    var invoiceGenerator = require('../invoices/invoiceGenerator')(Models)

    app.get(BASE_URL + '/invoices', function (req, res) {
        var where = {}
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }
        Models.Invoice.findAll({
            where,
            include: [Models.CallResult, Models.Lead, Models.Campaign, Models.Client]
        }).then(function (items) {
            res.status(200).send(items)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/invoices/paper', (req, res) => {
        fs.readdir(APP_CONFIG.PUBLIC_LOCATION + '/bulkinvoices', (err, files) => {
            console.log(files)
            res.send(files)
        })
    })

    app.get(BASE_URL + '/invoices/:id', function (req, res) {
        Models.Invoice.findOne({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.CallResult,
                include: [Models.Agent]
            }, Models.Lead, Models.Campaign, Models.RecurringPayment]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/invoices/getbycallresultid/:callresultId', function (req, res) {
        Models.Invoice.findOne({
            where: {
                callresultId: req.params.callresultId
            }
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/invoices/page/:page', function (req, res) {
        req.query.filters = JSON.parse(req.query.filters)
        var lead = req.query.filters.lead || {}
        var client = req.query.filters.client || {}
        var campaign = req.query.filters.campaign || {}

        var invoice = _.clone(req.query.filters)
        delete invoice.callresult
        delete invoice.lead
        delete invoice.client
        delete invoice.campaign

        if (req.user.isClientAgent || req.user.isClientAdmin) {
            invoice.clientId = req.user.clientId
        }
        var count, rows, campaignStages = []

        var start = Date.now()

        Models.Invoice.findAndCount({
            include: [{
                model: Models.Lead,
                where: lead,
                attributes: ['id', 'first_name', 'last_name', 'clientRef', 'clientId']
            }, {
                model: Models.Campaign,
                where: campaign,
                attributes: ['id', 'name', 'clientId']
            }, {
                model: Models.Client,
                where: client,
                attributes: ['id', 'name']
            }],
            limit: 20,
            offset: 20 * req.params.page,
            order: req.query.orderby + ' ' + req.query.dir,
            where: invoice
        }).then(result => {
            console.log('Getting invoices took ' + (Date.now() - start) + ' ms')

            count = result.count
            rows = result.rows
            
            if (!rows || !rows.length) return Promise.reject('No results found.')
        })
        .then(_ => {
            return Models.CampaignStage.findAll({
                where: {
                    campaignId: {
                        $in: rows.map(r => r.campaign.id)
                    }
                }    
            })
        })
        .then(_stages => {
            campaignStages = _stages

            return Models.CampaignLead.findAll({
                where: {
                    leadId: {
                        $in: rows.map(r => r.lead.id)
                    }
                }
            })
        })
        .then(campaignLeads => {
            // console.log(campaignStages, campaignLeads)

            var invoices = []
            rows.forEach(row => {
                var invoice = row.toJSON()

                var campaignLead = campaignLeads.find(cl => {
                    // console.log(`Comparing campaign lead ID ${cl.toJSON().leadId} to ${invoice.lead.id}`)
                    return cl.toJSON().leadId === invoice.lead.id && cl.campaignId === invoice.campaign.id
                })
                
                if (campaignLead)
                    invoice.campaignstage = campaignStages.find(cs => {
                        var stage = cs.toJSON()
                        // console.log(`Comparing stage ID ${stage.id} to ${campaignLead.currentCampaignStageId}`)
                        return stage.id === campaignLead.currentCampaignStageId
                    })

                invoices.push(invoice)
            })
            res.status(200).send({
                data: {
                    invoices,
                    count
                }
            })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/invoices/:id/history', function (req, res) {
        Models.InvoiceHistory.findAll({
            where: {
                invoiceId: req.params.id
            },
            include: [{
                model: Models.Invoice,
                include: [Models.Lead, Models.Client]
            }]
        })
            .then(function (results) {
                res.status(200).send(results)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/invoices/:id/events', function (req, res) {
        Models.InvoiceEvent.findAll({
            where: {
                invoiceId: req.params.id
            },
            include: [Models.User]
        }).then(function (results) {
            res.status(200).send(results)
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.get(BASE_URL + '/invoices/:id/payments', (req, res) => {
        Models.PaymentLog.findAll({
            where: {
                invoiceId: req.params.id
            },
            include: [Models.Invoice, Models.Lead, Models.Client, Models.User]
        }).then(results => {
            res.status(200).send(results)
        })
    })


    app.get(BASE_URL + '/invoices/:id/notes', (req, res) => {
        Models.InvoiceNote.findAll({
            where: {
                invoiceId: req.params.id
            },
            include: [Models.User]
        }).then(results => {
            res.status(200).send(results)
        })
    })

    app.post(BASE_URL + '/invoices', function (req, res) {
        Models.Invoice.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/invoices/:id/writeoff', function (req, res) {
        Models.Invoice.findById(req.params.id).then(invoice => {
            writeOff(Models, invoice, req.user.id, req.body ? req.body.amount : null)
            .then(invoice => res.send(invoice))
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
        })
        .catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
})

    app.post(BASE_URL + '/invoices/:id/audit', function (req, res) {
        Models.InvoiceEvent.create(req.body).then(event => {
            res.send(event)
        })
    })

    app.post(BASE_URL + '/invoices/:id/notes', function (req, res) {
        Models.InvoiceNote.create(req.body).then(event => {
            res.send(event)
        })
    })

    app.post(BASE_URL + '/invoices/:id/generatetest', function (req, res) {
        Models.Invoice.findOne({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.Lead
            }, {
                model: Models.Client
            }, {
                model: Models.Campaign
            }, {
                model: Models.CallResult,
                include: [Models.CampaignStage]
            }]
        })
            .then(function (invoice) {
                invoiceGenerator.generatePdfsForInvoices([invoice], false, true)
                    .then(function (results) {
                        if (results && results.length) {
                            res.status(201).send(results[0])
                        } else {
                            return res.status(500).send({
                                error: 'Error Creating Pdf'
                            })
                        }
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.post(BASE_URL + '/invoices/:id/generate', function (req, res) {
        Models.Invoice.findOne({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.Lead
            }, {
                model: Models.Client
            }, {
                model: Models.Campaign
            }, {
                model: Models.CallResult,
                include: [Models.CampaignStage]
            }]
        })
            .then(function (invoice) {
                invoiceGenerator.generatePdfsForInvoices([invoice])
                    .then(function (results) {
                        if (results && results.length) {
                            res.status(201).send(results[0])
                        } else {
                            return res.status(500).send({
                                error: 'Error Creating Pdf'
                            })
                        }
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/invoices/:id', function (req, res) {
        var invoice, destination
        Models.Invoice.findById(req.params.id)
            .then(function (result) {
                invoice = result
                // if (req.body.hasOwnProperty('writtenOff') && invoice.writtenOff != req.body.writtenOff) {
                //     leadUtils.resetDontContactUntil(Models, invoice.leadId).catch(() => { })
                //     Models.InvoiceEvent.create({
                //         invoiceId: invoice.id,
                //         userId: req.user.id,
                //         changeType: 'Write Off',
                //         field: 'writtenOff',
                //         fromValue: invoice.writtenOff || false,
                //         toValue: req.body.writtenOff || false
                //     })
                // }

                // if (req.body.hasOwnProperty('amountRemaining') && invoice.amountRemaining != req.body.amountRemaining) {
                //     if (req.body.partialWriteOff) {
                //         Models.InvoiceEvent.create({
                //             invoiceId: invoice.id,
                //             userId: req.user.id,
                //             changeType: 'Partial Write Off',
                //             field: 'amountRemaining',
                //             fromValue: invoice.amountRemaining,
                //             toValue: req.body.amountRemaining
                //         })
                //     } else {
                //         Models.InvoiceEvent.create({
                //             invoiceId: invoice.id,
                //             userId: req.user.id,
                //             changeType: 'Amount Remaining',
                //             field: 'amountRemaining',
                //             fromValue: invoice.amountRemaining,
                //             toValue: req.body.amountRemaining
                //         })
                //     }
                // }

                if (req.body.hasOwnProperty('grandTotal') && invoice.grandTotal !== req.body.grandTotal) {
                    Models.InvoiceEvent.create({
                        invoiceId: invoice.id,
                        userId: req.user.id,
                        changeType: 'Total',
                        field: 'grandTotal',
                        fromValue: invoice.grandTotal,
                        toValue: req.body.grandTotal
                    })
                }

                if (req.body.hasOwnProperty('notes') && invoice.notes !== req.body.notes) {
                    Models.InvoiceEvent.create({
                        invoiceId: invoice.id,
                        userId: req.user.id,
                        changeType: 'Notes',
                        field: 'notes',
                        fromValue: invoice.notes,
                        toValue: req.body.notes
                    })
                }

                if (req.body.hasOwnProperty('amountRemaining') && invoice.amountRemaining && !req.body.amountRemaining && invoice.campaignId && !req.body.writtenOff) {
                    // find the destination stage based on the collections wrapups
                    return Models.CampaignStageDisposition.findOne({
                        include: [{
                            model: Models.CampaignStage,
                            attributes: ['id', 'name'],
                            where: {
                                campaignId: invoice.campaignId,
                                name: 'Collections'
                            },
                            required: true
                        }, {
                            model: Models.Disposition,
                            attributes: ['id', 'name'],
                            where: {
                                name: {
                                    $like: 'Invoice Payment%'
                                }
                            },
                            required: true
                        }]
                    }).then(campaignStageDisposition => {
                        if (campaignStageDisposition) {
                            if (campaignStageDisposition.transitionToCampaignStageId) {
                                if (campaignStageDisposition.transitionCutOffDate && moment() > moment(campaignStageDisposition.transitionCutOffDate)) {
                                    destination = campaignStageDisposition.transitionCutOffDateDispositionId
                                } else {
                                    destination = campaignStageDisposition.transitionToCampaignStageId
                                }
                            } else {
                                destination = null
                            }
                            return transitionLead(Models, invoice.leadId, destination, invoice.campaignId)
                        }
                    })
                }
            }).then(() => {
                return invoice.updateAttributes(req.body)
            })
            .then(() => {
                if (destination) {
                    return Models.CampaignStage.findById(destination, {
                        attributes: ['id', 'name']
                    })
                }
            }).then(stage => {
                var result = JSON.parse(JSON.stringify(invoice))
                if (stage) result.destination = stage.name
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.put(BASE_URL + '/invoices/:id/notes/:noteId', function (req, res) {
        Models.InvoiceNote.findOne({
            where: {
                invoiceId: req.params.id,
                id: req.params.noteId
            }
        }).then(note => {
            if (note) {
                return note.update({
                    notes: req.body.notes
                })
            }
        }).then(note => {
            res.send(note)
        }).catch(err => {
            return res.status(500).send({
                error: err.message
            })
        })
    })

    app.delete(BASE_URL + '/invoices/:id', function (req, res) {
        Models.Invoice.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.delete(BASE_URL + '/invoices/:id/payments', function (req, res) {
        var total = 0
        Models.PaymentLog.findAll({
            where: {
                invoiceId: req.params.id,
                status: 'pending',
                deleted: false,
                disabled: false
            }
        }).then(payments => {
            payments.forEach(p => total += p.amount)
            return Models.PaymentLog.update({
                disabled: true
            }, {
                where: {
                    id: {
                        $in: payments.map(p => p.id)
                    }
                }
            })
        }).then(() => {
            return Models.Invoice.findById(req.params.id).then(invoice => {
                return invoice.updateAttributes({
                    amountRemaining: total
                }).then(() => leadUtils.resetDontContactUntil(invoice.leadId))
            })
        }).then(() => {
            res.status(200).send({
                success: true,
                amountRemaining: total
            })
        }).catch(function (err) {
            return res.status(500).send({
                error: err.message
            })
        })
    })
}