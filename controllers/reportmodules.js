var _ = require('underscore')
var MODULES = ['Client'
	, 'Campaign'
	, 'CampaignStage'
	, 'Lead'
	, 'Skill'
	, 'SubSkill'
	, 'Agent'
	, 'CallAttempt'
	, 'CallResult'
	, 'CallRecord'
	, 'Callback'
	, 'CampaignLead'
	, 'CampaignStageAgent'
	, 'CampaignProduct'
	, 'Sale'
	, 'Invoice'
	, 'PaymentLog'
	, 'Payroll'
	, 'Suppression']

module.exports = function (app, Models, BASE_URL) {
	app.get(BASE_URL + '/reportmodules', function (req, res) {
		var result = []
		for (var prop in Models) {
			if (MODULES.indexOf(prop) > -1) {

				var object = Models[prop]
				var item = {}
				item.name = prop
				item.attributes = getAttributes(object)
				item.associations = getAssociates(object, Models)
				result.push(item)
			}
		}

		res.status(200).send(result)
	})
}

function getAttributes(obj) {
	var keys = Object.keys(obj.attributes)
	var results = []
	for (var i = 0; i < keys.length; i++) {


		var item = {
			name: keys[i]
		}
		item.type = obj.attributes[keys[i]].type.key
		results.push(item)

	}
	return results
}

function getAssociates(obj, Models) {
	var results = []
	var keys = Object.keys(obj.associations)
	var modelNames = Object.keys(Models)
	for (var i = 0; i < keys.length; i++) {
		var name = '';
		var ass = obj.associations[keys[i]]
		if (ass.isSingleAssociation) {
			name = ass.as
		} else if (ass.isMultiAssociation) {
			name = ass.target.name
		}
		for (var j = 0; j < modelNames.length; j++) {
			var lowerName = modelNames[j].toLowerCase()
			if (lowerName == name && MODULES.indexOf(modelNames[j]) > -1) {
				results.push(modelNames[j])
			}
		}
	}
	return results
}