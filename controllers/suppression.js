var path = require('path')
var thread = require('../thread-scripts/threadWrapper')
var transitionLead = require('../data-merge/transitionLead')
var ensureDirectoryExists = require('../utils/fileUtils').ensureDirectoryExists
var copyFile = require('../utils/fileUtils').copyFile
var moment = require('moment')

module.exports = function (app, Models, BASE_URL) {
    app.get(BASE_URL + '/suppressed', (req, res) => {
        var where = {
            finished: !!req.query.finished
        }
        if (req.query.campaignId) where.campaignId = req.query.campaignId
        if (req.query.campaignstageId) where.campaignstageId = req.query.campaignstageId
        if (req.query.leadId) where.leadId = req.query.leadId
        if (req.query.skipped) {
			where.skipped = true
			delete where.finished
		}
        var offset = parseInt(req.query.offset) || 0
        var limit = parseInt(req.query.limit) || 50
        var orderBy = req.query.orderBy || (req.query.finished ? 'actualEndDate' : 'startDate')
        var orderDir = req.query.orderDir || 'desc'

        var subWhere
        if (req.query.clientRef) {
            subWhere = { clientRef: req.query.clientRef }
        }

        Models.Suppression.findAndCountAll({
            where,
            include: [{
                model: Models.Lead,
                where: subWhere,
                attributes: ['id', 'first_name', 'last_name', 'clientRef'],
                required: !!subWhere
            }, {
                model: Models.CampaignStage,
                attributes: ['id', 'name']
            }, {
                model: Models.Campaign,
                attributes: ['id', 'name']
            }],
            limit,
            offset,
            order: [[orderBy, orderDir]]
        }).then(results => {
            res.send(results)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.get(BASE_URL + '/suppressed/history', (req, res) => {
        var offset = parseInt(req.query.offset) || 0
        var limit = parseInt(req.query.limit) || 50
        var orderBy = req.query.orderBy || 'createdAt'
        var orderDir = req.query.orderDir || 'desc'
        var where = {}
        if (req.query.campaignId) where.campaignId = req.query.campaignId
        Models.SuppressionHistory.findAndCountAll({
            where,
            limit,
            offset,
            order: [[orderBy, orderDir]],
        }).then(results => {
            res.send(results)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.get(BASE_URL + '/suppressed/history/:id/download', (req, res) => {
        Models.SuppressionHistory.findById(req.params.id).then(history => {
            if (!history) return res.status(400).send('Suppression History Not Found')
            res.download(history.file, history.filename)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.put(BASE_URL + '/suppressed/:id', (req, res) => {
        Models.Suppression.findById(req.params.id).then(suppression => {
            var updateObj = {}
            if (req.body.endDate !== undefined) updateObj.endDate = req.body.endDate
            if (!suppression.actualStartDate && req.body.startDate !== undefined) updateObj.startDate = req.body.startDate
            if (req.body.campaignstageId !== undefined) updateObj.campaignstageId = req.body.campaignstageId
            return suppression.update(updateObj)
        }).then(result => {
            res.send(result)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.put(BASE_URL + '/suppressed/:id/finish', (req, res) => {
        var suppression
        Models.Suppression.findById(req.params.id).then(_suppression => {
            suppression = _suppression
            if (!suppression) return Promise.reject('Suppression Not Found')
            if (suppression.finished) return Promise.reject('Suppression Already Finished')
            if (!suppression.leadId) return Promise.reject('Suppression Has No Lead')
            if (!suppression.campaignId) return Promise.reject('Suppression Has No Campaign')
            return transitionLead(Models, suppression.leadId, suppression.campaignstageId, suppression.campaignId)
        }).then(() => {
            return suppression.update({
                actualEndDate: new Date(),
                finished: true
            })
        }).then(result => {
            res.send(result)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.post(BASE_URL + '/suppressed', (req, res) => {
        if (!req.body.leadId) return res.status(400).send('leadId required')
        if (!req.body.campaignId) return res.status(400).send('campaignId required')

        if (!req.body.startDate) req.body.startDate = new Date()

        Models.Suppression.create(req.body).then(result => {
            res.send(result)
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })

    app.post(BASE_URL + '/suppressed/import', (req, res) => {
        if (!req.query.campaignId) return res.status(400).send('campaignId required')
        var filePath = req.files.file.path
        console.log(req.files.file)
        var filename = req.files.file.originalname
        var targetDir = path.resolve('./leadUploads/suppress_' + req.query.campaignId + '/')
        var destinationFilePath = path.join(targetDir, req.files.file.name)
        var campaign

        Models.Campaign.findById(req.query.campaignId, {
            attributes: ['id', 'name', 'clientId']
        }).then(_campaign => {
            campaign = _campaign
            if (!campaign || !campaign.clientId) return Promise.reject('campaign not found')
            return Models.SuppressionHistory.create({
                campaignId: campaign.id,
                filename,
                stage: req.query.campaignstageId ? parseInt(req.query.campaignstageId) : req.query.campaignstageId,
                updateOnly: !!req.query.updateOnly,
                file: destinationFilePath
            })
        }).then(history => {
            ensureDirectoryExists(targetDir, function (err) {
                copyFile(filePath, destinationFilePath, function (err) {
                    console.log(err)
                    var taskId = thread.start('importSuppression', {
                        campaignId: req.query.campaignId,
                        clientId: campaign.clientId,
                        historyId: history.id,
                        updateOnly: !!req.query.updateOnly,
                        campaignstageId: req.query.campaignstageId ? parseInt(req.query.campaignstageId) : req.query.campaignstageId,
                        startDate: req.query.startDate,
                        endDate: req.query.endDate,
                        filePath: filePath
                    })

                    res.send({
                        success: true,
                        taskId: taskId
                    })
                })
            })
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'Unknown Error')
        })

    })

    app.delete(BASE_URL + '/suppressed/:id', (req, res) => {
        Models.Suppression.findById(req.params.id).then(suppression => {
            if (!suppression) return Promise.reject('Suppression Not Found')
            if (suppression.actualStartDate) return Promise.reject('Suppression Already In Progress')
            return suppression.update({
                finished: true
            })
        }).then(() => {
            res.send({ success: true })
        }).catch(err => {
            console.log(err)
            res.status(500).send(err ? err.message || err : 'ERROR')
        })
    })
}