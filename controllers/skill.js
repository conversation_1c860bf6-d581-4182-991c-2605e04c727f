module.exports = function (app, Models, BASE_URL) {
	// Get skill list
	app.get(BASE_URL + '/skills', function (req, res) {
		Models.Skill.findAll({
				order: ['name']
			})
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get skill by id
	app.get(BASE_URL + '/skills/:id', function (req, res) {
		Models.Skill.findById(req.params.id)
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create skill
	app.post(BASE_URL + '/skills', function (req, res) {
		Models.Skill.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update skill
	app.put(BASE_URL + '/skills/:id', function (req, res) {
		Models.Skill.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete skill
	app.delete(BASE_URL + '/skills/:id', function (req, res) {
		Models.Skill.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}