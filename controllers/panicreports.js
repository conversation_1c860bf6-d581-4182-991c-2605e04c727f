module.exports = function(app, Models, BASE_URL) {
	// Get agentstate list
	app.get(BASE_URL + '/panicreports', function(req, res) {
		Models.PanicReports.findAll({
			limit: req.query.limit || 100,
			offset: req.query.offset || 0,
			order: req.query.order || 'createdAt DESC'
		})
		.then(function(items) {
			items.forEach(function(result) {
				if (result.dataValues.jsonData) {
					result.dataValues.jsonData = JSON.parse(result.dataValues.jsonData)
				}
				if (result.dataValues.errors) {
					result.dataValues.errors = JSON.parse(result.dataValues.errors)
				}
			})
			res.status(200).send(items)
		})
		.catch(function(err) {
			return res.status(500).send({
				error: err.message
			})
		})
	})

	// Get agentstate by id
	app.get(BASE_URL + '/panicreports/:id', function(req, res) {
		Models.PanicReports.findById(req.params.id)
			.then(function(result) {
				if (result.dataValues.jsonData) {
					result.dataValues.jsonData = JSON.parse(result.dataValues.jsonData)
				}
				if (result.dataValues.errors) {
					result.dataValues.errors = JSON.parse(result.dataValues.errors)
				}
				res.status(200).send(result)
			})
			.catch(function(err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Create agentstate
	app.post(BASE_URL + '/panicreports', function(req, res) {
		if (req.body.jsonData) {
			req.body.jsonData = JSON.stringify(req.body.jsonData)
		}
		if (req.body.errors) {
			req.body.errors = JSON.stringify(req.body.errors)
		}
		Models.PanicReports.create(req.body)
			.then(function(result) {
				res.status(201).send(result)
			})
			.catch(function(err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Update agentstate
	app.put(BASE_URL + '/panicreports/:id', function(req, res) {
		Models.PanicReports.findById(req.params.id)
			.then(function(result) {
				if (req.body.jsonData) {
					req.body.jsonData = JSON.stringify(req.body.jsonData)
				}
				if (req.body.errors) {
					req.body.errors = JSON.stringify(req.body.errors)
				}
				result.updateAttributes(req.body)
					.then(function(result) {
						res.status(200).send(result)
					})
					.catch(function(err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function(err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Delete agentstate
	app.delete(BASE_URL + '/panicreports/:id', function(req, res) {
		Models.PanicReports.destroy({
				where: {
					id: req.params.id
				}
			})
			.then(function(result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function(err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})
}