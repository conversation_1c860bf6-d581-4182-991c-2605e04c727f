module.exports = function (app, Models, BASE_URL) {
	// Get campaign training docs list
	app.get(BASE_URL + '/campaigntrainingdocs', function (req, res) {
		Models.CampaignTrainingDoc.findAll()
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Get campaign training docs by id
	app.get(BASE_URL + '/campaigntrainingdocs/:id', function (req, res) {
		Models.CampaignTrainingDoc.findById(req.params.id)
			.then(function (result) {
				res.status(200).sendFile(result.path, {
					headers: {
						'content-disposition': 'attachment; filename=' + result.name
					}
				})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Create campaign training docs
	app.post(BASE_URL + '/campaigntrainingdocs', function (req, res) {
		Models.CampaignTrainingDoc.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Update campaign training docs
	app.put(BASE_URL + '/campaigntrainingdocs/:id', function (req, res) {
		Models.CampaignTrainingDoc.findById(req.params.id)
			.then(function (result) {
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({ error: err.message })
					})
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})

	// Delete campaign training docs
	app.delete(BASE_URL + '/campaigntrainingdocs/:id', function (req, res) {
		Models.CampaignTrainingDoc.destroy({
				where: { id: req.params.id }
			})
			.then(function (result) {
				res.status(200).send({ success: true })
			})
			.catch(function (err) {
				return res.status(500).send({ error: err.message })
			})
	})
}