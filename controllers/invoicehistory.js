var path = require('path')

module.exports = function (app, Models, BASE_URL) {
    var email = require('../emailing/email')(Models)

    app.get(BASE_URL + '/invoicehistory', function (req, res) {
        Models.InvoiceHistory.findAll()
            .then(function (items) {
                res.status(200).send(items)
            })
            .catch(function (err) {
                return res.status(500).send({ error: err.message })
            })
    })

    app.get(BASE_URL + '/invoicehistory/:id/pdf', function (req, res) {
        Models.InvoiceHistory.findById(req.params.id)
            .then(function (result) {
                var fileName = result.invoiceHtml + '.pdf'
                var filePath = path.resolve('./invoices/pdf/')
                var fileLocation = filePath + '/' + fileName
                res.status(200).sendFile(fileLocation, {
                    headers: {
                        'content-disposition': 'attachment; filename=' + fileName
                    }
                })
            })
            .catch(function (err) {
                return res.status(500).send({ error: err.message })
            })
    })

    app.post(BASE_URL + '/invoicehistory', function (req, res) {
        Models.InvoiceHistory.create(req.body)
            .then(function (result) {
                res.status(201).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({ error: err.message })
            })
    })

    app.put(BASE_URL + '/invoicehistory/:id/email', function (req, res) {
        Models.InvoiceHistory.findOne({
            where: {
                id: req.params.id
            },
            include: [{
                model: Models.Invoice,
                include: [Models.Lead, Models.Client]
            }]
        }).then(history => {
            if (history && history.invoice) {
                var invoice = history.invoice
                var filepath = path.resolve('./invoices/pdf/' + history.invoiceHtml + '.pdf')
                var attachments = [{
                    filename: history.invoiceHtml + '.pdf',
                    path: filepath
                }]
                var context = {
                    leadName: invoice.lead.first_name + ' ' + invoice.lead.last_name,
                    client: invoice.client.name,
                    amountRemaining: history.requestAmount
                }
                var emailContext = {
                    client: invoice.client.name
                }
                email.emailWithAttachments('invoiceAttached', invoice.lead.email, invoice.client.returnEmail, attachments, invoice.client.name + ' Pledge Reminder', emailContext)
                    .then(emailResult => {
                        res.status(200).send(emailResult)
                    })
                    .catch(err => {
                        return res.status(500).send({ error: err.message })
                    })
            } else {
                return res.status(500).send({ error: 'no invoice found' })
            }
        })
            .catch(err => {
                return res.status(500).send({ error: err.message })
            })
    })

    app.put(BASE_URL + '/invoicehistory/:id', function (req, res) {
        Models.InvoiceHistory.findById(req.params.id)
            .then(function (result) {
                result.updateAttributes(req.body)
                    .then(function (result) {
                        c
                    })
                    .catch(function (err) {
                        return res.status(500).send({ error: err.message })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({ error: err.message })
            })
    })

    app.delete(BASE_URL + '/invoicehistory/:id', function (req, res) {
        Models.InvoiceHistory.destroy({
            where: { id: req.params.id }
        })
            .then(function (result) {
                res.status(200).send({ success: true })
            })
            .catch(function (err) {
                return res.status(500).send({ error: err.message })
            })
    })
}