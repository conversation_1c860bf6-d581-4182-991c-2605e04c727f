var jwt = require('jwt-simple')
var Promise = require('sequelize').Promise
var crypt = require('../utils/encryption')()
var sessionAPI = require('../database/redisAPI')('agentsession')
var usersessionAPI = require('../database/redisAPI')('usersession')

module.exports = function (Models) {
	var auth = {
		login: function (req, res) {
			var username = req.body.username || ''
			username = username.toLowerCase()
			var password = req.body.password || ''

			if (username == '' || password == '') {
				res.status(401)
				res.json({
					"status": 401,
					"message": "Invalid credentials"
				})
				return
			}

			if (username === "dualtone_admin") {
				if (password === "Lavabug87") {
					var user = {
						id: 0,
						username: 'dualtone_admin',
						name: 'superadmin',
						homeState: 'admin.clients',
						showStats: true,
						isAdmin: true,
						isSupervisor: true,
						isAgent: false
					}
					usersessionAPI.save({
						id: 0,
						lastKeepAlivePing: Date.now()
					})
					res.json(genToken(user))
					return
				} else {
					res.json({
						"status": 401,
						"message": "Invalid credentials"
					})
					return
				}
			}

			// Fire a query to your DB and check if the credentials are valid
			auth.validate(username, password)
				.then(function (result) {
					if (!result) { // If authentication fails, we send a 401 back
						res.status(401)
						res.json({
							"status": 401,
							"message": "bad username or password"
						})
						return
					}
					var existingSession = false

					new Promise(function (resolve, reject) {
						if (result.isAgent && result.agentId) {
							//check the start and end dates of the agent in the DB
							if (result.agent.hireDate && result.agent.hireDate > new Date()) {
								return reject('agent has not started yet')
							}

							if (result.agent.termDate && result.agent.termDate < new Date()) {
								return reject('agent has been terminated')
							}
						}

						if (!result.isAdmin && !result.isSupervisor && !result.isAgent && !result.isClient && !result.isClientAdmin && !result.isClientAgent) {
							return reject('user disabled')
						}

						//check the user session does not already exist

						usersessionAPI.get(result.id)
							.then(function (session) {
								if (session) {
									existingSession = true
									return resolve(result)
									// reject("logged in elsewhere, please try again in 30 seconds")
								} else {
									resolve(result)
								}
							})
					}).then(function (user) {
						delete user.password
						res.json(genToken(result))
						usersessionAPI.save({
							id: user.id,
							agentId: user.agentId,
							lastKeepAlivePing: Date.now(),
							monitorTimeout: user.agentId && user.isAgent && !user.isAdmin && !user.isSupervisor
						})
						if (!existingSession && user.id) {
							Models.UserEvent.create({
								userId: user.id,
								agentId: user.agentId,
								eventType: 'Login',
								eventInfo: 'login'
							})
						}
						if (user.agentId) {
							var newEvent = {}
							newEvent.eventType = "Login"
							newEvent.eventName = "Logged In"
							newEvent.agentId = user.agentId
							Models.AgentEvent.create(newEvent);
						}
					}).catch(function (reason) {
						res.status(401)
						res.json({
							"status": 401,
							"message": reason
						})
						return
					})
				})
				.catch(function (err) {
					res.status(401)
					res.json({
						"status": 401,
						"message": "Invalid credentials"
					})
					return
				})
		},

		validate: function (_username, _password) {
			return new Promise(function (resolve, reject) {
				var pass = crypt.encrypt(_password)

				Models.User.find({
					where: {
						username: _username,
						password: pass
					},
					include: [{
						model: Models.Client
					}, {
						model: Models.Agent,
						include: [Models.Device]
					}]
				})
					.then(function (result) {
						resolve(result)
					})
					.catch(function (err) {
						reject(err)
					})
			})

		},

		validateUser: function (_username) {
			return new Promise(function (resolve, reject) {
				_username = _username.toLowerCase()
				if (_username === 'dualtone_admin') {
					var user = {
						id: 0,
						username: 'dualtone_admin',
						name: 'superadmin',
						homeState: 'admin.clients',
						showStats: true,
						isAdmin: true,
						isSupervisor: true,
						isAgent: false
					}
					return resolve(user)
				}

				Models.User.find({
					where: {
						username: _username
					},
					include: [Models.Client]
				})
					.then(function (result) {
						resolve(result)
					})
					.catch(function (err) {
						reject(err)
					})
			})
		},
	}

	function genToken(user) {
		var expires = expiresIn(7) // days
		var token = jwt.encode({
			exp: expires,
			username: user.username
		},
			require('../config/secret')()
		)

		return {
			token: token,
			expires: expires,
			user: user
		}
	}

	function expiresIn(numDays) {
		var dateObj = new Date()
		return dateObj.setDate(dateObj.getDate() + numDays)
	}

	return auth

}