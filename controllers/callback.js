var Sequelize = require('sequelize')
var Promise = Sequelize.Promise

module.exports = function (app, Models, BASE_URL) {
    // Get callback list
    app.get(BASE_URL + '/callbacks', function (req, res) {
        var where = {}
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }
        Models.Callback.findAll({
            where,
            include: [Models.Agent, Models.Lead]
        })
            .then(function (items) {
                res.status(200).send(items)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/callbacks/expired', function (req, res) {
        var where = {
            expired: true
        }

        //add paging
        if (req.user.isClientAgent || req.user.isClientAdmin) {
            where.clientId = req.user.clientId
        }

        Models.Callback.findAndCount({
            where,
            attributes: Object.keys(Models.Callback.attributes).concat([
                [
                    Sequelize.literal(`(
                        SELECT COUNT(*) 
                        FROM callresults 
                        WHERE callresults.leadId = callback.leadId
                          AND callresults.createdAt > callback.endDateTime 
                          AND callresults.campaignId = callback.campaignId
                    )`),
                    'calledSinceExpiry'
                ]
            ]),
            include: [
                Models.Agent,
                {
                    model: Models.Lead,
                    include: [Models.CampaignLead, Models.Suppression]
                },
                {
                    model: Models.Campaign,
                    include: [Models.CampaignStage]
                }
            ],
            offset: req.query.limit * req.query.page,
            limit: req.query.limit,
            order: req.query.orderby + ' ' + req.query.dir
        })
            .then(result => {
                res.status(200).send(result)
            })
            .catch(err => {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    app.get(BASE_URL + '/callbacks/page', function (req, res) {
        if (req.query.filters) {
            req.query.filters = JSON.parse(req.query.filters)
        } else {
            req.query.filters = {}
        }

        var lead = req.query.filters.lead || {}
        var callback = req.query.filters.callback || {}
        var agent = req.query.filters.agent || {}

        callback.deleted = {
            $or: {
                $ne: true,
                $eq: null
            }
        }

        if (req.user.isClientAgent || req.user.isClientAdmin) {
            callback.clientId = req.user.clientId
        }

        Models.Callback.findAndCount({
            where: callback,
            include: [{
                model: Models.Agent,
                where: agent,
                required: true
            }, {
                model: Models.Lead,
                where: lead,
                required: true
            }, Models.Campaign, Models.CallResult],
            limit: 20,
            offset: 20 * req.query.page,
            order: req.query.orderby + ' ' + req.query.dir,
        })
            .then(results => {
                res.status(200).send(results)
            })
            .catch(err => {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Get callback by id
    app.get(BASE_URL + '/callbacks/:id', function (req, res) {
        Models.Callback.find({
            where: {
                id: req.params.id
            },
            include: [Models.Agent, Models.Lead]
        })
            .then(function (result) {
                res.status(200).send(result)
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Create callback
    app.post(BASE_URL + '/callbacks', function (req, res) {
        // check for current callbacks for this lead already and set them to deleted
        if (req.body.leadId) {
            Models.Callback.findAll({
                where: {
                    leadId: req.body.leadId
                }
            }).then(function (callbacks) {
                var promises = []
                callbacks.forEach(function (callback) {
                    promises.push(callback.updateAttributes({
                        deleted: true,
                        expired: false
                    }))
                })
                return Promise.all(promises)
            }).then(function () {
                return Models.Callback.create(req.body)
            }).then(function (result) {
                res.status(201).send(result)
            }).catch(function (err) {
                res.status(500).send({
                    error: err ? err.message || err : 'Error Creating Callback'
                })
            })
        } else {
            return res.status(400).send({
                error: 'Invalid Lead'
            })
        }
    })

    // Update callback
    app.put(BASE_URL + '/callbacks/:id', function (req, res) {
        Models.Callback.findById(req.params.id)
            .then(function (result) {
                result.updateAttributes(req.body)
                    .then(function (result) {
                        res.status(200).send(result)
                    })
                    .catch(function (err) {
                        return res.status(500).send({
                            error: err.message
                        })
                    })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })

    // Delete callback
    app.delete(BASE_URL + '/callbacks/:id', function (req, res) {
        Models.Callback.destroy({
            where: {
                id: req.params.id
            }
        })
            .then(function (result) {
                res.status(200).send({
                    success: true
                })
            })
            .catch(function (err) {
                return res.status(500).send({
                    error: err.message
                })
            })
    })
}