var thread = require('../thread-scripts/threadWrapper')
var path = require('path')
var constants = require('../config/constants')
var fileUtils = require('../utils/fileUtils.js')
var ensureDirectoryExists = fileUtils.ensureDirectoryExists
var copyFile = fileUtils.copyFile
var crypt = require('../utils/encryption')()
var clientAttr = ['id', 'name',
	'address1',
	'address2',
	'city',
	'state',
	'zip',
	'primaryContactName',
	'primaryContactNumber',
	'secondaryContactName',
	'secondaryContactNumber',
	'email',
	'timezone',
	'additionalInfo',
	'defaultInvoiceType',
	'salesTax',
	'orderFee',
	'letterSalutation',
	'contactTitle',
	'website',
	'returnEmail',
	'invoiceMessage',
	'firstAppealInvoiceText',
	'secondAppealInvoiceText',
	'followUpInvoiceText',
	'UpafFirstAppealInvoiceText',
	'UpafSecondAppealInvoiceText',
	'UpafFollowUpInvoiceText',
	'signature',
	'allowExistingCC',
	'logo',
	'membershipCard',
	'showReportsAsViews',
	'reportPasswordRequired',
	'reportPasswordLastChange',
	'directPayments',
	'paperInvoices',
	'emailInvoices',
	'invoiceSubject']

module.exports = function (app, Models, BASE_URL) {
	var invoiceGenerator = require('../invoices/invoiceGenerator')(Models)

	// Get clients list
	app.get(BASE_URL + '/clients', function (req, res) {
		var where = {}
		if (req.user.isClientAgent || req.user.isClientAdmin) {
			where.id = req.user.clientId
		}
		Models.Client.findAll({
			where,
			include: [Models.Campaign],
			attributes: clientAttr
		})
			.then(function (items) {
				res.status(200).send(items)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get client by id
	app.get(BASE_URL + '/clients/:id', function (req, res) {
		Models.Client.findById(req.params.id, {
			include: [Models.Campaign],
			attributes: clientAttr
		})
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// Get client leads by client id
	app.get(BASE_URL + '/clients/:id/campaigns', function (req, res) {
		if (req.query && req.query.countOnly) {
			Models.Campaign.count({
				where: {
					clientId: req.params.id
				}
			})
				.then(function (result) {
					res.status(200).send({
						count: result
					})
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		} else {
			Models.Campaign.findAll({
				where: {
					clientId: req.params.id
				},
				include: [Models.CampaignStage]
			})
				.then(function (result) {
					res.status(200).send(result)
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		}
	})

	app.get(BASE_URL + '/clients/:id/demoinvoice', function (req, res) {
		Models.Client.findById(req.params.id, {
			attributes: clientAttr
		}).then(function (client) {
			var invoice = {
				client: JSON.parse(JSON.stringify(client)),
				lead: {
					first_name: 'David',
					last_name: 'Reynolds',
					address1: '123 Fake Street',
					address2: '',
					id: 123456,
					clientSourceCode: '105050',
					city: 'Indianapolis',
					state: 'IN',
					zip: '53551',
					clientRef: 1000
				},
				callresult: {
					payDate: new Date(),
					freeTickets: 0,
					pledgeDate: new Date(),
					campaignstage: {
						name: '1st Appeal'
					}
				},
				campaign: {
					name: 'Demo Campaign'
				},
				amountRemaining: 100,
				requestCount: 0
			}

			invoiceGenerator.generatePdfsForInvoices([invoice], true)
				.then(function (results) {
					if (results && results.length) {
						res.status(201).send(results[0])
					} else {
						return res.status(500).send({
							error: 'Error Creating Pdf'
						})
					}
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		}).catch(function (err) {
			return res.status(500).send({
				error: err.message
			})
		})
	})

	// Get client leads by client id
	app.get(BASE_URL + '/clients/:id/leads', function (req, res) {
		if (req.query && req.query.countOnly) {
			Models.Lead.count({
				where: {
					clientId: req.params.id
				}
			})
				.then(function (result) {
					res.status(200).send({
						count: result
					})
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		} else {
			Models.Lead.findAll({
				include: [{
					model: Models.Skill,
					as: 'tfSkill'
				}, {
					model: Models.SubSkill,
					as: 'tfSubSkill'
				}, {
					model: Models.Skill,
					as: 'tmSkill'
				}, {
					model: Models.SubSkill,
					as: 'tmSubSkill'
				}],
				where: {
					clientId: req.params.id
				}
			})
				.then(function (result) {
					res.status(200).send(result)
				})
				.catch(function (err) {
					return res.status(500).send({
						error: err.message
					})
				})
		}
	})

	// Create client
	app.post(BASE_URL + '/clients', function (req, res) {
		Models.Client.create(req.body)
			.then(function (result) {
				res.status(201).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.post(BASE_URL + '/clients/:id/uploadsignature', function (req, res) {
		var filePath = req.files.file.path

		var targetDir = path.resolve(constants.SIGNATURE_LOCATION)
		var destinationFilePath = path.join(targetDir, req.files.file.name)

		ensureDirectoryExists(targetDir, function () {
			copyFile(filePath, destinationFilePath, function () {
				Models.Client.findById(req.params.id)
					.then(function (client) {
						if (client) {
							client.updateAttributes({
								signature: req.files.file.name
							})
								.then(function (result) {
									res.status(201).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({
										error: err.message
									})
								})
						}
					})
			})
		})
	})

	app.post(BASE_URL + '/clients/:id/uploadlogo', function (req, res) {
		var filePath = req.files.file.path

		var targetDir = path.resolve(constants.CLIENT_LOGO_LOCATION)
		var destinationFilePath = path.join(targetDir, req.files.file.name)

		ensureDirectoryExists(targetDir, function () {
			copyFile(filePath, destinationFilePath, function () {
				Models.Client.findById(req.params.id)
					.then(function (client) {
						if (client) {
							client.updateAttributes({
								logo: req.files.file.name
							})
								.then(function (result) {
									res.status(201).send(result)
								})
								.catch(function (err) {
									return res.status(500).send({
										error: err.message
									})
								})
						}
					})
			})
		})
	})

	// Update client
	app.put(BASE_URL + '/clients/:id', function (req, res) {
		Models.Client.findById(req.params.id)
			.then(function (result) {
				delete req.body.signature
				delete req.body.logo
				if (req.body.reportPassword) {
					// encrypt and store
					req.body.reportPassword = crypt.encrypt(req.body.reportPassword)
					if (req.user.client && req.user.client.id == req.params.id) req.body.reportPasswordLastChange = new Date()
					else req.body.reportPasswordLastChange = null

				} else {
					delete req.body.reportPasswordLastChange
				}
				result.updateAttributes(req.body)
					.then(function (result) {
						res.status(200).send(result)
					})
					.catch(function (err) {
						return res.status(500).send({
							error: err.message
						})
					})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	// reset client report password
	app.put(BASE_URL + '/clients/:id/resetpassword', (req, res) => {
		Models.Client.update({
			reportPasswordLastChange: null
		}, {
			where: {
				id: req.params.id
			}
		}).then(() => {
			res.status(200).send({ success: true })
		}).catch(function (err) {
			return res.status(500).send({
				error: err.message
			})
		})
	})

	// Delete client
	app.delete(BASE_URL + '/clients/:id', function (req, res) {
		Models.Client.destroy({
			where: {
				id: req.params.id
			}
		})
			.then(function (result) {
				res.status(200).send({
					success: true
				})
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/clients/:id/costings', function (req, res) {
		Models.ClientCosting.findAll({
			where: {
				clientId: req.params.id
			},
			include: [Models.Campaign]
		})
			.then(function (results) {
				res.status(200).send(results)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/clients/:id/campaigns', function (req, res) {
		Models.Campaign.findAll({
			where: {
				clientId: req.params.id
			}
		})
			.then(function (results) {
				res.status(200).send(results)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/clients/:id/campaignnotes', function (req, res) {
		Models.CampaignNote.findAll({
			where: {
				clientId: req.params.id
			}
		})
			.then(function (result) {
				res.status(200).send(result)
			})
			.catch(function (err) {
				return res.status(500).send({
					error: err.message
				})
			})
	})

	app.get(BASE_URL + '/clients/:id/checkpassword', function (req, res) {
		if (req.query.ignore) {
			return res.status(200).send({
				result: true
			})
		}
		var pass = crypt.encrypt(req.query.password)
		if (!req.user.client) return res.status(200).send({
			result: false
		})
		if (req.user.client.reportPassword === pass) {
			res.status(200).send({
				result: true
			})
		} else {
			res.status(200).send({
				result: false
			})
		}
	})
}